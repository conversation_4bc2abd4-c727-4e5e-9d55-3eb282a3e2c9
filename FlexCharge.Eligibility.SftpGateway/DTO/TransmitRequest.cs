using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc.Validators;

namespace FlexCharge.Eligibility.SftpGateway.DTO;

public class TransmitRequest : RequestDTOBase, ITransmitAndEvaluateRequest
{
    [Required] public bool? IsDeclined { get; set; }


    public Guid? Mid { get; set; }

    [Required(ErrorMessage = "Merchant's orderId is required")]
    public string OrderId { get; set; } //External order Id

    public string? SiteUrl { get; set; }

    public string? OrderSource { get; set; }
    [Required] public Transaction Transaction { get; set; }
    [Required] public Payer Payer { get; set; }
    public Merchant? Merchant { get; set; }

    public List<OrderItem>? OrderItems { get; set; }


    public BillingInformation? BillingInformation { get; set; }
    public ShippingInformation? ShippingInformation { get; set; }

    /// <summary>
    /// Is Merchant-Initiated Transaction?
    /// </summary>
    public bool? IsMIT { get; set; }

    [RequiredIf(nameof(IsMIT), true)] public bool? IsRecurring { get; set; }

    [RequiredIf(nameof(IsRecurring), true)]
    public Subscription Subscription { get; set; }

    public List<AdditionalField> AdditionalFields { get; set; }


    public PaymentMethodLast4Digits PaymentMethod { get; set; }
}