// <auto-generated />
using System;
using FlexCharge.Eligibility.SftpGateway;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Eligibility.SftpGateway.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20230417132850_added-BatchSequenceNumber-column-to-requests-table")]
    partial class addedBatchSequenceNumbercolumntorequeststable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Eligibility.SftpGateway.Entities.Activity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccessLevel")
                        .HasColumnType("text");

                    b.Property<string>("ActionResult")
                        .HasColumnType("text");

                    b.Property<DateTime>("ActionTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<Guid>("CorrelationId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Data")
                        .HasColumnType("jsonb");

                    b.Property<string>("Domain")
                        .HasColumnType("text");

                    b.Property<string>("Event")
                        .HasColumnType("text");

                    b.Property<int>("InformationLevel")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRoot")
                        .HasColumnType("boolean");

                    b.Property<string>("Meta")
                        .HasColumnType("jsonb");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifyOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("PreviousActionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Source")
                        .HasColumnType("text");

                    b.Property<string>("SubCategory")
                        .HasColumnType("text");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.Property<string>("Version")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Activities");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.SftpGateway.Entities.Batch", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifyOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ProcessingStatus")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RequestFileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RequestFileName")
                        .IsUnique();

                    b.ToTable("Batches");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.SftpGateway.Entities.BatchRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("BatchId")
                        .HasColumnType("uuid");

                    b.Property<int>("BatchSequenceNumber")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EvaluationCompleted")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EvaluationStarted")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifyOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ProcessingStarted")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ProcessingStatus")
                        .HasColumnType("text");

                    b.Property<string>("ProcessorId")
                        .HasColumnType("text");

                    b.Property<string>("Request")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RequestFileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("RequestSessionKey")
                        .HasColumnType("uuid");

                    b.Property<string>("RequestType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Result")
                        .HasColumnType("text");

                    b.Property<string>("ResultsFileName")
                        .HasColumnType("text");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("BatchId");

                    b.ToTable("BatchRequests");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.SftpGateway.Entities.Merchant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Folder")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifyOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Merchants");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.SftpGateway.Entities.BatchRequest", b =>
                {
                    b.HasOne("FlexCharge.Eligibility.SftpGateway.Entities.Batch", "Batch")
                        .WithMany("Requests")
                        .HasForeignKey("BatchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Batch");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.SftpGateway.Entities.Batch", b =>
                {
                    b.Navigation("Requests");
                });
#pragma warning restore 612, 618
        }
    }
}
