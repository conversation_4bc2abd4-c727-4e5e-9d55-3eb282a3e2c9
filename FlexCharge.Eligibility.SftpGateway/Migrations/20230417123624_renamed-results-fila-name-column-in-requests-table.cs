using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.SftpGateway.Migrations
{
    /// <inheritdoc />
    public partial class renamedresultsfilanamecolumninrequeststable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EvaluationCompleted",
                table: "Batches");

            migrationBuilder.DropColumn(
                name: "EvaluationStarted",
                table: "Batches");

            migrationBuilder.RenameColumn(
                name: "ReportFileName",
                table: "BatchRequests",
                newName: "ResultsFileName");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ResultsFileName",
                table: "BatchRequests",
                newName: "ReportFileName");

            migrationBuilder.AddColumn<DateTime>(
                name: "EvaluationCompleted",
                table: "Batches",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "EvaluationStarted",
                table: "Batches",
                type: "timestamp with time zone",
                nullable: true);
        }
    }
}
