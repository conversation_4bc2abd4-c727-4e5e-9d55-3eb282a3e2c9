using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.SftpGateway.Activities;
using FlexCharge.Eligibility.SftpGateway.Services;
using FlexCharge.Eligibility.SftpGateway.Services.SftpGatewayService;
using FlexCharge.Eligibility.SftpGateway.Services.SftpService;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.SftpGateway.Consumers;

public class CreateSftpUserCommandConsumer :
    IdempotentCommandConsumer<CreateSftpUserCommand, CreateSftpUserCommandResponse>
{
    private readonly ISftpGatewayService _sftpGatewayService;
    private readonly ISftpService _sftpService;
    private readonly IActivityService _activityService;

    public CreateSftpUserCommandConsumer(ISftpService sftpService, ISftpGatewayService sftpGatewayService,
        IServiceScopeFactory serviceScopeFactory, IActivityService activityService) : base(serviceScopeFactory)
    {
        _sftpGatewayService = sftpGatewayService;
        _sftpService = sftpService;
        _activityService = activityService;
    }

    protected override async Task<CreateSftpUserCommandResponse> ConsumeCommand(CreateSftpUserCommand command,
        CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", command.Mid)
            .Baggage("FolderName", command.FolderName)
            .Baggage("UserName", command.UserName);

        Workspan.Log.Information("CreateSftpUserCommandConsumer started for {UserName} and {FolderName}",
            command.UserName, command.FolderName);
        try
        {
            var folderNameWithMid = $"{command.Mid.ToString("D")}";

            var result = await _sftpService.CreateSftpUserAsync(command.UserName, folderNameWithMid);

            await _sftpGatewayService.AddMerchantToWatchAsync(command.Mid, command.UserName, folderNameWithMid);

            if (result != null)
            {
                await _activityService.CreateActivityAsync(SftpGatewayActivities.CreateSftpUser,
                    set => set
                        .TenantId(command.Mid)
                        .Meta(meta => meta
                            .SetValue("FolderName", command.FolderName)
                            .SetValue("UserName", command.UserName)
                        ));

                return new CreateSftpUserCommandResponse
                {
                    Succeeded = true,
                    Enabled = true
                };
            }
        }
        catch (Exception e)
        {
            Workspan.RecordException(e);
            await _activityService.CreateActivityAsync(SftpGatewayActivitiesErrorActivities.CreateSftpUser_Error,
                set => set
                    .TenantId(command.Mid)
                    .Data(e)
                    .Meta(meta => meta
                        .SetValue("FolderName", command.FolderName)
                        .SetValue("UserName", command.UserName)
                    ));
        }

        return new CreateSftpUserCommandResponse
        {
            Succeeded = false
        };
    }
}