using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.SftpGateway.Activities;
using FlexCharge.Eligibility.SftpGateway.DTO;
using FlexCharge.Eligibility.SftpGateway.Entities;
using FlexCharge.Eligibility.SftpGateway.Services.S3WatcherBackgroundService;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.SftpGateway.Services.ResultsProcessorService;

class ResultsProcessorService : IResultsProcessorService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly IOptions<SftpGatewayOptions> _sftpGatewayOptions;
    private readonly ICloudStorage _cloudStorage;
    private readonly IActivityService _activityService;

    private string BucketName => _sftpGatewayOptions.Value.Bucket;
    private string RootFolderToWatch => _sftpGatewayOptions.Value.RootFolderToWatch;
    private string ProcessingRootFolder => _sftpGatewayOptions.Value.ProcessingRootFolder;

    public ResultsProcessorService(PostgreSQLDbContext dbContext,
        IServiceScopeFactory scopeFactory,
        IOptions<SftpGatewayOptions> sftpGatewayOptions,
        ICloudStorage cloudStorage,
        IActivityService activityService)
    {
        _dbContext = dbContext;
        _scopeFactory = scopeFactory;
        _sftpGatewayOptions = sftpGatewayOptions;
        _cloudStorage = cloudStorage;
        _activityService = activityService;
    }

    public async Task ProcessResultsAsync(CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ResultsProcessorService>()
            .LogEnterAndExit();

        using (var serviceScope = _scopeFactory.CreateScope())
        {
            List<string> terminalProcessingStatuses = new()
            {
                nameof(RequestProcessingStatus.Processed),
                nameof(RequestProcessingStatus.CancelledByMerchant),
                //nameof(RequestProcessingStatus.Expired),
                //nameof(RequestProcessingStatus.Failed) //// not terminal - can be retried?
            };

            //Selecting requests that where completed, but not reported yet
            var newlyProcessedRequests = await _dbContext.BatchRequests
                .Where(x =>
                    terminalProcessingStatuses.Contains(x.ProcessingStatus) &&
                    x.ResultsFileName == null)
                .ToListAsync();

            workspan.Log.Information("Processing {Count} results", newlyProcessedRequests.Count);
            await _activityService.CreateActivityAsync(SftpGatewayActivities.ProcessResults,
                set => set
                    .TenantId(newlyProcessedRequests.FirstOrDefault()?.Mid)
                    .Meta(meta => meta
                        .SetValue("Count", newlyProcessedRequests.Count)
                    ));

            string resultsFilePath;

            foreach (var requestsFromOneBatch in newlyProcessedRequests
                         .GroupBy(x => x.RequestFileName))
            {
                var aRequest = requestsFromOneBatch.First();
                var batchRequestFilePath = aRequest.RequestFileName;
                string batchRequestFolder = Path.GetDirectoryName(batchRequestFilePath);
                string batchRequestFileName = Path.GetFileName(batchRequestFilePath);


                DTO.EvaluateResults results = new();
                results.Mid = aRequest.Mid;
                results.BatchId = aRequest.BatchId;
                results.Results = new List<EvaluateResult>();

                FileProcessor fileProcessor = new(BucketName,
                    RootFolderToWatch, ProcessingRootFolder,
                    batchRequestFolder, batchRequestFileName);

                resultsFilePath = fileProcessor.GetResultsFilePath();

                workspan.Log.Information("Saving results to {ResultsFileName} file", resultsFilePath);
                await _activityService.CreateActivityAsync(SftpGatewayActivities.ProcessResults_SaveResults,
                    set => set
                        .TenantId(aRequest.Mid)
                        .Meta(meta => meta
                            .SetValue("ResultsFileName", resultsFilePath)
                        ));

                foreach (var request in requestsFromOneBatch)
                {
                    request.ResultsFileName = resultsFilePath;

                    if (request.Result != null)
                    {
                        try
                        {
                            var evaluateResult = new EvaluateResult
                            {
                                SequenceNumber = request.BatchSequenceNumber,
                                OrderSessionKey = request.RequestSessionKey,
                                OrderId = request.OrderId,
                                Status = request.Result
                            };

                            results.Results.Add(evaluateResult);
                        }
                        catch (Exception e)
                        {
                            workspan.RecordException(e);
                        }
                    }
                }

                using (MemoryStream resultsFileMemoryStream = new())
                {
                    using (var writer = new StreamWriter(resultsFileMemoryStream))
                    {
                        await writer.WriteAsync(JsonConvert.SerializeObject(results));
                        await writer.FlushAsync();

                        resultsFileMemoryStream.Position = 0;

                        await _cloudStorage.UploadFileAsync(resultsFileMemoryStream, fileProcessor.BucketName,
                            Path.GetFileName(resultsFilePath), Path.GetDirectoryName(resultsFilePath));

                        await fileProcessor.CreateFileCopyInProcessedFolderAsync(_cloudStorage, resultsFilePath);
                    }
                }

                await _dbContext.SaveChangesAsync();
            }
        }
    }
}