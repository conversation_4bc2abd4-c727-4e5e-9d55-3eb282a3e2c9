using System.Threading.Tasks;

namespace FlexCharge.Eligibility.SftpGateway.Services.SftpService;

public interface ISftpService
{
    Task<dynamic> CreateSftpUserAsync(string username, string folderName,string publicSshKey);
    Task<dynamic> CreateSftpUserAsync(string username, string folderName);
    Task<dynamic> GetSftpUserAsync(string username,string serverId);
    Task<dynamic> GetSftpUserAsync(string username);
    Task<dynamic> CreateSftpUserAsync(string targetFolder, string username, string serverId, string arnRole);
    Task<string> UpdateSftpUserSsh(string username,string serverId,string publicSshKey);
    Task ResetSftpSshAccess(string username);
    Task CreateFolderAsync(string bucketName, string folderName);
    Task DeleteSftpUserSsh(string userName, string serverId, string sshPublicKeyId);
}