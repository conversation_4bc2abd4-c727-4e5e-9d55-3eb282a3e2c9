using System;
using System.Collections.Concurrent;

namespace FlexCharge.Eligibility.SftpGateway.Services.BatchProcessorBackgroundService;

public class BatchProcessorOptions
{
    public int MaxConcurrentRequestsToProcess { get; set; } = 5;
    /// <summary>
    /// Maximum batch file size in MB
    /// </summary>
    public long MaximumBatchFileSize { get; set; } = 200;

    public int MaximumRequestsPerBatch { get; set; } = 2000;
}