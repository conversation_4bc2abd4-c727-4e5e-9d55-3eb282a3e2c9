using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.SftpGateway.DTO;
using FlexCharge.Eligibility.SftpGateway.Entities;
using FlexCharge.Eligibility.SftpGateway.Enums;
using FlexCharge.Eligibility.SftpGateway.Exceptions;
using MassTransit;
using MassTransit.Util;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.SftpGateway.Services.S3WatcherBackgroundService;

public partial class S3WatcherBackgroundService
{
    private readonly IPublishEndpoint _publishEndpoint;

    private async Task ProcessNewFileAsync(string folder1, string fileName1,
        CancellationToken cancellationToken = default)
    {
        using var workspan = Workspan.Start<S3WatcherBackgroundService>()
            .LogEnterAndExit();

        using var serviceScope = _scopeFactory.CreateScope();
        var cloudStorage = serviceScope.ServiceProvider.GetRequiredService<ICloudStorage>();

        FileProcessor fileProcessor = new(BucketName,
            RootFolderToWatch, ProcessingRootFolder,
            folder1, fileName1);

        try
        {
            workspan
                .Baggage("MerchantName", fileProcessor.MerchantName)
                .Baggage("Mid", fileProcessor.Mid);

            if (fileProcessor.MerchantName != null)
            {
                workspan.Log.Information("Processing file {FileName} in folder {Folder} for merchant {Mid}",
                    fileProcessor.FileName, fileProcessor.Folder, fileProcessor.Mid);


                //var processingFileName = GetFileNameWithRandomPostfix(fileName, mid);

                await EnsureMerchantHasAccessToSftpProcessingAsync(fileProcessor.Mid, fileProcessor.Folder);

                switch (fileProcessor.RequestType)
                {
                    case RequestType.Evaluate:
                        await fileProcessor.MoveToProcessingFolderAsync(cloudStorage);
                        await ProcessBatchFileAsync<EvaluateRequest>(fileProcessor, cancellationToken);
                        break;
                    case RequestType.Transmit:
                        await fileProcessor.MoveToProcessingFolderAsync(cloudStorage);
                        await ProcessBatchFileAsync<TransmitRequest>(fileProcessor, cancellationToken);
                        break;
                    case null:
                        workspan.Log.Warning(
                            "File {FileName} in folder {Folder} for merchant {Mid} is not supported - no file type prefix specified",
                            fileProcessor.FileName, fileProcessor.Folder, fileProcessor.Mid);

                        await fileProcessor.MoveToQuarantineFolderAsync(cloudStorage);

                        break;
                    default:
                        throw new ArgumentException("unknown  batch request type");
                }
            }
            else
            {
                workspan.Log.Information("Skipping file {FileName} in folder {Folder} - no merchant info found",
                    fileProcessor.FileName, fileProcessor.Folder);
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Error processing file {FileName} in folder {Folder}", fileProcessor.FileName,
                fileProcessor.Folder);
        }
    }

    private async Task ProcessBatchFileAsync<T>(
        FileProcessor fileProcessor, CancellationToken cancellationToken)
        where T : RequestDTOBase, ITransmitAndEvaluateRequest
    {
        using var workspan = Workspan.Start<S3WatcherBackgroundService>();

        using var serviceScope = _scopeFactory.CreateScope();

        List<string> errorsInFile = new();
        Guid? createdBatchId = null;
        List<(string orderId, Guid orderSessionKey)> submittedRequests = new();
        List<(string orderId, List<string> errors)> errorsPerEvaluateRequest = new();

        try
        {
            RequestsBatch<T> requestsBatch = null;
            Guid midFromAuthorizationToken = Guid.Empty;

            if (await IsFileAlreadyProcessedAsync(fileProcessor.FilePath))
            {
                workspan.Log.Error("Duplicate file {FilePath} cannot be processed", fileProcessor.FilePath);

                errorsInFile.Add("Duplicate file name. All inbound file names must be unique.");
                ThrowHelper.ThrowException_UnprocessableFile();
            }

            var batchFileSize = await GetBatchFileSizeAsync(fileProcessor, serviceScope);

            bool fileTooBig = await CheckForMaximumBatchFileSizeAsync(errorsInFile, batchFileSize);

            bool duplicateBatchFile = await CheckForDuplicateBatchFileAsync(errorsInFile, midFromAuthorizationToken,
                fileProcessor.FileName, batchFileSize);

            if (!fileTooBig && !duplicateBatchFile)
            {
                RequestsBatchHeader requestsBatchHeader = null;

                #region Checking JWT Access Token

                try
                {
                    requestsBatchHeader =
                        await DeserializeBatchFileAsync<RequestsBatchHeader>(fileProcessor.BucketName,
                            fileProcessor.FilePath,
                            cancellationToken);
                }
                catch (Exception e)
                {
                    errorsInFile.Add(e.Message);
                    throw;
                }

                var authorizationJwtToken = requestsBatchHeader.AuthorizationToken;

                EnsureMerchantHasAccessToBatchEvaluateOrTransmitRequests(fileProcessor.Mid, authorizationJwtToken,
                    out midFromAuthorizationToken);

                #endregion

                #region Deserializing batch evaluate request

                try
                {
                    object reqBatch = fileProcessor.RequestType switch
                    {
                        RequestType.Evaluate => await DeserializeBatchFileAsync<EvaluateRequestBatch>(
                            fileProcessor.BucketName,
                            fileProcessor.FilePath,
                            cancellationToken),
                        RequestType.Transmit => await DeserializeBatchFileAsync<TransmitRequestBatch>(
                            fileProcessor.BucketName,
                            fileProcessor.FilePath,
                            cancellationToken),

                        _ => throw new ArgumentException("Unknown  request typet")
                    };
                    requestsBatch = (RequestsBatch<T>) reqBatch;
                }
                catch (Exception e)
                {
                    errorsInFile.Add(e.Message);
                    throw;
                }

                #endregion


                await ValidateRequestBatchAsync(requestsBatch, errorsInFile, errorsPerEvaluateRequest,
                    midFromAuthorizationToken, fileProcessor.FileName, batchFileSize);
            }

            #region Loging errors

            bool hasAnyInvalidRequests = errorsPerEvaluateRequest.Any(x => x.errors.Any());

            if (errorsInFile.Any() || hasAnyInvalidRequests)
            {
                if (errorsInFile.Any())
                {
                    workspan.Log.Warning("File {FilePath} is not valid: {Errors}", fileProcessor.FilePath,
                        JsonConvert.SerializeObject(errorsInFile));
                }

                if (hasAnyInvalidRequests)
                {
                    workspan.Log.Warning("One or more requests are invalid in file {FilePath}: {Errors}",
                        fileProcessor.FilePath,
                        JsonConvert.SerializeObject(errorsPerEvaluateRequest));
                }
            }
            else
            {
                workspan.Log.Information("File {FilePath} is valid", fileProcessor.FilePath);

                #endregion


                #region Processing batch evaluate request

                using (var dbContext = serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>())
                {
                    var batch = new Entities.Batch
                    {
                        Mid = midFromAuthorizationToken,
                        Pid = requestsBatch.Pid,
                        BatchTimeStamp = requestsBatch.BatchTimeStamp,
                        RequestFileName = fileProcessor.FilePath,
                        ProcessingStatus = nameof(BatchProcessingStatus.Submitted),
                        FileSize = batchFileSize
                    };

                    dbContext.Batches.Add(batch);

                    await dbContext.SaveChangesAsync();

                    createdBatchId = batch.Id;
                    if (fileProcessor.RequestType == RequestType.Evaluate)
                    {
                        for (int i = 0; i < requestsBatch.Requests.Count; i++)
                        {
                            var request = requestsBatch.Requests[i];

                            // try
                            // {
                            Guid requestSessionKey = Guid.NewGuid();

                            if (request.Mid == null)
                            {
                                request.Mid = batch.Mid;
                            }

                            var batchRequest = new Entities.BatchRequest
                            {
                                RequestType = fileProcessor.RequestType.ToString(),
                                RequestFileName = fileProcessor.InitialFilePath,
                                Mid = midFromAuthorizationToken,
                                OrderId = request.OrderId,
                                Amount = request.Transaction.Amount,
                                BatchSequenceNumber = request.SequenceNumber,
                                BatchId = batch.Id,
                                RequestSessionKey = requestSessionKey,
                                ProcessingStatus = nameof(RequestProcessingStatus.Submitted),
                                Request = JsonConvert.SerializeObject(request),
                            };

                            dbContext.BatchRequests.Add(batchRequest);

                            // var orderSessionKey = await ProcessEvaluateRequestAsync(serviceScope, mid, evaluateRequest,
                            //     cancellationToken);

                            submittedRequests.Add((request.OrderId, requestSessionKey));
                            // }
                            // catch (Exception e)
                            // {
                            //     errorsPerEvaluateRequest.Add((request.OrderId, new List<string> {e.Message}));
                            // }
                        }
                    }
                    else if (fileProcessor.RequestType == RequestType.Transmit)
                    {
                        var publishEndpoint = serviceScope.ServiceProvider.GetService<IPublishEndpoint>();
                        var mapper = serviceScope.ServiceProvider.GetRequiredService<IMapper>();
                        for (int i = 0; i < requestsBatch.Requests.Count; i++)
                        {
                            var request = requestsBatch.Requests[i];

                            if (request.Mid == null)
                            {
                                request.Mid = batch.Mid;
                            }

                            TransmitRecordEvent transmitRecordEvent = new();
                            mapper.Map(request, transmitRecordEvent);

                            await publishEndpoint.Publish(transmitRecordEvent);
                        }
                    }

                    await dbContext.SaveChangesAsync();
                }

                #endregion
            }
        }
        catch (SecurityException sex)
        {
            workspan.RecordException(sex, "Security exception {ExceptionMessage}. Processing file: {FilePath}",
                sex.Message, fileProcessor.FilePath);

            errorsInFile.Add($"{sex.Message}");

            throw;
        }
        catch (Exception e)

        {
            workspan.RecordException(e, "Error processing file {FilePath}", fileProcessor.FilePath);

            errorsInFile.Add($"Error processing file");
            throw;
        }
        finally
        {
            await GenerateSubmittedFileAsync(
                fileProcessor, createdBatchId,
                submittedRequests, errorsInFile,
                errorsPerEvaluateRequest, cancellationToken);


            var cloudStorage = serviceScope.ServiceProvider.GetRequiredService<ICloudStorage>();


            if (createdBatchId.HasValue)
            {
                await fileProcessor.MoveToProcessedFolderAsync(cloudStorage);
            }
            else
            {
                await fileProcessor.MoveToInvalidFolderAsync(cloudStorage);
            }
        }
    }

    private static async Task<long> GetBatchFileSizeAsync(FileProcessor fileProcessor, IServiceScope serviceScope)
    {
        var cloudStorage = serviceScope.ServiceProvider.GetRequiredService<ICloudStorage>();
        long batchFileSize = await fileProcessor.GetFileSizeAsync(cloudStorage);
        return batchFileSize;
    }

    private async Task<bool> IsFileAlreadyProcessedAsync(string filePath)
    {
        using var serviceScope = _scopeFactory.CreateScope();

        using (var dbContext = serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>())
        {
            return await dbContext.Batches.AnyAsync(x => x.RequestFileName == filePath);
        }
    }

    private async Task GenerateSubmittedFileAsync(
        FileProcessor fileProcessor, Guid? createdBatchId,
        List<(string orderId, Guid orderSessionKey)> submittedOrders,
        List<string> batchFileProcessingErrors,
        List<(string orderId, List<string> errors)> errorsInFile, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<S3WatcherBackgroundService>();

        using var serviceScope = _scopeFactory.CreateScope();

        var submittedFilePath = fileProcessor.GetSubmittedFilePath();

        var submittedFileContent = new SubmittedResult
        {
            BatchCreated = createdBatchId.HasValue,
            BatchId = createdBatchId,
            SubmittedRequests = submittedOrders.Select(x => new SubmittedEvaluateRequestDTO()
            {
                OrderId = x.orderId,
                OrderSessionKey = x.orderSessionKey
            }),
            FileProcessingErrors = batchFileProcessingErrors,
            InvalidRequests = errorsInFile.Where(x => x.errors.Any()).Select(x => new InvalidRequestDTO()
            {
                OrderId = x.orderId,
                Errors = x.errors
            })
        };

        var cloudStorage = serviceScope.ServiceProvider.GetRequiredService<ICloudStorage>();

        using (var stream = new MemoryStream())
        {
            await SerializeJsonToStreamAsync(submittedFileContent, stream, cancellationToken);
            stream.Position = 0;

            await cloudStorage.UploadFileAsync(stream, BucketName, submittedFilePath);
        }

        workspan.Log.Information("Submitted file {SubmittedFilePath} generated for request file {ProcessedFilePath}",
            submittedFilePath, submittedFilePath);


        // Storing a copy of submitted file in processing folder
        await fileProcessor.CreateFileCopyInProcessedFolderAsync(cloudStorage, submittedFilePath);
    }
}