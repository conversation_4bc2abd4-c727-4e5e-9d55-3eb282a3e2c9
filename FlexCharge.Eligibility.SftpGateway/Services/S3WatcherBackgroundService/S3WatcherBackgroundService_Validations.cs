using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.SftpGateway.DTO;
using FlexCharge.Eligibility.SftpGateway.Enums;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.SftpGateway.Services.S3WatcherBackgroundService;

public partial class S3WatcherBackgroundService
{
    //TODO: Must be set in Merchant Portal
    private static readonly TimeSpan MINIMUM_MIT_EXPIRATION_INTERVAL =
        TimeSpan.FromHours(24).Add(TimeSpan.FromMinutes(-1));

    private async Task ValidateRequestBatchAsync<T>(RequestsBatch<T> requestsBatch,
        List<string> errorsInFile, List<(string orderId, List<string> errors)> errorsPerEvaluateRequest,
        Guid midFromAuthorizationToken, string batchFileName, long batchFileSize)
        where T : RequestDTOBase//, ITransmitAndEvaluateRequest
    {
        using var workspan = Workspan.Start<S3WatcherBackgroundService>();

        var batchValidationContext = new ValidationContext(requestsBatch);

        List<ValidationResult> validationResults = new();

        if (!Validator.TryValidateObject(requestsBatch, batchValidationContext, validationResults, true))
        {
            //errorsInFile.AddRange(validationResults.Select(x =>$"Members: {JsonConvert.SerializeObject(x.MemberNames)}. Error: {x.ErrorMessage}"));
            errorsInFile.AddRange(validationResults.Select(x => x.ErrorMessage));
        }

        if (requestsBatch.Mid != midFromAuthorizationToken)
        {
            errorsInFile.Add("MerchantId (Mid) from authorization token is not equal to the one in the batch header");
        }


        IList<T> requests = GetRequestsList(requestsBatch);

        bool fileContainsTooManyRequests = await CheckForMaximumRequestsPerBatchFile(errorsInFile, requests.Count);

        if (!fileContainsTooManyRequests)
        {
            for (int i = 1; i <= requests.Count; i++)
            {
                var request = requests[i - 1];

                string errorPathPrefix = $"Request[{i}]";
                if (request.SequenceNumber != i)
                {
                    errorsInFile.Add(errorPathPrefix + ": " +
                                     "Invalid or missing sequenceNumber (must be equal to the request index in the list, starting from 1)");
                }

                var requestValidationContext = new ValidationContext(request);

                if (!Validator.TryValidateObject(request, requestValidationContext, validationResults, true))
                {
                    //errorsInFile.AddRange(validationResults.Select(x =>$"Members: {JsonConvert.SerializeObject(x.MemberNames)}. Error: {x.ErrorMessage}"));
                    //errorsInFile.AddRange(validationResults.Select(x => x.ErrorMessage));
                }

                var errorsInRequest = new List<string>();

                var requestId =
                    await ValidateRequestAsync(errorsInRequest, request, requestsBatch, errorPathPrefix,
                        midFromAuthorizationToken, requestsBatch.Pid);

                errorsPerEvaluateRequest.Add((requestId, errorsInRequest));
            }
        }
    }

    private async Task<bool> CheckForMaximumBatchFileSizeAsync(List<string> errorsInFile, long batchFileSize)
    {
        using var workspan = Workspan.Start<S3WatcherBackgroundService>();

        bool fileTooBig = false;

        long maximumBatchFileSizeInBytes = _batchProcessorOptions.Value.MaximumBatchFileSize * 1024 * 1024;

        if (batchFileSize > maximumBatchFileSizeInBytes)
        {
            errorsInFile.Add(
                $"Batch file size exceeds the maximum allowed ({_batchProcessorOptions.Value.MaximumBatchFileSize} MB)");
            fileTooBig = true;
        }

        return fileTooBig;
    }

    private async Task<bool> CheckForMaximumRequestsPerBatchFile(List<string> errorsInFile, int requestsInFile)
    {
        using var workspan = Workspan.Start<S3WatcherBackgroundService>();

        bool fileTooBig = false;

        if (requestsInFile > _batchProcessorOptions.Value.MaximumRequestsPerBatch)
        {
            errorsInFile.Add(
                $"Number of requests exceeds the maximum allowed ({_batchProcessorOptions.Value.MaximumRequestsPerBatch.ToString()})");

            fileTooBig = true;
        }

        return fileTooBig;
    }

    private async Task<bool> CheckForDuplicateBatchFileAsync(List<string> errorsInFile,
        Guid midFromAuthorizationToken, string batchFileName, long batchFileSize)
    {
        using var workspan = Workspan.Start<S3WatcherBackgroundService>();

        bool duplicateBatchFile = false;

        using var serviceScope = _scopeFactory.CreateScope();

        using (var dbContext = serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>())
        {
            var duplicateBatchFileFound = await dbContext.Batches
                .Where(x =>
                    x.Mid == midFromAuthorizationToken &&
                    x.RequestFileName == batchFileName ||
                    x.FileSize == batchFileSize)
                .AnyAsync();

            if (duplicateBatchFileFound)
            {
                errorsInFile.Add("Duplicate batch file");
                duplicateBatchFile = true;
            }
            
        }

        return duplicateBatchFile;
    }


    private async Task CheckForDuplicateOrdersAsync(List<string> errorsInRequest, ITransmitAndEvaluateRequest request,
        string errorPathPrefix, Guid midFromAuthorizationToken, string requestId)
    {
        using var workspan = Workspan.Start<S3WatcherBackgroundService>();

        using var serviceScope = _scopeFactory.CreateScope();

        using (var dbContext = serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>())
        {
            var duplicateOrderFound = await dbContext.BatchRequests
                .Where(x =>
                    x.Mid == midFromAuthorizationToken &&
                    x.OrderId == request.OrderId &&
                    x.Amount == request.Transaction.Amount)
                .AnyAsync();

            if (duplicateOrderFound)
            {
                errorsInRequest.Add(errorPathPrefix + ": " + "Duplicate order");
            }
        }
    }

    private async Task<string> ValidateRequestAsync<T>(List<string> errorsInRequest, T request,
        RequestsBatch<T> requestsBatch,
        string errorPathPrefix,
        Guid midFromAuthorizationToken, Guid? partnerId)
        where T : RequestDTOBase//, ITransmitAndEvaluateRequest
    {
        string requestId = null;
        requestId = await ValidateBatchRequestAsync(errorsInRequest, (ITransmitAndEvaluateRequest)request,
            requestsBatch, errorPathPrefix, midFromAuthorizationToken, partnerId);
        return requestId;
    }

    private async Task<string> ValidateBatchRequestAsync<T>(List<string> errorsInFile,
        ITransmitAndEvaluateRequest request,
        RequestsBatch<T> requestsBatch,
        string errorPathPrefix, Guid midFromAuthorizationToken, Guid? partnerId)
        where T : RequestDTOBase//, ITransmitAndEvaluateRequest
    {
        string requestId = request.OrderId;


        if (request.Mid != null && request.Mid != midFromAuthorizationToken)
        {
            errorsInFile.Add("MerchantId (Mid) from authorization token is not equal to the one in the request");
        }

        if (request is EvaluateRequest evaluateRequest)
        {
            
            await CheckForDuplicateOrdersAsync(errorsInFile, (ITransmitAndEvaluateRequest)request, errorPathPrefix,
                midFromAuthorizationToken,
                requestId);

            
            if (evaluateRequest.ExpiryDateUtc == null   && requestsBatch is EvaluateRequestBatch  reqBatch)
            {
                if (reqBatch!.ExpiryDateUtc != null)
                {
                    // ExpiryDateUtc is not specified in the request, use the default value from the batch
                    evaluateRequest.ExpiryDateUtc = reqBatch.ExpiryDateUtc;
                }
                else
                {
                    errorsInFile.Add(
                        $"{nameof(evaluateRequest.ExpiryDateUtc)} is not specified in the request and there is no default value in the batch");
                }
            }

            #region ExpiryDate Validation

            if (request.IsMIT == true && evaluateRequest.ExpiryDateUtc != null &&
                evaluateRequest.ExpiryDateUtc - DateTime.UtcNow < MINIMUM_MIT_EXPIRATION_INTERVAL)
            {
                errorsInFile.Add($"{nameof(evaluateRequest.ExpiryDateUtc)} is lower than allowed");
            }

            #endregion
        }

        #region Validating nested objects

        ValidateRequiredObject(request, errorsInFile, errorPathPrefix);

        var paymentMethod = request switch
        {
            EvaluateRequest erp => erp.PaymentMethod,
            TransmitRequest erp => erp.PaymentMethod,
            _ => throw new ArgumentException("Unknown batch request type")
        };


        ValidateRequiredObject("PaymentMethod", errorsInFile, errorPathPrefix, "PaymentMethod");
        ValidateRequiredObject(request.Transaction, errorsInFile, errorPathPrefix, nameof(request.Transaction));
        ValidateRequiredObject(request.Payer, errorsInFile, errorPathPrefix, nameof(request.Payer));

        if (partnerId != null || request.Merchant != null) //Merchant is required for partner integrations
        {
            ValidateRequiredObject(request.Merchant, errorsInFile, errorPathPrefix, nameof(request.Merchant));
        }

        if (request.OrderItems != null)
        {
            ValidateRequiredList(request.OrderItems, errorsInFile, errorPathPrefix,
                nameof(request.OrderItems));
        }

        ValidateRequiredObject(request.BillingInformation, errorsInFile, errorPathPrefix,
            nameof(request.BillingInformation));

        if (request.ShippingInformation != null)
        {
            ValidateRequiredObject(request.ShippingInformation, errorsInFile, errorPathPrefix,
                nameof(request.ShippingInformation));
        }

        if (request.Subscription != null)
        {
            ValidateRequiredObject(request.Subscription, errorsInFile, errorPathPrefix,
                nameof(request.Subscription));
        }

        if (request.AdditionalFields != null)
        {
            ValidateRequiredList(request.AdditionalFields, errorsInFile, errorPathPrefix,
                nameof(request.AdditionalFields));
        }

        #endregion

        return requestId;
    }

    private static void ValidateRequiredObject(object objectToValidate, List<string> errorsInFile,
        string errorPathPrefix,
        params string[] memberPathNames)
    {
        var errorPath = errorPathPrefix;
        if (memberPathNames.Length > 0)
        {
            errorPath += "." + string.Join(".", memberPathNames) + ": ";
        }

        errorPath += ": ";

        if (objectToValidate == null)
        {
            errorsInFile.Add(errorPath + "This field is required");
            return;
        }

        List<ValidationResult> validationResults = new();
        var validationContext = new ValidationContext(objectToValidate);

        if (!Validator.TryValidateObject(objectToValidate, validationContext, validationResults, true))
        {
            errorsInFile.AddRange(validationResults.Select(x => errorPath + x.ErrorMessage));
        }
    }

    private static void ValidateRequiredList(IList listToValidate, List<string> errorsInFile, string errorPathPrefix,
        string memberName)
    {
        if (listToValidate == null)
        {
            errorsInFile.Add(errorPathPrefix + ":" + "This field is required");
            return;
        }

        for (int j = 0; j < listToValidate.Count; j++)
        {
            ValidateRequiredObject(listToValidate[j], errorsInFile, errorPathPrefix + $".{memberName}[{j}]");
        }
    }

    // private static string ToCamelCase(string text)
    // {
    //     if (string.IsNullOrEmpty(text) || !char.IsLetter(text[0])) return text;
    //
    //     return char.ToLowerInvariant(text[0]) + text.Substring(1);
    // }

    private static IList<T> GetRequestsList<T>(RequestsBatch<T> requestsBatch)
        where T : RequestDTOBase//, ITransmitAndEvaluateRequest
    {
        return requestsBatch.Requests.ToList();
    }
}