using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Common;
using Flexcharge.Eligibility.StripeAdapter.Ingestion;
using Stripe;

namespace FlexCharge.Eligibility.Adapters.Stripe.Ingestion.EventHandlers.Applications;

class AccountApplicationAuthorizedEventHandler : StripeWebhookEventHandlerBase<Application>
{
    public override string EventType => EventTypes.AccountApplicationAuthorized;

    protected override async Task HandleEventInternalAsync()
    {
        using var workspan = Workspan.Start<AccountApplicationAuthorizedEventHandler>();

        var application = EventData;

        var mid = await GetMerchantConfigurationAsync(OAuthProvider.Stripe, Context.StripeAccount);

        workspan
            .Baggage("Mid", mid)
            .Baggage("ApplicationId", application.Id)
            .Baggage("ApplicationName", application.Name)
            .Log.Information("Application authorized");
    }
}