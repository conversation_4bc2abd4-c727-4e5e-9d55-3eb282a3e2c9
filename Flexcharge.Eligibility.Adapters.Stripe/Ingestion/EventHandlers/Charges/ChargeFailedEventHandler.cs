using Flexcharge.Eligibility.StripeAdapter.Ingestion;
using Stripe;

namespace FlexCharge.Eligibility.Adapters.Stripe.Ingestion.EventHandlers.Charges;

class ChargeFailedEventHandler : StripeWebhookEventHandlerBase<Charge>
{
    public override string EventType => EventTypes.ChargeFailed;

    protected override async Task HandleEventInternalAsync()
    {
        // var processDeclineService = GetRequiredService<IProcessDeclineService>();
        // await processDeclineService.StoreDeclineDataAndTryToProcessAsync(stripeEvent.InvoiceId, EventType,
        //     rawRequestBody);
    }
}