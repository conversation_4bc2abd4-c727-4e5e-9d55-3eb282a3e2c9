using FlexCharge.Common.Shared.Adapters;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Adapters.Stripe.EventHandlers;
using FlexCharge.Eligibility.Adapters.Stripe.Services.StripeService;
using Flexcharge.Eligibility.StripeAdapter.Ingestion;
using FluentValidation;
using MassTransit;
using Stripe;

namespace FlexCharge.Eligibility.Adapters.Stripe.Ingestion.EventHandlers.Invoices;

class InvoiceMarkedUncollectibleEventHandler : StripeWebhookEventHandlerBase<Invoice>
{
    public override string EventType => EventTypes.InvoiceMarkedUncollectible;

    protected override Task HandleEventInternalAsync()
    {
        // Note: event if invoice is marked as uncollectible, it still can be retried
        return Task.CompletedTask;
    }

    // protected override async Task HandleEventInternalAsync()
    // {
    //     using var workspan = Workspan.Start<InvoiceMarkedUncollectibleEventHandler>();
    //
    //     var invoice = EventData;
    //
    //     var mid = await GetMidAsync(Context.StripeAccount);
    //
    //     workspan
    //         .Baggage("Mid", mid)
    //         .Baggage("InvoiceId", invoice.Id);
    //
    //     if (!invoice.Metadata.TryGetValue(FlexFactorMetadata.FlexFactorOrderId, out var orderIdString))
    //     {
    //         workspan.Log.Information("No OrderId found in Stripe metadata");
    //         return; //!!!
    //     }
    //
    //     workspan
    //         .Baggage("OrderId", orderIdString);
    //
    //     var orderId = Guid.Parse(orderIdString);
    //
    //     var publisher = GetRequiredService<IPublishEndpoint>();
    //
    //     await PublishInvoiceMarkedUncollectibleEvent(publisher, mid, orderId, invoice.Id);
    // }

    // private static async Task PublishInvoiceMarkedUncollectibleEvent(IPublishEndpoint publisher, Guid mid, Guid orderId,
    //     string invoiceId)
    // {
    //     await publisher.Publish(new ExternalProviderInvoiceMarkedUncollectibleEvent(mid, orderId, invoiceId));
    // }
}