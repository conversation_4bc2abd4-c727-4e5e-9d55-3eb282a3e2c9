using FlexCharge.Common.Cloud.SecretsManager;
using FlexCharge.Common.Shared.Adapters;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Common;
using FlexCharge.Eligibility.Adapters.Stripe.EventHandlers;
using FlexCharge.Eligibility.Adapters.Stripe.Services.StripeService;
using Flexcharge.Eligibility.StripeAdapter.Helpers;
using Flexcharge.Eligibility.StripeAdapter.Ingestion;
using FluentValidation;
using MassTransit;
using Stripe;

namespace FlexCharge.Eligibility.Adapters.Stripe.Ingestion.EventHandlers.Invoices;

class InvoicePaidEventHandler : StripeWebhookEventHandlerBase<Invoice>
{
    public override string EventType => EventTypes.InvoicePaid;

    protected override AbstractValidator<Invoice> GetEventDataValidator() => new PaidInvoiceValidator();

    protected override async Task HandleEventInternalAsync()
    {
        using var workspan = Workspan.Start<InvoicePaidEventHandler>();

        var invoice = EventData;

        var merchantConfiguration = await GetMerchantConfigurationAsync(OAuthProvider.Stripe, Context.StripeAccount);

        workspan
            .Baggage("Mid", merchantConfiguration)
            .Baggage("InvoiceId", invoice.Id);

        if (!invoice.Metadata.TryGetValue(FlexFactorMetadata.FlexFactorOrderId, out var orderIdString))
        {
            workspan.Log.Information("No OrderId found in Stripe metadata");
            return; //!!!
        }

        workspan
            .Baggage("OrderId", orderIdString);

        if (invoice.Currency != "usd")
        {
            workspan.Log.Fatal("Invoice currency is not USD");
            return;
        }


        var orderId = Guid.Parse(orderIdString);

        bool markedAsRescuedByFlex;
        if (invoice.Metadata.TryGetValue(FlexFactorMetadata.FlexFactorRescuedAt, out var rescuedAt))
        {
            markedAsRescuedByFlex = true;
        }
        else
        {
            markedAsRescuedByFlex = false;
        }

        // Invoice already paid out of band? We can't mark it as rescued by FlexFactor
        bool paidOutOfBand = false;
        bool rescuedByFlex;
        if (invoice.PaidOutOfBand)
        {
            paidOutOfBand = true;

            if (!markedAsRescuedByFlex)
            {
                workspan.Log.Information("Invoice paid out of band - not rescued by FlexFactor");
                rescuedByFlex = false;
            }
            else
            {
                workspan.Log.Information("Invoice paid out of band - rescued by FlexFactor");
                rescuedByFlex = true;
            }
        }
        else
        {
            var stripeProviderService = GetRequiredService<IStripeProviderService>();

            var charge = await stripeProviderService.GetChargeAsync(Context.StripeAccount, invoice.ChargeId);

            workspan
                .Baggage("ChargeId", charge.Id);

            if (!charge.Paid)
            {
                workspan
                    .Log.Fatal("Charge is not paid");

                return; //!!!
            }


            rescuedByFlex = true;

            if (markedAsRescuedByFlex)
            {
                workspan.Baggage("RescuedAt", rescuedAt)
                    .Log.Information("Order already marked as rescued");
            }
            else
            {
                var rescuedAtTime = charge.Created;

                // Mark the invoice as rescued by FlexFactor
                await stripeProviderService.MarkInvoiceAsync(Context.StripeAccount, invoice.Id, new()
                {
                    {FlexFactorMetadata.FlexFactorAttemptedAt, rescuedAtTime.ToString("u")},
                    {FlexFactorMetadata.FlexFactorRescuedAt, rescuedAtTime.ToString("u")},
                });

                markedAsRescuedByFlex = true;
            }
        }

        await ProcessInvoicePaidEvent(merchantConfiguration.Mid, orderId, invoice.Id, paidOutOfBand,
            (int) invoice.AmountPaid,
            rescuedByFlex);
    }

    private async Task ProcessInvoicePaidEvent(Guid mid, Guid orderId, string invoiceId, bool paidOutOfBand, int amount,
        bool rescuedByFlex)
    {
        var publisher = GetRequiredService<IPublishEndpoint>();

        await publisher.Publish(new ExternalProviderInvoicePaidEvent(paidOutOfBand, mid, orderId, invoiceId, amount,
            rescuedByFlex));
    }
}