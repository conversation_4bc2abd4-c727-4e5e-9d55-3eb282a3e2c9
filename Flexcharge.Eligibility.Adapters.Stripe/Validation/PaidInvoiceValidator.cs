using FluentValidation;
using Stripe;

namespace FlexCharge.Eligibility.Adapters.Stripe.EventHandlers;

public class PaidInvoiceValidator : AbstractValidator<Invoice>
{
    public PaidInvoiceValidator()
    {
        //see: https://docs.stripe.com/api/invoices/object

        RuleFor(invoice => invoice.Status)
            .Equal("paid");

        // This is validated in-code
        // RuleFor(invoice => invoice.Currency)
        //     .Equal("usd");

        RuleFor(invoice => invoice.Paid)
            .Equal(true);
    }
}