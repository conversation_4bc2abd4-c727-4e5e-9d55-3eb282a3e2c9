{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:62417", "sslPort": 44366}}, "profiles": {"FlexCharge.ApiClient": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "https://localhost:7079;http://localhost:5279", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DB_HOST": "localhost", "DB_PORT": "5432", "DB_DATABASE": "fc.apiclient", "DB_USERNAME": "apiclient-service-staging", "DB_PASSWORD": "11111", "SNS_IAM_REGION": "us-east-1", "SNS_IAM_ACCESS_KEY": "********************", "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW", "AWS_IAM_COGNITO_KEY": "********************", "AWS_IAM_COGNITO_SECRET": "gb9zjxMh5yU826U6oxLEnNfisUcCsBBPUBaweJ+c", "AWS_IAM_COGNITO_CLIENT_ID": "4n0i8a4i9o1vk3g3tf64o6blg7", "AWS_COGNITO_USER_POOL_ID": "us-east-1_rCUpTgXY4", "API_CLIENT_JWT_SIGNING_KEY": "$x*ztCv+B2cBme!@ASWIyFwFeLL2ust+@a"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DB_HOST": "localhost", "DB_PORT": "5432", "DB_DATABASE": "fc.apiclient", "DB_USERNAME": "apiclient-service-staging", "DB_PASSWORD": "11111", "SNS_IAM_REGION": "us-east-1", "SNS_IAM_ACCESS_KEY": "********************", "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW", "AWS_IAM_COGNITO_KEY": "********************", "AWS_IAM_COGNITO_SECRET": "gb9zjxMh5yU826U6oxLEnNfisUcCsBBPUBaweJ+c", "AWS_IAM_COGNITO_CLIENT_ID": "4n0i8a4i9o1vk3g3tf64o6blg7", "AWS_COGNITO_USER_POOL_ID": "us-east-1_rCUpTgXY4", "API_CLIENT_JWT_SIGNING_KEY": "$x*ztCv+B2cBme!@ASWIyFwFeLL2ust+@a"}}}}