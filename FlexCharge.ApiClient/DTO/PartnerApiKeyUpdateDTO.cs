using System.ComponentModel.DataAnnotations;

namespace FlexCharge.ApiClient.DTO
{
    public class PartnerApiKeyUpdateDTO
    {
        /// <summary>
        /// Updated description of the API key
        /// </summary>
        [StringLength(255, MinimumLength = 1)]
        public string? Description { get; set; }

        /// <summary>
        /// Updated note for the API key
        /// </summary>
        [StringLength(500)]
        public string? Note { get; set; }

        /// <summary>
        /// Updated type of the API key (e.g., "production", "sandbox")
        /// </summary>
        [StringLength(50)]
        public string? Type { get; set; }

        /// <summary>
        /// Updated list of scope IDs to assign to the API key
        /// </summary>
        public List<Guid>? Scopes { get; set; }
    }
}