using FlexCharge.Common.Authentication;
using FlexCharge.ApiClient.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;

namespace FlexCharge.ApiClient.Services
{
    public interface IApiKeyService
    {
        Task<(ApiKeyService.ApiKeyVerificationResult verificationResult, Entities.ApiClient apiClient)> Verify(
            string apiKey, string apiKeySecret);

        Task<(ApiClientSecret ApiClientSecret, Entities.ApiClient ApiClient)> Generate(Guid clientId,
            string description, TimeSpan expiration, string? note);

        Task RevokeAsync(string apiKey, Guid clientId);
    }

    public class ApiKeyService : IApiKeyService
    {
        private readonly IPasswordHasher<Entities.ApiClient> _passwordHasher;
        private readonly PostgreSQLDbContext _context;

        public ApiKeyService(PostgreSQLDbContext context, IPasswordHasher<Entities.ApiClient> passwordHasher)
        {
            _context = context;
            _passwordHasher = passwordHasher;
        }

        public async Task<(ApiKeyVerificationResult verificationResult, Entities.ApiClient apiClient)> Verify(
            string apiKey, string apiKeySecret)
        {
            ApiClientSecret apiClientSecret = await _context.ApiClientSecrets.Include(x => x.Client)
                .FirstOrDefaultAsync(x => x.Key == apiKey);

            if (apiClientSecret == null)
            {
                return (ApiKeyVerificationResult.Failed, null);
            }

            PasswordVerificationResult passwordVerificationResult =
                _passwordHasher.VerifyHashedPassword(apiClientSecret.Client, apiKeySecret, apiClientSecret.Key);

            if (passwordVerificationResult == PasswordVerificationResult.Failed)
            {
                return (ApiKeyVerificationResult.Failed, null);
            }

            return (ApiKeyVerificationResult.Success, apiClientSecret.Client);
        }

        public async Task<(ApiClientSecret ApiClientSecret, Entities.ApiClient ApiClient)> Generate(Guid clientId,
            string description, TimeSpan expiration, string? note)
        {
            var apiClient = await _context.ApiClients.SingleOrDefaultAsync(x => x.Id == clientId);
            var ApiClientSecret = await _context.ApiClientSecrets.SingleOrDefaultAsync(x => x.ClientId == clientId);

            if (apiClient is null)
                throw new KeyNotFoundException("Api client not found");

            if (ApiClientSecret != null)
                return (ApiClientSecret, apiClient);

            string apiKeySecret = null;
            string apiKeySecretHash = null;

            byte[] key = new byte[30];
            using (RandomNumberGenerator generator = RandomNumberGenerator.Create())
            {
                generator.GetBytes(key);
            }

            //Secret
            apiKeySecret = Convert.ToBase64String(key);
            apiKeySecretHash = _passwordHasher.HashPassword(apiClient, apiKeySecret);

            List<ApiResource> apiResources = await _context.ApiResources.Include(c => c.Claims).ToListAsync();

            List<ApiClientClaim> apiClientClaims = new List<ApiClientClaim>();
            foreach (var apiResource in apiResources)
            {
                foreach (var claim in apiResource?.Claims)
                {
                    apiClientClaims.Add(new ApiClientClaim
                    {
                        ApiClientId = apiClient.Id,
                        Type = MyClaimTypes.PERMISSION,
                        Value = claim.Value
                    });
                }
            }

            var clientClaims = await _context.ApiClientClaims.Where(x => x.ApiClientId == clientId).ToListAsync();
            apiClientClaims.AddRange(clientClaims);


            ApiClientSecret clientSecret = new ApiClientSecret()
            {
                ClientId = apiClient.Id, Type = "",
                Key = apiKeySecret,
                Value = apiKeySecretHash,
                Expiration = expiration,
                Description = description,
                Note = note,
            };

            _context.ApiClientClaims.AddRange(apiClientClaims);
            _context.ApiClientSecrets.Add(clientSecret);
            await _context.SaveChangesAsync();

            if (apiKeySecretHash != null)
            {
                return (clientSecret, apiClient);
            }

            return (null, null);
        }

        public async Task RevokeAsync(string apiKey, Guid clientId)
        {
            var ApiClientSecrets =
                await _context.ApiClientSecrets.SingleOrDefaultAsync(x => x.Key == apiKey && x.ClientId == clientId);
            if (ApiClientSecrets == null)
                throw new Exception("ApiClientSecret not found.");

            ApiClientSecrets.Revoke();
            _context.ApiClientSecrets.Update(ApiClientSecrets);
            await _context.SaveChangesAsync();
        }

        public enum ApiKeyVerificationResult
        {
            Success = 1,
            Failed
        }
    }
}