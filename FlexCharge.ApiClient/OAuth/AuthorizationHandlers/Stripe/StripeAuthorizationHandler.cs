using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using FlexCharge.ApiClient;
using FlexCharge.ApiClient.Entities;
using FlexCharge.Common.Cloud.SecretsManager;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Common;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Common.Shared.Authentication.OAuth.AuthorizationHandlers.Stripe;

public class StripeAuthorizationHandler : AuthorizationHandlerBase
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IConfiguration _configuration;
    private readonly ISecretsManager _secretsManager;
    private readonly IPublishEndpoint _publisher;
    public override OAuthProvider Provider => OAuthProvider.Stripe;

    public StripeAuthorizationHandler(PostgreSQLDbContext dbContext, IConfiguration configuration,
        ISecretsManager secretsManager, IPublishEndpoint publisher)
    {
        _dbContext = dbContext;
        _configuration = configuration;
        _secretsManager = secretsManager;
        _publisher = publisher;
    }

    protected override async Task<OAuth.AccessToken> ExchangeToAccessTokensInternalAsync(
        Dictionary<string, List<string>> queryString,
        Dictionary<string, List<string>> requestHeaders,
        string requestBody)
    {
        var requestStartTime = DateTime.UtcNow;

        var acCode = queryString.GetValueOrDefault("code")?.FirstOrDefault();
        var state = queryString.GetValueOrDefault("state")?.FirstOrDefault();

        var oAuthRequest = await GetAndVerifyOAuthRequest(state);

        var exchangeResult = await ExchangeTokenAsync(acCode);

        await _publisher.RunIdempotentCommandWithoutResponseAsync(
            new PaymentProviderGetMerchantLegalEntityInformationCommand(oAuthRequest.RelatedEntityId,
                Provider.ToString(),
                exchangeResult.stripe_user_id));

        await _publisher.RunIdempotentCommandWithoutResponseAsync(
            new PaymentProviderDownloadProcessingReportsCommand(oAuthRequest.RelatedEntityId, Provider.ToString(),
                exchangeResult.stripe_user_id));


        return new AccessToken
        (
            Provider,
            exchangeResult!.access_token,
            exchangeResult.refresh_token,
            requestStartTime.AddHours(1),
            requestStartTime.AddYears(1),
            RelatedEntityType: "Merchant",
            RelatedEntityId: oAuthRequest.RelatedEntityId,
            ExternalEntityType: "Account",
            ExternalEntityId: exchangeResult.stripe_user_id,
            TokenType: exchangeResult.token_type
        );
    }

    protected override string GenerateOAuthLinkInternal(RelatedEntityType relatedEntityType, Guid relatedEntityId,
        Guid oAuthRequestId)
    {
        using var workspan = Workspan.Start<StripeAuthorizationHandler>();

        try
        {
            var clientId = _configuration.GetValue<string>("Stripe:ClientId");
            var redirectUrl = _configuration.GetValue<string>("Stripe:OAuthRedirectUrl");
            var oAuthChannelLink = _configuration.GetValue<string>("Stripe:OAuthChannelLink");

            // State is used to prevent CSRF attacks and to link the request with the response
            StringBuilder authorizeUrl = new();

            authorizeUrl.Append($"https://marketplace.stripe.com/oauth/v2");

            if (!string.IsNullOrWhiteSpace(oAuthChannelLink))
            {
                authorizeUrl.Append($"/{oAuthChannelLink}");
            }

            authorizeUrl.Append($"/authorize");

            var uriBuilder = new UriBuilder(authorizeUrl.ToString())
            {
                // Query =
                //     $"response_type=code&client_id={clientId}&redirect_uri={Uri.EscapeDataString(redirectUrl)}&scope=read_write&state={oAuthRequestId}"
                Query =
                    $"client_id={clientId}&redirect_uri={Uri.EscapeDataString(redirectUrl)}&state={oAuthRequestId}"
            };

            return uriBuilder.ToString();
        }
        catch (Exception e)
        {
            workspan
                .Log.Fatal(e, "Failed to generate Stripe OAuth link");
            throw;
        }
    }

    private async Task<ExchangeResult> ExchangeTokenAsync(string? acCode)
    {
        // OAuth 2.0 Authorization flow with Stripe
        // see: https://docs.stripe.com/stripe-apps/api-authentication/oauth#url-parameters
        var httpClient = new HttpClient();


        //curl -X POST https://api.stripe.com/v1/oauth/token \
        // -u sk_live_***: \
        // -d code=ac_*** \
        // -d grant_type=authorization_code


        var stripeAppApiKeySecret = Environment.GetEnvironmentVariable("STRIPE_APPS_STRIPE_API_KEY_FOR_OAUTH");

#if DEBUG
        string stagingStripeAppApiForOAuthKeySecretArn =
            "arn:aws:secretsmanager:us-east-1:556663010871:secret:staging/integrations/stripe-apps/stripe-api-key-for-oauth-j60qpt";

        stripeAppApiKeySecret = await _secretsManager.GetSecretValueAsync(stagingStripeAppApiForOAuthKeySecretArn);
#endif


        var request = new HttpRequestMessage(HttpMethod.Post, "https://api.stripe.com/v1/oauth/token");

        request.Headers.Authorization = new AuthenticationHeaderValue("Basic",
            Convert.ToBase64String(Encoding.ASCII.GetBytes($"{stripeAppApiKeySecret}:")));

        request.Content = new FormUrlEncodedContent(new Dictionary<string, string>
        {
            {"code", acCode},
            {"grant_type", "authorization_code"}
        });

        Workspan.Current?
            .Tag("Code", acCode)
            .Tag("GrantType", "authorization_code")
            .Tag("RequestUri", request.RequestUri)
            .Tag("RequestContent", await request.Content.ReadAsStringAsync())
            .Log.Information("Stripe exchange token request");

        var response = await httpClient.SendAsync(request);

        var responseContent = await response.Content.ReadAsStringAsync();

        Workspan.Current?
            .Tag("Response", responseContent)
            .Log.Information("Stripe exchange token response");

        var exchangeResult = JsonSerializer.Deserialize<ExchangeResult>(responseContent);

        if (exchangeResult == null)
            throw new FlexChargeException("Error deserializing exchange result");

        if (!string.IsNullOrWhiteSpace(exchangeResult.error))
        {
            throw new FlexChargeException(
                $"Error exchanging authorization code: {exchangeResult.error} ({exchangeResult.error_description})");
        }

        return exchangeResult;
    }

    public async Task<OAuthRequest> GetAndVerifyOAuthRequest(string? state)
    {
        using var workspan = Workspan.Start<StripeAuthorizationHandler>()
            .Tag("State", state);

        var isStateIsValid = Guid.TryParse(state, out var requestId);

        if (!isStateIsValid)
            throw new ArgumentException("Stripe OAuth link is missing required query parameters");

        var request =
            await _dbContext.OAuthRequests.FirstOrDefaultAsync(x =>
                x.Id == requestId & x.Provider == Provider.ToString());

        if (request == null)
            throw new Exception("Request not found");

        if (request.CompletedAt.HasValue)
            throw new Exception("Request already completed");

        var expiration = DateTime.UtcNow.AddHours(24);

        if (request.CreatedOn > expiration)
            throw new Exception("Request expired");

        // TODO: Implement Stripe OAuth link parsing

        request.CompletedAt = DateTime.UtcNow;
        await _dbContext.SaveChangesAsync();

        return request;
    }
}