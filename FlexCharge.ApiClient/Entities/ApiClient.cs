using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace FlexCharge.ApiClient.Entities
{
    public class ApiClient : AuditableEntity
    {
        public string Name { get; set; }
        public string UniqueName { get; set; }
        public string Description { get; set; }
        public Uri? Uri { get; set; }
        public Guid? Mid { get; set; }
        public Guid? Pid { get; set; }
        public Guid? UserId { get; set; }
        public Guid? Aid { get; set; }

        public ICollection<ApiClientClaim> ApiClientClaims { get; set; }
    }
}