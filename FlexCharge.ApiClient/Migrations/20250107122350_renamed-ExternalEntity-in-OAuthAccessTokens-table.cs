using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.ApiClient.Migrations
{
    /// <inheritdoc />
    public partial class renamedExternalEntityinOAuthAccessTokenstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "EntityType",
                table: "OAuthAccessTokens",
                newName: "ExternalEntityType");

            migrationBuilder.RenameColumn(
                name: "EntityId",
                table: "OAuthAccessTokens",
                newName: "ExternalEntityId");

            migrationBuilder.RenameIndex(
                name: "IX_OAuthAccessTokens_EntityId",
                table: "OAuthAccessTokens",
                newName: "IX_OAuthAccessTokens_ExternalEntityId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ExternalEntityType",
                table: "OAuthAccessTokens",
                newName: "EntityType");

            migrationBuilder.RenameColumn(
                name: "ExternalEntityId",
                table: "OAuthAccessTokens",
                newName: "EntityId");

            migrationBuilder.RenameIndex(
                name: "IX_OAuthAccessTokens_ExternalEntityId",
                table: "OAuthAccessTokens",
                newName: "IX_OAuthAccessTokens_EntityId");
        }
    }
}
