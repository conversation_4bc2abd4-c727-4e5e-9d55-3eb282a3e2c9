using System.Security.Claims;
using FlexCharge.ApiClient.Services;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using MassTransit;
using Newtonsoft.Json;

namespace FlexCharge.ApiClient.Consumers;

public class UserSignedUpEventConsumer : IConsumer<UserSignedUpEvent>
{
    private readonly IApiIdentityService _apiIdentityService;
    private readonly IApiKeyService _apiKeyService;


    public UserSignedUpEventConsumer(IApiIdentityService apiIdentityService, IApiKeyService apiKeyService)
    {
        _apiIdentityService = apiIdentityService;
        _apiKeyService = apiKeyService;
    }

    public async Task Consume(ConsumeContext<UserSignedUpEvent> context)
    {
        using var workspan = Workspan.Start<UserSignedUpEventConsumer>()
            .Context(context)
            .LogEnterAndExit();

        try
        {
            if (context.Message.Group == SuperAdminGroups.MERCHANT_ADMIN)
            {
                var apiClientResult = await _apiIdentityService.CreateApiClient(context.Message.UserId,
                    context.Message.Mid,
                    context.Message.Pid, new ApiClientRequest
                    {
                        Name = "Default",
                        UniqueName = "Default",
                        Description = "Default",
                        Uri = new Uri("https://www.flexfactor.io"),
                        Claims = new Dictionary<string, string>
                        {
                            {"merchant_state", context.Message.MerchantState}
                        }
                    });

                await _apiKeyService.Generate(apiClientResult.ClientId, "Default",
                    TimeSpan.FromHours(1), "Default");
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }
    }
}