using System.Security.Claims;
using FlexCharge.ApiClient.Services;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Shared.Authentication.OAuth;
using FlexCharge.Common.Shared.Authentication.OAuth.Services.ProviderOAuthService;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Common;
using MassTransit;
using Newtonsoft.Json;

namespace FlexCharge.ApiClient.Consumers;

public class GetMerchantByExternalAccountCommandConsumer : CommandConsumer<GetMerchantByExternalAccountCommand,
    GetMerchantByExternalAccountCommandResponse>
{
    private readonly IProviderOAuthService _providerOAuthService;
    protected override bool LogEnterAndExit => false;

    private readonly IApiIdentityService _apiIdentityService;


    public GetMerchantByExternalAccountCommandConsumer(IProviderOAuthService providerOAuthService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _providerOAuthService = providerOAuthService;
    }

    protected override async Task<GetMerchantByExternalAccountCommandResponse> ConsumeCommand(
        GetMerchantByExternalAccountCommand command,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<GetMerchantByExternalAccountCommandConsumer>()
            .Baggage("ExternalAccountId", command.ExternalAccountId)
            .LogEnterAndExit();

        try
        {
            var latestAccessToken = await _providerOAuthService.GetLatestAccessTokenAsync(
                command.Provider,
                ExternalEntityType.Account, command.ExternalAccountId,
                RelatedEntityType.Merchant);

            return new GetMerchantByExternalAccountCommandResponse(latestAccessToken?.RelatedEntityId,
                latestAccessToken?.ForwardApiKey);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }
}