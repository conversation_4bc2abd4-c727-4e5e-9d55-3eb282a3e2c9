{
    "app": {
        "name": "vault-service",
        "version": "0.0.1"
    },
    "nwtoken": {
        "TestCards": [
            "****************"
        ],
        "TestTimeoutMs": 6000,
        "TimeoutCitMs": 100,
        "TimeoutMitMs": 6000,
        "AllowedMids": [
            "76e105a7-4d60-457b-82f2-648d46aba287"
        ]
    },
    "jwt": {
        "Provider": "Cognito",
        "validateLifetime": true,
        "ValidateAudience": true,
        "ValidAudience": "4n0i8a4i9o1vk3g3tf64o6blg7",
        "Region": "us-east-1",
        "UserPoolId": "us-east-1_rCUpTgXY4",
        "AppClientId": "4n0i8a4i9o1vk3g3tf64o6blg7"
    },
    "cache": {
        "connectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379",
        "instance": "",
        "schemaName": "dbo",
        "tableName": "Cache",
        "BigPayloadCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379",
        "IdempotencyCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379",
        "ExternalRequestsCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379"
    },
    "sendgrid": {
        "key": "",
        "SenderEmail": "",
        "SenderName": ""
    },
    "CancellationEmailOptions": {
        "productionEmail": "",
        "testingEmail": ""
    },
    "backgroundWorkerService": {
        "executionInterval": 500
    },
    "sms": {
        "SID": "",
        "Token": "",
        "TwilioPhone": "",
        "WhatsappPhone": "",
        "TwilioCompanyName": "",
        "ServiceSid": "", //Programmable Messaging/Messaging Services/Fithood Identity service
        "TwilioAuthyAPIKey": "",
        "TwilioVoiceSmsStartUrl": "https://api.authy.com/protected/json/phones/verification/start",
        "TwilioVoiceSmsChecktUrl": ""
    },
    "serilog": {
        "consoleEnabled": true,
        "level": "Information",
        "path": "../logs/vault-{0}.txt"
    },
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Information"
        }
    },
    "swagger": {
        "enabled": true,
        "reDocEnabled": false,
        "name": "v1",
        "title": "vault-service",
        "version": "v1",
        "routePrefix": "",
        "includeSecurity": true
    },
    "jaeger": {
        "agentHost" : "localhost",
        "agentPort" : 6831
    },
    "AllowedHosts": "*"
}
