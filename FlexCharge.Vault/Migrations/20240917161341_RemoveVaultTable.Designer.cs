// <auto-generated />
using System;
using System.Collections.Generic;
using System.Text.Json;
using FlexCharge.Vault;
using FlexCharge.Vault.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Vault.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20240917161341_RemoveVaultTable")]
    partial class RemoveVaultTable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Vault.Entities.AccountUpdaterBatch", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Format")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEnrypted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<string>("RequestId")
                        .HasColumnType("text");

                    b.Property<string>("RequestName")
                        .HasColumnType("text");

                    b.Property<DateTime>("RequestedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AccountUpdaterBatches");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.AccountUpdaterBatchItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("BatchId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("SequenceNumber")
                        .HasColumnType("integer");

                    b.Property<Guid>("VaultId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("BatchId", "VaultId")
                        .IsUnique();

                    b.ToTable("AccountUpdaterBatchItems");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.NetworkToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<CardInfo>("CardInfo")
                        .HasColumnType("jsonb");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Decision")
                        .HasColumnType("integer");

                    b.Property<int>("ExpirationMonth")
                        .HasColumnType("integer");

                    b.Property<int>("ExpirationYear")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Provider")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.Property<string>("TokenRefId")
                        .HasColumnType("text");

                    b.Property<string>("TokenReferenceId")
                        .HasColumnType("text");

                    b.Property<string>("TokenRequestorId")
                        .HasColumnType("text");

                    b.Property<string>("TxToken")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("TokenRefId");

                    b.HasIndex("TokenReferenceId")
                        .IsUnique();

                    b.ToTable("NetworkTokens");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.NetworkTokenRequestResponse", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastRequestAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastResponseAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("NetworkTokenId")
                        .HasColumnType("uuid");

                    b.Property<string>("Request")
                        .HasColumnType("jsonb");

                    b.Property<int>("RequestCount")
                        .HasColumnType("integer");

                    b.Property<JsonDocument>("Response")
                        .HasColumnType("jsonb");

                    b.Property<string>("ResponseStatusCode")
                        .HasColumnType("text");

                    b.Property<string>("ResponseStatusMessage")
                        .HasColumnType("text");

                    b.Property<string>("TxReferenceNumber")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("NetworkTokenId");

                    b.HasIndex("Request")
                        .IsUnique();

                    b.ToTable("NetworkTokenRequests");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.PaymentInstrument", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<List<Guid>>("AccountUpdaterVaultV2Ids")
                        .HasColumnType("uuid[]");

                    b.Property<Address>("BillingAddress")
                        .HasColumnType("jsonb");

                    b.Property<string>("CardCountry")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderFirstName")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderFirstNameNormalized")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderLastName")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderLastNameNormalized")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderName")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Cvv")
                        .HasColumnType("text");

                    b.Property<DeviceInfo>("DeviceInfo")
                        .HasColumnType("jsonb");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("NetworkTokenId")
                        .HasColumnType("uuid");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.Property<string>("SenseKey")
                        .HasColumnType("text");

                    b.Property<Address>("ShippingAddress")
                        .HasColumnType("jsonb");

                    b.Property<Guid>("VaultV2Id")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedOn");

                    b.HasIndex("NetworkTokenId");

                    b.HasIndex("VaultV2Id");

                    b.HasIndex(new[] { "CreatedOn" }, "IX_PaymentInstrument_CreatedOn_CvvNotNull")
                        .HasFilter("\"Cvv\" is not null")
                        .HasAnnotation("Npgsql:CreatedConcurrently", true);

                    b.ToTable("PaymentInstruments");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.VaultConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsAccountUpdateDisabled")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("config", t =>
                        {
                            t.HasCheckConstraint("CK_ONE_ROW", "\"Id\" = 1");
                        });

                    b.HasData(
                        new
                        {
                            Id = 1,
                            IsAccountUpdateDisabled = false
                        });
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.VaultV2", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AccountUpdateRelevantBefore")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("AccountUpdaterEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("AccountUpdaterMessage")
                        .HasColumnType("text");

                    b.Property<DateTime?>("AccountUpdaterRequestedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("AuditTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Bin")
                        .HasColumnType("text");

                    b.Property<string>("CardNumberMasked")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ExpirationMonth")
                        .HasColumnType("integer");

                    b.Property<int>("ExpirationYear")
                        .HasColumnType("integer");

                    b.Property<string>("Fingerprint")
                        .HasColumnType("text");

                    b.Property<bool>("IsAccountUpdaterActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Last4")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUsed")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("NextVaultId")
                        .HasColumnType("uuid");

                    b.Property<string>("Number")
                        .HasColumnType("text");

                    b.Property<string>("PaymentAccountReference")
                        .HasColumnType("text");

                    b.Property<bool>("PendingUpdate")
                        .HasColumnType("boolean");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.Property<bool>("ValidLuhn")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("IsDeleted");

                    b.HasIndex(new[] { "Fingerprint", "CardNumberMasked", "ExpirationYear", "ExpirationMonth" }, "FingerPrint_CardNumberMasked_ExpirationYear_ExpirationMonth_Idx")
                        .IsUnique()
                        .HasFilter("\"IsDeleted\"=false");

                    b.HasIndex(new[] { "PaymentAccountReference" }, "PaymentAccountReference_Idx");

                    b.ToTable("VaultsV2");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.AccountUpdaterBatchItem", b =>
                {
                    b.HasOne("FlexCharge.Vault.Entities.AccountUpdaterBatch", "Batch")
                        .WithMany("Items")
                        .HasForeignKey("BatchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Batch");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.NetworkTokenRequestResponse", b =>
                {
                    b.HasOne("FlexCharge.Vault.Entities.NetworkToken", "NetworkToken")
                        .WithMany("RequestResponses")
                        .HasForeignKey("NetworkTokenId");

                    b.Navigation("NetworkToken");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.PaymentInstrument", b =>
                {
                    b.HasOne("FlexCharge.Vault.Entities.NetworkToken", "NetworkToken")
                        .WithMany("PaymentInstruments")
                        .HasForeignKey("NetworkTokenId");

                    b.HasOne("FlexCharge.Vault.Entities.VaultV2", "VaultV2")
                        .WithMany("PaymentTokens")
                        .HasForeignKey("VaultV2Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NetworkToken");

                    b.Navigation("VaultV2");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.AccountUpdaterBatch", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.NetworkToken", b =>
                {
                    b.Navigation("PaymentInstruments");

                    b.Navigation("RequestResponses");
                });

            modelBuilder.Entity("FlexCharge.Vault.Entities.VaultV2", b =>
                {
                    b.Navigation("PaymentTokens");
                });
#pragma warning restore 612, 618
        }
    }
}
