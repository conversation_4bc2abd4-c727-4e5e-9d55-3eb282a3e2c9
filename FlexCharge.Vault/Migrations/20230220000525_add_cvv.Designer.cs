// <auto-generated />
using System;
using FlexCharge.Vault;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Vault.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20230220000525_add_cvv")]
    partial class addcvv
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Vault.Entities.Vault", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AuditTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Bin")
                        .HasColumnType("text");

                    b.Property<string>("CardCountry")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderFirstName")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderFirstNameNormalized")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderLastName")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderLastNameNormalized")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderName")
                        .HasColumnType("text");

                    b.Property<string>("CardNumberMasked")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Cvv")
                        .HasColumnType("text");

                    b.Property<int>("ExpirationMonth")
                        .HasColumnType("integer");

                    b.Property<int>("ExpirationYear")
                        .HasColumnType("integer");

                    b.Property<string>("Fingerprint")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Last4")
                        .HasColumnType("text");

                    b.Property<Guid?>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifyOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Number")
                        .HasColumnType("text");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Vaults");
                });
#pragma warning restore 612, 618
        }
    }
}
