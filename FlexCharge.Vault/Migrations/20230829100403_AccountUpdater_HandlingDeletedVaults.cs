using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Vault.Migrations
{
    /// <inheritdoc />
    public partial class AccountUpdaterHandlingDeletedVaults : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PaymentInstrumentLookups");

            migrationBuilder.DropIndex(
                name: "FingerPrint_CardNumberMasked_ExpirationYear_ExpirationMonth_Idx",
                table: "VaultsV2");

            migrationBuilder.AddColumn<DateTime>(
                name: "LastUsed",
                table: "VaultsV2",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.CreateTable(
                name: "PaymentInstruments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Mid = table.Column<Guid>(type: "uuid", nullable: true),
                    CardHolderFirstName = table.Column<string>(type: "text", nullable: true),
                    CardHolderLastName = table.Column<string>(type: "text", nullable: true),
                    CardHolderFirstNameNormalized = table.Column<string>(type: "text", nullable: true),
                    CardHolderLastNameNormalized = table.Column<string>(type: "text", nullable: true),
                    CardHolderName = table.Column<string>(type: "text", nullable: true),
                    CardCountry = table.Column<string>(type: "text", nullable: true),
                    Cvv = table.Column<string>(type: "text", nullable: true),
                    VaultV2Id = table.Column<Guid>(type: "uuid", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifyOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PaymentInstruments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PaymentInstruments_VaultsV2_VaultV2Id",
                        column: x => x.VaultV2Id,
                        principalTable: "VaultsV2",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "FingerPrint_CardNumberMasked_ExpirationYear_ExpirationMonth_Idx",
                table: "VaultsV2",
                columns: new[] { "Fingerprint", "CardNumberMasked", "ExpirationYear", "ExpirationMonth" },
                unique: true,
                filter: "\"IsDeleted\"=false");

            migrationBuilder.CreateIndex(
                name: "IX_VaultsV2_IsDeleted",
                table: "VaultsV2",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_PaymentInstruments_VaultV2Id",
                table: "PaymentInstruments",
                column: "VaultV2Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PaymentInstruments");

            migrationBuilder.DropIndex(
                name: "FingerPrint_CardNumberMasked_ExpirationYear_ExpirationMonth_Idx",
                table: "VaultsV2");

            migrationBuilder.DropIndex(
                name: "IX_VaultsV2_IsDeleted",
                table: "VaultsV2");

            migrationBuilder.DropColumn(
                name: "LastUsed",
                table: "VaultsV2");

            migrationBuilder.CreateTable(
                name: "PaymentInstrumentLookups",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    VaultV2Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CardCountry = table.Column<string>(type: "text", nullable: true),
                    CardHolderFirstName = table.Column<string>(type: "text", nullable: true),
                    CardHolderFirstNameNormalized = table.Column<string>(type: "text", nullable: true),
                    CardHolderLastName = table.Column<string>(type: "text", nullable: true),
                    CardHolderLastNameNormalized = table.Column<string>(type: "text", nullable: true),
                    CardHolderName = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Cvv = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    Mid = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ModifyOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PaymentInstrumentLookups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PaymentInstrumentLookups_VaultsV2_VaultV2Id",
                        column: x => x.VaultV2Id,
                        principalTable: "VaultsV2",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "FingerPrint_CardNumberMasked_ExpirationYear_ExpirationMonth_Idx",
                table: "VaultsV2",
                columns: new[] { "Fingerprint", "CardNumberMasked", "ExpirationYear", "ExpirationMonth" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PaymentInstrumentLookups_VaultV2Id",
                table: "PaymentInstrumentLookups",
                column: "VaultV2Id");
        }
    }
}
