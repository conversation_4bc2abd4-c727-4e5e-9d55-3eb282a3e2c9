using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Vault.Migrations
{
    /// <inheritdoc />
    public partial class NetworkTokenRequestResponse : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_NetworkTokenRequests_PaymentInstruments_PaymentInstrumentId",
                table: "NetworkTokenRequests");

            migrationBuilder.DropIndex(
                name: "IX_NetworkTokenRequests_PaymentInstrumentId",
                table: "NetworkTokenRequests");

            migrationBuilder.DropColumn(
                name: "PaymentInstrumentId",
                table: "NetworkTokenRequests");

            migrationBuilder.RenameIndex(
                name: "IX_Vault_CreatedOn_CvvNotNull",
                table: "Vaults",
                newName: "IX_Vault_CreatedOn_CvvNotNull_Idx");

            migrationBuilder.RenameColumn(
                name: "TokenexStatusMessage",
                table: "NetworkTokenRequests",
                newName: "ResponseStatusMessage");

            migrationBuilder.RenameColumn(
                name: "TokenexStatusCode",
                table: "NetworkTokenRequests",
                newName: "ResponseStatusCode");

            migrationBuilder.AddColumn<bool>(
                name: "IsSuccess",
                table: "NetworkTokenRequests",
                type: "boolean",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastRequestAt",
                table: "NetworkTokenRequests",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "LastResponseAt",
                table: "NetworkTokenRequests",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsSuccess",
                table: "NetworkTokenRequests");

            migrationBuilder.DropColumn(
                name: "LastRequestAt",
                table: "NetworkTokenRequests");

            migrationBuilder.DropColumn(
                name: "LastResponseAt",
                table: "NetworkTokenRequests");

            migrationBuilder.RenameIndex(
                name: "IX_Vault_CreatedOn_CvvNotNull_Idx",
                table: "Vaults",
                newName: "IX_Vault_CreatedOn_CvvNotNull");

            migrationBuilder.RenameColumn(
                name: "ResponseStatusMessage",
                table: "NetworkTokenRequests",
                newName: "TokenexStatusMessage");

            migrationBuilder.RenameColumn(
                name: "ResponseStatusCode",
                table: "NetworkTokenRequests",
                newName: "TokenexStatusCode");

            migrationBuilder.AddColumn<Guid>(
                name: "PaymentInstrumentId",
                table: "NetworkTokenRequests",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_NetworkTokenRequests_PaymentInstrumentId",
                table: "NetworkTokenRequests",
                column: "PaymentInstrumentId");

            migrationBuilder.AddForeignKey(
                name: "FK_NetworkTokenRequests_PaymentInstruments_PaymentInstrumentId",
                table: "NetworkTokenRequests",
                column: "PaymentInstrumentId",
                principalTable: "PaymentInstruments",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
