using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Amazon.KeyManagementService;
using Amazon.KeyManagementService.Model;
using Amazon.S3.Model;
using CsvHelper;
using CsvHelper.Configuration;
using EntityFramework.Exceptions.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Encryption;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.SensitiveData.Guards;
using FlexCharge.Common.SftpServices;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Contracts.Common;
using FlexCharge.Contracts.Vault;
using FlexCharge.Utils;
using FlexCharge.Vault.Activities;
using FlexCharge.Vault.Consumers;
using FlexCharge.Vault.DTO;
using FlexCharge.Vault.Entities;
using FlexCharge.Vault.Services.AccountUpdaterServices;
using Jose;
using MassTransit;
using MassTransit.Testing.Implementations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using StackExchange.Redis;

namespace FlexCharge.Vault.Services;

public class TokenExAccountUpdaterService : IAccountUpdaterService
{
    /// <summary>
    /// If account updater results are required earlier than in
    /// <see cref="ACCOUNT_UPDATER_AVERAGE_PROCESSING_TIME"/> hours,
    /// account update will not be requested for such vaults.
    /// </summary>
    private readonly int ACCOUNT_UPDATER_AVERAGE_PROCESSING_TIME = EnvironmentHelper.IsInStagingOrDevelopment ? 10 : 72;

    public ISftpClientService _sftpClientService;
    public ICloudStorage _cloudStorage;
    public IPgpEncryptionService _pgpService;
    public IVaultService _vaultService;
    private readonly IPublishEndpoint _publisher;
    private PostgreSQLDbContext _dbContext;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IActivityService _activityService;

    private string sftpHost;
    private string username;
    private string password;
    private int port;

    //Provided by TokenEx (we use it to encrypt the file)
    private string _pathToTokenExpublicPGPKey;

    //We provide it for incoming files (tokenEx will use it to encrypt the file)
    private string _pathToInternalPublicPGPKey;
    private string _pathToInternalPrivatePGPKey;
    private string _internalPrivatePassphrase;

    public TokenExAccountUpdaterService(ISftpClientService sftpClientService, ICloudStorage cloudStorage,
        IPgpEncryptionService pgpService, PostgreSQLDbContext dbContext, IVaultService vaultService,
        IPublishEndpoint publisher, IServiceScopeFactory serviceScopeFactory,
        IActivityService activityService)
    {
        _sftpClientService = sftpClientService;
        _cloudStorage = cloudStorage;
        _pgpService = pgpService;
        _dbContext = dbContext;
        _vaultService = vaultService;
        _publisher = publisher;
        _serviceScopeFactory = serviceScopeFactory;
        _activityService = activityService;

        sftpHost = Environment.GetEnvironmentVariable("TOKENEX_SFTP_HOST");
        username = Environment.GetEnvironmentVariable("TOKENEX_SFTP_USERNAME");
        password = Environment.GetEnvironmentVariable("TOKENEX_SFTP_PASSWORD");
        port = int.Parse(Environment.GetEnvironmentVariable("TOKENEX_SFTP_PORT") ?? string.Empty);

        #region Commented

        // _pathToTokenExpublicPGPKey = EnvironmentHelper.IsInProduction
        //     ? "Services/accountupdaterservices/keys/tokenex-pgp-production-public-outgoing-encryption-key.asc"
        //     : "Services/accountupdaterservices/keys/tokenex-pgp-sandbox-public-outgoing-encryption-key.asc";
        //
        // _pathToInternalPrivatePGPKey = EnvironmentHelper.IsInProduction
        //     ? Environment.GetEnvironmentVariable("TOKENEX-ACCOUNT-UPDATER-SIGNING-KEY")
        //     : "Services/accountupdaterservices/keys/tokenex-pgp-private-key.asc";
        //
        // _pathToInternalPublicPGPKey = "Services/accountupdaterservices/keys/tokenex-pgp-public-key.asc";

        #endregion

        //Used by us to encrypt the file we send to tokenEx
        _pathToTokenExpublicPGPKey = Environment.GetEnvironmentVariable("TOKENEX_PGP_ACCOUNT_UPDATER_PUBLIC_KEY");

        //Used by tokenEx to encrypt the file we decrypt with the private key and passphrase
        _pathToInternalPrivatePGPKey = Environment.GetEnvironmentVariable("FLEXCHARGE_PGP_ACCOUNT_UPDATER_PRIVATE_KEY");
        _pathToInternalPublicPGPKey = Environment.GetEnvironmentVariable("FLEXCHARGE_PGP_ACCOUNT_UPDATER_PUBLIC_KEY");

        _internalPrivatePassphrase =
            Environment.GetEnvironmentVariable("FLEXCHARGE_PGP_ACCOUNT_UPDATER_PRIVATE_KEY_PASSPHRASE");
    }

    public CardBrand[] AllowedCardBrands =>
        new[] {CardBrand.Visa, CardBrand.MasterCard, CardBrand.Amex};


    private static async Task<string> DecryptPan(CheckoutCardUpdatedEvent evt, Workspan workspan)
    {
        if (string.IsNullOrWhiteSpace(evt.EncryptedCardNumber)) return null;

        try
        {
            // var kmsClient = new AmazonKeyManagementServiceClient();
            //
            // var ciphertext = new MemoryStream();
            // var stringForDecryption = Convert.FromBase64String(evt.EncodedPrivateKey);
            // ciphertext.Write(stringForDecryption, 0, stringForDecryption.Length);
            // var dRequest = new DecryptRequest()
            // {
            //     KeyId = evt.KmsKey,
            //     CiphertextBlob = ciphertext
            // };
            // var plainText2 = (await kmsClient.DecryptAsync(dRequest)).Plaintext;
            // //Convert.FromBase64String(base64String);
            // var decryptedString = Encoding.UTF8.GetString(plainText2.ToArray()) ?? string.Empty;

            var decryptedString = await Encryptor.DecryptPciData(evt.EncryptedPrivateKey);

            var rsa = RSA.Create(2048);
            var bytes = Convert.FromBase64String(decryptedString); //Encoding.UTF8.GetBytes(privateKey);
            rsa.ImportPkcs8PrivateKey(bytes.AsSpan(), out var _);
            var token = JWE.Decrypt(evt.EncryptedCardNumber, rsa, JweAlgorithm.RSA_OAEP_256, JweEncryption.A256GCM);
            return token.Plaintext;
        }
        catch (Exception e)
        {
#if DEBUG
            if (e.Message.Contains("Platform"))
            {
                Console.WriteLine("To use RSA in .NET 6 on MAC, please, run command in terminal:");
                Console.WriteLine("sudo ln -s /opt/homebrew/lib/libssl.3.dylib /usr/local/lib/");
            }
#endif
            workspan.RecordException(e);

            return null;
        }

        return null;
    }


    #region Upload Request to TokenEx

    public async Task ProcessPendingAccountUpdatesAsync(CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<IAccountUpdaterService>()
            .LogEnterAndExit();

        if ((await _dbContext.GetConfigurationAsync()).IsAccountUpdateDisabled)
        {
            #region Observability

            await _activityService.CreateActivityAsync(AccountUpdaterActivities
                .AccountUpdater_Stopped_ProcessingDisabled);

            #endregion

            return;
        }

        #region Observability

        await _activityService.CreateActivityAsync(AccountUpdaterActivities.AccountUpdater_ProcessingPendingUpdates);

        #endregion

        try
        {
            var utcNow = DateTime.UtcNow;

            var vaults =
                await _dbContext.VaultsV2
                    .Where(x =>
                        x.PendingUpdate
                        && x.AccountUpdaterEnabled == true
                        && x.IsAccountUpdaterActive
                        && x.NextVaultId == null // Skipping already updated vaults
                        // Do not request account updater for vaults that have been updated recently
                        && (!x.AccountUpdaterRequestedOn.HasValue
                            || x.AccountUpdaterRequestedOn.Value <
                            utcNow.AddHours(-ACCOUNT_UPDATER_AVERAGE_PROCESSING_TIME))
                        && x.AccountUpdateRelevantBefore > utcNow
                    ).ToListAsync(cancellationToken);

            if (vaults.Count == 0)
            {
                #region Observability

                workspan.Log.Information("No accounts found for account updater");
                await _activityService.CreateActivityAsync(AccountUpdaterActivities.AccountUpdater_NoUpdatesPending);

                #endregion

                return;
            }
            else
            {
                #region Observability

                await _activityService.CreateActivityAsync(AccountUpdaterActivities.AccountUpdater_RequestingUpdates,
                    set: set => set
                        .Meta(meta => meta
                            .ServiceProvider(ServiceProviderName)
                            .SetValue("UpdatesRequested", vaults.Count))
                );

                #endregion
            }

            var batch = await InitiateBatchAsync();
            await _dbContext.AccountUpdaterBatches.AddAsync(batch);

            int sequenceCounter = 1;
            foreach (var vault in vaults)
            {
                var item = new AccountUpdaterBatchItem()
                    {VaultId = vault.Id, SequenceNumber = sequenceCounter++};
                batch.Items.Add(item);
                vault.AccountUpdaterRequestedOn = DateTime.UtcNow;
                vault.PendingUpdate = true;
            }

            await UploadAccountsAsync(batch, cancellationToken);

            await _dbContext.SaveChangesAsync(cancellationToken);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            await _activityService.CreateActivityAsync(AccountUpdaterErrorActivities.AccountUpdater_Error);

            throw;
        }
    }


    public async Task<AccountUpdaterBatch> InitiateBatchAsync()
    {
        using var workspan = Workspan.Start<IAccountUpdaterService>();

        workspan.Log.Information("Initiating batch for timestamp: {Timestamp}", DateTime.UtcNow);

        try
        {
            const string filePrefix = "accounts";
            var batchGuidId = Guid.NewGuid().ToString();
            var timestampUtc = DateTime.UtcNow;
            var timestamp = timestampUtc.ToString("yyyyMMddHHmmss");
            var batchRequestId = $"{$"{batchGuidId}_{timestamp}".GetHashCode():X}".ToUpper();

            var requestName = $"{filePrefix}_{batchRequestId}_{timestamp}";

            var batch = new AccountUpdaterBatch
            {
                RequestId = batchRequestId,
                RequestName = requestName,
                Format = "csv",
                IsEnrypted = true,
                Status = AccountUpdaterBatchStatus.Open.ToString(),
                RequestedAt = timestampUtc
            };

            workspan.Log.Information("Initiating batch for timestamp: {Timestamp} with RequestId: {RequestId}",
                DateTime.UtcNow, batch.RequestId);

            await _dbContext.AccountUpdaterBatches.AddAsync(batch);
            //// await _dbContext.SaveChangesAsync();

            return batch;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    private async Task UploadAccountsAsync(AccountUpdaterBatch batch, CancellationToken token)

    {
        using var workspan = Workspan.Start<IAccountUpdaterService>()
            .LogEnterAndExit();

        //Create batch record in DB

        if (batch.Items.Count == 0)
            return;

        try
        {
            var vaults = await _dbContext.VaultsV2
                .Where(x => batch.Items.Select(y => y.VaultId).Contains(x.Id))
                .Select(v => new {v.Id, v.Token, v.ExpirationMonth, v.ExpirationYear})
                .ToListAsync(token);

            workspan.Log.Information("Found vaults: {TotalVaults}", vaults.Count);

            var matchedItems = batch.Items
                .Join(vaults,
                    item => item.VaultId,
                    vault => vault.Id,
                    (item, vault) => new
                    {
                        Item = item,
                        Token = vault.Token,
                        // Formatting expiry date to MMYY
                        Expiry =
                            $"{vault.ExpirationMonth.ToString("D2")}{vault.ExpirationYear % 100:D2}"
                    })
                .Select(match => new
                    {_vaultService.DecryptPciData(match.Token).Result, match.Expiry, match.Item.SequenceNumber})
                .ToList();

            if (EnvironmentHelper.IsInStagingOrDevelopment)
            {
                matchedItems = matchedItems.Where(m => SensitiveDataGuard.AccountUpdaterTestCards.Contains(m.Result))
                    .ToList();
            }

            workspan.Log.Information("Found matched items: {TotalMatchedItems}",
                matchedItems.Count);

            var csvStream = await ConvertToStream(matchedItems);

            //Upload to SFTP
            var filename = $"{batch.RequestName}.csv.pgp";

            using var outputStream = new MemoryStream();

            //PGP encrypt file 
            var key = EnvironmentHelper.IsInDevelopment
                ? _pgpService.LoadPublicKey(_pathToTokenExpublicPGPKey)
                : _pgpService.LoadPublicKeyFromString(_pathToTokenExpublicPGPKey);

            await _pgpService.EncryptStreamAsync(batch.RequestName, key,
                csvStream, outputStream, true);
            outputStream.Position = 0;

            await _sftpClientService.UploadFileAsync(outputStream, $"/In/{filename}", sftpHost, port,
                username, password);

            outputStream.Close();
            batch.Status = AccountUpdaterBatchStatus.Closed.ToString();
            await _dbContext.SaveChangesAsync();
            //await CloseBatchAsync(batch.Id);

            //this event not consumed anywhere yet
            await _publisher.Publish(new AccountUpdaterBatchSentEvent
            {
                BatchId = batch.Id,
                RequestId = batch.RequestId,
                ItemsCount = batch.Items.Count,
                RequestName = batch.RequestName,
            }, token);

            #region Observability

            await _activityService.CreateActivityAsync(AccountUpdaterActivities.AccountUpdater_BatchUploadedSuccesfully,
                set: set => set
                    .Meta(meta => meta
                        .SetValue("FileName", filename)
                        .SetValue("ItemsCount", matchedItems.Count)
                    )
            );

            #endregion
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            await _activityService.CreateActivityAsync(AccountUpdaterErrorActivities.AccountUpdater_Error);

            await _publisher.Publish(new AccountUpdaterBatchSendingFailedEvent
            {
                BatchId = batch.Id,
                RequestId = batch.RequestId,
                ItemsCount = batch.Items.Count,
                RequestName = batch.RequestName,
                Message = e.Message
            }, token);

            throw;
        }
    }

    #endregion

    #region Get Results From TokenEx

    /// <summary>
    /// Extracts the response from the sftp server
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<string>> PollSftp()
    {
        using var workspan = Workspan.Start<IAccountUpdaterService>();

        workspan.Log.Information(
            "ENTERED: PollSftp for response from SFTP server {SftpHost} port {Port} username {UserName}", sftpHost,
            port, username);

        try
        {
            var storageBucketName = Environment.GetEnvironmentVariable("TOKENEX_ACCOUNT_UPDATER_BUCKET");

            //Poll SFTP for response
            var response =
                await _sftpClientService.PollSftpAndUploadToS3Async(sftpHost, port, username, password, "/out",
                    storageBucketName, string.Empty);

            workspan.Log.Information("EXIT: PollSftp polled {Count} files", response.Count());
            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            await _activityService.CreateActivityAsync(AccountUpdaterErrorActivities.AccountUpdater_Error);

            throw;
        }
    }


    public async Task RetrieveAccountUpdaterResults(CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<IAccountUpdaterService>()
            .LogEnterAndExit();

        var updatedVaultsCount = 0;
        try
        {
            #region Observability

            await _activityService.CreateActivityAsync(AccountUpdaterActivities.AccountUpdater_RetreivingResults,
                set: set => set
                    .Meta(meta => meta
                        .ServiceProvider(ServiceProviderName)
                    )
            );

            #endregion

            var storageBucketName = Environment.GetEnvironmentVariable("TOKENEX_ACCOUNT_UPDATER_BUCKET");

            // var publicPgpKey = _pathToInternalPublicPGPKey;
            // var privatePgpKey = _pathToInternalPrivatePGPKey;
            var publicPgpKey = EnvironmentHelper.IsInDevelopment
                ? _pgpService.LoadPublicKey(_pathToInternalPublicPGPKey)
                : _pgpService.LoadPublicKeyFromString(_pathToInternalPublicPGPKey);

            var privatePgpKey = EnvironmentHelper.IsInDevelopment
                ? _pgpService.LoadPrivateKey(publicPgpKey, _pathToInternalPrivatePGPKey, _internalPrivatePassphrase)
                : _pgpService.LoadPrivateKeyFromString(publicPgpKey, _pathToInternalPrivatePGPKey,
                    _internalPrivatePassphrase);

            var passphrase = _internalPrivatePassphrase;

            var files = await PollSftp();

            var requestIds = files.Select(x => x.ExtractPart('_', 1)).ToList();

            var batches = await _dbContext.AccountUpdaterBatches
                .Include(x => x.Items)
                .Where(x => requestIds.Contains(x.RequestId)).ToListAsync();

            #region Observability

            var batchesCount = batches.Count();
            workspan.Log.Information("Found batches: {TotalBatches}", batchesCount);

            await _activityService.CreateActivityAsync(AccountUpdaterActivities.AccountUpdater_ProcessingUpdateResults,
                set: set => set
                    .Meta(meta => meta
                        .SetValue("FilesCount", requestIds.Count)
                        .SetValue("BatchesCount", batchesCount)
                    )
            );

            #endregion

            foreach (var batch in batches)
            {
                var batchUpdatedItemsCount = 0;

                var filePath = $"{batch.RequestName}";
                if (batch.Format == "csv")
                    filePath += ".csv";

                if (batch.IsEnrypted)
                    filePath += ".pgp";

                workspan.Log.Information("Processing file: {FilePath}", filePath);

                //Get file from S3
                using (var nonSeekableStream = await _cloudStorage.GetFileStreamAsync(storageBucketName, filePath))
                {
                    var memoryStream = new MemoryStream();
                    await nonSeekableStream.CopyToAsync(memoryStream, cancellationToken);
                    memoryStream.Position = 0; // Reset the position to the beginning of the stream

                    //PGP decrypt file
                    IEnumerable<AccountUpdateRecordResponse> updatedAccounts;
                    if (batch.IsEnrypted)
                    {
                        //PGP encrypt file 

                        var decryptedStream = await _pgpService.DecryptStreamAsync(publicPgpKey, privatePgpKey,
                            passphrase,
                            memoryStream);

                        //Deserialize response
                        updatedAccounts = await DeserializeStreamToCsv(decryptedStream);
                    }
                    else
                    {
                        //Deserialize response
                        updatedAccounts = await DeserializeStreamToCsv(memoryStream);
                    }

                    var updatedAccountsList = updatedAccounts.ToList();

                    #region Observability

                    workspan.Log.Information(
                        "Found updated accounts: {TotalUpdatedAccounts} in file {FilePath}. RequestName: {BatchRequestName}",
                        updatedAccountsList.Count, filePath, batch.RequestName);

                    #endregion

                    var batchSequenceNumberToRecord = updatedAccountsList.ToDictionary(a => a.SequenceNumber, a => a);
                    var vaultsToUpdate = batch.Items.Select(a => a.VaultId).ToArray();

                    var vaults = await _dbContext.VaultsV2.Where(v => vaultsToUpdate.Contains(v.Id))
                        .ToListAsync();
                    var vaultIdToVault = vaults.ToDictionary(v => v.Id, v => v);

                    Dictionary<string, decimal> dStats = new();

                    // string Card_updated = "Card updated";
                    // string Card_account_closed = "Card account closed";
                    // string Card_expiration_updated = "Card expiration updated";
                    // string Card_bank_not_in_program="Card bank not in program";
                    // string Card_account_flagged = "Card account flagged";
                    // string Card_account_not_found = "Card account not found";
                    // string Card_unchanged = "Card unchanged";
                    // string Card_update_error="Card update error";

                    ///  

                    foreach (var item in batch.Items)
                    {
                        if (!batchSequenceNumberToRecord.TryGetValue(item.SequenceNumber, out var updatedAccount))
                            continue;
                        if (!vaultIdToVault.TryGetValue(item.VaultId, out var vault)) continue;

                        var message = updatedAccount.ResponseMessage;

                        #region Observability

                        workspan.Log.Information(
                            "Account updated for vaultId: {VaultId} responded with Message: {ResponseMessage}",
                            item.VaultId, updatedAccount.ResponseMessage);

                        await _activityService.CreateActivityAsync(
                            AccountUpdaterActivities.AccountUpdater_VaultUpdatedSuccesfully,
                            set: set => set
                                .Meta(meta => meta
                                    .SetValue("VaultId", item.VaultId)
                                    .SetValue("ResponseMessage", updatedAccount.ResponseMessage)
                                )
                        );

                        #endregion

                        var oldCardFingerprintFromAU =
                            CryptoSigningHelper.ComputeContentHash(updatedAccount.Token.GetNumbers());

                        if (vault.Fingerprint != oldCardFingerprintFromAU)
                            throw new Exception("Updated wrong card: fingerprints do not match");

                        if (dStats.TryGetValue(message, out var cnt))
                        {
                            dStats[message] = ++cnt;
                        }
                        else
                        {
                            dStats.Add(message, 1);
                        }

                        var stopWaitingForAccountUpdate =
                            CardAccountUpdaterHelper.MessagesToStopWaitingForAccountUpdateOn.Contains(message);
                        var deactivateCard =
                            CardAccountUpdaterHelper.MessagesToDeactivateCardOn.Contains(message);
                        var disableAccountUpdater =
                            CardAccountUpdaterHelper.MessagesToDisableAccountUpdater.Contains(message);

                        var hasNewNumber = !string.IsNullOrWhiteSpace(updatedAccount.UpdatedToken) &&
                                           updatedAccount.Token != updatedAccount.UpdatedToken;
                        var hasNewExpDate = !string.IsNullOrWhiteSpace(updatedAccount.UpdatedExpDate);

                        vault.FlexAccountUpdaterMessage = message switch
                        {
                            "New Card" => FlexAccountUpdaterMessage.NewCard,
                            "Valid account; no update" => FlexAccountUpdaterMessage.NoUpdate,
                            "Account Expiration Date Updated" => FlexAccountUpdaterMessage.ExpirationUpdated,
                            "Account Number Updated" => FlexAccountUpdaterMessage.AccountNumberUpdated,
                            "Account is Closed" => FlexAccountUpdaterMessage.AccountIsClosed,
                            "Contact Cardholder" => FlexAccountUpdaterMessage.ContactCardholderAdvice,
                            "Error - Merchant Not Registered" => FlexAccountUpdaterMessage.MerchantIsNotRegistered,
                            "Blocked Merchant or Reported Fraud" => FlexAccountUpdaterMessage.BlockedMerchant,
                            "Inactive Card" => FlexAccountUpdaterMessage.InactiveCard,
                            "No Match" => FlexAccountUpdaterMessage.NoMatch,
                            "Inactive or Canceled Seller" => FlexAccountUpdaterMessage.CardholderOptOut,
                            "Invalid Expiration Date" => FlexAccountUpdaterMessage.GenericError,
                            "Invalid Account Number" => FlexAccountUpdaterMessage.GenericError,
                            "Invalid Card Type" => FlexAccountUpdaterMessage.GenericError,
                            "Remove Successful" => FlexAccountUpdaterMessage.GenericError,
                            "Invalid or Blank Seller ID" => FlexAccountUpdaterMessage.GenericError,
                            "Card Removed Previously" => FlexAccountUpdaterMessage.GenericError,
                            "Card Record Not Found" => FlexAccountUpdaterMessage.GenericError,
                            _ => FlexAccountUpdaterMessage.GenericError
                        };


                        vault.AccountUpdaterEnabled = !disableAccountUpdater;
                        vault.PendingUpdate = !(stopWaitingForAccountUpdate || deactivateCard || disableAccountUpdater);
                        vault.IsAccountUpdaterActive = !(deactivateCard || stopWaitingForAccountUpdate);

                        if (!vault.PendingUpdate)
                            vault.AccountUpdateRelevantBefore = null;

                        if (hasNewNumber || hasNewExpDate)
                        {
                            updatedVaultsCount++;
                            batchUpdatedItemsCount++;

                            var expirationMonth = vault.ExpirationMonth;
                            var expirationYear = vault.ExpirationYear;

                            if (hasNewExpDate)
                            {
                                #region Update expiration date

                                string expMonthStr = updatedAccount.ExpDate?.Substring(0, 2);
                                string expYearStr = updatedAccount.ExpDate?.Substring(2, 2);

                                if (int.TryParse(expMonthStr, out int expMonthInt) &&
                                    int.TryParse(expYearStr, out int expYearInt))
                                {
                                    expirationMonth = expMonthInt;
                                    expirationYear = expYearInt;
                                }
                                else
                                {
                                    throw new Exception("Could not parse expiration date");
                                }

                                #endregion
                            }

                            var updatedVault = await _vaultService.AddOrGetVault(
                                string.IsNullOrWhiteSpace(updatedAccount.UpdatedToken)
                                    ? updatedAccount.Token
                                    : updatedAccount.UpdatedToken,
                                expirationMonth, expirationYear);

                            if (vault.Id != updatedVault.Id)
                                vault.NextVaultId = updatedVault.Id;

                            vault.PendingUpdate = false;
                        }
                    }

                    List<string> stats = new();

                    if (batch.Items.Count > 0)
                    {
                        var statList = dStats.OrderBy(kv => kv.Key).ToList();
                        foreach (var kv in statList)
                        {
                            var percent = kv.Value * 100 / batch.Items.Count;
                            string spercent = percent > 0 && percent < 1
                                ? "<1"
                                : Math.Round(percent, MidpointRounding.ToEven).ToString();
                            stats.Add($"{kv.Key}|{kv.Value.ToString()}|{spercent}%");
                        }
                    }

                    await _publisher.Publish(new AccountUpdaterResultsReceivedEvent()
                    {
                        BatchId = batch.Id,
                        TotalItemsCount = batch.Items.Count,
                        UpdatedItemsCount = batchUpdatedItemsCount,
                        Stats = stats,
                        SentAt = batch.RequestedAt,
                        Environment = EnvironmentHelper.GetCurrentEnvironment().ToString()
                    }, CancellationToken.None);
                }
            }

            #region Observability

            workspan.Log.Information("Account updates processed: {UpdatedVaultsCount} ", updatedVaultsCount);
            await _activityService.CreateActivityAsync(
                AccountUpdaterActivities.AccountUpdater_ResultsProcessedSuccesfully,
                set: set => set
                    .Meta(meta => meta
                        .SetValue("VaultsUpdated", updatedVaultsCount)
                    )
            );

            #endregion

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            await _activityService.CreateActivityAsync(AccountUpdaterErrorActivities.AccountUpdater_Error);

            throw;
        }
    }

    public async Task UpdateAccount(CheckoutCardUpdatedEvent evt)
    {
        Workspan workspan = Workspan.Start<CheckoutCardUpdatedEventConsumer>().LogEnterAndExit();

        var paymentInstrument =
            await _dbContext.PaymentInstruments.SingleOrDefaultAsync(x => x.Id == evt.PaymentInstrumentId);

        var newNumber = await DecryptPan(evt, Workspan.Start<TokenExAccountUpdaterService>());

        var newCard = new UpdatedCardDTO
        {
            ExpirationYear = evt.Year,
            ExpirationMonth = evt.Month,
            Number = newNumber
        };


        var vaultV2 = await _dbContext.VaultsV2.SingleOrDefaultAsync(x => x.Id == paymentInstrument.VaultV2Id);

        var cardNumber = newCard.Number;
        var oldCardNumber = await _vaultService.DecryptPciData(vaultV2.Token);
        if (string.IsNullOrWhiteSpace(cardNumber))
        {
            cardNumber = oldCardNumber;
        }

        if (vaultV2.ExpirationMonth == newCard.ExpirationMonth
            && vaultV2.ExpirationYear == newCard.ExpirationYear
            && oldCardNumber == cardNumber)
        {
            workspan.Log.Information("Card unchanged");
            return;
        }

        var updatedVault = await _vaultService.AddOrGetVault(
            cardNumber,
            newCard.ExpirationMonth, newCard.ExpirationYear);

        if (vaultV2.Id != updatedVault.Id)
        {
            vaultV2.NextVaultId = updatedVault.Id;
            vaultV2.PendingUpdate = false;
            paymentInstrument.VaultV2Id = updatedVault.Id;
            //vaultV2.AccountUpdaterMessage = "CHECKOUT"; //todo-check
            await _dbContext.SaveChangesAsync();
        }
    }

    #endregion

    #region Helper Code

    private async Task<IEnumerable<AccountUpdateRecordResponse>> DeserializeStreamToCsv(Stream updatedAccountsStream)
    {
        return Utils.CSVHelper.Parse<AccountUpdateRecordResponse>(updatedAccountsStream,
            new CsvConfiguration(CultureInfo.InvariantCulture) {HasHeaderRecord = false});
    }

    private async Task<Stream> ConvertToStream<TReq>(IEnumerable<TReq> updatedAccounts)
    {
        try
        {
            // Create a StringWriter to write the CSV data to
            var writer = new StringWriter();

            // Create a CsvWriter and configure it
            var csvWriter = new CsvWriter(writer,
                new CsvConfiguration(CultureInfo.InvariantCulture) {HasHeaderRecord = false});

            // Write the records to the CSV file
            await csvWriter.WriteRecordsAsync(updatedAccounts);

            // Convert the CSV data to a MemoryStream
            var csvData = Encoding.UTF8.GetBytes(writer.ToString());
            var csvStream = new MemoryStream(csvData);

            return csvStream;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    #endregion

    public string ServiceProviderName => "TOKENEX";
}