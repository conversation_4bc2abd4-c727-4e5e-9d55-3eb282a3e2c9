using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Vault.Entities;

namespace FlexCharge.Vault.Services;

public interface IAccountUpdaterService
{
    Task ProcessPendingAccountUpdatesAsync(CancellationToken cancellationToken);

    Task RetrieveAccountUpdaterResults(CancellationToken cancellationToken);
    Task UpdateAccount(CheckoutCardUpdatedEvent evt);
}