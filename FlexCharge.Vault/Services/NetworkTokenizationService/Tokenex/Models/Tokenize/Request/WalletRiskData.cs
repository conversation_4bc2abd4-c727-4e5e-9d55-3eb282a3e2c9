namespace FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.Tokenize.Request;

public class WalletRiskData
{
    public int WalletScore { get; set; }

    /// <summary>
    /// The time in days that wallet account has been in use.
    /// </summary>
    public int WalletAccountLength { get; set; }

    /// <summary>
    /// he number of transactions performed with the wallet in the last 12 months.
    /// </summary>
    public int WalletTransactions { get; set; }

    /// <summary>
    /// Name of the wallet account holder and the name of the cardholder match or not.
    /// </summary>
    public bool WalletNameMatchesCardholderName { get; set; }

    /// <summary>
    /// (260)	???*??? is used for masking the email address.
    /// </summary>
    public string MaskedEmail { get; set; }

    /// <summary>
    /// 60-character string generated by BCrypt using OpenBSD implementation from BouncyCastle.
    /// </summary>
    public string HashedEmail { get; set; }
}

