using System.Collections.Generic;
using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.Tokenize.Response;

namespace FlexCharge.Vault.Services.TokenExNetworkTokenization.Models;

public class CardMetaData
{
    public string TermsAndConditionsId { get; set; }
    public string TermsAndConditionsUrl { get; set; }
    public string CardArtUrl{ get; set; }
    public string CardBackgroundColor { get; set; }
    public string CardForegroundColor { get; set; }
    public string LabelColor { get; set; }
    public string LongDescription { get; set; }
    public string ShortDescription { get; set; }
    public IssuerData IssuerData { get; set; }
    public IssuerAppData IssuerAppData { get; set; }
    public List<CardAsset> CardAssets { get; set; }
}