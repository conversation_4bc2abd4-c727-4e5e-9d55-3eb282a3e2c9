namespace FlexCharge.Vault.Services.TokenExNetworkTokenization.Models;

public enum NetworkTokenRequestReasonCode
{
    ACTIVATED_BY_CUSTOMER_SERVICE,
    ACTIVATED_BY_COBADGED_TOKEN,
    ACTIVATED_BY_ISSUER,
    ACTIVATED_BY_ISSUER_APP,
    ACTIVATED_BY_USER_REQUEST,
    SUSPENDED_BY_ISSUER,
    SUSPENDED_DUE_TO_FRAUD,
    SUSPENDED_BY_USER_REQUEST,
    SUSPENDED_BY_CUSTOMER_SERVICE,
    SUSPENDED_BY_TR,
    SUSPENDED_BY_TR_DUE_TO_FRAUD,
    RESUMED_BY_ISSUER,
    RESUMED_BY_USER_REQUEST,
    RESUMED_BY_CUSTOMER_SERVICE,
    RESUMED_BY_TR,
    RPAN_UPDATE,
    DELETED_FOR_SECURITY,
    DELETED_BY_CUSTOMER_SERVICE,
    DELETED_BY_ISSUER,
    DELETED_DUE_TO_FRAUD,
    DELETED_BY_USER_REQUEST,
    DELETED_DUE_TO_TOKEN_PROBLEM,
    DELETED_BY_TIME_LIMIT,
    DELETED_BY_TR,
    DELETED__BY_TR_DUE_TO_FRAUD,
    DELETED_DUE_TO_ERROR,
    STATE_CHANGED_ON_DEVICE,
    UNKNOWN_REASON_CODE
}