using System;
using System.Text;
using System.Text.Json.Serialization;

namespace FlexCharge.Vault.Services.TokenExNetworkTokenization.Models;

public class PaymentBundleRequest
{
    
    
    public BundleMetaData BundleMetaData { get; set; }

    /// <summary>
    /// for Amex only,
    /// The transaction amount. Up to 10 digits in ‘xxx.xx’ format.
    /// </summary>
    public string Amount { get; set; }

    /// <summary>
    /// for mastercard only
    /// A value to provide variability and uniqueness to the generation
    /// of a cryptogram (8-byte hex encoded string). *Required
    /// if the GenerateUnpredictableNumber flag
    /// is not set during on-boarding of MDES.
    /// </summary>
    public string UnpredictableNumber { get; set; } = Encryptor.GenerateSlug();
    
    /// <summary>
    /// visa only
    /// *Only required for INAPP PresentationMode type
    /// (see Network Tokenize method).
    /// </summary>
    public string ApplicationTransactionCounter { get; set; }
    
    /// <summary>
    /// visa only
    /// Value of the CAVV cryptogram.
    /// Required for CTAVV type cryptogram. N/A for TAVV and DTVV types.
    /// </summary>
    public string CryptogramValue { get; set; }

    /// <summary>
    /// visa and Amex only
    /// Merchant identifier assigned by the acquirer. *Required if available.
    /// </summary>
    public string MerchantId { get; set; }
    
    /// <summary>
    /// EC indicator for liability shift.
    /// *Required when CTAVV type is cryptogram AND BundleMetaData is Cryptogram.
    /// </summary>
    [JsonPropertyName("ECI")]
    public string ECI { get; set; }

    /// <summary>
    /// visa only
    /// </summary>
   
    public TransactionInitiationType TransactionInitiationType { get; set; }

    public string Token { get; set; }
    
   
    public TransactionType TransactionType { get; set; }
    
    
}



