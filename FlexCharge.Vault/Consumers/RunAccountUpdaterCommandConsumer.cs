using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.SensitiveData.Guards;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Contracts.Vault;
using FlexCharge.Vault.Entities;
using FlexCharge.Vault.Services;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;


namespace FlexCharge.Vault.Consumers;

public class RunAccountUpdaterCommandConsumer : IConsumer<RunAccountUpdaterCommand>
{
    private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;

    public RunAccountUpdaterCommandConsumer(IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue)
    {
        _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
    }

    public async Task Consume(ConsumeContext<RunAccountUpdaterCommand> context)
    {
        using var workspan = Workspan.Start<RunAccountUpdaterCommandConsumer>().LogEnterAndExit();

        try
        {
            _backgroundWorkerCommandQueue.Enqueue(new RunAccountUpdaterWorker());
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: CardAccountUpdatedEventConsumer: {Context}", context);
            throw;
        }
    }
}

public class RunAccountUpdaterWorker : BackgroundWorkerCommand
{
    protected override async Task ExecuteAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<RunAccountUpdaterWorker>();
        try
        {
            using var serviceScope = serviceProvider.CreateScope();
            var auService = serviceScope.ServiceProvider.GetRequiredService<IAccountUpdaterService>();
            await auService.ProcessPendingAccountUpdatesAsync(cancellationToken);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: CardAccountUpdatedEventConsumer");
            throw;
        }
    }
}