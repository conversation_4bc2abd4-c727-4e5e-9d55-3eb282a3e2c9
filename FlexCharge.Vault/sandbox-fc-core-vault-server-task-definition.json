{"containerDefinitions": [{"name": "core-vault", "image": "************.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-vault:f8c4135ae76b563441c7f7dee7a0ff4aba7ed914", "cpu": 0, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"], "interval": 20, "timeout": 5, "retries": 3, "startPeriod": 60}, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "vault_service_sandbox"}, {"name": "DB_DATABASE", "value": "fc_vault"}, {"name": "DB_HOST", "value": "flexcharge-sandbox.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Sandbox"}, {"name": "AWS_COGNITO_USER_POOL_ID", "value": "us-east-1_7Snu9L5Rt"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}, {"name": "TOKENEX_SFTP_HOST", "value": "test-batch.tokenex.com"}, {"name": "TOKENEX_SFTP_PORT", "value": "22"}, {"name": "TOKENEX_SFTP_USERNAME", "value": "****************"}, {"name": "TOKENEX_ID", "value": "****************"}, {"name": "TOKENEX_ACCOUNT_UPDATER_BUCKET", "value": "cc-account-updater"}, {"name": "NEW_RELIC_APP_NAME", "value": "Vault-sandbox"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX_DB_VAULT_PASSWORD-cEWUGF"}, {"name": "AWS_IAM_COGNITO_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STG_AWS_IAM_COGNITO_KEY-SVc81A"}, {"name": "AWS_IAM_COGNITO_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STG_AWS_IAM_COGNITO_SECRET-7KOA9j"}, {"name": "AWS_IAM_COGNITO_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX_COGNITO_CLIENT_ID-flykbv"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX_SNS_IAM_ACCESS_KEY-vt5dWo"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX_SNS_IAM_SECRET_KEY-iDGfTK"}, {"name": "TOKENEX_SFTP_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX-TOKENEX-SFTP-PASSWORD-tgiOiO"}, {"name": "FLEXCHARGE_PGP_ACCOUNT_UPDATER_PUBLIC_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX-PGP-FLEXCHARGE-ACCOUNT-UPDATER-PUBLIC-KEY-uwb7BZ"}, {"name": "FLEXCHARGE_PGP_ACCOUNT_UPDATER_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX-PGP-FLEXCHARGE-ACCOUNT-UPDATER-PRIVATE-KEY-79ATjq"}, {"name": "FLEXCHARGE_PGP_ACCOUNT_UPDATER_PRIVATE_KEY_PASSPHRASE", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX-TOKENEX-ACCOUNT-UPDATER-SIGNING-KEY-PASSPHRASE-pRH1Ep"}, {"name": "TOKENEX_PGP_ACCOUNT_UPDATER_PUBLIC_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX-TOKENEX-ACCOUNT-UPDATER-SIGNING-KEY-iz<PERSON>hy"}, {"name": "TOKENEX_NETWORK_TOKENIZATION_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX-TOKENEX-API-KEY-Mlp7JJ"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-vault-server-sandbox", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-vault-server-sandbox", "taskRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionWithSecretAccess-Sandbox-Role", "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionWithSecretAccess-Sandbox-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "6144"}