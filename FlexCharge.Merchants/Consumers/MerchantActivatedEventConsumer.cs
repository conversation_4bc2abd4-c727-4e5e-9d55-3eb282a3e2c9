using System;
using System.Threading.Tasks;
using FlexCharge.Common.Emails;
using FlexCharge.Contracts;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Merchants.Consumers;

public class MerchantActivatedEventConsumer : IConsumer<MerchantActivatedEvent>
{
    private readonly ILogger<MerchantActivatedEventConsumer> _logger;
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IEmailSender _emailSender;
    private readonly IConfiguration _configuration;
    
    public MerchantActivatedEventConsumer(ILogger<MerchantActivatedEventConsumer> logger, PostgreSQLDbContext dbContext, IEmailSender emailSender, IConfiguration configuration)
    {
        _logger = logger;
        _dbContext = dbContext;
        _emailSender = emailSender;
        _configuration = configuration;
    }


    public async Task Consume(ConsumeContext<MerchantActivatedEvent> context)
    {
        try
        {
            _logger.LogInformation(
                "ENTERED: Correlation: {CorrelationId} > MerchantActivatedEventConsumer > {ApplicationId}",
                context.Message.Mid, context.CorrelationId);
            
            var existingMerchant = await _dbContext.Merchants.Include(x=>x.PrimaryContact).SingleOrDefaultAsync(x=>x.Id == context.Message.Mid);
            ArgumentNullException.ThrowIfNull(existingMerchant);
            
            _logger.LogInformation(
                "In: Merchant: {Merchant}",JsonConvert.SerializeObject(existingMerchant) );

            await _emailSender.SendEmailAsync(existingMerchant.PrimaryContact.Email,
                $"Merchant activated {existingMerchant.LegalEntityName}", "Content", new
                {
                    subject = "FlexCharge account is now active!",
                    ID = existingMerchant.Id,
                    loginUrl =  _configuration.GetValue<string>("email:baseUrl"),
                    name = $"{existingMerchant.PrimaryContact.FirstName}",
                    dbo = existingMerchant.Dba,
                    Type = existingMerchant.Type,
                    userName= $"{existingMerchant.PrimaryContact.Email}"
                },
                _configuration.GetValue<string>("email:merchantActivatedEmailTemplateId"));
            
        }
        catch (Exception e)
        {
            _logger.LogError(e,
                $"EXCEPTION: Correlation: {context.CorrelationId} > Failed MerchantActivatedEventConsumer > {context.Message.Mid}˚");
        }
    }
}