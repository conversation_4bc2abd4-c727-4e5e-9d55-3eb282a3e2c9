// using System;
// using System.Threading.Tasks;
// using FlexCharge.Contracts;
// using MassTransit;
// using Microsoft.Extensions.Logging;
//
// namespace FlexCharge.Merchants.Consumers;
//
// public class OrderUpdatedEventConsumer : IConsumer<OrderUpdatedEvent>
// {
//         ILogger<OrderUpdatedEventConsumer> _logger;
//
//         public OrderUpdatedEventConsumer(ILogger<OrderUpdatedEventConsumer> logger)
//         {
//             _logger = logger;
//         }
//
//         public async Task Consume(ConsumeContext<OrderUpdatedEvent> context)
//         {
//             try
//             {
//                 _logger.LogInformation("Value: {Value}", context.Message.OrderId);
//             }
//             catch (Exception e)
//             {
//                 _logger.LogError(e, $"EXCEPTION: OrderUpdatedEventConsumer > FAILED");
//             }
//         }
// }

