using System.Collections.Generic;
using System.Linq;

namespace FlexCharge.Merchants.Entities;

public static class MerchantExtensions
{
    public static List<string> GetSupportedCountriesList(this Merchant merchant)
    {
        if (string.IsNullOrWhiteSpace(merchant.SupportedCountries))
            return new List<string>();

        var supportedCountries = merchant.SupportedCountries
            .Split(',')
            .Where(x => !string.IsNullOrWhiteSpace(x))
            .Select(x => x.Trim());

        return new List<string>(supportedCountries);
    }
}