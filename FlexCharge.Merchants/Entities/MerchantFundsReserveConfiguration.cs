using System;
using FlexCharge.Merchants.Enums;

namespace FlexCharge.Merchants.Entities;

public class MerchantFundsReserveConfiguration : AuditableEntity
{
    private MerchantFundsReserveConfiguration()
    {
    }

    public MerchantFundsReserveConfiguration(
        Merchant merchant,
        FundsReserveConfiguration? fundsReserveConfiguration)
    {
        Merchant = merchant;
        FundsReserveConfiguration = fundsReserveConfiguration;
    }

    public MerchantFundsReserveConfiguration(
        Guid? merchantId,
        Guid? fundsReserveConfigurationId)
    {
        MerchantId = merchantId;
        FundsReserveConfigurationId = fundsReserveConfigurationId;
    }

    public Guid? MerchantId { get; private set; }
    public Merchant Merchant { get; private set; }

    public Guid? FundsReserveConfigurationId { get; private set; }
    public FundsReserveConfiguration? FundsReserveConfiguration { get; private set; }
}