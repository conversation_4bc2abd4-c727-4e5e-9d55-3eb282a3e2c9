using System;

namespace FlexCharge.Merchants.Entities
{
    public class Notification : AuditableEntity
    {
        public bool Read { get; set; }
        public bool Seen { get; set; }
        public bool Notified { get; set; }

        public string TriggerCode { get; set; }
        public int Category { get; set; }
        public int Severity { get; set; }
        public int Source { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public string Params { get; set; }

        public Merchant Merchant { get; set; }
        public Guid MerchantId { get; set; }

        public DateTime Expiry { get; set; }
    }
}
