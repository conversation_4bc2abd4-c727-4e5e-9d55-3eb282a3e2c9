using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Merchants.Entities;

namespace FlexCharge.Merchants
{
    public class SenseJsExternalPostgreSQLDbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public SenseJsExternalPostgreSQLDbContext(DbContextOptions<SenseJsExternalPostgreSQLDbContext> options,
            IHttpContextAccessor httpContextAccessor)
            : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // modelBuilder.Entity<SenseJSMerchantConfiguration>().Property<bool>("isDeleted");
            // modelBuilder.Entity<SenseJSMerchantConfiguration>().HasQueryFilter(m => EF.Property<bool>(m, "isDeleted") == false);
        }

        public DbSet<SenseJSMerchantConfiguration> Configurations { get; set; }

        public override int SaveChanges()
        {
            UpdateSoftDeleteStatuses();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UpdateSoftDeleteStatuses();
            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        private void UpdateSoftDeleteStatuses()
        {
            var user = _httpContextAccessor?.HttpContext?.User?.Claims.SingleOrDefault(x =>
                x.Type == ClaimTypes.NameIdentifier);
            foreach (var entry in ChangeTracker.Entries())
            {
                // switch (entry.State)
                // {
                //     case EntityState.Modified:
                //     {
                //         if (user != null)
                //         {
                //             if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                //                 entry.Property("ModifiedBy").CurrentValue = user.Value;
                //         }
                //
                //
                //         if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                //             entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                //         entry.CurrentValues["IsDeleted"] = false;
                //         break;
                //     }
                //     case EntityState.Added:
                //     {
                //         if (user != null)
                //         {
                //             if (entry.Properties.Any(o => o.Metadata.Name == "CreatedBy"))
                //                 entry.Property("CreatedBy").CurrentValue = user.Value;
                //             if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                //                 entry.Property("ModifiedBy").CurrentValue = user.Value;
                //         }
                //
                //         if (entry.Properties.Any(o => o.Metadata.Name == "CreatedOn"))
                //             entry.Property("CreatedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                //         if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                //
                //             entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                //         entry.CurrentValues["IsDeleted"] = false;
                //         break;
                //     }
                //
                //     case EntityState.Deleted:
                //         entry.State = EntityState.Modified;
                //         entry.CurrentValues["IsDeleted"] = true;
                //         break;
                // }

                switch (entry.State)
                {
                    case EntityState.Modified:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "modifiedBy"))
                                entry.Property("modifiedBy").CurrentValue = user.Value;
                        }


                        if (entry.Properties.Any(o => o.Metadata.Name == "modifiedAt"))
                            entry.Property("modifiedAt").CurrentValue = DateTime.Now.ToUniversalTime();
                        //entry.CurrentValues["IsDeleted"] = false;
                        break;
                    }
                    case EntityState.Added:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "createdBy"))
                                entry.Property("createdBy").CurrentValue = user.Value;
                            if (entry.Properties.Any(o => o.Metadata.Name == "modifiedBy"))
                                entry.Property("modifiedBy").CurrentValue = user.Value;
                        }

                        if (entry.Properties.Any(o => o.Metadata.Name == "createdAt"))
                            entry.Property("createdAt").CurrentValue = DateTime.Now.ToUniversalTime();
                        if (entry.Properties.Any(o => o.Metadata.Name == "modifiedAt"))

                            entry.Property("modifiedAt").CurrentValue = DateTime.Now.ToUniversalTime();
                        //entry.CurrentValues["IsDeleted"] = false;
                        break;
                    }

                    // case EntityState.Deleted:
                    //     entry.State = EntityState.Modified;
                    //     entry.CurrentValues["IsDeleted"] = true;
                    //     break;
                }
            }
        }
    }
}