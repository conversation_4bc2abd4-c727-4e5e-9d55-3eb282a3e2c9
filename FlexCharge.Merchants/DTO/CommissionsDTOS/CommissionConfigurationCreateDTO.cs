using System;
using FlexCharge.Common.Shared;

namespace FlexCharge.Merchants.DTO.CommissionsDTOS;

public class CommissionConfigurationCreateDTO
{
    public string Name { get; set; }
    public string Description { get; set; }
    public string Type { get; set; } // percentage or fixed
    public int Amount { get; set; }

    public DateTime From { get; set; }
    public DateTime To { get; set; }
    public int? MinimumFeeAmount { get; set; }
    public bool IsStandard { get; set; }
}