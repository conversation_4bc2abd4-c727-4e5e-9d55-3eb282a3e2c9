using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc.Validators;

namespace FlexCharge.Merchants.DTO;

public class ApplicantBoardingRequestDTO
{
    public bool Next { get; set; }
    public LegalEntityDTO CompanyInformation { get; set; }
    public PrimaryContact PrimaryContact { get; set; }
    public ApplicationCustomerSupportInformationDTO CustomerSupportContact { get; set; }
    public DeveloperContact DeveloperContact { get; set; }

    public List<OwnerDTO>? Owners { get; set; }

    [RequiredIf(nameof(Next), true)] public ProductInfoDTO ProductInfo { get; set; }

    [RequiredIf(nameof(Next), true)] public BusinessModelDTO BusinessModel { get; set; }

    [RequiredIf(nameof(Next), true)] public TransactionInformationDTO TransactionInformation { get; set; }
    public AddressDTO Address { get; set; }
    public BankAccountInformationDTO BankAccountInformation { get; set; }
    public List<DocumentDTO>? Documents { get; set; }
}

public class ProductInfoDTO
{
    // public List<string> TrafficAcquisition { get; set; }

    // public string TrialPeriod { get; set; }

    // public string MaxSubDuration { get; set; }

    public string Regulated { get; set; }

    public string Healthcare { get; set; }
    // public string ProductsSold { get; set; }
    // public string AvgDeliveryTime { get; set; }
}

public class LegalEntityDTO
{
    [DisplayName("Legal Entity Name")] public string LegalEntityName { get; set; }

    public string LegalEntityCountry { get; set; }

    public string Dba { get; set; }

    public string Mcc { get; set; }

    [DisplayName("EIN/TIN")] public string TaxId { get; set; }


    public string EcommercePlatform { get; set; }
    // public string EcommercePlatformUsagePeriod { get; set; }

    // public string TimeInBusiness { get; set; }
    public bool IsSiteValidated { get; set; }
    public string Descriptor { get; set; }
    // public string BusinessType { get; set; }
    // public string Website { get; set; }
    public string Description { get; set; }
}

public class BusinessModelDTO
{
    public string UrlsCount { get; set; }

    public List<string> Urls { get; set; }

    // public string Industry { get; set; }

    // public string InfoDisclosed { get; set; }
}