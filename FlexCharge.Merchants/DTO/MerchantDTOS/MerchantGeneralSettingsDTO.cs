using System;
using System.Collections.Generic;
using FlexCharge.Common.Shared.Orders;

namespace FlexCharge.Merchants.DTO;

public class MerchantGeneralSettingsDTO : MerchantGeneralBaseSettingsDTO
{
    public bool IsMitEnabled { get; set; }
    public bool IsMITEvaluateAsync { get; set; }
    public bool IsCITEvaluateAsync { get; set; }
    public bool VirtualTerminalEnabled { get; set; }
    public bool BillingInformationOptional { get; set; }
    public bool MITGetSiteByDynamicDescriptorEnabled { get; set; }
    public bool MITConsumerNotificationsEnabled { get; set; }
    public bool MITConsumerCuresEnabled { get; set; }

    public bool? UIWidgetOptional { get; set; }
    public bool CITConsumerNotificationsEnabled { get; set; }
    public bool AllowBinCheckOnTokenization { get; set; }
    public bool EnableGlobalNetworkTokenization { get; set; }

    public bool CITClickToRefundEnabled { get; set; }
    public bool MITClickToRefundEnabled { get; set; }
    public int? MITAgreedExpiryHours { get; set; }
    public bool UseDefaultSiteForUnknownMerchantUrlsEnabled { get; set; }
    public bool IsSenseJsOptional { get; set; }
    public bool AccountUpdaterEnabled { get; set; }
    public bool IsCrawlingEnabled { get; set; }
    public bool IsIframeMessagesCollectEnabled { get; set; }
    public bool IsEnforceMFAEnabled { get; set; }
    public bool CaptureRequired { get; set; }
    public bool Global3DSEnabled { get; set; }
    public bool InformationalOnly3DS { get; set; }
    public bool SchemeTransactionIdEnabled { get; set; }
    public bool MITImmediateRetryEnabled { get; set; }

    public int MinOrderAmount { get; set; }
    public int MaxOrderAmount { get; set; }
    public ConsumerNotificationChannel? ConsumerOrderNotificationChannel { get; set; }
    
    public bool IsAvsRequired { get; set; }
    public bool IsCvvRequired { get; set; }
    public List<string> SupportedCountries { get; set; }
    public bool IgnoreSiteIdFromClient { get; set; }
    public bool PayerEnabled { get; set; }
    public bool RedactIpEnabled { get; set; }
}