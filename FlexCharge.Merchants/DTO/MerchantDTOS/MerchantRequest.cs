using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc;
using FlexCharge.Common.Mvc.Validators;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Enums;

namespace FlexCharge.Merchants.DTO
{
    public class MerchantRequest
    {
        public string ExternalId { get; set; }

        public bool ResendForReview { get; set; } = false;
        [Required] public string LegalEntityName { get; set; }
        [Required] public string Dba { get; set; }
        [Required] public string Type { get; set; }
        [Required] public string TaxId { get; set; }
        [Required] public string Descriptor { get; set; }
        public string BusinessEstablishedDate { get; set; }
        [Required] public string Mcc { get; set; }
        [Required] public string Industry { get; set; }
        public bool Pcidss { get; set; }
        public string EcommercePlatform { get; set; }
        [Required] public string Website { get; set; }

        [Required] public ApplicationStatus Status { get; set; }
        [Required] public MerchantIntegrationTypes IntegrationType { get; set; }

        public string Description { get; set; }
        public string SpecialTerms { get; set; }

        public MerchantCustomerSupportInformationDTO CustomerSupportInformation { get; set; }

        public Guid PartnerId { get; set; }
        public Guid SalesAgencyId { get; set; }

        public string LogoFile { get; set; }

        public MerchantAddressDTO Address { get; set; }
        public MerchantPrimaryContact? PrimaryContact { get; set; }
        public DeveloperContact DeveloperContact { get; set; }
        public MerchantBankAccountInformation BankAccountInformation { get; set; }

        public CustomFields CustomFields { get; set; }

        public IEnumerable<MerchantFeeDTO> Fees { get; set; }
        public bool IsSiteValidated { get; set; } = false;
        public bool IsIntegrationGuideEnabled { get; set; } = false;
    }

    public class MerchantUpdateRequest
    {
        public string ExternalId { get; set; }

        public bool ResendForReview { get; set; } = false;

        [Required]
        [DisplayName("Legal Entity Name")]
        public string LegalEntityName { get; set; }

        public string Dba { get; set; }
        public string Type { get; set; }
        public string TaxId { get; set; }
        [Required] public string Descriptor { get; set; }
        public string BusinessEstablishedDate { get; set; }
        public string Mcc { get; set; }
        public string Industry { get; set; }
        public bool Pcidss { get; set; }
        public string EcommercePlatform { get; set; }
        public string Website { get; set; }

        public ApplicationStatus Status { get; set; }
        public MerchantIntegrationTypes IntegrationType { get; set; }

        public string Description { get; set; }
        public string SpecialTerms { get; set; }

        public Guid? PartnerId { get; set; }
        public Guid? IntegrationPartnerId { get; set; }
        public Guid? SalesAgencyId { get; set; }

        public string LogoFile { get; set; }

        public MerchantAddressDTO Address { get; set; }
        public MerchantPrimaryContactUpdate? PrimaryContact { get; set; }
        public DeveloperContact DeveloperContact { get; set; }

        public MerchantCustomerSupportInformationDTO CustomerSupportInformation { get; set; }
        public MerchantBankAccountInformation BankAccountInformation { get; set; }

        public CustomFields CustomFields { get; set; }

        public decimal? GhostModeThrottlePercentage { get; set; }
        public decimal? DynamicAuthorizationDiscountThrottlePercentage { get; set; }
        public int? OfferRequestsRateLimitIntervalMS { get; set; }
        public int? OfferRequestsRateLimitCount { get; set; }
        public int? OfferRequestsMaxPerDay { get; set; }
        public decimal? OfferRequestsThrottlePercentage { get; set; }

        /// <summary>
        ///  % of NSF declines FlexCharge is willing to pass on evaluate
        /// </summary>
        public decimal? Offer_NSF_RequestsThrottle_Percentage { get; set; }

        /// <summary>
        /// Max value of offers FlexCharge is willing to approve and payout per month (In Cents)
        /// </summary>
        public int? Orders_MaxMonthlyAmount { get; set; }

        public bool IsBureauProductionActive { get; set; }
        public bool IsMitEnabled { get; set; }
        
        public bool IsMITEvaluateAsync { get; set; }
        public bool IsCITEvaluateAsync { get; set; }

        public bool PayoutsEnabled { get; set; }
        public IEnumerable<MerchantFeeDTO> Fees { get; set; }
        public bool IsSiteValidated { get; set; }
        public bool IsIntegrationGuideEnabled { get; set; }
        public int? Timezone { get; set; }
        public string? Language { get; set; }
        public string? TimezoneName { get; set; }
        public bool VirtualTerminalEnabled { get; set; }
        public bool BillingInformationOptional { get; set; }
        public bool MITGetSiteByDynamicDescriptorEnabled { get; set; }
        public bool MITConsumerNotificationsEnabled { get; set; }
        public bool MITConsumerCuresEnabled { get; set; }

        public bool? UIWidgetOptional { get; set; }
        public bool CITConsumerNotificationsEnabled { get; set; }

        public bool CITClickToRefundEnabled { get; set; }

        public bool MITClickToRefundEnabled { get; set; }

        // public DateTime? MITAgreedExpiryDate { get; set; }
        public bool UseDefaultSiteForUnknownMerchantUrlsEnabled { get; set; }

        public bool AccountUpdaterEnabled { get; set; }
        public bool Global3DSEnabled { get; set; }
        public bool InformationalOnly3DS { get; set; }

        public int MinOrderAmount { get; set; }
        public int MaxOrderAmount { get; set; }

        public bool IsCrawlingEnabled { get; set; }
        public bool IsIframeMessagesCollectEnabled { get; set; }
        public bool IsEnforceMFAEnabled { get; set; }
        public bool CaptureRequired { get; set; }
        public bool SchemeTransactionIdEnabled { get; set; }
        public bool MITImmediateRetryEnabled { get; set; }

        public int DisputeRate { get; set; }

        public bool IsAvsRequired { get; set; }
        public bool IsCvvRequired { get; set; }
    }


    public class MerchantBankAccountInformation
    {
        public string DdaType { get; set; }

        [
            IsDigit(true, ErrorMessage = "Account # must contain digits only"),
            MinLength(9, ErrorMessage = "Account # cannot be less then 9 digits"),
            MaxLength(19, ErrorMessage = "Account # cannot be greater then 19 digits")
        ]
        [DisplayName("Account #")]
        public string AccountNumber { get; set; }

        public string? MaskedAccountNumber { get; set; }


        [IsDigit(true, ErrorMessage = "Routing # must contain digits only"),
         RoutingValidator(true)]
        [DisplayName("Routing #")]
        public string RoutingNumber { get; set; }

        public string? MaskedRoutingNumber { get; set; }

        [DisplayName("Bank Name")] public string BankName { get; set; }
        public DateTime? BankAccountVerified { get; set; }
    }

    public class MerchantPrimaryContact
    {
        [Required] public string FirstName { get; set; }
        [Required] public string LastName { get; set; }
        [Required] public string Email { get; set; }
        [Required] public string Phone { get; set; }
    }

    public class MerchantPrimaryContactUpdate
    {
        [Required] public string FirstName { get; set; }
        [Required] public string LastName { get; set; }
        [Required] public string Phone { get; set; }
    }

    public class MerchantAddressDTO
    {
        public string Addressline1 { get; set; }
        public string Addressline2 { get; set; }
        public string ZipCode { get; set; }
        public string State { get; set; }
        public string StateCode { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
    }

    public class MerchantFeeDTO
    {
        [Required] public Guid Id { get; set; }
        [Required] public int Amount { get; set; }

        public int? MinimumFeeAmount { get; set; }
    }
}