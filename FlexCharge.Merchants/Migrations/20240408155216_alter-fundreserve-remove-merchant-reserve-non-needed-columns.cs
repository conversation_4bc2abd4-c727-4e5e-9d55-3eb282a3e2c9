using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class alterfundreserveremovemerchantreservenonneededcolumns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Period",
                table: "MerchantFundsReserveConfiguration");

            migrationBuilder.DropColumn(
                name: "ReserveRate",
                table: "MerchantFundsReserveConfiguration");

            migrationBuilder.DropColumn(
                name: "ReserveRateType",
                table: "MerchantFundsReserveConfiguration");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "MerchantFundsReserveConfiguration");

            migrationBuilder.RenameColumn(
                name: "PayoutEnabled",
                table: "Merchants",
                newName: "PayoutsEnabled");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PayoutsEnabled",
                table: "Merchants",
                newName: "PayoutEnabled");

            migrationBuilder.AddColumn<int>(
                name: "Period",
                table: "MerchantFundsReserveConfiguration",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "ReserveRate",
                table: "MerchantFundsReserveConfiguration",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "ReserveRateType",
                table: "MerchantFundsReserveConfiguration",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "MerchantFundsReserveConfiguration",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }
    }
}
