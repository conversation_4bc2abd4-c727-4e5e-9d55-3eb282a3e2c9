using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class addedPspLoadBalancingcoulmnsinMerchantstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "PspLoadBalancing_RoundRobbinPercentage",
                table: "Merchants",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "PspLoadBalancing_WeightedPercentage",
                table: "Merchants",
                type: "numeric",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PspLoadBalancing_RoundRobbinPercentage",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "PspLoadBalancing_WeightedPercentage",
                table: "Merchants");
        }
    }
}
