using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class addWorkflowIdrelatedcolumnstoMerchantstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "EligibilityStrategyWorkflowId",
                table: "Merchants",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "NotEligibleOrderProcessingWorkflowId",
                table: "Merchants",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "RecyclingStrategyWorkflowId",
                table: "Merchants",
                type: "uuid",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EligibilityStrategyWorkflowId",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "NotEligibleOrderProcessingWorkflowId",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "RecyclingStrategyWorkflowId",
                table: "Merchants");
        }
    }
}
