// <auto-generated />
using System;
using FlexCharge.Merchants;
using FlexCharge.Merchants.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20221122152037_updateghostModeThrottlePercentage")]
    partial class updateghostModeThrottlePercentage
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.9")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "active_in_active", new[] { "active", "inactive" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "application_status", new[] { "draft", "cancelled", "submitted", "in_review", "approved", "declined" });
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Address", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Line1")
                        .HasColumnType("text");

                    b.Property<string>("Line2")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("ZipCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Addresses");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Application", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccountNumber")
                        .HasColumnType("text");

                    b.Property<Guid?>("AddressId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AgreeToTerms")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("AnnualCreditVolume")
                        .HasColumnType("integer");

                    b.Property<int>("AnnualDebitVolume")
                        .HasColumnType("integer");

                    b.Property<int>("AnnualSalesVolume")
                        .HasColumnType("integer");

                    b.Property<Guid>("Assignee")
                        .HasColumnType("uuid");

                    b.Property<int>("AvgPurchaseAmount")
                        .HasColumnType("integer");

                    b.Property<string>("BankName")
                        .HasColumnType("text");

                    b.Property<string>("BusinessEstablishedDate")
                        .HasColumnType("text");

                    b.Property<decimal>("ChargebackPercentage")
                        .HasColumnType("numeric");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerSupportEmail")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportLink")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportName")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportPhone")
                        .HasColumnType("text");

                    b.Property<string>("Dba")
                        .HasColumnType("text");

                    b.Property<string>("DdaType")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<string>("EcommercePlatform")
                        .HasColumnType("text");

                    b.Property<string>("ExternalId")
                        .HasColumnType("text");

                    b.Property<string>("Industry")
                        .HasColumnType("text");

                    b.Property<int>("IntegrationType")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LegalEntityName")
                        .HasColumnType("text");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("text");

                    b.Property<string>("Mcc")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Pcidss")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("PrimaryContactId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("ReturnsPercent")
                        .HasColumnType("numeric");

                    b.Property<string>("RoutingNumber")
                        .HasColumnType("text");

                    b.Property<string>("SpecialTerms")
                        .HasColumnType("text");

                    b.Property<ApplicationStatus>("Status")
                        .HasColumnType("application_status");

                    b.Property<string>("TaxId")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("Website")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("PartnerId");

                    b.HasIndex("PrimaryContactId");

                    b.ToTable("Applications");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.ApplicationFee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<int>("ChargeType")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsStandard")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationId");

                    b.ToTable("ApplicationFee");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Contact", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("ConfirmedUser")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<Guid>("ExternalId")
                        .HasColumnType("uuid");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.Property<bool>("Primary")
                        .HasColumnType("boolean");

                    b.Property<string>("Role")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryPhone")
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Contacts");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.FeeConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<int>("ChargeType")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsStandard")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("FeeConfigurations");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.MccCode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("MccCodes");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Merchant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccountNumber")
                        .HasColumnType("text");

                    b.Property<Guid?>("AddressId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<string>("BankName")
                        .HasColumnType("text");

                    b.Property<string>("BusinessEstablishedDate")
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerSupportEmail")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportLink")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportName")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportPhone")
                        .HasColumnType("text");

                    b.Property<string>("Dba")
                        .HasColumnType("text");

                    b.Property<string>("DdaType")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<string>("EcommercePlatform")
                        .HasColumnType("text");

                    b.Property<decimal?>("GhostModeThrottlePercentage")
                        .HasColumnType("numeric");

                    b.Property<string>("Icon")
                        .HasColumnType("text");

                    b.Property<string>("Industry")
                        .HasColumnType("text");

                    b.Property<int>("IntegrationType")
                        .HasColumnType("integer");

                    b.Property<bool>("IsBureauProductionActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LegalEntityName")
                        .HasColumnType("text");

                    b.Property<bool>("Locked")
                        .HasColumnType("boolean");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("text");

                    b.Property<string>("Mcc")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("OfferRequestsMaxPerDay")
                        .HasColumnType("integer");

                    b.Property<int?>("OfferRequestsRateLimitCount")
                        .HasColumnType("integer");

                    b.Property<int?>("OfferRequestsRateLimitIntervalMS")
                        .HasColumnType("integer");

                    b.Property<decimal?>("OfferRequestsThrottlePercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Offer_NSF_RequestsThrottle_Percentage")
                        .HasColumnType("numeric");

                    b.Property<int?>("Orders_MaxMonthlyAmount")
                        .HasColumnType("integer");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("PayoutsEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("Pcidss")
                        .HasColumnType("boolean");

                    b.Property<string>("PrimaryColor")
                        .HasColumnType("text");

                    b.Property<Guid?>("PrimaryContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("RoutingNumber")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryColor")
                        .HasColumnType("text");

                    b.Property<string>("SpecialTerms")
                        .HasColumnType("text");

                    b.Property<ActiveInActive>("Status")
                        .HasColumnType("active_in_active");

                    b.Property<string>("TaxId")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("Website")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("PartnerId");

                    b.HasIndex("PrimaryContactId");

                    b.ToTable("Merchants");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.MerchantFee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<int>("ChargeType")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid?>("RelatedMerchantId")
                        .HasColumnType("uuid");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RelatedMerchantId");

                    b.ToTable("MerchantFees");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Category")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("Expiry")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Notified")
                        .HasColumnType("boolean");

                    b.Property<string>("Params")
                        .HasColumnType("text");

                    b.Property<bool>("Read")
                        .HasColumnType("boolean");

                    b.Property<bool>("Seen")
                        .HasColumnType("boolean");

                    b.Property<int>("Severity")
                        .HasColumnType("integer");

                    b.Property<int>("Source")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("TriggerCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MerchantId");

                    b.ToTable("Notifications");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Partner", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ContactId");

                    b.ToTable("Partners");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Report", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastRunDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MerchantId")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<DateTime>("NextRunDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("RelatedMerchantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("SubscribedDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("RelatedMerchantId");

                    b.ToTable("Report");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Site", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerSupportEmail")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportLink")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportName")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportPhone")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeveloperContactId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid?>("SupportContactId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DeveloperContactId");

                    b.HasIndex("MerchantId");

                    b.HasIndex("SupportContactId");

                    b.ToTable("Sites");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SiteWhitelistedUrl", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Link")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("SiteWhitelistedUrls");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Application", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId");

                    b.HasOne("FlexCharge.Merchants.Entities.Partner", "Partner")
                        .WithMany()
                        .HasForeignKey("PartnerId");

                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "PrimaryContact")
                        .WithMany()
                        .HasForeignKey("PrimaryContactId");

                    b.Navigation("Address");

                    b.Navigation("Partner");

                    b.Navigation("PrimaryContact");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.ApplicationFee", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Application", "Application")
                        .WithMany("Fees")
                        .HasForeignKey("ApplicationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Application");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Merchant", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId");

                    b.HasOne("FlexCharge.Merchants.Entities.Partner", "Partner")
                        .WithMany()
                        .HasForeignKey("PartnerId");

                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "PrimaryContact")
                        .WithMany()
                        .HasForeignKey("PrimaryContactId");

                    b.Navigation("Address");

                    b.Navigation("Partner");

                    b.Navigation("PrimaryContact");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.MerchantFee", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", "RelatedMerchant")
                        .WithMany("Fees")
                        .HasForeignKey("RelatedMerchantId");

                    b.Navigation("RelatedMerchant");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Notification", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Partner", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "Contact")
                        .WithMany()
                        .HasForeignKey("ContactId");

                    b.Navigation("Contact");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Report", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", "RelatedMerchant")
                        .WithMany("Reports")
                        .HasForeignKey("RelatedMerchantId");

                    b.Navigation("RelatedMerchant");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Site", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "DeveloperContact")
                        .WithMany()
                        .HasForeignKey("DeveloperContactId");

                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", "Merchant")
                        .WithMany("Sites")
                        .HasForeignKey("MerchantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "SupportContact")
                        .WithMany()
                        .HasForeignKey("SupportContactId");

                    b.Navigation("DeveloperContact");

                    b.Navigation("Merchant");

                    b.Navigation("SupportContact");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SiteWhitelistedUrl", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Site", "Site")
                        .WithMany("WhitelistedUrls")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Application", b =>
                {
                    b.Navigation("Fees");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Merchant", b =>
                {
                    b.Navigation("Fees");

                    b.Navigation("Reports");

                    b.Navigation("Sites");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Site", b =>
                {
                    b.Navigation("WhitelistedUrls");
                });
#pragma warning restore 612, 618
        }
    }
}
