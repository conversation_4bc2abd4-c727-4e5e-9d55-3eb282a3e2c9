using FlexCharge.Common.Response;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using FlexCharge.Common.Authentication;

namespace FlexCharge.Merchants.Controllers
{
    public class BaseController : ControllerBase
    {
        protected Guid GetUserId()
        {
            var IsValid = Guid.TryParse(HttpContext.User.Claims.Where(x => x.Type == ClaimTypes.NameIdentifier)
                .Select(x => x.Value).SingleOrDefault(), out Guid aid);

            if (IsValid)
                return aid;

            return aid;
        }


        protected Guid GetAID()
        {
            var IsValid = Guid.TryParse(HttpContext.User.Claims.Where(x => x.Type == MyClaimTypes.ACCOUNT_ID)
                .Select(x => x.Value).SingleOrDefault(), out Guid aid);

            if (IsValid)
                return aid;

            return aid;
        }

        protected Guid GetMID()
        {
            var IsValidMid = Guid.TryParse(HttpContext.User.Claims.Where(x => x.Type == MyClaimTypes.MERCHANT_ID)
                .Select(x => x.Value).SingleOrDefault(), out Guid mid);

            if (IsValidMid)
                return mid;

            return mid;
        }

        protected Guid GetPID()
        {
            var IsValidPid = Guid.TryParse(HttpContext.User.Claims.Where(x => x.Type == MyClaimTypes.PARTNER_ID)
                .Select(x => x.Value).SingleOrDefault(), out Guid pid);

            if (IsValidPid)
                return pid;

            return pid;
        }

        protected Guid GetSAID()
        {
            var IsValidSaid = Guid.TryParse(HttpContext.User.Claims.Where(x => x.Type == MyClaimTypes.SALESAGENCY_ID)
                .Select(x => x.Value).SingleOrDefault(), out Guid said);

            if (IsValidSaid)
                return said;

            return said;
        }
        
        protected List<string> GetScopeClaims()
        {
            var scope = HttpContext.User.Claims.Where(x => x.Type == MyClaimTypes.SCOPE)
                .Select(x => x.Value).ToList();

            return scope;
        }

        protected void SetTokenCookie(string token)
        {
            var cookieOptions = new CookieOptions
            {
                Domain = "",
                HttpOnly = true,
                Secure = true,
                Expires = DateTime.UtcNow.AddDays(7)
            };
            Response.Cookies.Append("RefreshToken", token, cookieOptions);
        }

        protected IActionResult ReturnResponse<T>(T response) where T : BaseResponse
        {
            if (!response.Success)
            {
                if (response.CustomProperties.Any())
                    response.CustomProperties.ToList().ForEach(x => ModelState.AddModelError(x.Key, x.Value));

                response.Errors.Where(x => x.FriendlyError).ToList()
                    .ForEach(x => ModelState.AddModelError(x.Key, x.Error));

                var problemDetails = new ValidationProblemDetails(ModelState)
                {
                    Status = StatusCodes.Status400BadRequest,
                };
                problemDetails.Extensions["traceId"] = HttpContext.TraceIdentifier;

                return BadRequest(problemDetails);
            }
            else
                return Ok(response);
        }
    }
}