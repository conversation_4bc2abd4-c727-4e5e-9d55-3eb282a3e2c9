// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Threading;
// using System.Threading.Tasks;
// using AutoMapper;
// using FlexCharge.Common;
// using FlexCharge.Common.Authentication;
// using FlexCharge.Merchants.DTO;
// using FlexCharge.Merchants.Entities;
// using FlexCharge.Merchants.Enums;
// using FlexCharge.Common.GeoServices;
// using FlexCharge.Common.Recaptcha;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Contracts;
// using FlexCharge.Merchants.DTO.CommissionsDTOS;
// using FlexCharge.Merchants.Services.CommissionsServices;
// using FlexCharge.Merchants.Services.SalesAgencyServices;
// using FlexCharge.Utils;
// using MassTransit;
// using Microsoft.AspNetCore.Authorization;
// using Microsoft.AspNetCore.Mvc;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
// using StackExchange.Redis;
// using Twilio.TwiML.Voice;
// using Z.EntityFramework.Plus;
//
// namespace FlexCharge.Merchants.Controllers;
//
// [Route("[controller]")]
// [ApiController]
// [JwtAuth()]
// public class CommissionsController : ControllerBase
// {
//     private PostgreSQLDbContext _dbContext;
//     private readonly IPublishEndpoint _publisher;
//     private readonly ICommissionService _commissionService;
//     private readonly AppOptions _globalData;
//
//
//     public CommissionsController(PostgreSQLDbContext dbContext,
//         IPublishEndpoint publisher, ICommissionService commissionService, IOptions<AppOptions> globalData)
//     {
//         _dbContext = dbContext;
//         _publisher = publisher;
//         _commissionService = commissionService;
//         _globalData = globalData.Value;
//     }
//
//     [HttpPost]
//     [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
//     [ProducesResponseType(200)]
//     public async Task<IActionResult> Post(CommissionConfigurationCreateDTO payload, CancellationToken token)
//     {
//         using var workspan = Workspan.StartEndpoint<SalesController>(this, payload, _globalData)
//             .LogEnterAndExit();
//
//         try
//         {
//             if (!ModelState.IsValid)
//                 return ValidationProblem();
//
//             var configuration = await _commissionService.CreateCommissionAsync(payload);
//
//             return Ok(new
//             {
//                 Id = configuration.Id,
//                 Name = configuration.Name
//             });
//         }
//         catch (Exception e)
//         {
//             workspan.Log.Error(e, $"EXCEPTION: SalesController > POST > Failed adding sail agent");
//             throw;
//         }
//     }
//
//     [HttpPut]
//     [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
//     [ProducesResponseType(200)]
//     public async Task<IActionResult> Put(CommissionsConfiguration payload, CancellationToken token)
//     {
//         using var workspan = Workspan.StartEndpoint<SalesController>(this, payload, _globalData)
//             .LogEnterAndExit();
//         try
//         {
//             if (!ModelState.IsValid)
//                 return ValidationProblem();
//
//             await _commissionService.UpdateCommissionAsync(payload);
//
//             return Ok();
//         }
//         catch (Exception e)
//         {
//             workspan.Log.Error(e, $"EXCEPTION: SalesController > POST > Failed updating sail agent");
//             throw;
//         }
//     }
//
//     [HttpGet]
//     [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
//     [ProducesResponseType(200)]
//     public async Task<IActionResult> GetCommissions(CancellationToken token)
//     {
//         using var workspan = Workspan.StartEndpoint<SalesController>(this, null, _globalData)
//             .LogEnterAndExit();
//
//         try
//         {
//             var dConfigurations = await _commissionService.GetAllCommissionsAsync();
//
//             return Ok(dConfigurations);
//         }
//         catch (Exception e)
//         {
//             workspan.Log.Error(e, $"EXCEPTION: commissionController > GET > Failed getting commissions");
//             throw;
//         }
//     }
//
//     [HttpDelete]
//     [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
//     [ProducesResponseType(200)]
//     public async Task<IActionResult> Delete(Guid id, CancellationToken token)
//     {
//         using var workspan = Workspan.StartEndpoint<SalesController>(this, id, _globalData)
//             .LogEnterAndExit();
//
//         try
//         {
//             if (!ModelState.IsValid)
//                 return ValidationProblem();
//
//             var response = await _commissionService.DeleteCommissionAsync(id);
//
//             return Ok(response);
//         }
//         catch (Exception e)
//         {
//             workspan.RecordException(e);
//             throw;
//         }
//     }
// }

