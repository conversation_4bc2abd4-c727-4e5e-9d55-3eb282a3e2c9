// Cache not working for now as QuickSight tracks when browser closed iframe with dashboard and breaks session
//#define CACHE_QUICKSIGHT_DASHBOARD_URLS

using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cloud.BI.Amazon;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Merchants.DistributedCache;
using FlexCharge.Merchants.DTO.Analytics;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;


namespace FlexCharge.Merchants.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class AnalyticsController : BaseController
    {
        // https://docs.aws.amazon.com/cli/latest/reference/quicksight/get-dashboard-embed-url.html
        const int
            QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES = 10 * 60; // 10 hours is the maximum session time for QuickSight 

        private readonly IQuickSight _quickSight;
        private readonly AppOptions _globalData;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly IDistributedCache _distributedCache;

        public AnalyticsController(
            IOptions<AppOptions> globalData,
            IQuickSight quickSight,
            PostgreSQLDbContext dbContext,
            IDistributedCache distributedCache
        )
        {
            _quickSight = quickSight;
            _globalData = globalData.Value;
            _dbContext = dbContext;
            _distributedCache = distributedCache;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="browserSessionId">A Guid unique to this browser that does not change between sessions</param>
        /// <returns></returns>
        [HttpGet("merchant/dashboard")]
        [Authorize(policy: MyPolicies.ALL_MERCHANTS)]
        [ProducesResponseType(typeof(MerchantAnalyticsResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetMerchantDashboard(Guid browserSessionId)
        {
            using var workspan = Workspan.StartEndpoint<AnalyticsController>(this, null, _globalData);

            try
            {
                var mid = GetMID();

                #region Return cached dashboard url if available

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                var urlCacheKey =
                    CacheKeyFactory.CreateQuickSightMerchantDashboardUrlCacheKey(
                        QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES -
                        5, // 5 minutes less than the maximum session time to be safe
                        browserSessionId);

                var cachedDashboardUrl =
                    await _distributedCache.GetStringValueAsync(urlCacheKey.Key);

                if (cachedDashboardUrl != null)
                {
                    workspan.Log.Information("Returning cached dashboard url");

                    return Ok(new MerchantAnalyticsResponse
                    {
                        DashboardUrl = cachedDashboardUrl
                    });
                }

#endif

                #endregion

                workspan.Log.Information("Generating new dashboard url");

                Dictionary<string, string> dashboardParameters = new();
                dashboardParameters["MerchantId"] = mid.ToString();

                var url = await _quickSight.GenerateEmbedUrlForAnonymousUserAsync(
                    "b4a77a4a-218b-44b8-a501-aab60ca148cc",
                    QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES,
                    GetAllowedDomainsToShowQuickSightReports(),
                    dashboardParameters: dashboardParameters);

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                await _distributedCache.SetStringAsync(urlCacheKey.Key, url, urlCacheKey.CacheOptions);
#endif

                return Ok(new MerchantAnalyticsResponse
                {
                    DashboardUrl = url
                });
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="mid"></param>
        /// <param name="browserSessionId">A Guid unique to this browser that does not change between sessions</param>
        /// <returns></returns>
        [HttpGet("partner/dashboard")]
        [Authorize(policy: MyPolicies.PARTNER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(MerchantAnalyticsResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetIntegrationPartnerDashboard(Guid mid, Guid browserSessionId)
        {
            using var workspan = Workspan.StartEndpoint<AnalyticsController>(this, null, _globalData);

            try
            {
                var pid = GetPID();

                if (pid == Guid.Empty)
                    return StatusCode(StatusCodes.Status500InternalServerError);

                var merchant = await _dbContext.Merchants.FirstAsync(m => m.Id == mid && m.PartnerId == pid);

                #region Return cached dashboard url if available

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                var urlCacheKey =
                    CacheKeyFactory.CreateQuickSightMerchantDashboardUrlCacheKey(
                        QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES -
                        5, // 5 minutes less than the maximum session time to be safe
                        browserSessionId);

                var cachedDashboardUrl =
                    await _distributedCache.GetStringValueAsync(urlCacheKey.Key);

                if (cachedDashboardUrl != null)
                {
                    workspan.Log.Information("Returning cached dashboard url");

                    return Ok(new MerchantAnalyticsResponse
                    {
                        DashboardUrl = cachedDashboardUrl
                    });
                }

#endif

                #endregion

                workspan.Log.Information("Generating new dashboard url");

                Dictionary<string, string> dashboardParameters = new();
                dashboardParameters["MerchantId"] = merchant.Id.ToString();

                var url = await _quickSight.GenerateEmbedUrlForAnonymousUserAsync(
                    "b4a77a4a-218b-44b8-a501-aab60ca148cc",
                    QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES,
                    GetAllowedDomainsToShowQuickSightReports(),
                    dashboardParameters: dashboardParameters);

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                await _distributedCache.SetStringAsync(urlCacheKey.Key, url, urlCacheKey.CacheOptions);
#endif

                return Ok(new MerchantAnalyticsResponse
                {
                    DashboardUrl = url
                });
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="mid"></param>
        /// <param name="browserSessionId">A Guid unique to this browser that does not change between sessions</param>
        /// <returns></returns>
        [HttpGet("integrationpartner/dashboard")]
        [Authorize(policy: MyPolicies.INTEGRATION_PARTNER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(MerchantAnalyticsResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetPartnerDashboard(Guid mid, Guid browserSessionId)
        {
            using var workspan = Workspan.StartEndpoint<AnalyticsController>(this, null, _globalData);

            try
            {
                var pid = GetPID();

                if (pid == Guid.Empty)
                    return StatusCode(StatusCodes.Status500InternalServerError);

                var merchant = await _dbContext.Merchants.FirstAsync(m => m.Id == mid && m.IntegrationPartnerId == pid);

                #region Return cached dashboard url if available

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                var urlCacheKey =
                    CacheKeyFactory.CreateQuickSightMerchantDashboardUrlCacheKey(
                        QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES -
                        5, // 5 minutes less than the maximum session time to be safe
                        browserSessionId);

                var cachedDashboardUrl =
                    await _distributedCache.GetStringValueAsync(urlCacheKey.Key);

                if (cachedDashboardUrl != null)
                {
                    workspan.Log.Information("Returning cached dashboard url");

                    return Ok(new MerchantAnalyticsResponse
                    {
                        DashboardUrl = cachedDashboardUrl
                    });
                }

#endif

                #endregion

                workspan.Log.Information("Generating new dashboard url");

                Dictionary<string, string> dashboardParameters = new();
                dashboardParameters["MerchantId"] = merchant.Id.ToString();

                var url = await _quickSight.GenerateEmbedUrlForAnonymousUserAsync(
                    "b4a77a4a-218b-44b8-a501-aab60ca148cc",
                    QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES,
                    GetAllowedDomainsToShowQuickSightReports(),
                    dashboardParameters: dashboardParameters);

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                await _distributedCache.SetStringAsync(urlCacheKey.Key, url, urlCacheKey.CacheOptions);
#endif

                return Ok(new MerchantAnalyticsResponse
                {
                    DashboardUrl = url
                });
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="mid"></param>
        /// <param name="browserSessionId">A Guid unique to this browser session</param>
        /// <returns></returns>
        [HttpGet("admin/dashboard")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(MerchantAnalyticsResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetAdminDashboard(Guid mid, Guid browserSessionId)
        {
            using var workspan = Workspan.StartEndpoint<AnalyticsController>(this, null, _globalData);

            try
            {
                #region Return cached dashboard url if available

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                var urlCacheKey =
                    CacheKeyFactory.CreateQuickSightMerchantDashboardUrlCacheKey(
                        QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES -
                        5, // 5 minutes less than the maximum session time to be safe
                        browserSessionId);

                var cachedDashboardUrl =
                    await _distributedCache.GetStringValueAsync(urlCacheKey.Key);

                if (cachedDashboardUrl != null)
                {
                    workspan.Log.Information("Returning cached dashboard url");

                    return Ok(new MerchantAnalyticsResponse
                    {
                        DashboardUrl = cachedDashboardUrl
                    });
                }

#endif

                #endregion

                workspan.Log.Information("Generating new dashboard url");

                Dictionary<string, string> dashboardParameters = new();
                dashboardParameters["MerchantId"] = mid.ToString();

                var url = await _quickSight.GenerateEmbedUrlForAnonymousUserAsync(
                    "b4a77a4a-218b-44b8-a501-aab60ca148cc",
                    QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES,
                    GetAllowedDomainsToShowQuickSightReports(),
                    dashboardParameters: dashboardParameters);

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                await _distributedCache.SetStringAsync(urlCacheKey.Key, url, urlCacheKey.CacheOptions);
#endif

                return Ok(new MerchantAnalyticsResponse
                {
                    DashboardUrl = url
                });
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="browserSessionId">A Guid unique to this browser that does not change between sessions</param>
        /// <returns></returns>
        [HttpGet("partner/analytics")]
        [Authorize(policy: MyPolicies.PARTNER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(MerchantAnalyticsResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetPartnerAnalytics(Guid browserSessionId)
        {
            using var workspan = Workspan.StartEndpoint<AnalyticsController>(this, null, _globalData);

            try
            {
                var pid = GetPID();

                if (pid == Guid.Empty)
                    return StatusCode(StatusCodes.Status500InternalServerError);


                #region Return cached dashboard url if available

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                var urlCacheKey =
                    CacheKeyFactory.CreateQuickSightPartnerDashboardUrlCacheKey(
                        QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES -
                        5, // 5 minutes less than the maximum session time to be safe
                        browserSessionId);

                var cachedDashboardUrl =
                    await _distributedCache.GetStringValueAsync(urlCacheKey.Key);

                if (cachedDashboardUrl != null)
                {
                    workspan.Log.Information("Returning cached dashboard url");

                    return Ok(new MerchantAnalyticsResponse
                    {
                        DashboardUrl = cachedDashboardUrl
                    });
                }
#endif

                #endregion

                workspan.Log.Information("Generating new dashboard url");

                Dictionary<string, string> dashboardParameters = new();
                dashboardParameters["PartnerId"] = pid.ToString();

                var url = await _quickSight.GenerateEmbedUrlForAnonymousUserAsync(
                    "e5ae79f9-6769-43fb-a3dd-c4fb339c36c8",
                    QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES,
                    GetAllowedDomainsToShowQuickSightReports(),
                    dashboardParameters:
                    dashboardParameters);

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                await _distributedCache.SetStringAsync(urlCacheKey.Key, url, urlCacheKey.CacheOptions);
#endif

                return Ok(new MerchantAnalyticsResponse
                {
                    DashboardUrl = url
                });
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="browserSessionId">A Guid unique to this browser session</param>
        /// <returns></returns>
        [HttpGet("admin/analytics")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(MerchantAnalyticsResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetAdminAnalytics(Guid browserSessionId)
        {
            using var workspan = Workspan.StartEndpoint<AnalyticsController>(this, null, _globalData);

            try
            {
                #region Return cached dashboard url if available

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                var urlCacheKey =
                    CacheKeyFactory.CreateQuickSightAdminDashboardUrlCacheKey(
                        QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES -
                        5, // 5 minutes less than the maximum session time to be safe
                        browserSessionId);

                var cachedDashboardUrl =
                    await _distributedCache.GetStringValueAsync(urlCacheKey.Key);

                if (cachedDashboardUrl != null)
                {
                    workspan.Log.Information("Returning cached dashboard url");

                    return Ok(new MerchantAnalyticsResponse
                    {
                        DashboardUrl = cachedDashboardUrl
                    });
                }
#endif

                #endregion

                workspan.Log.Information("Generating new dashboard url");

                Dictionary<string, string> dashboardParameters = new();
                dashboardParameters["PartnerId"] = "ALL";

                var url = await _quickSight.GenerateEmbedUrlForAnonymousUserAsync(
                    "e5ae79f9-6769-43fb-a3dd-c4fb339c36c8",
                    QUICK_SIGHT_SESSION_LIFE_TIME_IN_MINUTES,
                    GetAllowedDomainsToShowQuickSightReports(),
                    dashboardParameters: dashboardParameters);

#if CACHE_QUICKSIGHT_DASHBOARD_URLS
                await _distributedCache.SetStringAsync(urlCacheKey.Key, url, urlCacheKey.CacheOptions);
#endif

                return Ok(new MerchantAnalyticsResponse
                {
                    DashboardUrl = url
                });
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError);
            }
        }

        private static List<string> GetAllowedDomainsToShowQuickSightReports()
        {
            List<string> allowedDomains = new();

            switch (EnvironmentHelper.GetCurrentEnvironment())
            {
                case EnvironmentHelper.Environment.Production:
                    allowedDomains.Add("https://portal.flexfactor.io");
                    allowedDomains.Add("https://portal.flex-charge.com");
                    break;
                case EnvironmentHelper.Environment.Sandbox:
                    allowedDomains.Add("https://portal-sandbox.flexfactor.io");
                    allowedDomains.Add("https://portal-sandbox.flex-charge.com");
                    break;
                case EnvironmentHelper.Environment.Staging:
                    allowedDomains.Add("https://portal-staging.flexfactor.io");
                    allowedDomains.Add("https://portal-staging.flex-charge.com");
                    break;
                case EnvironmentHelper.Environment.Development:
                    allowedDomains.Add("http://localhost");
                    break;
                default:
                    throw new FlexChargeException(
                        $"Unknown environment: {EnvironmentHelper.GetCurrentEnvironment()}");
            }

            return allowedDomains;
        }
    }
}