using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Merchants.DTO;
using FlexCharge.Merchants.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Elasticsearch.Net;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Cloud.Storage.Amazon.AmazonDTOs;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Merchants.Entities;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.ModelBinding.Metadata;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using Merchant = FlexCharge.Merchants.Entities.Merchant;
using MerchantFee = FlexCharge.Merchants.Entities.MerchantFee;
using HtmlAgilityPack;
using Thrift.Protocol;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace FlexCharge.Merchants.Controllers;

[Route("[controller]")]
[ApiController]
[JwtAuth]
public class AuditLogsController : BaseController
{
    private PostgreSQLDbContext _context { get; set; }
    private readonly AppOptions _globalData;
    private readonly IMapper _mapper;

    public AuditLogsController(PostgreSQLDbContext context, IMapper mapper,
        IOptions<AppOptions> globalData)
    {
        _context = context;
        _mapper = mapper;
        _globalData = globalData.Value;
    }

    [HttpGet()] // GET ALL
    [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(typeof(PagedDTO<AuditLogResponseDTO>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Get(
        string? sort,
        string? entityName,
        string? userId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageNumber = 1, int pageSize = 10)
    {
        using var workspan = Workspan.Start<AuditLogsController>();
        try
        {
            // Set default values if not provided
            startDate ??= DateTime.UtcNow.AddHours(-24);
            endDate ??= DateTime.UtcNow;

            var auditLogs = _context.AuditLogs.AsQueryable();

            // Apply filters based on user input
            if (startDate.HasValue)
                auditLogs = auditLogs.Where(log => log.CreatedOn >= startDate.Value);

            if (endDate.HasValue)
                auditLogs = auditLogs.Where(log => log.CreatedOn <= endDate.Value);

            if (!string.IsNullOrEmpty(entityName))
                auditLogs = auditLogs.Where(log => log.TableName == entityName);

            if (!string.IsNullOrEmpty(userId))
                auditLogs = auditLogs.Where(log => log.UserId == userId);

            auditLogs = sort == "asc"
                ? auditLogs.OrderBy(x => x.CreatedOn)
                : auditLogs.OrderByDescending(x => x.CreatedOn);

            var filteredLogs = await auditLogs
                .Select(x => new AuditLogResponseDTO
                {
                    CreatedOn = x.CreatedOn,
                    TableName = x.TableName,
                    PrimaryKey = x.PrimaryKey,
                    Operation = x.Operation,
                    OldValues = DeserializeValues(x.OldValues),
                    NewValues = DeserializeValues(x.NewValues),
                    AffectedColumns = x.AffectedColumns,
                    UserId = x.UserId,
                    UserFirstName = x.UserFirstName,
                    UserLastName = x.UserLastName,
                    UserRole = x.UserRole,
                    UserEmail = x.UserEmail,
                    UserIp = x.UserIp
                })
                .ToPagedListAsync(pageNumber, pageSize);
            
            var mappedLogs = _mapper.Map<IPagedList<AuditLogResponseDTO>, PagedDTO<AuditLogResponseDTO>>(filteredLogs);

            return Ok(mappedLogs);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
        }
    }
    
    [HttpGet("tables")]
    [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(typeof(List<string>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetTables()
    {
        using var workspan = Workspan.Start<AuditLogsController>();
        try
        {
            var tableNames = _context.Model.GetEntityTypes().Select(x => x.GetTableName()).ToList();;
            
            return Ok(tableNames);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
        }
    }
    
    private static object DeserializeValues(string values)
    {
        if (string.IsNullOrEmpty(values))
            return null;
        var obj = new System.Dynamic.ExpandoObject();
        var dictionary = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(values);
        
        foreach (var (key, value) in dictionary)
        {
            ((IDictionary<string, object>)obj)[key] = value.ToString();
        }
        
        return obj;
    }
}