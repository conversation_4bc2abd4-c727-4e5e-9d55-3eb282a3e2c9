using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Elasticsearch.Net;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Cloud;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Merchants.DTO;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Enums;
using FlexCharge.Common.GeoServices;
using FlexCharge.Common.Response;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Twilio.TwiML.Voice;
using Task = System.Threading.Tasks.Task;

namespace FlexCharge.Merchants.Controllers;

[Route("[controller]")]
[ApiController]
[JwtAuth()]
public class FundsReserveController : BaseController
{
    private readonly IMapper _mapper;
    private PostgreSQLDbContext _dbContext;
    private readonly AppOptions _globalData;

    public FundsReserveController(PostgreSQLDbContext dbContext, IMapper mapper,
        SenseJsExternalPostgreSQLDbContext senseJsExternalPostgreSqlDbContext,
        IOptions<AppOptions> globalData)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _globalData = globalData.Value;
    }

    [HttpGet]
    [Authorize(policy: MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> Get(bool isActive = true)
    {
        using var workspan = Workspan.StartEndpoint<FundsReserveController>(this, null, _globalData);

        try
        {
            var fundsReserve = await _dbContext.FundsReserveConfigurations
                .Where(x => x.IsActive == isActive && x.IsDeleted == false)
                .OrderBy(x => x.DisputeRateMin)
                .ToListAsync();

            return Ok(_mapper.Map<IEnumerable<FundsReserveConfigurationResponse>>(fundsReserve));
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    [HttpGet("list")]
    [Authorize(policy: MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> GetConfiguration()
    {
        using var workspan = Workspan.StartEndpoint<FundsReserveController>(this, null, _globalData);

        try
        {
            var types = Enum.GetValues(typeof(FundReserveType))
                .Cast<FundReserveType>()
                .ToDictionary(k => (int) k, v => v.ToString());


            var rateTypes = Enum.GetValues(typeof(FundReserveRateType))
                .Cast<FundReserveRateType>()
                .ToDictionary(v => (int) v, k => k.ToString());

            var response = new
            {
                types,
                rateTypes
            };

            return Ok(response);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    [HttpGet]
    [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
    [Route("{id:guid}")]
    public async Task<IActionResult> GetConfigurationById([FromRoute] Guid id)
    {
        using var workspan = Workspan.StartEndpoint<FundsReserveController>(this, null, _globalData);

        try
        {
            var fundsReserve = await _dbContext.FundsReserveConfigurations
                .SingleOrDefaultAsync(x => x.Id == id);

            if (fundsReserve == null)
            {
                return NotFound();
            }

            var response = new FundsReserveConfigurationResponse
            {
                Id = fundsReserve.Id,
                DisputeRateMin = Utils.Formatters.IntToDecimal(fundsReserve.DisputeRateMin),
                DisputeRateMax = Utils.Formatters.IntToDecimal(fundsReserve.DisputeRateMax),
                ReserveRate = fundsReserve.ReserveRate,
                ReserveRateType = fundsReserve.ReserveRateType,
                Type = fundsReserve.Type,
                Period = fundsReserve.Period,
            };

            return Ok(response);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    [HttpPut]
    [Authorize(policy: MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> UpdateConfiguration([FromBody] FundsReserveConfigurationUpdateRequest payload)
    {
        using var workspan = Workspan.StartEndpoint<FundsReserveController>(this, payload, _globalData);

        try
        {
            var fundsReserve = await _dbContext.FundsReserveConfigurations
                .SingleOrDefaultAsync(x => x.Id == payload.Id);

            if (fundsReserve == null)
            {
                return NotFound();
            }

            fundsReserve.IsActive = false;
            _dbContext.FundsReserveConfigurations.Update(fundsReserve);

            var newFundsReserve = new FundsReserveConfiguration
            {
                Name = payload.Name,
                DisputeRateMin = payload.DisputeRateMin != null
                    ? Utils.Formatters.DecimalToInt(payload.DisputeRateMin.Value)
                    : fundsReserve.DisputeRateMin,
                DisputeRateMax = payload.DisputeRateMax != null
                    ? Utils.Formatters.DecimalToInt(payload.DisputeRateMax.Value)
                    : fundsReserve.DisputeRateMax,
                ReserveRate = payload.ReserveRate ?? fundsReserve.ReserveRate,
                ReserveRateType = payload.ReserveRateType ?? fundsReserve.ReserveRateType,
                Type = payload.Type ?? fundsReserve.Type,
                Period = payload.Period ?? fundsReserve.Period,
            };

            _dbContext.FundsReserveConfigurations.Add(newFundsReserve);
            await _dbContext.SaveChangesAsync();

            return Ok();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }
}