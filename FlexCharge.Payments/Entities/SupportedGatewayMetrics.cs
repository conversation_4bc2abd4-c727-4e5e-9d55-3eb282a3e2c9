namespace FlexCharge.Payments.Entities;

using System;
using System.Collections.Generic;

public class SupportedGatewayMetrics : AuditableEntity
{
    public Guid SupportedGatewayId { get; set; }

    private const double Alpha = 0.2; // for EWMA (latency, cost, etc.)


    public long TotalAttempts { get; set; }
    public long TotalSuccesses { get; set; }
    public long TotalErrors { get; set; }

    public long TotalMasterCardAttempts { get; set; }
    public long TotalMasterCardSuccesses { get; set; }
    public long TotalMasterCardErrors { get; set; }

    public long TotalVisaAttempts { get; set; }
    public long TotalVisaSuccesses { get; set; }
    public long TotalVisaErrors { get; set; }

    public double OverallSuccessRate
    {
        get
        {
            if (TotalAttempts == 0) return 0.0;
            return (double) TotalSuccesses / TotalAttempts;
        }
    }

    public double OverallErrorRate
    {
        get
        {
            if (TotalAttempts == 0) return 0.0;
            return (double) TotalErrors / TotalAttempts;
        }
    }

    public double OverallMasterCardSuccessRate
    {
        get
        {
            if (TotalMasterCardSuccesses == 0) return 0.0;
            return (double) TotalMasterCardSuccesses / TotalMasterCardAttempts;
        }
    }

    public double OverallMasterCardErrorRate
    {
        get
        {
            if (TotalMasterCardErrors == 0) return 0.0;
            return (double) TotalMasterCardSuccesses / TotalMasterCardAttempts;
        }
    }

    public double OverallVisaSuccessRate
    {
        get
        {
            if (TotalVisaSuccesses == 0) return 0.0;
            return (double) TotalVisaSuccesses / TotalVisaAttempts;
        }
    }

    public double OverallVisaErrorRate
    {
        get
        {
            if (TotalVisaErrors == 0) return 0.0;
            return (double) TotalVisaErrors / TotalVisaAttempts;
        }
    }

    public double AverageLatencyMs { get; set; }

    public int TransactionsCount { get; set; }
}