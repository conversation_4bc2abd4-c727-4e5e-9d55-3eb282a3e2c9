using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace FlexCharge.Payments.Entities;

public class DisputeQueueItem : AuditableEntity
{
    public Guid DisputeQueueId { get; set; }
    public Guid RefundTransactionId { get; set; }

    public string Descriptor { get; set; }

    public string CaseNumber { get; set; }

    //public string ReferenceNumber { get; set; }
    public string Stage { get; set; }
    public int Amount { get; set; }

    public int DisputeAmount { get; set; }

    //public string TransactionMethod { get; set; }
    public string AuthorizationId { get; set; }
    public string Currency { get; set; }
    public DateTime TransactionDate { get; set; }
    public DateTime RequestDate { get; set; }

    public DateTime? DueDate { get; set; }

    //public DateTime? ExpirationDate { get; set; }
    public string Mcc { get; set; }
    public string Arn { get; set; }
    public string CardAcceptorId { get; set; }
    public string ProviderName { get; set; }
    public string ProcessorName { get; set; }
    public string DisputeManagementSystem { get; set; }
    public string DisputeType { get; set; }
    public string CardBrand { get; set; }
    public string CardType { get; set; }
    public string Bin { get; set; }
    public string Last4 { get; set; }
    public string Message { get; set; }
    public bool EarlyFraudWarning { get; set; } = false;
    public bool IsWebhook { get; set; } = false;
    public bool IsFileImported { get; set; } = false;
    public bool IsSftp { get; set; } = false;
    public string Note { get; set; }
    [Column(TypeName = "jsonb")] public string? Meta { get; set; }
    public List<DisputeActivity> DisputeActivities { get; set; }
    public bool? HasMatch { get; set; }
    public DateTime? IsArchived { get; set; }
    public int? MatchedTransactionCount { get; set; }
    public string? Reason { get; set; }
    public DateTime? AuthorizationDate { get; set; }
    public string? Issuer { get; set; }
    public string? ExternalReferenceID { get; set; } // Provider transactionID
    public Guid? TransactionId { get; set; } // Our transactionID
    public string? KountAuthorizationCode { get; set; }
    public string? KountEventType { get; set; }
    public string? OrderId { get; set; } // OrderID from provider
    public Guid? PartnerId { get; set; } // Partner who send the dispute
    public DateTime? RepliedToEthoca { get; set; }
    public string? RequestId { get; set; }
    public bool IsManualInserted { get; set; } = false;
    public string EventType { get; set; }
    public string Status { get; set; }
    public string? ChargebackWinLoss { get; set; }
    public string? AuthorizationCode { get; set; }
    public Guid? DisputeId { get; set; }
    public Guid? AlertProviderId { get; set; }

    public List<PotentialMatch> PotentialMatches { get; set; }
    public string? MatchError { get; set; }
}

public class PotentialMatch : AuditableEntity
{
    public Guid TransactionId { get; set; }
    public int Value { get; set; }
    public List<string> MatchedFields { get; set; }

    public Guid DisputeQueueItemId { get; set; }
}