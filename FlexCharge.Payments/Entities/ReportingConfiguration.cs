using System;

namespace FlexCharge.Payments.Entities;

public class ReportingConfiguration : AuditableEntity
{
    public string ProviderName { get; set; }
    public string Source { get; set; }
    public bool AutoMatch { get; set; } = false;
    public bool IsActive { get; set; }
    public string SftpHost { get; set; }
    public string SftpUsername { get; set; }
    public string SftpPassword { get; set; }
    public string SftpPrivateKey { get; set; } // not actual key it will be secret aws
    public string SftpPrivateName { get; set; } // key name in aws secret manager (frm object that secret manager returns)
    public int? SftpPort { get; set; }
    public string SftpPollingPath { get; set; }
    public string SftpFileNamePart { get; set; }
}