using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Payments.Entities
{
    [Index(nameof(CreatedOnDate))]
    public class MonitoredTransaction : AuditableHardDeleteEntity
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Key]
        public Guid Id { get; set; }

        public string MonitoringType { get; set; }

        public Transaction Transaction { get; set; }

        // Virtual indexed column for fast search by date
        public DateOnly? CreatedOnDate { get; set; } = DateOnly.FromDateTime(DateTime.UtcNow);

        public DateTime TransactionCreatedOn { get; set; }
        public string TransactionType { get; set; }
        public string PaymentType { get; set; }
        public Guid ProviderId { get; set; }
        public Guid OrderId { get; set; }
        public int Amount { get; set; }
    }
}