using System;

namespace FlexCharge.Payments.Entities;

public class AlertProvider : AuditableEntity
{
    public string Name { get; set; }
    public string ApiUrl { get; set; }
    public string PrivateKey { get; set; }
    public string PrivateName { get; set; }
    public Guid PartnerId { get; set; }
    public bool IsRdrProvider { get; set; }
    public bool IsEthocaProvider { get; set; }
    public bool IsChargebacksManagementProvider { get; set; }
    public bool IsSandbox { get; set; }
    public string Description { get; set; }
    public bool IsActive { get; set; }
}