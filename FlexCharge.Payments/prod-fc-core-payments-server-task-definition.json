{"containerDefinitions": [{"name": "core-payments", "image": "************.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-payments:7da9b252999a69d0cc1ab1ed52f769f29f960e1d", "cpu": 0, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "payments_service_prod"}, {"name": "DB_DATABASE", "value": "fc_payments"}, {"name": "DB_HOST", "value": "flexcharge-prod.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Production"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}, {"name": "TOKENEX_ID", "value": "****************"}, {"name": "OPEN_BANKING_ENCRYPTION_KMS_KEY_ID", "value": "arn:aws:kms:us-east-1:************:key/mrk-4d29702913f4440890d391a8f91c7fa5"}, {"name": "NEW_RELIC_APP_NAME", "value": "Payments-production"}, {"name": "OTEL_SERVICE_NAME", "value": "Payments-production"}, {"name": "KOUNT_API", "value": "https://api.kount.com"}, {"name": "KOUNT_ISSUER", "value": "https://login.kount.com/oauth2/ausdppksgrbyM0abp357"}, {"name": "MYRCVR_API", "value": "https://api.reports.myrcvr.com/data/received_alert"}, {"name": "REDSHIFT_CONNECTION_STRING", "value": "arn:aws:secretsmanager:us-east-1:************:secret:prod/redshift-sydKSu"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PROD_DB_PAYMENTS_PASSWORD-26unVj"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PROD_SNS_IAM_ACCESS_KEY-lbCS4b"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PROD_SNS_IAM_SECRET_KEY-a0ce7k"}, {"name": "PLAID_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PRODUCTION_PLAID_CLIENT_ID-yBowST"}, {"name": "PLAID_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PRODUCTION_PLAID_SECRET-De9WfM"}, {"name": "SVB_ACH_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PRODUCTION-SVB-ACH-CLIENT-ID-52cS9X"}, {"name": "SVB_ACH_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PRODUCTION-SVB-ACH-CLIENT-SECRET-3zyfSW"}, {"name": "TOKENEX_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PRODUCTION-TOKENEX-API-KEY-mPZ8vU"}, {"name": "API_CLIENT_JWT_SIGNING_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PRODUCTION-API-CLIENT-JWT-SIGNING-KEY-iGzRih"}, {"name": "KOUNT_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:prod/declinedefense/kount-kcm/apikey-OlBSMh"}, {"name": "STRIPE_APPS_STRIPE_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:prod/integrations/stripe-apps/stripe-api-key-pxlY0d"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-payments-server-prod", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-payments-server-prod", "taskRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionWithSecretAccess-Production-Role", "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionWithSecretAccess-Production-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096"}