using System;
using System.Threading.Tasks;
using FlexCharge.Payments.Entities;

namespace FlexCharge.Payments.Services.TransactionMonitoring.MerchantTransactions;

public interface ITransactionMonitoringService
{
    /// <summary>
    /// Start monitoring transaction for updates
    /// (for example, ACH transaction status can change over time and needs to be updated)
    /// </summary>
    /// <param name="dbContext"></param>
    /// <param name="transaction"></param>
    /// <returns></returns>
    Task StartMonitoringTransactionAsync(PostgreSQLDbContext dbContext, Transaction transaction, bool saveChanges);

    Task StopMonitoringTransactionAsync(PostgreSQLDbContext dbContext, Guid transactionId, bool saveChanges);
}