using System;
using System.Collections.Generic;
using FlexCharge.Common.SensitiveData.Obfuscation;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.SVBAchServices.Models;

public class TransfersInfoResponse
{
    /// <summary>
    /// Gets or Sets Items
    /// </summary>
    [JsonProperty("items")]
    public List<DomesticAch> Items { get; set; }

    /// <summary>
    /// Total number of items
    /// </summary>
    /// <value>Total number of items</value>
    [JsonProperty("total_items")]
    public int? TotalItems { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    /// <value>Total number of pages</value>
    [JsonProperty("total_pages")]
    public int? TotalPages { get; set; }

    /// <summary>
    /// Gets or Sets Links
    /// </summary>
    [JsonProperty("links")]
    public List<LinkDescription> Links { get; set; }
}

public class LinkDescription
{
    /// <summary>
    /// a URI template, as defined by RFC 6570, with the addition of the $, ( and ) characters for pre-processing
    /// </summary>
    /// <value>a URI template, as defined by RFC 6570, with the addition of the $, ( and ) characters for pre-processing</value>
    [JsonProperty("href")]
    public string Href { get; set; }

    /// <summary>
    /// relation to the target resource of the link
    /// </summary>
    /// <value>relation to the target resource of the link</value>
    [JsonProperty("rel")]
    public string Rel { get; set; }

    /// <summary>
    /// a title for the link
    /// </summary>
    /// <value>a title for the link</value>
    [JsonProperty("title")]
    public string Title { get; set; }

    /// <summary>
    /// media type (as defined by RFC 2046) describing the link target
    /// </summary>
    /// <value>media type (as defined by RFC 2046) describing the link target</value>
    [JsonProperty("media_type")]
    public string MediaType { get; set; }

    /// <summary>
    /// method for requesting the target of the link (e.g. for HTTP this might be \&quot;GET\&quot; or \&quot;DELETE\&quot;)
    /// </summary>
    /// <value>method for requesting the target of the link (e.g. for HTTP this might be \&quot;GET\&quot; or \&quot;DELETE\&quot;)</value>
    [JsonProperty("method")]
    public string Method { get; set; }

    /// <summary>
    /// The media type in which to submit data along with the request
    /// </summary>
    /// <value>The media type in which to submit data along with the request</value>
    [JsonProperty("enc_type")]
    public string EncType { get; set; }
}

public class DomesticAch
{
    /// <summary>
    /// Bank account number that is receiving the ACH transfer.
    /// </summary>
    /// <value>Bank account number that is receiving the ACH transfer.</value>
    [SensitiveData(ObfuscationType.HeadVisible, LeaveUnobfuscatedSymbols = 4)]
    [JsonProperty("receiver_account_number")]
    public string ReceiverAccountNumber { get; set; }


    /// <summary>
    /// Name of the receiver (company/individual).
    /// </summary>
    /// <value>Name of the receiver (company/individual).</value>
    [JsonProperty("receiver_name")]
    public string ReceiverName { get; set; }

    /// <summary>
    /// ABA routing number associated with the receiver_account_number.
    /// </summary>
    /// <value>ABA routing number associated with the receiver_account_number.</value>
    [SensitiveData(ObfuscationType.HeadVisible, LeaveUnobfuscatedSymbols = 4)]
    [JsonProperty("receiver_routing_number")]
    public string ReceiverRoutingNumber { get; set; }

    /// <summary>
    /// Optional additional information to associate with the ACH transfer
    /// </summary>
    /// <value>Optional additional information to associate with the ACH transfer</value>
    [JsonProperty("additional_information")]
    public Object AdditionalInformation { get; set; }

    /// <summary>
    /// Amount of money to transfer specified in positive integral cents. Setting amount equal to zero will cause this to be a prenotification transaction.
    /// </summary>
    /// <value>Amount of money to transfer specified in positive integral cents. Setting amount equal to zero will cause this to be a prenotification transaction.</value>
    [JsonProperty("amount")]
    public int? Amount { get; set; }

    /// <summary>
    /// Optional string that can be used to pass data to the recipient bank. Examples an invoice number or transaction reference number. This field is mandatory for WEB credits.
    /// </summary>
    /// <value>Optional string that can be used to pass data to the recipient bank. Examples an invoice number or transaction reference number. This field is mandatory for WEB credits.</value>
    [JsonProperty("identification_number")]
    public string IdentificationNumber { get; set; }

    /// <summary>
    /// Addenda entry specified by the originator. Becomes an addenda row in the NACHA file for ccd, ppd and web payment types.
    /// </summary>
    /// <value>Addenda entry specified by the originator. Becomes an addenda row in the NACHA file for ccd, ppd and web payment types.</value>
    [JsonProperty("addenda")]
    public string Addenda { get; set; }

    /// <summary>
    /// A unique ID indicating the batch this transfer was sent with. Can be used to reconcile with your SVB bank statements. Will be null until the transfer reaches processing status.
    /// </summary>
    /// <value>A unique ID indicating the batch this transfer was sent with. Can be used to reconcile with your SVB bank statements. Will be null until the transfer reaches processing status.</value>
    [JsonProperty("batch_id")]
    public string BatchId { get; private set; }

    /// <summary>
    /// Uniquely identifies each ACH transfer object.
    /// </summary>
    /// <value>Uniquely identifies each ACH transfer object.</value>
    [JsonProperty("id")]
    public Guid? Id { get; private set; }


    /// <summary>
    /// Fed Trace number will be generated once the ACH payment NACHA File is validated at Pep+.
    /// </summary>
    /// <value>Fed Trace number will be generated once the ACH payment NACHA File is validated at Pep+.</value>
    [JsonProperty("fed_trace_number")]
    public string FedTraceNumber { get; private set; }

    /// <summary>
    /// Uniquely identifies multiple ach payments created as a group in one request.
    /// </summary>
    /// <value>Uniquely identifies multiple ach payments created as a group in one request.</value>
    [JsonProperty("mass_delivery_id")]
    public Guid? MassDeliveryId { get; private set; }

    /// <summary>
    /// Corrected data in case of corrected payments.
    /// </summary>
    /// <value>Corrected data in case of corrected payments.</value>
    [JsonProperty("corrected_data")]
    public string CorrectedData { get; private set; }

    /// <summary>
    /// Return code to represent failed/corrected ACH transfer.
    /// </summary>
    /// <value>Return code to represent failed/corrected ACH transfer.</value>
    [JsonProperty("return_code")]
    public string ReturnCode { get; private set; }

    /// <summary>
    /// Return description to represent failed/corrected ACH transfer.
    /// </summary>
    /// <value>Return description to represent failed/corrected ACH transfer.</value>
    [JsonProperty("return_description")]
    public string ReturnDescription { get; private set; }

    /// <summary>
    /// Return the status of the transfer
    /// </summary>
    /// <value>Return the status of the transfer</value>
    [JsonProperty("status")]
    public string Status { get; private set; }

    /// <summary>
    /// created timestamp
    /// </summary>
    /// <value>created timestamp</value>
    [JsonProperty("created_at")]
    public DateTime? CreatedAt { get; private set; }

    /// <summary>
    /// updated timestamp
    /// </summary>
    /// <value>updated timestamp</value>
    [JsonProperty("updated_at")]
    public DateTime? UpdatedAt { get; private set; }

    /// <summary>
    /// Gets or Sets Links
    /// </summary>
    [JsonProperty("links")]
    public List<LinkDescription> Links { get; private set; }
}