namespace FlexCharge.Payments.Services.SVBAchServices.Models;

public static class DomesticAchExtensions
{
    public static bool IsAchTransactionInProcessOrSucceeded(this DomesticAch queryResult)
    {
        return queryResult?.Status?.ToLowerInvariant() is
            "pending" or "processing" or "processed" or "hold" or "succeeded" or "corrected";
    }


    public static bool IsFailedOrCanceled(this DomesticAch queryResult)
    {
        return queryResult?.Status?.ToLowerInvariant() is "failed" or "canceled";
    }

    public static bool IsCanceled(this DomesticAch queryResult)
    {
        return queryResult?.Status?.ToLowerInvariant() is "canceled";
    }
}