using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Response;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Exceptions;
using FlexCharge.Payments.Services.SpreedlyService.CreateMerchantProfileRequest;
using FlexCharge.Payments.Services.SpreedlyService.CreateSCAProviderRequest;
using Hangfire.Client;
using MassTransit;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;


namespace FlexCharge.Payments.Services.SpreedlyService;

public class SpreedlyService : ISpreedlyService
{
    private readonly HttpClient _httpClient;
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly IMapper _mapper;
    private readonly IOptions<SpreedlyOptions> _spreedlyOptions;
    private readonly IOptions<Spreedly3DSMerchantOptions> _spreedly3dsMerchantOptions;

    public SpreedlyService(HttpClient httpClient, IMapper mapper,
        IOptions<SpreedlyOptions> spreedlyOptions, IOptions<Spreedly3DSMerchantOptions> spreedly3dsMerchantOptions,
        PostgreSQLDbContext dbContext, IPublishEndpoint publishEndpoint
    )
    {
        _spreedlyOptions = spreedlyOptions;
        _spreedly3dsMerchantOptions = spreedly3dsMerchantOptions;
        _httpClient = httpClient;
        _mapper = mapper;
        _dbContext = dbContext;
        _publishEndpoint = publishEndpoint;
    }

    public async Task<PurchaseResponse> PurchaseAsync(string environment, string secret, string gatewayId,
        Guid merchantId, PurchaseRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId)
            .Tag(nameof(gatewayId), gatewayId);

        workspan.Log.Information(
            "Entered: SpreedlyService > PurchaseAsync > MerchantID: {MerchantId} Request: {Payload} ", merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<PurchaseResponse, PurchaseRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/gateways/{gatewayId.Trim()}/purchase.json", environment, secret,
            merchantId, payload, token);
    }

    public async Task<AuthorizeResponse> AuthorizeAsync(Guid merchantId, string environment, string secret,
        string gatewayId,
        AuthorizeRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId)
            .Tag(nameof(gatewayId), gatewayId);

        workspan.Log.Information(
            $"Entered: SpreedlyService > AuthorizeAsync > MerchantID: {merchantId} Request: {JsonConvert.SerializeObject(payload)} ");

        return await SpreedlyPost<AuthorizeResponse, AuthorizeRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/gateways/{gatewayId}/authorize.json", environment, secret,
            merchantId, payload, token);
    }

    public async Task<CaptureResponse> CaptureAsync(Guid merchantId, string environment, string secret,
        string transactionToken,
        CaptureRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > CaptureAsync > MerchantID: {MerchantId} Request: {Payload} ", merchantId,
            JsonConvert.SerializeObject(payload));


        return await SpreedlyPost<CaptureResponse, CaptureRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/transactions/{transactionToken}/capture.json", environment, secret,
            merchantId, payload, token);
    }

    /// <summary>
    /// Refund a transaction
    /// </summary>
    /// <param name="merchantId"></param>
    /// <param name="environment"></param>
    /// <param name="secret"></param>
    /// <param name="transactionToken"></param>
    /// <param name="payload"></param>
    /// <param name="token"></param>
    /// <returns></returns>
    public async Task<CreditResponse> CreditAsync(Guid merchantId, string environment, string secret,
        string transactionToken,
        CreditRequest payload,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > CreditAsync > MerchantID: {MerchantId} Request: {Payload} ", merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<CreditResponse, CreditRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/transactions/{transactionToken}/credit.json", environment, secret,
            merchantId, payload, token);
    }

    public async Task<General_CreditResponse> General_CreditAsync(Guid merchantId, string environment, string secret,
        string gatewayId,
        General_CreditRequest payload,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId)
            .Tag(nameof(gatewayId), gatewayId);

        workspan.Log.Information(
            "Entered: SpreedlyService > General_CreditAsync > MerchantID: {MerchantId} Request: {Payload} ",
            merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<General_CreditResponse, General_CreditRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/transactions/{gatewayId}/general_credit.json", environment, secret,
            merchantId, payload, token);
    }

    public async Task<VoidResponse> VoidAsync(Guid merchantId, string environment, string secret,
        VoidRequest payload,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > CreditAsync > MerchantID: {MerchantId} Request: {Payload} ", merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<VoidResponse, VoidRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/transactions/{payload.TransactionToken}/void.json", environment, secret,
            merchantId, null, token);
    }

    public async Task<VerifyResponse> VerifyAsync(string environment, string secret, Guid merchantId, string gatewayId,
        VerifyRequest payload,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > VerifyAsync > MerchantID: {MerchantId} Request: {Payload} ", merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<VerifyResponse, VerifyRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/gateways/{gatewayId}/verify.json", environment, secret,
            merchantId, payload, token);
    }

    #region Env API

    public async Task<EnvironmentResponse> AddEnvironmentAsync(
        NewEnvironmentRequest payload, Guid merchantId, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > AddEnvironmentAsync > MerchantID: {MerchantId} Request: {Payload} ",
            merchantId,
            JsonConvert.SerializeObject(payload));

        //TODO once http ready update endpoint to base url
        return await SpreedlyPost<EnvironmentResponse, NewEnvironmentRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/environments.json", _spreedlyOptions.Value.Environment,
            _spreedlyOptions.Value.Secret,
            merchantId, payload, token);
    }

    public async Task<EnvironmentSecretResponse> AddEnvironmentSecretAsync(string newEnvironment,
        EnvironmentSecretRequest payload, Guid merchantId, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > AddEnvironmentSecretAsync > MerchantID: {MerchantId} Request: {Payload} ",
            merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<EnvironmentSecretResponse, EnvironmentSecretRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/environments/{newEnvironment}/access_secrets.json", newEnvironment,
            _spreedlyOptions.Value.Secret,
            merchantId, payload, token);
    }

    #endregion

    public async Task<AddGatewayResponse> AddGatewayAsync(string environment, string secret,
        Guid merchantId, AddGatewayRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > AddGatewayAsync > MerchantID: {MerchantId} Request: {Payload} ", merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<AddGatewayResponse, AddGatewayRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/gateways.json", environment, secret,
            merchantId, payload, token);
    }


    #region Payment Instruments

    public async Task<PaymentInstrumentsDTO> GetPaymentInstrumentsAsync(string environment, string secret,
        Guid merchantId, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information("ENTERED: Spreedly service > GetPaymentInstrumentsAsync > {MerchantId}", merchantId);

        var response = new PaymentInstrumentsDTO();

        var authenticationString = $"{environment}:{secret}";
        var base64EncodedAuthenticationString =
            Convert.ToBase64String(Encoding.ASCII.GetBytes(authenticationString));

        _httpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Basic", base64EncodedAuthenticationString);

        var methods = await _httpClient.GetAsync($"{_spreedlyOptions.Value.BaseUrl}/payment_methods.json", token);

        var responseContent = await methods.Content.ReadAsStringAsync(token);

        workspan.Log.Information("GetPaymentInstrumentAsync: MerchantID: {Mid} Response: {Response}", merchantId,
            responseContent);

        if (!methods.IsSuccessStatusCode)
        {
            response.AddError(responseContent);
            return response;
        }

        response.PaymentInstruments = JsonConvert.DeserializeObject<IEnumerable<PaymentInstrumentDTO>>(responseContent);

        return response;
    }

    public async Task<QueryPaymentInstrumentDTO> GetPaymentInstrumentAsync(string environment, string secret,
        Guid merchantId, string paymentInstrumentToken, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "ENTERED: Spreedly service > GetPaymentInstrumentAsync > {MerchantId} Token: {PaymentInstrumentToken}",
            merchantId, paymentInstrumentToken);

        var response = new QueryPaymentInstrumentDTO();

        var authenticationString = $"{environment}:{secret}";
        var base64EncodedAuthenticationString =
            Convert.ToBase64String(Encoding.ASCII.GetBytes(authenticationString));

        _httpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Basic", base64EncodedAuthenticationString);

        var getResponse =
            await _httpClient.GetAsync(
                $"{_spreedlyOptions.Value.BaseUrl}/payment_methods/" + paymentInstrumentToken + ".json", token);

        var responseContent = await getResponse.Content.ReadAsStringAsync(token);

        workspan.Log.Information("GetPaymentInstrumentAsync: MerchantID: {Mid} Response: {Response}", merchantId,
            responseContent);

        if (getResponse.IsSuccessStatusCode)
        {
            var instrumentResponse = JsonConvert.DeserializeObject<QueryPaymentInstrumentDTO>(responseContent);
            workspan.Log.Information(
                "Deserializing payment instrument response {Response}",
                instrumentResponse);

            return instrumentResponse;
        }

        response.AddError(responseContent, getResponse.ReasonPhrase, getResponse.StatusCode.ToString());
        return response;
    }

    public async Task<CreatePaymentInstrumentResponse> CreatePaymentInstrumentAsync(string environment, string secret,
        Guid merchantId, CreatePaymentInstrumentRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > CreatePaymentInstrumentAsync > MerchantID: {MerchantId} Request: {Payload} ",
            merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<CreatePaymentInstrumentResponse, CreatePaymentInstrumentRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/payment_methods.json", environment, secret,
            merchantId, payload, token);
    }

    public async Task<RecachePaymentMethodResponse> ReCachePaymentInstrumentAsync(Guid merchantId, string environment,
        string paymentInstrumentToken,
        RecachePaymentMethodRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > RecachePaymentInstrumentAsync > MerchantID: {MerchantId} Request: {Payload} ",
            merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<RecachePaymentMethodResponse, RecachePaymentMethodRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/payment_methods/{paymentInstrumentToken}/recache.json?environment_key={environment}",
            null, null
            , merchantId, payload, token);
    }

    // public async Task<RedactPaymentMethodResponse> RedactPaymentInstrumentAsync(Guid merchantId, string environment, string paymentInstrumentToken,
    //     RedactPaymentMethodRequest payload, CancellationToken token)
    // {
    //     using var workspan = Workspan.Start<SpreedlyService>()
    //         .Request(payload)
    //         .Tag(nameof(merchantId), merchantId);
    //
    //     workspan.Log.Information(
    //         "Entered: SpreedlyService > RedactPaymentInstrumentAsync > MerchantID: {MerchantId} Request: {Payload} ",
    //         merchantId,
    //         JsonConvert.SerializeObject(payload));
    //     
    //     return await SpreedlyPost<RedactPaymentMethodResponse, RedactPaymentMethodRequest>(
    //         $"{_spreedlyOptions.Value.BaseUrl}/payment_methods/{paymentInstrumentToken}/redact.json", environment, null
    //         , merchantId, payload, token);
    // }

    /// <summary>
    /// Use this method to create a payment instrument without a secret token,
    /// this is used for the initial creation of a payment instrument
    /// it will not be retained for future use.
    /// </summary>
    /// <param name="environment">Environment key can be fount on the merchant portal (Required)</param>
    /// <param name="merchantId"></param>
    /// <param name="payload"></param>
    /// <param name="token">Cancellation token</param>
    /// <returns></returns>
    public async Task<CreatePaymentInstrumentResponse> CreatePaymentInstrumentAsync(string environment,
        Guid merchantId, CreatePaymentInstrumentRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > CreatePaymentInstrumentAnonymousAsync > MerchantID: {MerchantId} Request: {Payload} ",
            merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<CreatePaymentInstrumentResponse, CreatePaymentInstrumentRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/payment_methods.json?environment_key={environment}", null, null,
            merchantId, payload, token);
    }

    public async Task<RetainResponse> RetainPaymentInstrumentAsync(Guid merchantId, string environment, string secret,
        string paymentInstrumentToken,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Tag(nameof(merchantId), merchantId)
            .Tag(nameof(paymentInstrumentToken), paymentInstrumentToken);

        workspan.Log.Information(
            "Entered: SpreedlyService > RetainPaymentInstrumentAsync > MerchantID: {MerchantId} Instrument: {PaymentInstrumentToken} ",
            merchantId,
            paymentInstrumentToken);

        return await SpreedlyPut<RetainResponse, string>(
            $"{_spreedlyOptions.Value.BaseUrl}/payment_methods/{paymentInstrumentToken}/retain.json",
            environment, secret,
            merchantId, null, token);
    }

    #endregion

    #region 3DSecure

    public async Task<Create3DSMerchantProfileResponse> Create3dsMerchantProfileAsync(string environment, string secret,
        Guid merchantId, Create3DSMerchantProfileRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > Create3DSMerchantProfileAsync > MerchantID: {MerchantId} Request: {Payload} ",
            merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<Create3DSMerchantProfileResponse, Create3DSMerchantProfileRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/merchant_profiles.json", environment, secret,
            merchantId, payload, token);
    }

    public async Task<CreateSCAProviderRequest.CreateSCAProviderResponse> Create3dsSCAProviderAsync(
        string environment, string secret,
        Guid merchantId, CreateSCAProviderRequest.CreateSCAProviderRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > Create3dsSCAProviderAsync > MerchantID: {MerchantId} Request: {Payload} ",
            merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<CreateSCAProviderRequest.CreateSCAProviderResponse,
            CreateSCAProviderRequest.CreateSCAProviderRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/sca/providers.json", environment, secret,
            merchantId, payload, token);
    }

    public async Task<AuthenticateResponse> AuthenticateAsync(Guid merchantId, string environment, string secret,
        string scaProviderKey,
        AuthenticateRequest payload, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > AuthenticateAsync > MerchantID: {MerchantId} Request: {Payload}",
            merchantId,
            JsonConvert.SerializeObject(payload));

        return await SpreedlyPost<AuthenticateResponse, AuthenticateRequest>(
            $"{_spreedlyOptions.Value.BaseUrl}/sca/providers/{scaProviderKey}/authenticate.json", environment, secret,
            merchantId, payload, token);
    }

    public async Task<AuthenticateResponse> GetAuthenticateTransactionAsync(
        Guid merchantId, string environment, string secret,
        string transactionToken, CancellationToken token)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Tag(nameof(merchantId), merchantId)
            .Tag(nameof(transactionToken), transactionToken);

        workspan.Log.Information(
            "Entered: SpreedlyService > GetAuthenticateTransactionAsync > MerchantID: {MerchantId} Transaction Token: {TransactionToken}",
            merchantId,
            transactionToken);

        return await SpreedlyGet<AuthenticateResponse>(
            $"{_spreedlyOptions.Value.BaseUrl}/transactions/{transactionToken}.json", environment, secret,
            merchantId, token);
    }

    public async Task<bool> Add3DSSupportIfMissingAsync(Entities.Merchant merchant,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Tag("Mid", merchant.Mid);

        #region Adding 3DS Support

        if (string.IsNullOrWhiteSpace(merchant.Spreedly3dsMerchantProfileKey))
        {
            //TODO D: Make separate for Visa/Mastercard/Amex?
            string merchantProfileDescription = _spreedly3dsMerchantOptions.Value.MerchantProfileDescription;

            #region Creating Merchant Profile

            MerchantProfileRequest merchantProfileRequest = new()
            {
                Description = merchantProfileDescription,
                //Parameter for each supported Schema (card network)
                Visa = _spreedly3dsMerchantOptions.Value.CardSchemas.Visa != null
                    ? new()
                    {
                        AcquirerMerchantId = _spreedly3dsMerchantOptions.Value.CardSchemas.Visa.AcquirerMerchantId,
                        MerchantName = _spreedly3dsMerchantOptions.Value.CardSchemas.Visa.MerchantName,
                        CountryCode = _spreedly3dsMerchantOptions.Value.CardSchemas.Visa.MerchantCountry,
                        Mcc = _spreedly3dsMerchantOptions.Value.CardSchemas.Visa.MCC
                    }
                    : null,
                Mastercard = _spreedly3dsMerchantOptions.Value.CardSchemas.Mastercard != null
                    ? new()
                    {
                        AcquirerMerchantId =
                            _spreedly3dsMerchantOptions.Value.CardSchemas.Mastercard.AcquirerMerchantId,
                        MerchantName = _spreedly3dsMerchantOptions.Value.CardSchemas.Mastercard.MerchantName,
                        CountryCode = _spreedly3dsMerchantOptions.Value.CardSchemas.Mastercard.MerchantCountry,
                        Mcc = _spreedly3dsMerchantOptions.Value.CardSchemas.Mastercard.MCC
                    }
                    : null,
                Amex = _spreedly3dsMerchantOptions.Value.CardSchemas.Amex != null
                    ? new()
                    {
                        AcquirerMerchantId = _spreedly3dsMerchantOptions.Value.CardSchemas.Amex.AcquirerMerchantId,
                        MerchantName = _spreedly3dsMerchantOptions.Value.CardSchemas.Amex.MerchantName,
                        CountryCode = _spreedly3dsMerchantOptions.Value.CardSchemas.Amex.MerchantCountry,
                        Mcc = _spreedly3dsMerchantOptions.Value.CardSchemas.Amex.MCC
                    }
                    : null
            };

            workspan.Log.Information("Creating Spreedly 3DS Merchant Profile {MerchantProfileRequest}",
                merchantProfileRequest.ToString());


            var createMerchantProfileResponse = await Create3dsMerchantProfileAsync(
                merchant.SpreedlyEnvironmentKey, merchant.SpreedlySecretKey, merchant.Mid,
                new Create3DSMerchantProfileRequest
                {
                    #region Merchant Profile

                    MerchantProfile = merchantProfileRequest

                    #endregion
                }, cancellationToken);

            if (!createMerchantProfileResponse.Success)
            {
                workspan.Log.Information(
                    "IN: MerchantCreatedEventConsumer > Unable to create 3DS merchant profile > {CreateMerchantProfileResponse}",
                    JsonConvert.SerializeObject(createMerchantProfileResponse));
                await _publishEndpoint.Publish(new MerchantProfileCreationFailedEvent
                {
                    MerchantId = merchant.Mid
                }, cancellationToken);
                return false;
            }

            merchant.Spreedly3dsMerchantProfileKey = createMerchantProfileResponse.MerchantProfile.Token;

            await _publishEndpoint.Publish(new MerchantProfileCreatedEvent
            {
                MerchantId = merchant.Mid,
                MerchantProfileKey = merchant.Spreedly3dsMerchantProfileKey
            }, cancellationToken);

            #endregion
        }

        if (string.IsNullOrWhiteSpace(merchant.Spreedly3dsScaProviderKey))
        {
            #region Creating SCA Provider Profile

            CreateSCAProviderRequest.CreateSCAProviderRequest scaProviderRequest = new()
            {
                ScaProvider = new()
                {
                    MerchantProfileKey = merchant.Spreedly3dsMerchantProfileKey,
                    Type = _spreedly3dsMerchantOptions.Value.Type, //"spreedly" or "test" (case sensitive)
                    Sandbox = _spreedly3dsMerchantOptions.Value.Sandbox,
                    Visa = _spreedly3dsMerchantOptions.Value.CardSchemas.Visa != null
                        ? new()
                        {
                            AcquirerBin = _spreedly3dsMerchantOptions.Value.CardSchemas.Visa.AcquirerBin,
                            MerchantUrl = _spreedly3dsMerchantOptions.Value.CardSchemas.Visa.MerchantUrl,
                            MerchantPassword = _spreedly3dsMerchantOptions.Value.CardSchemas.Visa.MerchantPassword
                        }
                        : null,
                    Mastercard = _spreedly3dsMerchantOptions.Value.CardSchemas.Mastercard != null
                        ? new()
                        {
                            AcquirerBin = _spreedly3dsMerchantOptions.Value.CardSchemas.Mastercard.AcquirerBin,
                            MerchantUrl = _spreedly3dsMerchantOptions.Value.CardSchemas.Mastercard.MerchantUrl,
                            MerchantPassword = _spreedly3dsMerchantOptions.Value.CardSchemas.Mastercard.MerchantPassword
                        }
                        : null,
                    Amex = _spreedly3dsMerchantOptions.Value.CardSchemas.Amex != null
                        ? new()
                        {
                            AcquirerBin = _spreedly3dsMerchantOptions.Value.CardSchemas.Amex.AcquirerBin,
                            MerchantUrl = _spreedly3dsMerchantOptions.Value.CardSchemas.Amex.MerchantUrl,
                            MerchantPassword = _spreedly3dsMerchantOptions.Value.CardSchemas.Amex.MerchantPassword
                        }
                        : null
                }
            };

            workspan.Log.Information("Creating Spreedly 3DS SCA Provider {ScaProviderRequest}",
                scaProviderRequest.ToString());

            var create3dsSCAProviderResponse = await Create3dsSCAProviderAsync(
                merchant.SpreedlyEnvironmentKey, merchant.SpreedlySecretKey, merchant.Mid,
                scaProviderRequest, cancellationToken);

            if (!create3dsSCAProviderResponse.Success)
            {
                workspan.Log.Information(
                    "Unable to create 3DS SCA provider > {Create3dsSCAProviderResponse}",
                    JsonConvert.SerializeObject(create3dsSCAProviderResponse));
                await _publishEndpoint.Publish(new ScaProviderCreationFailedEvent
                {
                    MerchantId = merchant.Mid
                }, cancellationToken);
                return false;
            }

            merchant.Spreedly3dsScaProviderKey = create3dsSCAProviderResponse.ScaProvider.Token;
            await _dbContext.SaveChangesAsync(cancellationToken);

            await _publishEndpoint.Publish(new ScaProviderCreatedEvent
            {
                MerchantId = merchant.Mid,
                ScaProviderKey = merchant.Spreedly3dsScaProviderKey
            }, cancellationToken);

            #endregion
        }

        #endregion

        return true;
    }

    #endregion

    #region Base

    private async Task<TResponse> SpreedlyPost<TResponse, TRequest>(string endpoint, string environment, string secret,
        Guid merchantId, TRequest? payload, CancellationToken token) where TResponse : BaseResponse, new()
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            "Entered: SpreedlyService > SpreedlyPost > {RequestName} > MerchantID: {MerchantId} endpoint: {Endpoint} Request: {Payload} ",
            typeof(TRequest).FullName, merchantId, endpoint, JsonConvert.SerializeObject(payload));

        var response = new TResponse();
        try
        {
            if (!string.IsNullOrEmpty(secret))
                AddBasicAuth(environment, secret);

            var postResponse = await SendRequest<TResponse, TRequest>(endpoint, payload, token);

            var responseContent = await postResponse.Content.ReadAsStringAsync(token);
            workspan.Log.Information(
                "SpreedlyPost: SpreedlyService > SpreedlyPost > {Request} > MerchantID: {MerchantId} Response: {ResponseContent} ",
                typeof(TRequest).FullName, merchantId, responseContent);

            response = JsonConvert.DeserializeObject<TResponse>(responseContent);
            if (postResponse.IsSuccessStatusCode)
                return response;

            //In case 422 response we still have the response object with errors in it
            if (postResponse.StatusCode == HttpStatusCode.UnprocessableEntity)
                return response;

            response.AddError(responseContent, postResponse.ReasonPhrase, postResponse.StatusCode.ToString());
            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Information(e,
                $"EXCEPTION: SpreedlyService > {typeof(TRequest).FullName} > MerchantID: {merchantId} Request: {JsonConvert.SerializeObject(payload)} ");
            throw;
        }
    }

    private async Task<TResponse> SpreedlyPut<TResponse, TRequest>(string endpoint, string environment, string secret,
        Guid merchantId, TRequest payload, CancellationToken token) where TResponse : BaseResponse, new()
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Request(payload)
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            $"Entered: SpreedlyService > {typeof(TRequest).FullName} > MerchantID: {merchantId} Request: {JsonConvert.SerializeObject(payload)} ");

        var response = new TResponse();
        try
        {
            AddBasicAuth(environment, secret);

            var postResponse = await SendPutRequest<TResponse, TRequest>(endpoint, payload, token);

            var responseContent = await postResponse.Content.ReadAsStringAsync(token);
            workspan.Log.Information(
                "SpreedlyPost: SpreedlyService > {Request} > MerchantID: {MerchantId} Response: {ResponseContent} ",
                typeof(TRequest).FullName, merchantId, responseContent);

            response = JsonConvert.DeserializeObject<TResponse>(responseContent);
            if (postResponse.IsSuccessStatusCode) return response;

            response.AddError(responseContent, postResponse.ReasonPhrase, postResponse.StatusCode.ToString());
            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Information(e,
                $"EXCEPTION: SpreedlyService > {typeof(TRequest).FullName} > MerchantID: {merchantId} Request: {JsonConvert.SerializeObject(payload)} ");
            throw;
        }
    }

    private async Task<TResponse> SpreedlyGet<TResponse>(string endpoint, string environment, string secret,
        Guid merchantId, CancellationToken token) where TResponse : BaseResponse, new()
    {
        using var workspan = Workspan.Start<SpreedlyService>()
            .Tag(nameof(merchantId), merchantId);

        workspan.Log.Information(
            $"Entered: SpreedlyService > GET > {typeof(TResponse).FullName} > MerchantID: {merchantId} endpoint: {endpoint} ");

        var response = new TResponse();
        try
        {
            AddBasicAuth(environment, secret);

            var getResponse = await SendGetRequest<TResponse>(endpoint, token);

            var responseContent = await getResponse.Content.ReadAsStringAsync(token);
            workspan.Log.Information(
                "SpreedlyGet: SpreedlyService > GET > {TResponse} > MerchantID: {MerchantId} Response: {ResponseContent} ",
                typeof(TResponse).FullName, merchantId, responseContent);

            response = JsonConvert.DeserializeObject<TResponse>(responseContent);
            if (getResponse.IsSuccessStatusCode) return response;

            response.AddError(responseContent, getResponse.ReasonPhrase, getResponse.StatusCode.ToString());
            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Information(e,
                $"EXCEPTION: SpreedlyService >  GET > {typeof(TResponse).FullName} > MerchantID: {merchantId} Endpoint: {JsonConvert.SerializeObject(endpoint)} ");
            throw;
        }
    }

    private async Task<HttpResponseMessage> SendRequest<TResponse, TRequest>(string endpoint, TRequest? payload,
        CancellationToken token)
        where TResponse : BaseResponse, new()
    {
        var jsonContent = JsonConvert.SerializeObject(payload);
        var postResponse = await _httpClient.PostAsync(requestUri: endpoint,
            payload is null ? null : new StringContent(jsonContent, Encoding.UTF8, "application/json"),
            cancellationToken: token);
        return postResponse;
    }

    private async Task<HttpResponseMessage> SendPutRequest<TResponse, TRequest>(string endpoint, TRequest? payload,
        CancellationToken token)
        where TResponse : BaseResponse, new()
    {
        var jsonContent = JsonConvert.SerializeObject(payload);
        var putResponse = await _httpClient.PutAsync(requestUri: endpoint, payload is null ? null :
            payload == null ? null : new StringContent(jsonContent, Encoding.UTF8, "application/json"),
            cancellationToken: token);
        return putResponse;
    }

    private async Task<HttpResponseMessage> SendGetRequest<TResponse>(string endpoint,
        CancellationToken token)
        where TResponse : BaseResponse, new()
    {
        return await _httpClient.GetAsync(requestUri: endpoint,
            cancellationToken: token);
    }

    private void AddBasicAuth(string environment, string secret)
    {
        var authenticationString = $"{environment}:{secret}";
        var base64EncodedAuthenticationString =
            Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(authenticationString));

        _httpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Basic", base64EncodedAuthenticationString);
    }

    #endregion
}