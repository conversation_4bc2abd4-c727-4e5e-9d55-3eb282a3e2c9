namespace FlexCharge.Payments.Services.SDKs.TokenExService.Models.ThreeDSecure.Authentication;

public record AuthenticationResponse(
    string token,
    ThreeDSecureResponse threeDSecureResponse,
    string referenceNumber,
    bool success,
    string error,
    string message,
    string thirdPartyStatusCode
);

public record ThreeDSecureResponse(
    // see: https://docs.3dsecure.io/3dsv2/specification_220.html#attr-ARes-transStatus
    /*
        Y 	Authentication/ Account Verification Successful
        N 	Not Authenticated /Account Not Verified; Transaction denied
        U 	Authentication/ Account Verification Could Not Be Performed; Technical or other problem, as indicated in ARes or RReq
        A 	Attempts Processing Performed; Not Authenticated/Verified , but a proof of attempted authentication/verification is provided
        C 	Challenge Required; Additional authentication is required using the CReq/CRes
        D 	Challenge Required; Decoupled Authentication confirmed.
        R 	Authentication/ Account Verification Rejected; Issuer is rejecting authentication/verification and request that authorisation not be attempted.
        I 	Informational Only; 3DS Requestor challenge preference acknowledged.
     */
    string transStatus,
    string authenticationValue, // For Y or U (terminal statuses)
    string eci,
    string dsTransID,
    string acsTransID,
    string messageVersion,
    string acsReferenceNumber,
    string acsOperatorID,
    string threeDSServerTransID,
    
    // For Not Authenticated response only (N or U)
    string transStatusReason,
    
    // For Challenge response only
    string acsChallengeMandated,
    string acsURL,
    string authenticationType,
    string encodedCReq,
    
    MessageExtension[] messageExtension
);



public record MessageExtension(
    Data data,
    string name,
    bool criticalityIndicator,
    string id,
    string version
);

public record Data(
    AddData addData
);

public record AddData(
    string[] authenticationMethod,
    string threeDSRequestorAppURLInd
);

