using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FlexCharge.Payments.Services.DisputeServices.Models;

public class DisputeBase
{
    // [Required]
    // public Guid Mid { get; set; }

    [Required] public Guid OrderId { get; set; }
    public string? ExternalOrderId { get; set; }

    [Required] public Guid TransactionId { get; set; }

    [Required] public string Descriptor { get; set; }

    public string CaseNumber { get; set; }
    public string ReferenceNumber { get; set; }
    public string Stage { get; set; }
    public string Disposition { get; set; }
    public int Amount { get; set; }
    public int DisputeAmount { get; set; }
    public string TransactionMethod { get; set; }
    public string AuthorizationId { get; set; }
    public string Currency { get; set; }
    public DateTime RequestDate { get; set; }
    public DateTime? DueDate { get; set; }
    public DateTime ExpirationDate { get; set; }
    public DateTime? CreateDateTime { get; set; }
    public string Mcc { get; set; }
    public string Arn { get; set; }
    public string CardAcceptorId { get; set; }

    [Required] public string DisputeType { get; set; }

    [Required] public string DisputeManagementSystem { get; set; }

    [Required] public string CardBrand { get; set; }
    public string? CardType { get; set; }
    public string? Bin { get; set; }
    public string? Last4 { get; set; }
    public string ProviderName { get; set; }
    public bool IsWebhook { get; set; } = false;
    public string? Meta { get; set; }
    public string Note { get; set; }
    public bool? HasMatch { get; set; }
    public bool? IsFileImported { get; set; } = false;
    public bool? IsManualInserted { get; set; } = false;
    public string? Issuer { get; set; }
    public bool IsSftp { get; set; } = false;
    public string? ChargebackWinLoss { get; set; }
}