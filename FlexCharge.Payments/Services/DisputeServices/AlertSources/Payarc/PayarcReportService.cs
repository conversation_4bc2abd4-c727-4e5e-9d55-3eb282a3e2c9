using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.DisputeServices;

public class PayarcReportService : CsvReportServiceBase<PayarcReportItemDTO>, IDisputeReportService
{
    public override string ProviderName => AlertProviders.Payarc;

    protected override string RequestDateFormat => "yyyy-MM-dd";
    protected override string AuthorizationDateFormat => "M/d/yyyy";
    protected override string DueDateFormat => "M/d/yyyy";

    public PayarcReportService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    protected override async Task<DisputeQueueItemDTO> CreateDisputeQueueItemDto(PayarcReportItemDTO record,
        int? parsedAmount, int? parsedDisputeAmount)
    {
        var disputeQueueItem = new DisputeQueueItemDTO
        {
            IsWebhook = false,
            IsManualInserted = false,
            IsFileImported = true,
            IsSftp = false,

            Currency = "USD",
            Bin = record?.CardNumber.GetFirst(6),
            Last4 = record?.CardNumber?.GetLast(4),
            CardBrand = (bool) record?.CardNumber.StartsWith("4")
                ? DisputeCardBrands.VISA
                : DisputeCardBrands.MASTERCARD,
            CaseNumber = record?.CaseNumber,

            DisputeManagementSystem = DisputeManagementSystem.Payarc,
            ProviderName = AlertProviders.Payarc,

            Stage = GetPayarcStage(record?.Status),
            EarlyFraudWarning = false,
            HasMatch = false,
            Reason = record?.Reason,
            DisputeType = record?.Reason,

            Meta = JsonConvert.SerializeObject(record),
        };

        if (parsedAmount.HasValue)
        {
            disputeQueueItem.Amount = parsedAmount.Value;
            disputeQueueItem.DisputeAmount = parsedAmount.Value;
        }

        if (DateTime.TryParse(record?.ChargebackDate, out var parsedChargebackDate))
        {
            disputeQueueItem.RequestDate = parsedChargebackDate.ToUtcDate();
        }

        if (DateTime.TryParse(record?.TransactionDate, out var parsedTransactionDate))
        {
            disputeQueueItem.AuthorizationDate = parsedTransactionDate.ToUtcDate();
            disputeQueueItem.TransactionDate = parsedTransactionDate.ToUtcDate();
        }

        if (DateTime.TryParse(record?.RespondedBy, out var parsedRespondedBy))
        {
            disputeQueueItem.DueDate = parsedRespondedBy.ToUtcDate();
        }

        return disputeQueueItem;
    }

    protected override int GetAmount(string amount)
    {
        if (string.IsNullOrEmpty(amount))
        {
            throw new FlexChargeException("Amount is null or empty");
        }

        string cleanedAmount = amount.TrimStart('-', '$');
        if (decimal.TryParse(cleanedAmount, out decimal parsedAmount))
        {
            return Formatters.DecimalToInt(parsedAmount);
        }
        else
        {
            throw new FlexChargeException("Amount is valid");
        }
    }

    protected override int? GetDisputeAmount(string disputeAmount)
    {
        if (string.IsNullOrEmpty(disputeAmount))
        {
            return null;
        }

        string cleanedAmount = disputeAmount.TrimStart('-', '$');
        if (decimal.TryParse(cleanedAmount, out decimal parsedAmount))
        {
            return Formatters.DecimalToInt(parsedAmount);
        }

        return null;
    }

    protected override bool CheckIfCurrencyIsValid(string currency)
    {
        return currency.Length > 2 && (currency.StartsWith("-$") || currency.StartsWith("$"));
    }

    protected override bool IsQueueItemExist(DisputeQueueItemDTO item)
    {
        var result = ExistingItems?.Where(x =>
            x.CaseNumber == item.CaseNumber && x.Amount == item.DisputeAmount &&
            x.RequestDate == item.RequestDate && x.Stage == item.Stage &&
            x.Bin == item.Bin && x.Last4 == item.Last4).ToList();

        return result?.Count > 0;
    }


    protected string GetPayarcStage(string status)
    {
        switch (status?.ToUpper())
        {
            case "RDR":
                return DisputeStage.RDR;
            case "PENDING":
            case "CHARGEBACK":
            default:
                return DisputeStage.CHARGEBACK;
        }
    }
}