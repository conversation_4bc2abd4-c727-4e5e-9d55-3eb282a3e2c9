using System;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Models;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.DisputeServices;

public class ChargeblastService : IDisputeAlertsService
{
    // private readonly IDisputeAlertsService _disputeAlertsService;
    public ChargeblastSDK _chargeblastSdk { get; set; }
    private readonly DisputeQueueService _disputeQueueService;
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IActivityService _activityService;

    public ChargeblastService(ChargeblastSDK chargeblastSdk, PostgreSQLDbContext dbContext, 
        IActivityService activityService, DisputeQueueService disputeQueueService)
    {
        // _disputeAlertsService = disputeAlertsService;
        _chargeblastSdk = chargeblastSdk;
        _dbContext = dbContext;
        _activityService = activityService;
        _disputeQueueService = disputeQueueService;
    }


    ///     - AlertSDK (Notify/get) (API)
    ///         - AlertService => Chargeblast/Kount/payshield)
    ///              - AlertsOrchestrator => AlertProviderResolver (handle())  + DBContext
    ///         
    
    
    public async Task ProcessAlertAsync(AlertDTO payload)
    {
        using var workspan = Workspan.Start<ChargeblastService>()
            .Baggage("AlertId", payload.DisputeQueueItemId)
            .Baggage("TransactionId", payload.TransactionId)
            .LogEnterAndExit();
        
        try
        {
            var transaction = await _dbContext.Transactions
                .FirstOrDefaultAsync(x => x.Id == payload.TransactionId);
            
            if (transaction == null)
            {
                workspan
                    .Baggage("TransactionId", payload.TransactionId)
                    .Log.Fatal("ChargeblastService > Transaction not found");
                throw new Exception("Transaction not found.");
            }
            
            var disputeQueueItem = await _dbContext.DisputeQueueItems
                .FirstOrDefaultAsync(x => x.Id == payload.DisputeQueueItemId);
            
            if (disputeQueueItem == null)
            {
                workspan
                    .Baggage("DisputeQueueItemId", payload.DisputeQueueItemId)
                    .Log.Fatal("ChargeblastService > DisputeQueueItem not found");
                throw new Exception("DisputeQueueItem not found.");
            }
            
            var alertProvider = await _dbContext.AlertProviders
                .FirstOrDefaultAsync(x => x.Id == disputeQueueItem.AlertProviderId);

            if (alertProvider == null)
            {
                workspan.Log.Fatal("ChargeblastService > AlertProvider not found: transactionId: {transactionId}",
                    transaction.Id);
                throw new Exception("AlertProvider not found.");
            }
            
            _chargeblastSdk.SetApiKey(alertProvider.PrivateKey);
            
            await _chargeblastSdk.UpdateAlert(disputeQueueItem.RequestId, new AlertUpdateRequest()
            {
                Result = payload.StatusCode
            });
            
            await _disputeQueueService.UpdateDisputeQueueItemAsync(disputeQueueItem, new DisputeQueueItemUpdateDTO
            {
                RepliedToEthoca = DateTime.UtcNow,
            }, default);
            
            await _disputeQueueService.RemoveDisputeQueueItemAsync(disputeQueueItem, default);
            
            workspan.Log.Information("Alert Chargeblast success: request: {request}",
                JsonConvert.SerializeObject(payload));

            await _activityService.CreateActivityAsync(
                PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_AlertProvider_Succeeded,
                set => set
                    .CorrelationId(transaction?.OrderId)
                    .TenantId(transaction?.Merchant?.Mid)
                    .Meta(meta => meta
                        .ServiceProvider(AlertProviders.Chargeblast)
                        .TransactionReference(transaction?.Id)
                        .SetValue("StatusCode", payload.StatusCode)
                        .SetValue("DisputeQueueItemId", disputeQueueItem.Id)));
        }
        catch (Exception e)
        {
            workspan.Log.Fatal("Error processing alert with ID: {AlertId} error {Error} ", payload.DisputeQueueItemId, e.Message);
            
            await _activityService.CreateActivityAsync(
                DisputeReportsErrorsActivities.DisputeProcessing_AlertProvider_Error,
                set => set
                    .Data(e)
                    .Meta(meta => meta
                        .ServiceProvider(AlertProviders.Chargeblast)
                        .TransactionReference(payload.TransactionId)
                        .SetValue("StatusCode", payload.StatusCode)
                        .SetValue("DisputeQueueItemId", payload.DisputeQueueItemId)));
            throw;
        }
    }
}