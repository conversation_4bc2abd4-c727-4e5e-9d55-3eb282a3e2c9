using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.CheckoutDisputeService;

public class CheckoutReportService : CsvReportServiceBase<CheckoutReportItemDTO>, IDisputeReportService
{
    public override string ProviderName => AlertProviders.Checkout;

    protected override string RequestDateFormat => "yyyy-MM-dd HH:mm:ss";
    protected override string AuthorizationDateFormat => "yyyy-MM-dd HH:mm:ss";
    protected override string DueDateFormat => "yyyy-MM-dd HH:mm:ss";

    private readonly PostgreSQLDbContext _dbContext;
    private IDisputeServices _disputeServices;

    public CheckoutReportService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
        _dbContext = serviceProvider.GetRequiredService<PostgreSQLDbContext>();
        _disputeServices = serviceProvider.GetRequiredService<IDisputeServices>();
    }


    protected override async Task<DisputeQueueItemDTO> CreateDisputeQueueItemDto(CheckoutReportItemDTO record,
        int? parsedAmount, int? parsedDisputeAmount)
    {
        var isOrderIdValid = Guid.TryParse(record?.TrackId, out Guid orderId);

        var disputeQueueItem = new DisputeQueueItemDTO
        {
            IsWebhook = false,
            IsManualInserted = false,
            IsFileImported = true,
            IsSftp = false,

            RequestDate = DateTime.Parse(record?.FraudIssueDate).ToUtcDate(),
            CreateDateTime = DateTime.Parse(record?.FraudIssueDate).ToUtcDate(),
            AuthorizationDate = DateTime.Parse(record?.TransactionDateTimestamp).ToUtcDate(),
            TransactionDate = DateTime.Parse(record?.TransactionDateTimestamp).ToUtcDate(),

            Currency = record?.CurrencySymbol,
            Bin = record?.BIN,
            CardBrand = record?.PaymentMethodName?.ToUpper(),
            Last4 = record?.CardNumber?.GetLast(4),
            Arn = record?.Arn,
            ExternalReferenceID = record?.PaymentId,
            OrderId = isOrderIdValid ? orderId : Guid.Empty,
            CardType = record?.CardSchemeType?.ToUpper(),

            DisputeManagementSystem = DisputeManagementSystem.Checkout,
            ProviderName = AlertProviders.Checkout,

            Stage = DisputeStage.EARLY_WARNING,
            EarlyFraudWarning = true,
            DisputeType = record?.FraudReason,
            Note = "Chargeback fraud report",
            Issuer = record?.IssuingBank,
            Reason = record?.FraudReason,
            DisputeReasonCode = record?.FraudReason,

            Meta = JsonConvert.SerializeObject(record),
        };

        if (parsedAmount.HasValue)
        {
            disputeQueueItem.Amount = parsedAmount.Value;
            // TODO: DTO has a few types of amount fields, need to check if we can use some of it as DisputeAmount
            disputeQueueItem.DisputeAmount = parsedAmount.Value;
        }

        return disputeQueueItem;
    }

    protected override async Task<PreDisputeRequest> MatchDispute(CheckoutReportItemDTO record, int? parsedAmount,
        int? parsedDisputeAmount)
    {
        var isOrderIdValid = Guid.TryParse(record?.TrackId, out Guid orderId);

        var trx = await _dbContext.Transactions
            .FirstOrDefaultAsync(
                x => x.ProviderTransactionToken == record.PaymentId &&
                     (!isOrderIdValid || isOrderIdValid && x.OrderId == orderId));

        if (trx == null)
        {
            // Activities in CsvReportService
            return null;
        }

        var preDisputeRequestPayload = new PreDisputeRequest
        {
            IsWebhook = false,
            IsManualInserted = false,
            RequestDate = DateTime.Parse(record?.FraudIssueDate).ToUtcDate(),
            CreateDateTime = DateTime.Parse(record?.FraudIssueDate).ToUtcDate(),
            Bin = record?.BIN,
            CardBrand = record?.PaymentMethodName?.ToUpper(),
            DisputeType = record?.FraudReason,
            DisputeManagementSystem = DisputeManagementSystem.Checkout,
            ProviderName = AlertProviders.Checkout,
            Stage = DisputeStage.EARLY_WARNING,
            EarlyFraudWarning = true,
            Meta = JsonConvert.SerializeObject(record),
            Currency = record?.CurrencySymbol,
            Note = "Chargeback fraud report",
            Issuer = record?.IssuingBank,
            IsFileImported = true,
            Last4 = record?.CardNumber?.GetLast(4),
            Arn = record?.Arn,
            OrderId = isOrderIdValid ? orderId : Guid.Empty,
            CardType = record?.CardSchemeType?.ToUpper(),
            TransactionId = trx.Id,
            Descriptor = trx.DynamicDescriptor,
            AuthorizationId = trx.AuthorizationId,
        };

        if (parsedAmount.HasValue)
        {
            preDisputeRequestPayload.Amount = parsedAmount.Value;
        }

        if (parsedDisputeAmount.HasValue)
        {
            preDisputeRequestPayload.DisputeAmount = parsedDisputeAmount.Value;
        }

        await _disputeServices.AddPreDisputeAsync(preDisputeRequestPayload);
        return preDisputeRequestPayload;
    }

    protected override int GetAmount(string amount)
    {
        if (string.IsNullOrEmpty(amount))
        {
            throw new FlexChargeException("Amount is null or empty");
        }

        if (decimal.TryParse(amount, out decimal parsedAmount))
        {
            return Formatters.DecimalToInt(parsedAmount);
        }
        else
        {
            throw new FlexChargeException("Amount is not in the correct format");
        }
    }

    protected override int? GetDisputeAmount(string disputeAmount)
    {
        if (string.IsNullOrEmpty(disputeAmount))
        {
            return null;
        }

        if (decimal.TryParse(disputeAmount, out decimal parsedDisputeAmount))
        {
            return Formatters.DecimalToInt(parsedDisputeAmount);
        }

        return null;
    }

    protected override bool CheckIfCurrencyIsValid(string currency)
    {
        // Checkout report amount doesn't have currency symbol
        return true;
    }

    protected override bool IsQueueItemExist(DisputeQueueItemDTO item)
    {
        var result = ExistingItems?.Where(x =>
            item?.ExternalReferenceID != null && x.ExternalReferenceID == item?.ExternalReferenceID).ToList();

        return result?.Count > 0;
    }
}