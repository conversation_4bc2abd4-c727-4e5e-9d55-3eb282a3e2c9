using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.DisputeServices.Fiserv;

public class FiservReportService : CsvReportServiceBase<FservReportItemDTO>, IDisputeReportService
{
    public override string ProviderName => AlertProviders.Fiserv;

    protected override string RequestDateFormat => "MM/dd/yyyy";
    protected override string AuthorizationDateFormat => "MM/dd/yyyy";
    protected override string DueDateFormat => "MM/dd/yyyy";

    public FiservReportService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    protected override async Task<DisputeQueueItemDTO> CreateDisputeQueueItemDto(FservReportItemDTO record,
        int? parsedAmount, int? parsedDisputeAmount)
    {
        var disputeQueueItem = new DisputeQueueItemDTO
        {
            IsWebhook = false,
            IsManualInserted = false,
            IsFileImported = true,
            IsSftp = false,

            RequestDate = DateTime.Parse(record?.ReceivedDate).ToUtcDate(),
            AuthorizationDate = DateTime.Parse(record?.AuthorizationDate).ToUtcDate(),
            TransactionDate = DateTime.Parse(record?.AuthorizationDate).ToUtcDate(),

            CaseNumber = record?.CaseNumber,
            Bin = record?.Bin,
            Last4 = record?.Last4,
            CardBrand = record?.AuthNetwork.ToUpper(),
            Arn = record?.Arn,
            Currency = record?.Currency,
            AuthorizationId = record?.AuthorizationCode,
            SiteId = record?.SiteId,
            InvoiceNumber = record?.InvoiceNumber,
            ChargebackWinLoss = record?.ChargebackWinLoss,
            ExternalReferenceID = record?.ExternalReferenceID,
            DisputeReasonCode = record?.DisputeReasonCode,

            DisputeType = record?.DisputeReasonCode,
            DisputeManagementSystem = DisputeManagementSystem.Fiserv,
            ProviderName = AlertProviders.Fiserv,

            Stage = GetFiservDisputeStage(record?.AuthNetwork, record?.PreDisputeIndicator,
                record?.ChargebackStatus),
            EarlyFraudWarning = false,

            HasMatch = false,
            Reason = record?.DisputeReasonCode,

            Meta = JsonConvert.SerializeObject(record),
        };

        if (parsedAmount.HasValue)
        {
            disputeQueueItem.Amount = parsedAmount.Value;
        }

        if (parsedDisputeAmount.HasValue)
        {
            disputeQueueItem.DisputeAmount = parsedDisputeAmount.Value;
        }

        return disputeQueueItem;
    }

    protected override bool CheckIfCurrencyIsValid(string currency)
    {
        throw new NotImplementedException();
    }

    protected override bool IsQueueItemExist(DisputeQueueItemDTO item)
    {
        var result = ExistingItems?.Where(x =>
            x.Arn == item.Arn && x.CaseNumber == item.CaseNumber && x.Amount == item.DisputeAmount &&
            x.DisputeType == item.DisputeType && x.RequestDate == item.RequestDate && x.Stage == item.Stage &&
            x.AuthorizationId == x.AuthorizationId && x.Bin == item.Bin && x.Last4 == item.Last4).ToList();

        return result?.Count > 0;
    }

    protected override int GetAmount(string amount)
    {
        if (string.IsNullOrEmpty(amount))
        {
            throw new FlexChargeException("Amount is null or empty");
        }

        if (decimal.TryParse(amount, out decimal parsedAmount))
        {
            return Formatters.DecimalToInt(parsedAmount);
        }
        else
        {
            throw new FlexChargeException("Amount is not in the correct format");
        }
    }

    protected override int? GetDisputeAmount(string disputeAmount)
    {
        if (string.IsNullOrEmpty(disputeAmount))
        {
            return null;
        }

        if (decimal.TryParse(disputeAmount, out decimal parsedDisputeAmount))
        {
            return Formatters.DecimalToInt(parsedDisputeAmount);
        }

        return null;
    }

    protected string GetFiservDisputeStage(string card, string preDisputeIndicator, string chargebackStatus)
    {
        if (chargebackStatus?.ToLower() == "reversed") return DisputeStage.REVERSED;

        if (card.ToLower() == "mastercard") return DisputeStage.CHARGEBACK;

        switch (preDisputeIndicator)
        {
            case "No": return DisputeStage.CHARGEBACK;
            case "Yes": return DisputeStage.RDR;
            default: return DisputeStage.UNMAPPED;
        }
    }
}