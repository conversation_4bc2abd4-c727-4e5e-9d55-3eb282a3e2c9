using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Payments.DTO.reports;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.DisputeServices
{
    public class EmsReportService : CsvReportServiceBase<EmsReportItemDTO>, IDisputeReportService
    {
        public override string ProviderName => AlertProviders.EMS;
        protected override string AuthorizationDateFormat => "M/dd/yyyy";
        protected override string RequestDateFormat => "M/dd/yyyy";
        protected override string DueDateFormat => "M/dd/yyyy";

        public EmsReportService(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        protected override async Task<DisputeQueueItemDTO> CreateDisputeQueueItemDto(EmsReportItemDTO record,
            int? parsedAmount, int? parsedDisputeAmount)
        {
            var disputeQueueItem = new DisputeQueueItemDTO
            {
                IsWebhook = false,
                IsManualInserted = false,
                IsFileImported = true,
                IsSftp = false,

                CardBrand = record?.CardNumber?.StartsWith("4") == true
                    ? DisputeCardBrands.VISA
                    : DisputeCardBrands.MASTERCARD,
                Bin = record?.CardNumber?.GetFirst(6),
                Last4 = record?.CardNumber?.GetLast(4),
                Arn = record?.ReferenceNumber,
                AuthorizationId = record?.AuthNumber,

                DisputeManagementSystem = DisputeManagementSystem.EMS,
                ProviderName = AlertProviders.EMS,

                Stage = record?.Represent?.ToUpper() == "RDR" ? DisputeStage.RDR : DisputeStage.CHARGEBACK,
                EarlyFraudWarning = false,
                HasMatch = false,
                Reason = record?.Reason,
                DisputeType = record?.Reason,

                Meta = JsonConvert.SerializeObject(record),
            };

            if (parsedAmount.HasValue)
            {
                disputeQueueItem.Amount = parsedAmount.Value;
            }

            if (parsedDisputeAmount.HasValue)
            {
                disputeQueueItem.DisputeAmount = parsedDisputeAmount.Value;
            }

            if (TryParseAndValidateRequestDate(record?.Date, out var parsedDate))
            {
                disputeQueueItem.RequestDate = parsedDate.ToUtcDate();
            }
            else
            {
                throw new FlexChargeException("Date is not in the correct format or out of valid range");
            }

            if (TryParseAndValidateAuthorizationDate(record?.TransDate, out var parsedTransactionDate))
            {
                disputeQueueItem.AuthorizationDate = parsedTransactionDate.ToUtcDate();
                disputeQueueItem.TransactionDate = parsedTransactionDate.ToUtcDate();
            }
            else
            {
                throw new FlexChargeException("Transaction Date is not in the correct format or out of valid range");
            }

            if (CheckIfCurrencyIsValid(record?.Amount))
            {
                disputeQueueItem.Currency = "USD";
            }

            return disputeQueueItem;
        }

        protected override bool IsQueueItemExist(DisputeQueueItemDTO item)
        {
            var result = ExistingItems?.Where(x =>
                x.Arn == item.Arn && x.Amount == item.DisputeAmount &&
                x.RequestDate == item.RequestDate && x.Stage == item.Stage).ToList();

            return result?.Count > 0;
        }

        protected override int GetAmount(string amount)
        {
            if (string.IsNullOrEmpty(amount))
            {
                throw new FlexChargeException("Amount is null or empty");
            }

            string cleanedAmount = amount.Trim('(', ')', '-', '$');
            if (decimal.TryParse(cleanedAmount, out decimal parsedAmount))
            {
                return Formatters.DecimalToInt(parsedAmount);
            }
            else
            {
                throw new FlexChargeException("Amount is not in the correct format");
            }
        }

        protected override bool CheckIfCurrencyIsValid(string currency)
        {
            return currency.Length > 2 && (currency.StartsWith("($") || currency.StartsWith("$"));
        }

        protected override int? GetDisputeAmount(string amount)
        {
            if (string.IsNullOrEmpty(amount))
            {
                return null;
            }

            string cleanedAmount = amount.Trim('(', ')', '-', '$');
            if (decimal.TryParse(cleanedAmount, out decimal parsedAmount))
            {
                return Formatters.DecimalToInt(parsedAmount);
            }

            return null;
        }
    }
}