using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Payments.DTO.reports;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.DisputeServices
{
    public class QuantumReportService : CsvReportServiceBase<QuantumReportItemDTO>, IDisputeReportService
    {
        public override string ProviderName => AlertProviders.Quantum;

        protected override string RequestDateFormat => "MM/dd/yyyy";
        protected override string AuthorizationDateFormat => "MM/dd/yyyy";
        protected override string DueDateFormat => "MM/dd/yyyy";

        public QuantumReportService(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        protected override async Task<DisputeQueueItemDTO> CreateDisputeQueueItemDto(QuantumReportItemDTO record,
            int? parsedAmount, int? parsedDisputeAmount)
        {
            var disputeQueueItem = new DisputeQueueItemDTO
            {
                IsWebhook = false,
                IsManualInserted = false,
                IsFileImported = true,
                IsSftp = false,

                CardBrand = record?.CardType?.ToUpper(),

                DisputeManagementSystem = DisputeManagementSystem.Quantum,
                ProviderName = AlertProviders.Quantum,

                // Stage = record?.CardType?.ToUpper() == "VISA" ? "RDR" : "CHARGEBACK",
                Stage = DisputeStage.CHARGEBACK,
                EarlyFraudWarning = false,
                HasMatch = false,
                Reason = record?.ReasonDesc,
                DisputeType = record?.ReasonDesc,
                CaseNumber = record?.CaseNumber,
                MerchantNumber = record?.MID,

                Meta = JsonConvert.SerializeObject(record),
            };

            if (parsedAmount.HasValue)
            {
                disputeQueueItem.Amount = parsedAmount.Value;
                disputeQueueItem.DisputeAmount = parsedAmount.Value;
            }

            if (TryParseAndValidateRequestDate(record?.DateResolved, out var parsedDate))
            {
                disputeQueueItem.RequestDate = parsedDate.ToUtcDate();
            }
            else
            {
                throw new FlexChargeException("Date is not in the correct format or out of valid range");
            }

            if (TryParseAndValidateAuthorizationDate(record?.DateTransaction, out var parsedTransactionDate))
            {
                disputeQueueItem.AuthorizationDate = parsedTransactionDate.ToUtcDate();
                disputeQueueItem.TransactionDate = parsedTransactionDate.ToUtcDate();
            }
            else
            {
                throw new FlexChargeException("Transaction Date is not in the correct format or out of valid range");
            }

            if (CheckIfCurrencyIsValid(record?.Amount))
            {
                disputeQueueItem.Currency = "USD";
            }

            return disputeQueueItem;
        }

        protected override int GetAmount(string amount)
        {
            if (string.IsNullOrEmpty(amount))
            {
                throw new FlexChargeException("Amount is null or empty");
            }

            string cleanedAmount = amount.TrimStart('-', '$');
            if (decimal.TryParse(cleanedAmount, out decimal parsedAmount))
            {
                return (int) (parsedAmount * 100);
            }
            else
            {
                throw new FlexChargeException("Amount is not in the correct format");
            }
        }

        protected override int? GetDisputeAmount(string disputeAmount)
        {
            if (string.IsNullOrEmpty(disputeAmount))
            {
                return null;
            }

            string cleanedAmount = disputeAmount?.TrimStart('-', '$');
            if (decimal.TryParse(cleanedAmount, out decimal parsedAmount))
            {
                return (int) (parsedAmount * 100);
            }

            return null;
        }

        protected override bool CheckIfCurrencyIsValid(string currency)
        {
            return currency.Length > 2 && (currency.StartsWith("-$") || currency.StartsWith("$"));
        }

        protected override bool IsQueueItemExist(DisputeQueueItemDTO item)
        {
            var result = ExistingItems?.Where(x =>
                x.CaseNumber == item.CaseNumber && x.Amount == item.DisputeAmount &&
                x.RequestDate == item.RequestDate && x.Stage == item.Stage).ToList();

            return result?.Count > 0;
        }
    }
}