namespace FlexCharge.Payments.Services.DisputeServices;

public static class MyRcvrRefundedState
{
    public const string Refunded = "refunded";
    public const string NotRefunded = "not refunded";
    public const string NotSettled = "not settled";
}

public static class MyRcvrEventType
{
    public const string ETHOCA_FRAUD = "ETHOCA_FRAUD";
    public const string ETHOCA_DISPUTE = "ETHOCA_DISPUTE";
    public const string DISPUTE = "DISPUTE";
}

public class MyRcvrDisputeAction
{
    public const string resolved = "resolved";
    public const string UNMAPPED = "UNMAPPED";
}