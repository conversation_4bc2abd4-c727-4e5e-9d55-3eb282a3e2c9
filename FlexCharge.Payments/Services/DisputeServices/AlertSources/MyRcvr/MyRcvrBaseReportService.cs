using System;
using System.Linq;
using FlexCharge.Common.Exceptions;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;

namespace FlexCharge.Payments.Services.DisputeServices;

public abstract class MyRcvrBaseReportService<TRecord> : CsvReportServiceBase<TRecord>, IDisputeReportService
    where TRecord : class, IDisputeReportRecord
{
    public override string ProviderName => AlertProviders.MyRCVR;

    protected override string RequestDateFormat => "yyyy-MM-dd HH:mm:ss";
    protected override string AuthorizationDateFormat => "yyyy-MM-dd HH:mm:ss";
    protected override string DueDateFormat => "yyyy-MM-dd HH:mm:ss";

    public MyRcvrBaseReportService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    protected override int GetAmount(string amount)
    {
        if (string.IsNullOrEmpty(amount))
        {
            throw new FlexChargeException("Amount is null or empty");
        }

        if (decimal.TryParse(amount, out decimal parsedAmount))
        {
            return Formatters.DecimalToInt(parsedAmount);
        }
        else
        {
            throw new FlexChargeException("Amount is not in the correct format");
        }
    }

    protected override int? GetDisputeAmount(string disputeAmount)
    {
        if (string.IsNullOrEmpty(disputeAmount))
        {
            return null;
        }

        if (decimal.TryParse(disputeAmount, out decimal parsedAmount))
        {
            return Formatters.DecimalToInt(parsedAmount);
        }

        return null;
    }

    protected override bool CheckIfCurrencyIsValid(string currency)
    {
        // Paysafe report amount doesn't have currency symbol
        return true;
    }

    protected override bool IsQueueItemExist(DisputeQueueItemDTO item)
    {
        var result = ExistingItems?.Where(x =>
            x.Arn == item.Arn && x.CaseNumber == item.CaseNumber && x.Amount == item.DisputeAmount &&
            x.DisputeType == item.DisputeType && x.RequestDate == item.RequestDate && x.Stage == item.Stage &&
            x.AuthorizationId == x.AuthorizationId && x.Bin == item.Bin && x.Last4 == item.Last4).ToList();

        return result?.Count > 0;
    }
}