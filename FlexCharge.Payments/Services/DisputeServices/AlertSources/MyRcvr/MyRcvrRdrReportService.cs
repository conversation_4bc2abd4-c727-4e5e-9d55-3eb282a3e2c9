using System;
using System.Threading.Tasks;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.DisputeServices;

public class MyRcvrRdrReportService : MyRcvrBaseReportService<MyRcvrRDRItemDTO>
{
    public MyRcvrRdrReportService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    protected override async Task<DisputeQueueItemDTO> CreateDisputeQueueItemDto(MyRcvrRDRItemDTO record,
        int? parsedAmount, int? parsedDisputeAmount)
    {
        var disputeQueueItem = new DisputeQueueItemDTO
        {
            IsWebhook = false,
            IsManualInserted = false,
            IsFileImported = true,
            IsSftp = false,

            RequestDate = DateTime.Parse(record.CaseReceivedDate).ToUtcDate(),
            AuthorizationDate = DateTime.Parse(record.TransactionDate).ToUtcDate(),
            TransactionDate = DateTime.Parse(record.TransactionDate).ToUtcDate(),

            Currency = record?.TransactionCurrencyCode,
            ReferenceNumber = record.CaseID,
            ExternalReferenceID = record.CaseID,
            CardBrand = record?.PaymentType?.ToUpper(),
            Bin = record?.TransactionBIN,
            Last4 = record?.AccountNumber?.Substring(record.AccountNumber.Length - 4),
            Arn = record?.AcquirerReferenceNumber,
            AuthorizationId = record?.AuthorizationCode,
            ExternalOrderId = record?.MerchantOrderId,

            DisputeManagementSystem = DisputeManagementSystem.MyRCVR,
            ProviderName = AlertProviders.MyRCVR,

            DisputeType = record?.ReasonCode,
            Stage = DisputeStage.RDR,
            EarlyFraudWarning = false,
            HasMatch = false,

            Meta = JsonConvert.SerializeObject(record),
        };

        if (parsedAmount.HasValue)
        {
            disputeQueueItem.Amount = parsedAmount.Value;
            // TODO Check if CreditAmount is the same as DisputeAmount
            disputeQueueItem.DisputeAmount = parsedAmount.Value;
        }

        // if (parsedDisputeAmount.HasValue)
        // {
        //     disputeQueueItem.DisputeAmount = parsedDisputeAmount.Value;
        // }

        return disputeQueueItem;
    }
}