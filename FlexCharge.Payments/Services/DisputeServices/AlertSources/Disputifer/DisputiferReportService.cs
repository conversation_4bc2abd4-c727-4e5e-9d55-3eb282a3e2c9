using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.DisputeServices;

public class DisputiferReportService : CsvReportServiceBase<DisputiferReportItemDTO>, IDisputeReportService
{
    public override string ProviderName => AlertProviders.Disputifer;

    protected override string RequestDateFormat => "MM-dd-yyyy";
    protected override string AuthorizationDateFormat => "MM-dd-yyyy";
    protected override string DueDateFormat => "MM-dd-yyyy";

    public DisputiferReportService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    protected override async Task<DisputeQueueItemDTO> CreateDisputeQueueItemDto(DisputiferReportItemDTO record,
        int? parsedAmount, int? parsedDisputeAmount)
    {
        var disputeQueueItem = new DisputeQueueItemDTO
        {
            IsWebhook = false,
            IsManualInserted = false,
            IsFileImported = true,
            IsSftp = false,

            RequestDate = DateTime.Parse(record?.ReceivedDate).ToUtcDate(),
            AuthorizationDate = DateTime.Parse(record?.TransactionDate).ToUtcDate(),
            TransactionDate = DateTime.Parse(record?.TransactionDate).ToUtcDate(),

            Bin = record?.CardNumber.GetFirst(6),
            Last4 = record?.CardNumber?.GetLast(4),
            CardBrand = (bool) record?.CardNumber.StartsWith("4")
                ? DisputeCardBrands.VISA
                : DisputeCardBrands.MASTERCARD,
            Currency = "USD",

            DisputeManagementSystem = GetManagementSystem(record?.Type),
            ProviderName = AlertProviders.Disputifer,

            Stage = GetStage(record?.Type),
            EarlyFraudWarning = false,
            HasMatch = false,
            Reason = record?.Flags,
            DisputeType = record?.Flags,

            Meta = JsonConvert.SerializeObject(record),
        };

        if (parsedAmount.HasValue)
        {
            disputeQueueItem.Amount = parsedAmount.Value;
            disputeQueueItem.DisputeAmount = parsedAmount.Value;
        }

        return disputeQueueItem;
    }


    protected override int GetAmount(string amount)
    {
        if (string.IsNullOrEmpty(amount))
        {
            throw new FlexChargeException("Amount is null or empty");
        }

        if (decimal.TryParse(amount, out decimal parsedAmount))
        {
            return Formatters.DecimalToInt(parsedAmount);
        }
        else
        {
            throw new FlexChargeException("Amount is not in the correct format");
        }
    }

    protected override int? GetDisputeAmount(string amount)
    {
        if (string.IsNullOrEmpty(amount))
        {
            return null;
        }

        if (decimal.TryParse(amount, out decimal parsedAmount))
        {
            return Formatters.DecimalToInt(parsedAmount);
        }

        return null;
    }

    protected override bool CheckIfCurrencyIsValid(string currency)
    {
        // Disputifer report amount doesn't have currency symbol
        return true;
    }

    protected override bool IsQueueItemExist(DisputeQueueItemDTO item)
    {
        var result = ExistingItems?.Where(x =>
            x.CaseNumber == item.CaseNumber && x.Amount == item.DisputeAmount &&
            x.RequestDate == item.RequestDate && x.Stage == item.Stage &&
            x.Bin == item.Bin && x.Last4 == item.Last4).ToList();

        return result?.Count > 0;
    }

    protected string GetStage(string type)
    {
        switch (type?.ToUpper())
        {
            case "VERIFI (CDRN)":
                return DisputeStage.PRE_DISPUTE;
            case "ETHOCA":
            case "DISPUTE":
            default:
                return DisputeStage.RDR;
        }
    }

    protected string GetManagementSystem(string type)
    {
        switch (type?.ToUpper())
        {
            case "VERIFI (CDRN)":
                return DisputeManagementSystem.DisputiferCDRN;
            case "ETHOCA":
                return DisputeManagementSystem.DisputiferEthoca;
            case "DISPUTE":
            default:
                return DisputeManagementSystem.DisputiferRDR;
        }
    }
}