using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Payments.DTO.reports;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.DisputeServices
{
    public class PaycosmoReportService : CsvReportServiceBase<PaycosmoReportItemDTO>, IDisputeReportService
    {
        public override string ProviderName => AlertProviders.Paycosmo;

        protected override string RequestDateFormat => "M/d/yyyy";
        protected override string AuthorizationDateFormat => "M/d/yyyy";
        protected override string DueDateFormat => "M/d/yyyy";

        public PaycosmoReportService(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        protected override async Task<DisputeQueueItemDTO> CreateDisputeQueueItemDto(PaycosmoReportItemDTO record,
            int? parsedAmount, int? parsedDisputeAmount)
        {
            var disputeQueueItem = new DisputeQueueItemDTO
            {
                IsWebhook = false,
                IsManualInserted = false,
                IsFileImported = true,
                IsSftp = false,

                RequestDate = DateTime.Parse(record?.DateIn).ToUtcDate(),
                AuthorizationDate = DateTime.Parse(record?.TranDate).ToUtcDate(),
                TransactionDate = DateTime.Parse(record?.TranDate).ToUtcDate(),

                Currency = "USD", // Assuming USD as currency is not provided in PaycosmoReportItemDTO
                Bin = record?.CardNumber?.GetFirst(6),
                Last4 = record?.CardNumber?.GetLast(4),
                CaseNumber = record.LocCode,
                CardBrand = (bool) record?.CardNumber.StartsWith("4")
                    ? DisputeCardBrands.VISA
                    : DisputeCardBrands.MASTERCARD,
                ExternalReferenceID = record?.OrderNumber,

                DisputeManagementSystem = record.Message?.Trim() == "QUANTUM"
                    ? DisputeManagementSystem.Quantum
                    : DisputeManagementSystem.Paycosmo,
                ProviderName = record.Message?.Trim() == "QUANTUM" ? AlertProviders.Quantum : AlertProviders.Paycosmo,

                Stage = (bool) record?.CardNumber.StartsWith("4")
                    ? DisputeStage.RDR
                    : DisputeStage.CHARGEBACK, // TODO check if this is correct
                EarlyFraudWarning = false,
                DisputeReasonCode = record.Reason,
                HasMatch = false,

                Meta = JsonConvert.SerializeObject(record),
            };

            if (parsedAmount.HasValue)
            {
                disputeQueueItem.Amount = parsedAmount.Value;
                disputeQueueItem.DisputeAmount = parsedAmount.Value;
            }

            return disputeQueueItem;
        }

        protected override int GetAmount(string amount)
        {
            if (string.IsNullOrEmpty(amount))
            {
                throw new Exception("Amount is null or empty");
            }

            string cleanedAmount = amount.TrimStart('-', '$');
            if (decimal.TryParse(cleanedAmount, out decimal parsedAmount))
            {
                return (int) (parsedAmount * 100);
            }
            else
            {
                throw new Exception("Amount is not in the correct format");
            }
        }

        protected override int? GetDisputeAmount(string disputeAmount)
        {
            if (string.IsNullOrEmpty(disputeAmount))
            {
                return null;
            }

            string cleanedAmount = disputeAmount?.TrimStart('-', '$');
            if (decimal.TryParse(cleanedAmount, out decimal parsedAmount))
            {
                return (int) (parsedAmount * 100);
            }

            return null;
        }

        protected override bool CheckIfCurrencyIsValid(string currency)
        {
            return currency.Length > 2 && (currency.StartsWith("-$") || currency.StartsWith("$"));
        }

        protected override bool IsQueueItemExist(DisputeQueueItemDTO item)
        {
            var result = ExistingItems?.Where(x =>
                x.Amount == item.DisputeAmount &&
                x.RequestDate == item.RequestDate && x.Stage == item.Stage).ToList();

            return result?.Count > 0;
        }
    }
}