namespace FlexCharge.Payments.Services.DisputeServices;

public static class KountRefundedState
{
    public const string Refunded = "refunded";
    public const string NotRefunded = "not refunded";
    public const string NotSettled = "not settled";
}

public static class KountEventType
{
    public const string ORDER_INQUIRY = "ORDER_INQUIRY";
    public const string DISPUTE_NOTICE = "DISPUTE_NOTICE";
    public const string FRAUD_NOTICE = "FRAUD_NOTICE";
    public const string ETHOCA_FRAUD = "ETHOCA_FRAUD";
    public const string ETHOCA_DISPUTE = "ETHOCA_DISPUTE";
    public const string DISPUTE = "DISPUTE";
}

public class KountDisputeAction
{
    public const string resolved = "resolved";
    public const string UNMAPPED = "UNMAPPED";
}