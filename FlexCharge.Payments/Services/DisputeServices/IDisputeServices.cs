using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.DisputeServices.Models;

namespace FlexCharge.Payments.Services.DisputeServices;

public interface IDisputeServices
{
    Task<PreDisputeResponse> AddPreDisputeAsync(PreDisputeRequest request, CancellationToken token = default);

    Task<CreateDisputeResponse> CreateDisputeAsync(CreateDisputeRequest request, CancellationToken token = default);

    Task<DisputeResponseDTO> AdminGetDisputes(int pageSize, int pageNumber, Guid? mid, string? query, DateTime? from,
        DateTime? to, DateTime? requestFrom, DateTime? requestTo, DateTime? createdFrom,
        DateTime? createdTo, string? timezone, string? sort, string? sortField,
        List<string>? providerName, List<string>? cardType, List<string>? stage, bool? isWebhook, bool? isMatch,
        bool? isArchived, bool? isFileImported, bool? isManualInserted);

    Task<DisputeResponseDTO> PartnerGetDisputes(int pageSize, int pageNumber, Guid? pid, Guid? mid, string? query,
        DateTime? from,
        DateTime? to, DateTime? requestFrom, DateTime? requestTo, DateTime? createdFrom,
        DateTime? createdTo, string? timezone, string? sort, string? sortField,
        List<string>? providerName, List<string>? cardType, List<string>? stage, bool? isWebhook, bool? isMatch,
        bool? isArchived, bool? isFileImported, bool? isManualInserted);

    Task<DisputeResponseDTO.DisputeItemQueryDTO> GetDisputeByIdAsync(Guid id);

    Task<string> GenerateAsync(Guid? partnerId, int pageSize, int pageNumber, Guid? mid, string? query, DateTime? from,
        DateTime? to,
        DateTime? requestFrom, DateTime? requestTo, DateTime? createdFrom,
        DateTime? createdTo, string? timezone, string? sort, string? sortField,
        List<string>? providerName, List<string>? cardType, List<string>? stage, bool? isWebhook, bool? isMatch,
        bool? isArchived, bool? isFileImported, bool? isManualInserted);

    Task<List<DisputeActivityResponseDTO>> GetActivities(Guid Id);

    Task<PreDisputeResponse> MatchDisputeAsync(DisputeMatchRequestDTO payload,
        CancellationToken token = default, bool shouldAlert = true);

    Task ReverseAsync(Guid? disputeId, Guid? orderId);

    Task UpdateDisputeAsync(Dispute dispute, DisputeUpdateRequestDTO payload,
        CancellationToken token);

    Task<DisputeReconcileResponseDTO> ReconcileDisputeAsync(DisputeReconcileRequestDTO payload,
        CancellationToken token = default);
}