// using System;
// using System.Threading.Tasks;
// using FlexCharge.Common.Exceptions;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Payments.Entities;
// using Microsoft.EntityFrameworkCore;
//
// namespace FlexCharge.Payments.Services.PartnersServices;
//
// public class PartnerService : IPartnerService
// {
//     private readonly PostgreSQLDbContext _dbContext;
//
//     public PartnerService(PostgreSQLDbContext dbContext)
//     {
//         _dbContext = dbContext;
//     }
//
//     public async Task UpsertPartnerAsync(Guid partnerId, string name, string type)
//     {
//         using var workspan = Workspan.Start<PartnerService>().LogEnterAndExit();
//
//         try
//         {
//             var partner = await _dbContext.Partners.SingleOrDefaultAsync(x => x.PartnerId == partnerId);
//             if (partner == null)
//             {
//                 var newPartner = new Partner
//                 {
//                     PartnerId = partnerId,
//                     Name = name,
//                     Type = type
//                 };
//
//                 _dbContext.Add(newPartner);
//                 await _dbContext.SaveChangesAsync();
//             }
//         }
//         catch (Exception e)
//         {
//             workspan.RecordFatalException(e, "Upserting partner failed: partnerId = {partnerId}", partnerId);
//             throw new FlexChargeException("Upserting partner failed", e);
//         }
//     }
//     
//     public async Task UpdatePartnerAsync(Guid partnerId, string name, string type)
//     {
//         using var workspan = Workspan.Start<PartnerService>().LogEnterAndExit();
//
//         try
//         {
//             var partner = await _dbContext.Partners.SingleOrDefaultAsync(x => x.Id == partnerId);
//             if (partner != null)
//             {
//                 partner.Name = name;
//                 partner.Type = type;
//
//                 _dbContext.Update(partner);
//                 await _dbContext.SaveChangesAsync();
//             }
//             else
//             {
//                 await UpsertPartnerAsync(partnerId, name, type);
//             }
//         }
//         catch (Exception e)
//         {
//             workspan.RecordFatalException(e, "Updating partner failed: partnerId = {partnerId}", partnerId);
//             throw new FlexChargeException("Updating partner failed", e);
//         }
//     }
// }