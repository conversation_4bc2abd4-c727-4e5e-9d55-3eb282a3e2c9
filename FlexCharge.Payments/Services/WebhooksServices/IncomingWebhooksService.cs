using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Payments.Services.KountDisputeService;
using FlexCharge.Stripe.Models.Webhooks;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Stripe;

namespace FlexCharge.Payments.Services;

public class IncomingWebhooksService : IIncomingWebhooksService
{
    private PostgreSQLDbContext _context;
    private readonly IDisputeServices _disputeServices;
    private readonly IActivityService _activityService;

    private readonly IPublishEndpoint _publishEndpoint;

    public IncomingWebhooksService(PostgreSQLDbContext context,
        IDisputeServices disputeServices,
        IActivityService activityService,
        IPublishEndpoint publishEndpoint)
    {
        _context = context;
        _disputeServices = disputeServices;
        _activityService = activityService;
    }

    public async Task ProcessIncomingStripeWebhookAsync(string requestBody)
    {
        using var workspan = Workspan.Start<IncomingWebhooksService>()
            .LogEnterAndExit();

        try
        {
            WebhookStripeDTO payload = JsonConvert.DeserializeObject<WebhookStripeDTO>(requestBody);

            workspan
                .Baggage("RequestBody", requestBody)
                .Log.Information(
                    $"ENTERED: WEBHOOK ManageStripeAsync: providerTransactionToken - {payload?.data?.@object?.payment_intent}; type - {payload?.type} ");

            await _activityService.CreateActivityAsync(
                PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_Webhook_Recieved,
                set => set
                    .Data(payload)
                    .Meta(meta => meta
                        .ServiceProvider(WebhookProviders.Stripe)
                        .SetValue("Provider", WebhookProviders.Stripe)));

            if (EnvironmentHelper.IsInProduction && payload?.livemode == false)
            {
                //log
                workspan.Log.Information(
                    $"EXIT: WEBHOOK ManageStripeAsync: providerTransactionToken - {payload?.data?.@object?.payment_intent}; type - {payload?.type} ");
                return;
            }

            if (payload?.data?.@object?.payment_intent == null)
            {
                workspan.Log.Information("No payment_intent in payload -> ignoring webhook");

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                    set => set
                        .Data(payload)
                        .Meta(meta => meta
                            .ServiceProvider(WebhookProviders.Stripe)
                            .Error("No payment_intent in payload")));


                // workspan.Log.Error(
                //     $"ERROR: WEBHOOK ManageStripeAsync: payment_intent is null");
                // await _activityService.CreateActivityAsync(
                //     PaymentsIncomingWebhooksErrorActivities.PaymentsWebhooks_MissingPaymentIntent_Error,
                //     set => set
                //         .Data(payload)
                //         .Meta(meta => meta
                //             .SetValue("Provider", "Stripe")));
                return;
            }

            var timer = DateTime.Now;

            var transaction = await _context.Transactions
                .FirstOrDefaultAsync(x =>
                    x.Type.ToLower() == "capture" &&
                    (x.ProviderTransactionToken == payload.data.@object.payment_intent ||
                     x.Meta.RootElement
                         .GetProperty("transaction")
                         .GetProperty("gateway_specific_response_fields")
                         .GetProperty("stripe_payment_intents")
                         .GetProperty("data_id")
                         .GetString() == <EMAIL>));

            var paymentInstrument = transaction != null
                ? await _context.PaymentInstruments.SingleOrDefaultAsync(x => x.Id == transaction.PaymentMethodId)
                : null;

            if (transaction == null)
            {
                workspan.Log.Error(
                    $"ERROR: WEBHOOK ManageStripeAsync: Transaction not found: transaction - {payload?.data?.@object?.payment_intent}; provider - stripe; time: {DateTime.Now - timer}");

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                    set => set
                        .Data(payload)
                        .Meta(meta => meta
                            .ServiceProvider(WebhookProviders.Stripe)
                            .Error("Transaction not found")
                            .TransactionReference(payload?.data?.@object?.payment_intent)));
                return;
            }

            if (paymentInstrument == null)
            {
                workspan.Log.Error(
                    $"ERROR: WEBHOOK ManageStripeAsync: PaymentInstrument not found: transaction - {payload?.data?.@object?.payment_intent}; provider - stripe; time: {DateTime.Now - timer}");

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                    set => set
                        .CorrelationId(transaction.OrderId)
                        .Data(payload)
                        .Meta(meta => meta
                            .ServiceProvider(WebhookProviders.Stripe)
                            .Error("PaymentInstrument not found")
                            .TransactionReference(payload?.data?.@object?.payment_intent)));
                return;
            }


            switch (payload?.type)
            {
                case "charge.dispute.closed":
                {
                    var stage = DisputeStage.CHARGEBACK;

                    if (payload?.data?.@object?.network_details != null)
                    {
                        stage = payload?.data?.@object?.network_details?.visa?.rapid_dispute_resolution == true
                            ? DisputeStage.RDR
                            : DisputeStage.PRE_DISPUTE;
                    }

                    var preDisputeRequestPayload = new PreDisputeRequest
                    {
                        IsWebhook = true,
                        IsManualInserted = false,
                        IssueRefund = false,
                        EarlyFraudWarning = false,
                        TransactionId = transaction.Id,
                        OrderId = transaction.OrderId,
                        Descriptor = transaction.DynamicDescriptor,
                        // RequestDate = transaction.CreatedOn,
                        RequestDate = payload?.created != null
                            ? DateTimeOffset.FromUnixTimeSeconds(payload.created).DateTime
                            : DateTime.UtcNow,
                        Bin = paymentInstrument?.Bin,
                        Last4 = paymentInstrument?.Last4,
                        CardBrand = paymentInstrument.Bin.StartsWith("4") ? "VISA" : "MASTERCARD",
                        CardType = paymentInstrument?.Type.ToString().ToUpper(),
                        // DisputeType = (CreditCardDisputeType)reasonCode,
                        DisputeType = payload?.data?.@object?.payment_method_details?.card
                            ?.network_reason_code,
                        DisputeManagementSystem = "Stripe",
                        ProviderName = "Stripe",
                        Stage = stage,
                        Meta = requestBody,
                    };

                    await _disputeServices.AddPreDisputeAsync(preDisputeRequestPayload);

                    await _activityService.CreateActivityAsync(
                        PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_Webhook_Succeeded,
                        set => set
                            .CorrelationId(transaction.OrderId)
                            .Meta(meta => meta
                                .ServiceProvider(WebhookProviders.Stripe)
                                .SetValue("Type", payload?.type)
                                .SetValue("TransactionId", transaction.Id)));
                    break;
                }
                case "radar.early_fraud_warning.created":
                    var preDisputeRequestPayload2 = new PreDisputeRequest
                    {
                        IsWebhook = true,
                        IsManualInserted = false,
                        IssueRefund = true,
                        EarlyFraudWarning = true,
                        TransactionId = transaction.Id,
                        OrderId = transaction.OrderId,
                        Descriptor = transaction.DynamicDescriptor,
                        // RequestDate = transaction.CreatedOn,
                        RequestDate = payload?.created != null
                            ? DateTimeOffset.FromUnixTimeSeconds(payload.created).DateTime
                            : DateTime.UtcNow,
                        Bin = paymentInstrument?.Bin,
                        Last4 = paymentInstrument?.Last4,
                        CardBrand = paymentInstrument.Bin.StartsWith("4") ? "VISA" : "MASTERCARD",
                        CardType = paymentInstrument?.Type.ToString().ToUpper(),
                        DisputeType = "",
                        DisputeManagementSystem = "None",
                        ProviderName = "Stripe",
                        Stage = DisputeStage.EARLY_WARNING,
                        Meta = requestBody
                    };

                    await _disputeServices.AddPreDisputeAsync(preDisputeRequestPayload2);

                    await _activityService.CreateActivityAsync(
                        PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_Webhook_Succeeded,
                        set => set
                            .CorrelationId(transaction.OrderId)
                            .Meta(meta => meta
                                .ServiceProvider(WebhookProviders.Stripe)
                                .SetValue("Type", payload?.type)
                                .SetValue("TransactionId", transaction.Id)));

                    break;
            }


            workspan.Log.Information(
                $"EXIT: WEBHOOK ManageStripeAsync: providerTransactionToken - {payload?.data?.@object?.payment_intent}; type - {payload?.type} ");
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            await _activityService.CreateActivityAsync(
                PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                set => set
                    .Data(e)
                    .Meta(meta => meta.SetValue("Provider", "Stripe")));
            throw;
        }
    }

    public async Task ProcessIncomingNuveiWebhookAsync(string requestBody)
    {
        using var workspan = Workspan.Start<IncomingWebhooksService>()
            .LogEnterAndExit();

        try
        {
            WebhookNuveiDTO nuveiData = JsonConvert.DeserializeObject<WebhookNuveiDTO>(requestBody);

            workspan
                .Baggage("RequestBody", requestBody)
                .Log.Information(
                    $"ENTERED: WEBHOOK ManageNuveiAsync: providerTransactionToken - {nuveiData?.TransactionDetails?.TransactionId}; category - {nuveiData?.Chargeback?.ChargebackStatusCategory} ");

            await _activityService.CreateActivityAsync(
                PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_Webhook_Recieved,
                set => set
                    .Data(nuveiData)
                    .Meta(meta => meta
                        .ServiceProvider(WebhookProviders.Nuvei)));

            if (nuveiData?.TransactionDetails?.TransactionId == null)
            {
                workspan.Log.Error(
                    $"ERROR: WEBHOOK ManageNuveiAsync: TransactionId is null");

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                    set => set
                        .Data(nuveiData)
                        .Meta(meta => meta
                            .ServiceProvider(WebhookProviders.Nuvei)
                            .Error("TransactionId is null")));
            }
            else
            {
                var transaction = await _context.Transactions
                    .FirstOrDefaultAsync(x =>
                        x.Type.ToLower() == "capture" && x.ProviderTransactionToken ==
                        nuveiData.TransactionDetails.TransactionId.ToString());

                var paymentInstrument = transaction != null
                    ? await _context.PaymentInstruments.SingleOrDefaultAsync(x => x.Id == transaction.PaymentMethodId)
                    : null;

                if (transaction != null && paymentInstrument != null)
                {
                    var payload = new PreDisputeRequest
                    {
                        IssueRefund = false,
                        TransactionId = transaction.Id,
                        OrderId = transaction.OrderId,
                        Descriptor = transaction.DynamicDescriptor,
                        RequestDate = nuveiData?.Chargeback?.Date != null
                            ? DateTime.Parse(nuveiData?.Chargeback?.Date).ToUtcDate()
                            : DateTime.UtcNow,
                        Bin = paymentInstrument?.Bin,
                        Last4 = paymentInstrument?.Last4,
                        CardBrand = paymentInstrument.Bin.StartsWith("4") ? "VISA" : "MASTERCARD",
                        CardType = paymentInstrument?.Type.ToString().ToUpper(),
                        // DisputeType = (CreditCardDisputeType)reasonCode,
                        DisputeType = nuveiData?.Chargeback?.ChargebackReason,
                        DisputeManagementSystem = DisputeManagementSystem.Nuvei,
                        ProviderName = AlertProviders.Nuvei,
                        Arn = nuveiData?.TransactionDetails?.Arn,
                        Currency = nuveiData?.Chargeback?.Currency,
                        Stage = nuveiData?.Chargeback?.ChargebackStatusCategory != null &&
                                nuveiData.Chargeback.ChargebackStatusCategory.ToLower().Contains("rdr")
                            ? DisputeStage.RDR
                            : DisputeStage.CHARGEBACK,
                        EarlyFraudWarning = false,
                        IsWebhook = true,
                        IsManualInserted = false,
                        Meta = requestBody
                    };

                    await _disputeServices.AddPreDisputeAsync(payload);
                    workspan.Log.Information(
                        "EXIT: WEBHOOK ManageNuveiAsync: Nuvei transaction ended (webhook) > PreDisputeRequest added");

                    await _activityService.CreateActivityAsync(
                        PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_Webhook_Succeeded,
                        set => set
                            .CorrelationId(transaction.OrderId)
                            .Meta(meta => meta
                                .ServiceProvider(WebhookProviders.Nuvei)
                                .SetValue("Category", nuveiData.Chargeback.ChargebackStatusCategory)
                                .SetValue("TransactionId", transaction.Id)));
                }
                else if (transaction == null)
                {
                    workspan.Log.Error(
                        $"ERROR: WEBHOOK ManageNuveiAsync: Transaction not found: transaction - {nuveiData?.TransactionDetails?.TransactionId.ToString()}; provider - nuvei");

                    await _activityService.CreateActivityAsync(
                        PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                        set => set
                            .Data(nuveiData)
                            .Meta(meta => meta
                                .ServiceProvider(WebhookProviders.Nuvei)
                                .Error("Transaction not found")));
                }
                else if (paymentInstrument == null)
                {
                    workspan.Log.Error(
                        $"ERROR: WEBHOOK ManageNuveiAsync: PaymentInstrument not found: transaction - {nuveiData?.TransactionDetails?.TransactionId.ToString()}; provider - nuvei");

                    await _activityService.CreateActivityAsync(
                        PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                        set => set
                            .CorrelationId(transaction.OrderId)
                            .Data(nuveiData)
                            .Meta(meta => meta
                                .ServiceProvider(WebhookProviders.Nuvei)
                                .Error("PaymentInstrument not found")
                                .TransactionReference(transaction.Id)));
                }
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);

            await _activityService.CreateActivityAsync(
                PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                set => set
                    .Data(e)
                    .Meta(meta => meta.ServiceProvider(WebhookProviders.Nuvei)));

            throw;
        }
    }

    public async Task ProcessIncomingCheckoutWebhookAsync(string requestBody)
    {
        using var workspan = Workspan.Start<IncomingWebhooksService>()
            .LogEnterAndExit();

        try
        {
            WebhookCheckoutDTO checkout = JsonConvert.DeserializeObject<WebhookCheckoutDTO>(requestBody);

            workspan
                .Baggage("RequestBody", checkout)
                .Log.Information(
                    $"ENTERED: WEBHOOK ManageCheckoutAsync: providerTransactionToken - {checkout?.data?.payment_id}; ");

            await _activityService.CreateActivityAsync(
                PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_Webhook_Recieved,
                set => set
                    .Data(requestBody)
                    .Meta(meta => meta.ServiceProvider(WebhookProviders.Checkout)));

            if (checkout?.data?.payment_id == null)
            {
                workspan.Log.Fatal(
                    $"ERROR: WEBHOOK ManageCheckoutAsync: Payment_id is null");

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                    set => set
                        .Data(requestBody)
                        .Meta(meta => meta
                            .ServiceProvider(WebhookProviders.Checkout)
                            .Error("Payment_id is null")));
            }
            else
            {
                var transaction = await _context.Transactions
                    .FirstOrDefaultAsync(x =>
                        x.Type.ToLower() == "capture" && x.ProviderTransactionToken == checkout.data.payment_id);

                var paymentInstrument = transaction != null
                    ? await _context.PaymentInstruments.SingleOrDefaultAsync(x => x.Id == transaction.PaymentMethodId)
                    : null;

                if (transaction != null && paymentInstrument != null)
                {
                    // var reasonCode = int.Parse(checkout.data.reason_code.Replace(".", ""));

                    var payload = new PreDisputeRequest
                    {
                        IssueRefund = false,
                        TransactionId = transaction.Id,
                        OrderId = transaction.OrderId,
                        Descriptor = transaction.DynamicDescriptor,
                        RequestDate = checkout?.data?.date != null
                            ? DateTime.Parse(checkout?.data.date).ToUtcDate()
                            : DateTime.UtcNow,
                        Bin = paymentInstrument?.Bin,
                        Last4 = paymentInstrument?.Last4,
                        CardBrand = checkout?.data?.payment_method?.Trim().ToUpper(),
                        // DisputeType = (CreditCardDisputeType)reasonCode,
                        DisputeType = checkout?.data?.reason_code,
                        DisputeManagementSystem = "Checkout",
                        ProviderName = "Checkout",
                        IsWebhook = true,
                        IsManualInserted = false,
                        Meta = requestBody,
                    };

                    await _disputeServices.AddPreDisputeAsync(payload);

                    workspan.Log.Information(
                        "EXIT: WEBHOOK ManageCheckoutAsync: Transaction ended (webhook) > PreDisputeRequest added");

                    await _activityService.CreateActivityAsync(
                        PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_Webhook_Succeeded,
                        set => set
                            .CorrelationId(transaction.OrderId)
                            .Meta(meta => meta
                                .ServiceProvider(WebhookProviders.Checkout)
                                .SetValue("TransactionId", transaction.Id)));
                }
                else if (transaction == null)
                {
                    workspan.Log.Fatal(
                        $"ERROR: WEBHOOK ManageCheckoutAsync: Transaction not found: transaction - {checkout?.data?.payment_id}; provider - checkout");

                    await _activityService.CreateActivityAsync(
                        PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                        set => set
                            .CorrelationId(transaction.OrderId)
                            .Data(checkout)
                            .Meta(meta => meta
                                .ServiceProvider(WebhookProviders.Checkout)
                                .Error("PaymentInstrument not found")
                                .TransactionReference(transaction.Id)));
                }
                else if (paymentInstrument == null)
                {
                    workspan.Log.Fatal(
                        $"ERROR: WEBHOOK ManageCheckoutAsync: PaymentInstrument not found: transaction - {checkout?.data?.payment_id}; provider - checkout");

                    await _activityService.CreateActivityAsync(
                        PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                        set => set
                            .CorrelationId(transaction.OrderId)
                            .Data(checkout)
                            .Meta(meta => meta
                                .ServiceProvider(WebhookProviders.Checkout)
                                .Error("PaymentInstrument not found")
                                .TransactionReference(transaction.Id)));
                }
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);

            await _activityService.CreateActivityAsync(
                PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                set => set
                    .Data(e)
                    .Meta(meta => meta.ServiceProvider(WebhookProviders.Checkout)));

            throw;
        }
    }
}