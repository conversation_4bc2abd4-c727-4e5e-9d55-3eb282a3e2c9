using FlexCharge.Utils;
using FluentValidation;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Revolv3.Model;

public class RevolvSalesRequest : ISensitiveData
{
    public RevolvNetworkProcessing networkProcessing { get; set; }
    public RevolvPaymentMethod paymentMethod { get; set; }
    public long? customerId { get; set; }
    public RevolvInvoice invoice { get; set; }
    public RevolvDynamicDescriptor dynamicDescriptor { get; set; }


    public class Validator : AbstractValidator<RevolvSalesRequest>
    {
        public Validator()
        {
            RuleFor(x => x.paymentMethod).NotNull();
            RuleFor(x => x.invoice).NotNull().SetValidator(new RevolvInvoice.Validator());
        }
    }
}