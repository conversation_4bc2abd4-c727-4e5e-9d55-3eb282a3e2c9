using FluentValidation;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models;

public class RapydCustomer
{
    public RapydAddress[] addresses { get; set; }
    public string business_vat_id { get; set; }
    public string coupon { get; set; }
    public string description { get; set; }
    public string email { get; set; }
    public string ewallet { get; set; }
    public string invoice_prefix { get; set; }
    public object metadata { get; set; }
    public string name { get; set; }
    public object payment_method { get; set; }
    public string phone_number { get; set; }

    public class Validator : AbstractValidator<RapydCustomer>
    {
        public Validator()
        {
            // RuleFor(x => x.addresses).NotNull();
            // RuleFor(x => x.business_vat_id).MaximumLength(255);
            // RuleFor(x => x.coupon).MaximumLength(255);
            // RuleFor(x => x.description).MaximumLength(255);
            RuleFor(x => x.email).EmailAddress().When(x => !string.IsNullOrWhiteSpace(x.email))
                .WithSeverity(Severity.Warning);
            ;
            // RuleFor(x => x.ewallet).MaximumLength(255);
            // RuleFor(x => x.invoice_prefix).MaximumLength(255);
            // //RuleFor(x => x.metadata).NotNull();
            RuleFor(x => x.name).NotEmpty().WithSeverity(Severity.Warning);
            ;
            // RuleFor(x => x.payment_method).NotNull();
            RuleFor(x => x.phone_number).Matches(@"^\+[1-9]\d{1,14}$")
                .When(x => !string.IsNullOrWhiteSpace(x.phone_number)).WithSeverity(Severity.Warning);
            ;
        }
    }
}