using System.Text.Json.Serialization;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models;

public class Status
{
    [JsonPropertyName("error_code")] public string Error_code { get; set; }

    [JsonPropertyName("status")] public string OperationStatus { get; set; }

    [JsonPropertyName("message")] public string Message { get; set; }

    [JsonPropertyName("response_code")] public string Response_code { get; set; }

    [JsonPropertyName("operation_id")] public string Operation_id { get; set; }
}