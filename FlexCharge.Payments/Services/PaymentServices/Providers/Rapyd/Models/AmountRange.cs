using System.Text.Json.Serialization;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models.PaymentMethodTypes.ListTypes;

public class AmountRange
{
    [JsonPropertyName("currency")] public string Currency { get; set; }

    [JsonPropertyName("maximum_amount")] public string Maximum_amount { get; set; }

    [JsonPropertyName("minimum_amount")] public string Minimum_amount { get; set; }
}