namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models;

public class RapydDispute
{
    public int amount { get; set; }
    public string central_processing_date { get; set; }
    public int created_at { get; set; }
    public string currency { get; set; }
    public string dispute_category { get; set; }
    public string dispute_reason_description { get; set; }
    public int due_date { get; set; }
    public string evidence { get; set; }
    public string evidence_reason_code { get; set; }
    public string ewallet_id { get; set; }
    public string id { get; set; }
    public int original_dispute_amount { get; set; }
    public string original_dispute_currency { get; set; }
    public int original_transaction_amount { get; set; }
    public string original_transaction_currency { get; set; }
    public string original_transaction_id { get; set; }
    public string payment_method { get; set; }
    public object payment_method_data { get; set; }
    public bool pre_dispute { get; set; }
    public decimal rate { get; set; }
    public RapydDisputeStatus? status { get; set; }
    public string token { get; set; }
    public int updated_at { get; set; }
}