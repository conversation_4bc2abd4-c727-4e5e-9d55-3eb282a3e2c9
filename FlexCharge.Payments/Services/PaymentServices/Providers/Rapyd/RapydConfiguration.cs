using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using FlexCharge.Contracts.Common;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd;

public class RapydConfiguration
{
    [Required]
    [Json<PERSON>ropertyName("secretKey")]
    public string SecretKey { get; set; }

    [Required]
    [JsonPropertyName("accessKey")]
    public string AccessKey { get; set; }

    [Required]
    [JsonPropertyName("walletId")]
    public string WalletId { get; set; }

    [Required]
    [JsonPropertyName("balanceCurrency")]
    public CurrencyEnum? BalanceCurrency { get; set; }

    [JsonPropertyName("supportedCurrencies")]
    [Required]
    public CurrencyEnum[] SupportedCurrencies { get; set; }
}