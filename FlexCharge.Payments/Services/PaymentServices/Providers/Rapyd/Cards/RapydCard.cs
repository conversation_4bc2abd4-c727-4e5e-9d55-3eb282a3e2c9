using System;
using System.Text.Json.Serialization;
using FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models;
using FluentValidation;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Cards;

public class RapydCard

{
    [JsonPropertyName("number")] public string Number { get; set; }

    [JsonPropertyName("expiration_month")] public string ExpirationMonth { get; set; }

    [JsonPropertyName("expiration_year")] public string ExpirationYear { get; set; }

    [JsonPropertyName("cvv")] public string CVV { get; set; }

    [JsonPropertyName("name")] public string Name { get; set; }

    [JsonPropertyName("network_reference_id")]
    public string NetworkReferenceId { get; set; }


    [JsonPropertyName("recurrence_type")] public CardRecurrence? RecurrenceType { get; set; }

    [JsonPropertyName("number_type")] public CardNumberType? NumberType { get; set; }

    public class Validator : AbstractValidator<RapydCard>
    {
        public Validator()
        {
            RuleFor(x => x.Number).NotEmpty();
            RuleFor(x => x.ExpirationMonth).NotEmpty();
            RuleFor(x => x.ExpirationYear).NotEmpty();
            RuleFor(x => x.CVV).Matches("^[0-9]{3,4}$").When(x => !string.IsNullOrWhiteSpace(x.CVV))
                .WithSeverity(Severity.Warning);
            RuleFor(x => x.CVV).NotNull().When(x => x.NetworkReferenceId is null);
            RuleFor(x => x.NetworkReferenceId).NotNull().When(x => x.CVV is null);
        }
    }
}