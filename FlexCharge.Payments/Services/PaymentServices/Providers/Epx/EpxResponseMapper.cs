using System.Collections.Generic;
using System.ComponentModel;
using FlexCharge.Common.Shared.Payments;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Epx;

public class EpxResponseMapper : IndexTableProviderResponseMapperBase
{
    public static void Initialize()
    {
    }

    static EpxResponseMapper()
    {
        IndexTable = new Dictionary<string, MappedResponse>
        {
            {"", InternalResponseMapper.GetMappedResponse("-1")},
            {"0", InternalResponseMapper.GetMappedResponse("0")},
            {"1", InternalResponseMapper.GetMappedResponse("1")},
            {"2", InternalResponseMapper.GetMappedResponse("2")},
            {"3", InternalResponseMapper.GetMappedResponse("3")},
            {"4", InternalResponseMapper.GetMappedResponse("4")},
            {"5", InternalResponseMapper.GetMappedResponse("5")},
            {"6", InternalResponseMapper.GetMappedResponse("6")},
            {"7", InternalResponseMapper.GetMappedResponse("7")},
            {"10", InternalResponseMapper.GetMappedResponse("10")},
            {"12", InternalResponseMapper.GetMappedResponse("12")},
            {"13", InternalResponseMapper.GetMappedResponse("13")},
            {"14", InternalResponseMapper.GetMappedResponse("14")},
            {"15", InternalResponseMapper.GetMappedResponse("15")},
            {"19", InternalResponseMapper.GetMappedResponse("19")},
            {"21", InternalResponseMapper.GetMappedResponse("21")},
            {"25", InternalResponseMapper.GetMappedResponse("25")},
            {"28", InternalResponseMapper.GetMappedResponse("28")},
            {"30", InternalResponseMapper.GetMappedResponse("30")},
            {"33", InternalResponseMapper.GetMappedResponse("54")},
            {"34", InternalResponseMapper.GetMappedResponse("34")},
            {"39", InternalResponseMapper.GetMappedResponse("39")},
            {"41", InternalResponseMapper.GetMappedResponse("41")},
            {"43", InternalResponseMapper.GetMappedResponse("43")},
            {"46", InternalResponseMapper.GetMappedResponse("46")},
            {"51", InternalResponseMapper.GetMappedResponse("51")},
            {"52", InternalResponseMapper.GetMappedResponse("52")},
            {"53", InternalResponseMapper.GetMappedResponse("53")},
            {"54", InternalResponseMapper.GetMappedResponse("54")},
            {"55", InternalResponseMapper.GetMappedResponse("55")},
            {"57", InternalResponseMapper.GetMappedResponse("57")},
            {"58", InternalResponseMapper.GetMappedResponse("58")},
            {"59", InternalResponseMapper.GetMappedResponse("59")},
            {"61", InternalResponseMapper.GetMappedResponse("61")},
            {"62", InternalResponseMapper.GetMappedResponse("62")},
            {"63", InternalResponseMapper.GetMappedResponse("63")},
            {"65", InternalResponseMapper.GetMappedResponse("65")},
            {"75", InternalResponseMapper.GetMappedResponse("75")},
            {"76", InternalResponseMapper.GetMappedResponse("76")},
            {"77", InternalResponseMapper.GetMappedResponse("77")},
            {"78", InternalResponseMapper.GetMappedResponse("78")},
            {"80", InternalResponseMapper.GetMappedResponse("80")},
            {"81", InternalResponseMapper.GetMappedResponse("81")},
            {"82", InternalResponseMapper.GetMappedResponse("82")},
            {"83", InternalResponseMapper.GetMappedResponse("83")},
            {"85", InternalResponseMapper.GetMappedResponse("85")},
            {"86", InternalResponseMapper.GetMappedResponse("86")},
            {"91", InternalResponseMapper.GetMappedResponse("91")},
            {"92", InternalResponseMapper.GetMappedResponse("92")},
            {"93", InternalResponseMapper.GetMappedResponse("93")},
            {"94", InternalResponseMapper.GetMappedResponse("94")},
            {"96", InternalResponseMapper.GetMappedResponse("96")},
            {"1A", InternalResponseMapper.GetMappedResponse("8")},
            {"6P", InternalResponseMapper.GetMappedResponse("6P")},
            {"B1", InternalResponseMapper.GetMappedResponse("B1")},
            {"DA", InternalResponseMapper.GetMappedResponse("DA")},
            {"E2", InternalResponseMapper.GetMappedResponse("E2")},
            {"E3", InternalResponseMapper.GetMappedResponse("E3")},
            {"E4", InternalResponseMapper.GetMappedResponse("E4")},
            {"E5", InternalResponseMapper.GetMappedResponse("E5")},
            {"E6", InternalResponseMapper.GetMappedResponse("E6")},
            {"E7", InternalResponseMapper.GetMappedResponse("E7")},
            {"E9", InternalResponseMapper.GetMappedResponse("E9")},
            {"EA", InternalResponseMapper.GetMappedResponse("EA")},
            {"EB", InternalResponseMapper.GetMappedResponse("EB")},
            {"EC", InternalResponseMapper.GetMappedResponse("EC")},
            {"ED", InternalResponseMapper.GetMappedResponse("ED")},
            {"EE", InternalResponseMapper.GetMappedResponse("EE")},
            {"EF", InternalResponseMapper.GetMappedResponse("EF")},
            {"EH", InternalResponseMapper.GetMappedResponse("EH")},
            {"EI", InternalResponseMapper.GetMappedResponse("EI")},
            {"EJ", InternalResponseMapper.GetMappedResponse("EJ")},
            {"EK", InternalResponseMapper.GetMappedResponse("EK")},
            {"EL", InternalResponseMapper.GetMappedResponse("EL")},
            {"EM", InternalResponseMapper.GetMappedResponse("EM")},
            {"EN", InternalResponseMapper.GetMappedResponse("EN")},
            {"EO", InternalResponseMapper.GetMappedResponse("EO")},
            {"EQ", InternalResponseMapper.GetMappedResponse("EQ")},
            {"ES", InternalResponseMapper.GetMappedResponse("ES")},
            {"ET", InternalResponseMapper.GetMappedResponse("ET")},
            {"EU", InternalResponseMapper.GetMappedResponse("EU")},
            {"EV", InternalResponseMapper.GetMappedResponse("EV")},
            {"EW", InternalResponseMapper.GetMappedResponse("EW")},
            {"EX", InternalResponseMapper.GetMappedResponse("EX")},
            {"EY", InternalResponseMapper.GetMappedResponse("EY")},
            {"N0", InternalResponseMapper.GetMappedResponse("N0")},
            {"N3", InternalResponseMapper.GetMappedResponse("N3")},
            {"N4", InternalResponseMapper.GetMappedResponse("N4")},
            {"N6", InternalResponseMapper.GetMappedResponse("N6")},
            {"N7", InternalResponseMapper.GetMappedResponse("N7")},
            {"NR", InternalResponseMapper.GetMappedResponse("NR")},
            {"P2", InternalResponseMapper.GetMappedResponse("P2")},
            {"P5", InternalResponseMapper.GetMappedResponse("P5")},
            {"P6", InternalResponseMapper.GetMappedResponse("P6")},
            {"Q1", InternalResponseMapper.GetMappedResponse("Q1")},
            {"R0", InternalResponseMapper.GetMappedResponse("R0")},
            {"R1", InternalResponseMapper.GetMappedResponse("R1")},
            {"R3", InternalResponseMapper.GetMappedResponse("R3")},
            {"RR", InternalResponseMapper.GetMappedResponse("RR")},
            {"S4", InternalResponseMapper.GetMappedResponse("S4")},
            {"S5", InternalResponseMapper.GetMappedResponse("S5")},
            {"S7", InternalResponseMapper.GetMappedResponse("S7")},
            {"S8", InternalResponseMapper.GetMappedResponse("S8")},
            {"S9", InternalResponseMapper.GetMappedResponse("S9")},
            {"Z1", InternalResponseMapper.GetMappedResponse("Z1")},
            {"Z3", InternalResponseMapper.GetMappedResponse("Z3")},
        };
    }

    public static MappedResponse GetMappedResponse(string providerResponseCode)
    {
        return GetMappedResponse(providerResponseCode, IndexTable);
    }

    private static Dictionary<string, MappedResponse> IndexTable { get; }
}