using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.ExceptionServices;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Checkout;
using Checkout.Common;
using Checkout.Payments;
using Checkout.Payments.Request;
using Checkout.Payments.Request.Source;
using Checkout.Payments.Response.Source;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Payments.Services.SVBAchServices;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Nager.Country;
using Newtonsoft.Json;
using Address = Checkout.Common.Address;
using AuthorizationRequest = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.AuthorizationRequest;
using Environment = Checkout.Environment;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Checkout;

public class CheckoutPaymentProvider : PaymentProviderBase
{
    private readonly PostgreSQLDbContext _context;
    private readonly IPaymentInstrumentsService _paymentInstrumentsService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    private ICheckoutApi _api;
    public IServiceProvider ServiceProvider { get; private set; }
    private readonly IMapper _mapper;
    private readonly IPublishEndpoint _publisher;
    private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;
    private bool _isSandbox;
    private string _processingChannelId;
    private readonly CountryProvider _countryProvider = new();
    private CheckoutConfiguration _configuration;

    public CheckoutPaymentProvider(
        IMapper mapper,
        PostgreSQLDbContext context, IHttpContextAccessor httpContextAccessor,
        IPaymentInstrumentsService paymentInstrumentsService, IPublishEndpoint publisher, ISVBService achService,
        IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue)
    {
        _mapper = mapper;
        _context = context;
        _httpContextAccessor = httpContextAccessor;
        _paymentInstrumentsService = paymentInstrumentsService;
        _publisher = publisher;
        _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
        _jsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
    }

    private void ProcessException(Exception originalException,
        Workspan workspan, Interfaces.IResult result,
        string method)
    {
        if (originalException is CheckoutApiException e)
        {
            result.RawResult = JsonConvert.SerializeObject(e);
            var message = e.Message;
            result.AddError(e.Message);
            result.ProviderResponseCode = "HTTP422"; //Invalid data was sent

            if (e.ErrorDetails is not null && e.ErrorDetails.Count > 0)
            {
                foreach (var kv in e.ErrorDetails)
                {
                    var value = kv.Value is string ? kv.Value.ToString() : JsonConvert.SerializeObject(kv.Value);
                    result.AddErrorWithCode(kv.Key, value); //todo-check;
                    message += $"; {value}";
                }

                result.ProviderResponseMessage = message;

                workspan.Log.Error(originalException,
                    "Checkout API {Error} occurred while processing the credit card {method}  with {InnerErrors} ",
                    e.Message, method, JsonConvert.SerializeObject(e.ErrorDetails));
            }
            else
            {
                workspan.Log.Error(originalException,
                    "Checkout API {Error} occurred while processing the credit card {method} ",
                    e.Message, method);
            }
        }
        else if (originalException is CheckoutArgumentException ce)
        {
            result.ProviderResponseMessage = ce.Message;
            result.RawResult = JsonConvert.SerializeObject(ce);
            workspan.Log.Error(originalException,
                "Checkout argument  error occurred while processing the credit card {method}",
                method);
            result.AddErrorWithCode(nameof(CheckoutArgumentException), ce.Message);
        }
        else if (originalException is CheckoutAuthorizationException cae)
        {
            result.ProviderResponseMessage = cae.Message;
            result.RawResult = JsonConvert.SerializeObject(cae);
            result.ProviderResponseCode = "HTTP401"; //authorization error
            workspan.Log.Error(originalException,
                "Checkout authorization error occurred while processing the credit card {method}",
                method);
            result.AddErrorWithCode(nameof(CheckoutAuthorizationException), cae.Message);
        }
        else if (originalException is CheckoutException che)

        {
            result.ProviderResponseMessage = che.Message;
            result.RawResult = JsonConvert.SerializeObject(che);
            workspan.Log.Error(originalException, $"Checkout error occurred while processing the credit card {method}");
            result.AddErrorWithCode(nameof(CheckoutAuthorizationException), che.Message);
        }
        else
        {
            result.RawResult = JsonConvert.SerializeObject(originalException);
            workspan.Log.Error(originalException, $"Generic error occurred while processing the credit card {method}");
            result.AddErrorWithCode(nameof(CheckoutAuthorizationException), originalException.Message);
        }
    }

    private JsonSerializerOptions _jsonSerializerOptions => new()
    {
        ReferenceHandler = ReferenceHandler.IgnoreCycles,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = true
    };

    private async Task<TRes> ProcessTransactionAsync<TReq, TRes>(TReq authRequest,
        CheckoutOperationType operationType,
        CancellationToken token = default)
        where TRes : IAuthorizationResult, new()
        where TReq : IAuthorizationRequest
    {
        using var workspan = Workspan.Start<CheckoutPaymentProvider>().LogEnterAndExit();

        workspan.Log.Information($"ENTERED: CheckoutPaymentProvider => {operationType}");

        await InitiateProviderAsync(authRequest.SupportedGateway.Sandbox, authRequest.SupportedGateway.Id, token);
        workspan.Log.Information("CheckoutPaymentProvider =initiated");

        var card = authRequest.CreditCard;
        var billingAddress = authRequest.BillingAddress;
        var shippingAddress = authRequest.ShippingAddress;
        var threeDs = authRequest.ThreeDS;

        var useNetworkToken = authRequest.NetworkTokenInfo is not null
                              && this.NetworkTokenCardBrands?.Contains(authRequest.CreditCard.CardBrand) == true;

        var useSchemeTransactionId = !authRequest.IsCit
                                     && authRequest.NetworkReferenceData?.TransactionId is not null;

        if (useNetworkToken)
        {
            workspan.Log.Information("CheckoutPaymentProvider NetworkToken will be used");
        }


        CountryCode? CastCountryCode(string ccode)
        {
            try
            {
                return Enum.Parse<CountryCode>(ccode);
            }
            catch
            {
                return null;
            }
        }

        Currency CastCurrencyCode(string ccode) => Enum.Parse<Currency>(ccode);

        TRes retval = new();
        retval.OrderId = authRequest.OrderId;
        retval.Provider = this.CurrentPaymentProvider;
        retval.BinNumber = authRequest.CreditCard.Bin;
        retval.PaymentInstrumentId = authRequest.PaymentInstrumentId;
        retval.CavvCode = authRequest.ThreeDS?.AuthenticationValue;
        try
        {
            var request = new PaymentRequest()
            {
                Amount = operationType == CheckoutOperationType.Verify ? 0 : authRequest.Amount,
                AuthorizationType = AuthorizationType.Final,
                Currency = authRequest.CurrencyCode is null ? null : CastCurrencyCode(authRequest.CurrencyCode),
                MerchantInitiated = !authRequest.IsCit,
                Capture = operationType == CheckoutOperationType.Sale,
                Reference = authRequest.OrderId.ToString(),
                ProcessingChannelId = _processingChannelId,
                Customer = authRequest.BillingAddress is null
                    ? null
                    : new CustomerRequest()
                    {
                        Email = authRequest.BillingAddress.Email,
                        Name = $"{authRequest.BillingAddress.FirstName} {authRequest.BillingAddress.LastName}",
                        Phone = string.IsNullOrWhiteSpace(authRequest.BillingAddress.PhoneNumber)
                            ? null
                            : new Phone()
                            {
                                Number = authRequest.BillingAddress is null
                                    ? null
                                    : Regex.Replace(authRequest.BillingAddress.PhoneNumber, "[^0-9]", ""),

                                CountryCode = authRequest.BillingAddress.Country is null
                                    ? null
                                    : GetCallingCode(authRequest.BillingAddress.Country)
                            }
                    },
                Metadata = new Dictionary<string, object>()
                {
                    {
                        "OrderId", authRequest.OrderId.ToString()
                    },
                    {
                        "Mid", authRequest.Mid.ToString()
                    }
                },
                BillingDescriptor = authRequest.Descriptor?.Name is null
                    ? null
                    : new()
                    {
                        City = authRequest.Descriptor?.City is null
                            ? null
                            : authRequest.Descriptor.City.Substring(0,
                                Math.Min(authRequest.Descriptor.City.Length, 13)),
                        Name = authRequest.Descriptor.Name.Substring(0,
                            Math.Min(authRequest.Descriptor.Name.Length, 25)),

                        // Reference = authRequest.Descriptor.
                    },
                PaymentIp = authRequest.Device?.IpAddress,
                Source = useNetworkToken
                    ? new RequestNetworkTokenSource()
                    {
                        Type = PaymentSourceType.NetworkToken, //todo
                        Cryptogram = authRequest.NetworkTokenInfo.Cryptogram,
                        Token = authRequest.NetworkTokenInfo.Token,
                        TokenType = card.Number.StartsWith("4") ? NetworkTokenType.Vts : NetworkTokenType.Mdes,
                        Eci = authRequest.NetworkTokenInfo.Eci,
                        ExpiryMonth = authRequest.NetworkTokenInfo.ExpirationMonth,
                        ExpiryYear = authRequest.NetworkTokenInfo.ExpirationYear < 2000
                            ? 2000 + authRequest.NetworkTokenInfo.ExpirationYear
                            : authRequest.NetworkTokenInfo.ExpirationYear,
                        BillingAddress = billingAddress is null
                            ? null
                            : new Address()
                            {
                                City = billingAddress.City,
                                Country = CastCountryCode(billingAddress.Country),
                                State = billingAddress.State,
                                Zip = billingAddress.Zip,

                                AddressLine1 = billingAddress.Address1 is null
                                    ? null
                                    : billingAddress.Address1.Substring(0,
                                        Math.Min(billingAddress.Address1.Length, 200)),

                                AddressLine2 = billingAddress.Address2 is null
                                    ? null
                                    : billingAddress.Address2.Substring(0,
                                        Math.Min(billingAddress.Address2.Length, 200)),
                            },

                        Phone = string.IsNullOrWhiteSpace(billingAddress?.PhoneNumber)
                            ? null
                            : new Phone() //todo add phones later
                            {
                                Number = billingAddress?.PhoneNumber is null
                                    ? null
                                    : Regex.Replace(billingAddress.PhoneNumber, "[^0-9]", ""),

                                CountryCode = billingAddress?.Country is null
                                    ? null
                                    : GetCallingCode(billingAddress.Country)
                            },
                        Stored = !authRequest.IsCit
                    }
                    : new RequestCardSource()
                    {
                        Type = PaymentSourceType.Card,
                        Cvv = card.VerificationValue,
                        Number = card.Number,
                        ExpiryMonth = card.Month,
                        ExpiryYear = card.Year,
                        Name = $"{card.FirstName ?? string.Empty} {card.LastName ?? string.Empty}",
                        BillingAddress = billingAddress is null
                            ? null
                            : new Address()
                            {
                                City = billingAddress.City,
                                Country = CastCountryCode(billingAddress.Country),
                                State = billingAddress.State,
                                Zip = billingAddress.Zip,

                                AddressLine1 = billingAddress.Address1 is null
                                    ? null
                                    : billingAddress.Address1.Substring(0,
                                        Math.Min(billingAddress.Address1.Length, 200)),

                                AddressLine2 = billingAddress.Address2 is null
                                    ? null
                                    : billingAddress.Address2.Substring(0,
                                        Math.Min(billingAddress.Address2.Length, 200)),
                            },
                        Phone = string.IsNullOrWhiteSpace(billingAddress?.PhoneNumber)
                            ? null
                            : new Phone() //todo add phones later
                            {
                                Number = billingAddress?.PhoneNumber is null
                                    ? null
                                    : Regex.Replace(billingAddress.PhoneNumber, "[^0-9]", ""),

                                CountryCode = billingAddress?.Country is null
                                    ? null
                                    : GetCallingCode(billingAddress.Country)
                            },
                        Stored = !authRequest.IsCit
                    },

                Shipping = shippingAddress is null
                    ? null
                    : new ShippingDetails()
                    {
                        Address = new Address()
                        {
                            City = shippingAddress.City,
                            Country = shippingAddress.Country is null ? null : CastCountryCode(shippingAddress.Country),
                            State = shippingAddress.State,
                            Zip = shippingAddress.Zip,

                            AddressLine1 = shippingAddress.Address1 is null
                                ? null
                                : shippingAddress.Address1.Substring(0,
                                    Math.Min(shippingAddress.Address1.Length, 200)),

                            AddressLine2 = shippingAddress.Address2 is null
                                ? null
                                : shippingAddress.Address2.Substring(0,
                                    Math.Min(shippingAddress.Address2.Length, 200)),
                        },
                        Phone = string.IsNullOrWhiteSpace(shippingAddress.PhoneNumber)
                            ? null
                            : new Phone()
                            {
                                Number = shippingAddress.PhoneNumber is null
                                    ? null
                                    : Regex.Replace(shippingAddress.PhoneNumber, "[^0-9]", ""),
                                CountryCode = shippingAddress.Country is null
                                    ? null
                                    : GetCallingCode(shippingAddress.Country)
                            }
                    },
                PaymentType = PaymentType.Unscheduled,
                ThreeDs = threeDs is null || threeDs.Enrolled != "Y"
                    ? null
                    : new ThreeDsRequest()
                    {
                        Enabled = true,
                        AttemptN3D = true,
                        Eci = threeDs.EcommerceIndicator,
                        Cryptogram = threeDs.AuthenticationValue,
                        Xid = threeDs.Xid,
                        Version = threeDs.ThreeDsVersion,

                        //ChallengeIndicator = threeDs.
                        //ChallengeCancelReason = 
                    }
            };


            if (useSchemeTransactionId)
            {
                request.PreviousPaymentId = authRequest.NetworkReferenceData?.TransactionId;
                workspan.Log.Information("Checkout using scheme transaction id {SchemeTransactionId}",
                    authRequest.NetworkReferenceData?.TransactionId);
            }


            ////log request
            if (authRequest is not null)
            {
                try
                {
                    var logreq =
                        System.Text.Json.JsonSerializer.Deserialize<TReq>(
                            System.Text.Json.JsonSerializer.Serialize(authRequest, _jsonSerializerOptions),
                            _jsonSerializerOptions);
                    var crd = logreq.CreditCard;
                    crd.Number = crd.Number?.Substring(0, 8);
                    crd.VerificationValue = "***";
                    logreq.CreditCard = crd;
                    workspan.Log.Information("Checkout authrequest= " +
                                             System.Text.Json.JsonSerializer.Serialize(logreq, _jsonSerializerOptions));
                }
                catch (Exception e)
                {
                    workspan.Log.Error("Checkout request log exception {Message}", e.Message);
                }
            }


            var response = await _api.PaymentsClient().RequestPayment(request, Guid.NewGuid().ToString(), token);

            if (response is not null)
            {
                try
                {
                    workspan.Log.Information("Checkout response= " +
                                             System.Text.Json.JsonSerializer.Serialize(response,
                                                 _jsonSerializerOptions));
                }
                catch (Exception e)
                {
                    workspan.Log.Error("Checkout response log exception {Message}", e.Message);
                }
            }


            retval.NetworkTokenUsed = useNetworkToken;
            if (!ResponseIsSuccess(response.ResponseCode))
            {
                if (response.ResponseCode is not null)
                {
                    retval.AddErrorWithCode(response.ResponseCode, response.ResponseSummary);
                    workspan.Log.Information("CheckoutPaymentProvider failed with {ResponseSummary}",
                        response.ResponseSummary);
                }
                else
                {
                    retval.AddError("Response Code is null");
                    workspan.Log.Information("CheckoutPaymentProvider failed with {ResponseSummary}",
                        "Response Code is null");
                }
            }
            else
            {
                workspan.Log.Information(
                    "CheckoutPaymentProvider  {operationType}  done {Status}  {ThreeDs} {Processing} {usingNetworkToken}",
                    operationType,
                    response.Status, response.ThreeDs, response.Processing,
                    request.Source is RequestNetworkTokenSource);
            }

            retval.UpdatedCard =
                CheckoutAccountUpdater.GetCheckoutUpdatedCard(response.Body, _configuration.AccountUpdaterPrivateKey,
                    workspan);


            retval.RawResult = JsonConvert.SerializeObject(response);
            retval.ProviderResponseCode = response.ResponseCode;
            retval.ProviderResponseMessage = response.ResponseSummary;
            retval.ProviderTransactionToken = response.Id; // to capture or to void
            //retval.TransactionId = response.Id; // to search? check it
            retval.AuthorizationCode = response.AuthCode;
            var src = response.Source as CardResponseSource;

            if (src is not null)
            {
                retval.AvsCode = src!.AvsCheck;
                var cvvCheck = src.CvvCheck;
                retval.CvvCode = cvvCheck switch
                {
                    "D" => "N",
                    "N" => "N",
                    "P" => "P",
                    "U" => "U",
                    "X" => "X",
                    "Y" => "M",
                    null => "N",
                    string s => s
                };
            }

            if (authRequest.NetworkTokenInfo is not null) // todo-patch
            {
                retval.CvvCode = "M";
            }

            retval.SchemeTransactionIdUsed = useSchemeTransactionId;
            retval.SchemeTransactionId = response.SchemeId;
        }
        catch (Exception e)
        {
            ProcessException(e, workspan, retval, operationType.ToString());
        }

        var internalResponse = CheckoutResponseMapper.GetMappedResponse(retval.ProviderResponseCode);
        retval.InternalResponseCode = internalResponse?.MappedResponseCode;
        retval.InternalResponseMessage = internalResponse?.MappedResponseMessage;
        retval.InternalResponseGroup = internalResponse?.MappedResponseGroup.ToString();

        return retval;
    }


    private async Task InitiateProviderAsync(bool isSandbox, Guid supportedGatewayId, CancellationToken token = default)
    {
        this._isSandbox = isSandbox;

        var gateway = await _context.SupportedGateways
            .Where(x => x.Id == supportedGatewayId && x.Sandbox == isSandbox)
            .FirstOrDefaultAsync(token);

        if (gateway == null)
            throw new Exception("Checkout  gateway not found");

        _configuration = System.Text.Json.JsonSerializer.Deserialize<CheckoutConfiguration>(gateway.Configuration);
        _processingChannelId = _configuration.ProcessingChannelId;
        _api = CheckoutSdk.Builder().StaticKeys()
            .PublicKey(_configuration.PublicKey) // optional, only required for operations related with tokens
            .SecretKey(gateway.SecretKey)
            .Environment(isSandbox ? Environment.Sandbox : Environment.Production)
            .Build();
    }

    private bool ResponseIsSuccess(string code) => code?.StartsWith("100") == true;

    private string GetCallingCode(string countryCode)
    {
        if (_countryProvider.TryGetCountry(countryCode, out var country))
        {
            return country.CallingCodes?.Length > 0 ? country.CallingCodes[0] : null;
        }

        return null;
    }

    public override async Task<AuthResult> AuthorizeAsync(AuthorizationRequest authRequest,
        CancellationToken token = default)
    {
        return await ProcessTransactionAsync<AuthorizationRequest, AuthResult>(authRequest,
            CheckoutOperationType.Authorize, CancellationToken.None);
    }

    public override async Task<CapturePaymentResult> CaptureAsync(CapturePaymentRequest captureRequest,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<CheckoutPaymentProvider>();
        workspan.Log.Information("ENTERED: CheckoutPaymentProvider => CaptureAsync");

        await InitiateProviderAsync(captureRequest.SupportedGateway.Sandbox, captureRequest.SupportedGateway.Id, token);
        CapturePaymentResult retval = new();
        CaptureRequest request = new CaptureRequest
        {
            Amount = captureRequest.Amount,
            Reference = captureRequest.OrderId.ToString(),
            CaptureType = captureRequest.IsPartialAmount
                ? CaptureType.NonFinal
                : CaptureType.Final,
            Metadata = new Dictionary<string, object>()
            {
                {
                    "OrderId", captureRequest.OrderId.ToString()
                },
                {
                    "Mid", captureRequest.Mid.ToString()
                }
            },
        };

        try
        {
            CaptureResponse response =
                await _api.PaymentsClient().CapturePayment(captureRequest.TransactionToken, request);

            retval.RawResult = JsonConvert.SerializeObject(response);
            retval.Provider = this.CurrentPaymentProvider;
            retval.ProviderResponseCode = response.HttpStatusCode.ToString();
            retval.ProviderResponseMessage = "Approved";
            retval.ProviderTransactionToken = captureRequest.TransactionToken;
            retval.TransactionId = captureRequest.TransactionId;
            if (response.HttpStatusCode != 202)
            {
                retval.ProviderResponseMessage = "Failed";
                retval.AddError(response.HttpStatusCode.ToString());
                workspan.Log.Error("CheckoutPaymentProvider  Capture failed  with {error}",
                    response.HttpStatusCode.ToString());
            }
            else
            {
                workspan.Log.Information("CheckoutPaymentProvider  Capture  success");
            }
        }
        catch (Exception e)
        {
            retval.ProviderResponseMessage = "Failed";
            ProcessException(e, workspan, retval, "capture");
        }

        return retval;
    }

    public override async Task<SaleResult> SaleAsync(SaleRequest request, CancellationToken token = default)
    {
        return await ProcessTransactionAsync<SaleRequest, SaleResult>(request,
            CheckoutOperationType.Sale, CancellationToken.None);
    }

    public override async Task<ICreditPaymentResult> CreditAsync(ICreditPaymentRequest creditRequest,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<CheckoutPaymentProvider>();
        workspan.Log.Information("ENTERED: CheckoutPaymentProvider => CreditAsync");

        await InitiateProviderAsync(creditRequest.SupportedGateway.Sandbox, creditRequest.SupportedGateway.Id, token);
        CreditPaymentResult result = new();
        RefundRequest request = new RefundRequest()
        {
            Amount = creditRequest.Amount,
            Reference = creditRequest.OrderId.ToString(),
            Metadata = new Dictionary<string, object>()
            {
                {
                    "OrderId", creditRequest.OrderId.ToString()
                },

                {
                    "Mid", creditRequest.Mid.ToString()
                }
            },
        };

        try
        {
            RefundResponse response =
                await _api.PaymentsClient().RefundPayment(creditRequest.ProviderTransactionToken, request);
            result.RawResult = JsonConvert.SerializeObject(response);
            result.Provider = this.CurrentPaymentProvider;
            result.ProviderResponseCode = response.HttpStatusCode.ToString();
            result.ProviderResponseMessage = "Approved";
            result.ProviderTransactionToken = creditRequest.ProviderTransactionToken;
            result.PaymentInstrumentId = creditRequest.PaymentInstrumentId;
            result.InternalTransactionId = new Guid(response.Reference); //todo- check
            if (response.HttpStatusCode != 202)
            {
                result.AddError(response.HttpStatusCode.ToString());
                result.ProviderResponseMessage = "Failed";
                workspan.Log.Error("CheckoutPaymentProvider  CreditAsync {error}", response.HttpStatusCode.ToString());
            }
            else
            {
                workspan.Log.Information("CheckoutPaymentProvider => CreditAsync success");
            }
        }
        catch (Exception e)
        {
            result.ProviderResponseMessage = "Failed";
            ProcessException(e, workspan, result, "credit");
        }

        return result;
    }

    public override async Task<IVoidPaymentResult> VoidAsync(
        IVoidPaymentRequest voidPaymentRequest,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<CheckoutPaymentProvider>();
        workspan.Log.Information("ENTERED: CheckoutPaymentProvider => VoidAsync");
        await InitiateProviderAsync(voidPaymentRequest.SupportedGateway.Sandbox, voidPaymentRequest.SupportedGateway.Id,
            token);

        VoidPaymentResult result = new();
        VoidRequest request = new VoidRequest
        {
            Reference = voidPaymentRequest.OrderId.ToString(),
            Metadata = new Dictionary<string, object>
            {
                {"OrderId", voidPaymentRequest.OrderId.ToString()},
                {"Mid", voidPaymentRequest.Mid.ToString()}
            }
        };

        try
        {
            VoidResponse response =
                await _api.PaymentsClient().VoidPayment(voidPaymentRequest.ProviderTransactionToken, request);

            result.RawResult = JsonConvert.SerializeObject(response);

            result.Provider = this.CurrentPaymentProvider;
            result.ProviderResponseCode = response.HttpStatusCode.ToString();
            result.ProviderResponseMessage = "Approved";
            result.ProviderTransactionToken = voidPaymentRequest.ProviderTransactionToken;
            result.PaymentInstrumentId = voidPaymentRequest.PaymentInstrumentId;
            if (response.HttpStatusCode != 202)
            {
                result.AddError(response.HttpStatusCode.ToString());
                result.ProviderResponseMessage = "Failed";
                workspan.Log.Error("CheckoutPaymentProvider  VoidAsync {error}", response.HttpStatusCode.ToString());
            }
            else
            {
                workspan.Log.Information("CheckoutPaymentProvider => VoidAsync success");
            }
        }
        catch (Exception e)
        {
            result.ProviderResponseMessage = "Failed";
            ProcessException(e, workspan, result, "void");
        }

        return result;
    }


    public override async Task<IVerifyInstrumentResult> VerifyAsync(IVerifyInstrumentRequest payload,
        CancellationToken token = default)
    {
        return await ProcessTransactionAsync<IVerifyInstrumentRequest, VerifyInstrumentResult>(payload,
            CheckoutOperationType.Verify, CancellationToken.None);
    }

    public override async Task<(bool CanHandle, Guid? SupportedGatewayId)> CanHandleWebhookEventAsync(string body,
        IHeaderDictionary headers, CancellationToken token)
    {
        return (false, null);
    }

    //public bool IsSandbox;
    public override bool SupportCapture => true;
    public override bool SupportPartiallyRefund => true;
    public override bool SupportRefund => true;
    public override bool SupportVoid => true;
    public override bool SupportTokenization => true;
    public override bool RequirePostProcessPayment => true;
    public override bool SupportPayouts => false;
    public override string CurrentPaymentProvider => GatewayTypesConstants.Checkout;
    public override bool SupportsAch => false;
    public override bool SupportsCreditCards => true;
    public override bool SupportsCreditCardVerification => true;
    public override bool SupportsSubscription => false;
    public override bool SupportsStandaloneCredit => false;
    public override bool SupportsExternalThreeDS => true;

    public override CardBrand[] NetworkTokenCardBrands => new[]
    {
        CardBrand.MasterCard, CardBrand.Visa,
        CardBrand.Amex //todo-check
    };
}

public enum CheckoutOperationType
{
    Authorize,
    Sale,
    Verify
}