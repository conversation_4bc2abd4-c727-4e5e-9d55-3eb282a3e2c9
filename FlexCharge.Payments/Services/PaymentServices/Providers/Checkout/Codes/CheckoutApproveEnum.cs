namespace FlexCharge.Payments.Services.PaymentServices.Providers.Checkout;

public enum CheckoutApproveEnum
{
    Approved = 10000,

    /// <summary>
    /// Approved - Honor with ID (Debit Cards)
    /// </summary>
    Approved_HonorId = 10008,

    /// <summary>
    /// Approved - VIP (not used)
    /// </summary>
    Approved_VIP = 10011,
    Approved_Country_Club = 10076,
    Approved_Local_Banks = 10077,
    Approved_Commercial = 10081,
    Approved_Risky = 10100,
    Approved_Deferred_capture = 10200
}