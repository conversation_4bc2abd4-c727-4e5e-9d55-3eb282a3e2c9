using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Response.Errors;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Settlement;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Void;

public class PaysafeVoidResponse : IPaysafeResponse
{
    public string Id { get; set; }
    public string MerchantRefNum { get; set; }
    public bool DupCheck { get; set; }
    public string TxnTime { get; set; }
    public PaysafeVoidStatus Status { get; set; }
    public int Amount { get; set; }
    public int[] RiskReasonCode { get; set; }
    public Error Error { get; set; }

    public Link[] Links { get; set; }

    // [JsonExtensionData]
    // private IDictionary<string, JToken> _additionalData;
    [JsonExtensionData] private IDictionary<string, JsonElement> _additionalData;
}

public enum PaysafeVoidStatus
{
    RECEIVED,
    COMPLETED,
    FAILED
}