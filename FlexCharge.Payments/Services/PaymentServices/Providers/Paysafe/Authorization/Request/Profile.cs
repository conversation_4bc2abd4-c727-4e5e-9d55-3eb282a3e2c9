using System.ComponentModel.DataAnnotations;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization;

public class Profile
{
    [StringLength(80)] public string FirstName { get; set; }
    [StringLength(80)] public string LastName { get; set; }
    [EmailAddress] [StringLength(255)] public string Email { get; set; }

    /// <summary>
    /// This is a Customer ID that the merchant provides with the request
    /// for their own internal Customer identification.
    /// This value must be unique for each Customer belonging to a merchant.
    /// In NETBANX this is known as the Ref ID
    /// </summary>
    [StringLength(100)]
    public string MerchantCustomerId { get; set; }
}