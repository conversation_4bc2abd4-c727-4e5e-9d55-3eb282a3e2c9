using System.ComponentModel.DataAnnotations;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization;

public class StoredCredentials
{
    public CredentialType Type { get; set; }
    public Occurrence Occurrence { get; set; }

    /// <summary>
    /// Id of the initial Recurring Payment transaction.
    /// </summary>
    [StringLength(36)]
    public string InitialTransactionId { get; set; }

    [StringLength(256)] public string ExternalInitialTransactionId { get; set; }
}