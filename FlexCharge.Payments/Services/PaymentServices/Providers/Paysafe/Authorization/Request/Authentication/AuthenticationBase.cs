using System.ComponentModel.DataAnnotations;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Authentication;

public abstract class AuthenticationBase:IAuthentication
{
    [Required][Range(0, 10)]
    public int Eci { get; set; }
    [Required][StringLength(80)]
    public string Cavv { get; set; }
    [MinLength(5)]
    [MaxLength(8)]
    public string ThreeDSecureVersion { get; set; }

}