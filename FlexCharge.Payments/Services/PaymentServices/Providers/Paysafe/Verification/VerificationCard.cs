using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Card;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Common;
using Profile = AutoMapper.Profile;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Verification;

public class VerificationCard
{
    
    public CardType Type { get; set; }
    public string LastDigits { get; set; }
    public  CardExpiry CardExpiry  { get; set; }
}