using System.ComponentModel.DataAnnotations;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Refund;

public class PaysafeRefundRequest
{
    [Required]
    public string MerchantRefNum { get; set; }
    public int Amount { get; set; }
    public bool DupCheck { get; set; } = true;
    public  SplitPay  Splitpay { get; set; }
    /// <summary>
    /// ransaction identifier that can be used to reconcile this transaction with the acquirer/processor.
    /// Note: Not all processing gateways support this parameter. Contact your account
    /// manager for more information
    /// </summary>
    [StringLength(255)]
    public string GatewayReconciliationId  { get; set; }
}