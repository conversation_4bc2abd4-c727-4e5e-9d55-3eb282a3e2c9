namespace FlexCharge.Payments.Services.PaymentServices;

public static class NmiResponseCodes
{
    public const string TransactionApproved = "100";
    public const string TransactionDeclinedByProcessor = "200";
    public const string DoNotHonor = "201";
    public const string InsufficientFunds = "202";
    public const string OverLimit = "203";
    public const string TransactionNotAllowed = "204";
    public const string IncorrectPaymentInformation = "220";
    public const string NoSuchCardIssuer = "221";
    public const string NoCardNumberOnFile = "222";
    public const string ExpiredCard = "223";
    public const string InvalidExpirationDate = "224";
    public const string InvalidCardSecurityCode = "225";
    public const string InvalidPIN = "226";
    public const string CallIssuerForFurtherInfo = "240";
    public const string PickUpCard = "250";
    public const string LostCard = "251";
    public const string StolenCard = "252";
    public const string FraudulentCard = "253";
    public const string DeclinedWithFurtherInstructions = "260";
    public const string DeclinedStopAllRecurringPayments = "261";
    public const string DeclinedStopRecurringProgram = "262";
    public const string DeclinedUpdateCardholderData = "263";
    public const string DeclinedRetryInFewDays = "264";
    public const string TransactionRejectedByGateway = "300";
    public const string TransactionErrorByProcessor = "400";
    public const string InvalidMerchantConfiguration = "410";
    public const string MerchantAccountInactive = "411";
    public const string CommunicationError = "420";
    public const string CommunicationErrorWithIssuer = "421";
    public const string DuplicateTransactionAtProcessor = "430";
    public const string ProcessorFormatError = "440";
    public const string InvalidTransactionInformation = "441";
    public const string ProcessorFeatureNotAvailable = "460";
    public const string UnsupportedCardType = "461";
}