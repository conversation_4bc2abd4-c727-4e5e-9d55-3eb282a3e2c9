// namespace FlexCharge.Payments.Services.PaymentServices.Providers.Adyen;
//
// public class AdyenAuthorizationCodes
// {
//     /// <summary>
//     /// 	Inform the shopper that the payment was successful.
//     /// You also get a webhook with an updated payment
//     /// status that you can use to update your order management system.
//     /// </summary>
//     public const string Authorised = "Authorised";
//
//     public const string Canceled = "Canceled";
//     public const string Error = "Error";
//     public const string Refused = "Refused";
// }

