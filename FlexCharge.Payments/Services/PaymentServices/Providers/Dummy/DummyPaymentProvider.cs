#define IMITATE_PROCESSING_DELAY

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;


namespace FlexCharge.Payments.Services.PaymentServices.Providers.Dummy;

public class DummyPaymentProvider : PaymentProviderBase
{
    private readonly PostgreSQLDbContext _dbContext;

    public DummyPaymentProvider(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public override async Task<CapturePaymentResult> CaptureAsync(CapturePaymentRequest request,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<DummyPaymentProvider>()
            .Baggage("Mid", request.Mid)
            .Baggage("OrderId", request.OrderId)
            .Baggage("GatewayId", request.Gateway?.Id);


        workspan.Log.Information("CaptureAsync");
        workspan.Log.Information(JsonConvert.SerializeObject(request));

        CapturePaymentResult response = null;

        try
        {
#if IMITATE_PROCESSING_DELAY
            await Task.Delay(160, token); // Paysafe average processing time
#endif

            response = new CapturePaymentResult()
            {
                Provider = this.CurrentPaymentProvider,
                ProviderResponseCode = "0",
                ProviderResponseMessage = "CAPTURED",
                ProviderTransactionToken = Guid.NewGuid().ToString(),
                PaymentInstrumentId = request.PaymentInstrumentId
            };

            response.RawResult = JsonConvert.SerializeObject(response);
            if (response.RawResult != null) workspan.Log.Information(response.RawResult);

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Payload: {SerializeObject}", JsonConvert.SerializeObject(response));
            throw;
        }
    }

    public override async Task<SaleResult> SaleAsync(SaleRequest request, CancellationToken token)
    {
        using var workspan = Workspan.Start<DummyPaymentProvider>()
            .Baggage("Mid", request.Mid)
            .Baggage("OrderId", request.OrderId)
            .Baggage("GatewayId", request.Gateway?.Id);

        workspan.Log.Information("SaleAsync");
        workspan.Log.Information(JsonConvert.SerializeObject(request));

        var response = new SaleResult();
        try
        {
            await ProcessAuthorizationRequest(request, response, token);

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Payload: {SerializeObject}", JsonConvert.SerializeObject(request));
            throw;
        }
    }

    public override async Task<AuthResult> AuthorizeAsync(AuthorizationRequest request, CancellationToken token)
    {
        using var workspan = Workspan.Start<DummyPaymentProvider>()
            .Baggage("Mid", request.Mid)
            .Baggage("OrderId", request.OrderId)
            .Baggage("GatewayId", request.Gateway?.Id);


        workspan.Log.Information("AuthorizeAsync");
        workspan.Log.Information(JsonConvert.SerializeObject(request));

        var response = new AuthResult();
        try
        {
            await ProcessAuthorizationRequest(request, response, token);

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Payload: {SerializeObject}", JsonConvert.SerializeObject(request));
            throw;
        }
    }

    private async Task InitiateProviderAsync(bool isSandbox, Guid supportedGatewayId, CancellationToken token = default)
    {
        //log
        using var workspan = Workspan.Start<DummyPaymentProvider>()
            .Tag(nameof(isSandbox), isSandbox)
            .Tag(nameof(supportedGatewayId), supportedGatewayId)
            .LogEnterAndExit();

        var gateway = await _dbContext.SupportedGateways
            .Where(x => x.Id == supportedGatewayId && x.Sandbox == isSandbox)
            .FirstOrDefaultAsync(token);

        if (gateway == null)
            throw new Exception("Dummy gateway not found");
    }

    private async Task ProcessAuthorizationRequest(IAuthorizationRequest request, IAuthorizationResult response,
        CancellationToken token = default)
    {
        try
        {
            //Validate processor id matches the gateway
            await InitiateProviderAsync(request.SupportedGateway.Sandbox, request.SupportedGateway.Id, token);

            response.Provider = CurrentPaymentProvider;
            response.AuthorizationCode = Guid.NewGuid().ToString();
            response.ProviderTransactionToken = Guid.NewGuid().ToString();

#if IMITATE_PROCESSING_DELAY
            await Task.Delay(990, token); // Paysafe average processing time
#endif

            //Note: use card numbers from here: https://stripe.com/docs/testing
            switch (request.CreditCard.Number)
            {
                case "****************" when request.CreditCard.VerificationValue == "999":
                case "****************" when request.CreditCard.VerificationValue == "999": //invalid luhn
                case "****************" when request.CreditCard.VerificationValue == "999":
                    response.CvvCode = "M";
                    response.AvsCode = "Y";
                    response.ProviderResponseCode = "succeeded";
                    response.ProviderResponseMessage = "APPROVED";
                    break;
                case "2303779951000396":
                case "2303779951000347":
                case "2303779951000370":
                case "2303779951000529":
                case "2303779951000503":
                case "2320160000000001":
                case "2303770003400000":
                    response.CvvCode = request.CreditCard.VerificationValue == "999" ? "M" : "N";
                    response.AvsCode = "Y";
                    if (request.CreditCard.VerificationValue != "366")
                    {
                        response.ProviderResponseCode = "succeeded";
                        response.ProviderResponseMessage = "APPROVED";
                    }
                    else
                    {
                        response.AddError("generic_decline");
                        response.ProviderResponseCode = "generic_decline";
                        response.ProviderResponseMessage = "GENERIC DECLINE";
                    }

                    break;
                case "51002600235851":
                case "****************" when request.CreditCard.VerificationValue == "123":
                    response.CvvCode = "N";
                    response.AvsCode = "Y";
                    response.ProviderResponseCode = "succeeded";
                    response.ProviderResponseMessage = "APPROVED";
                    break;

                case "****************":
                    response.CvvCode = "N";
                    response.AvsCode = "Y";
                    response.ProviderResponseCode = "succeeded";
                    response.ProviderResponseMessage = "APPROVED";
                    break;
                case "****************":
                    response.CvvCode = "M";
                    response.AvsCode = "M";
                    response.CavvCode = "Y";
                    response.ProviderResponseCode = "succeeded";
                    response.ProviderResponseMessage = "APPROVED";
                    break;

                #region Spreedly Test Cards

                case "****************":
                    response.CvvCode = "M";
                    response.AvsCode = "M";
                    response.CavvCode = "Y";
                    response.ProviderResponseCode = "succeeded";
                    response.ProviderResponseMessage = "APPROVED";
                    break;

                #endregion


                #region Everi Test Cards

                case "****************":
                case "****************":
                case "****************":
                case "***************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                    response.CvvCode = "M";
                    response.AvsCode = "M";
                    response.CavvCode = "Y";
                    response.ProviderResponseCode = "succeeded";
                    response.ProviderResponseMessage = "APPROVED";
                    break;

                case "****************":
                case "****************":
                    response.AvsCode = "Y";
                    if (request.CreditCard.VerificationValue != "366")
                    {
                        response.CvvCode = "M";
                        response.ProviderResponseCode = "succeeded";
                        response.ProviderResponseMessage = "APPROVED";
                    }
                    else
                    {
                        response.CvvCode = "N";
                        response.ProviderResponseCode = "generic_decline";
                        response.ProviderResponseMessage = "GENERIC DECLINE";
                    }

                    break;

                #endregion

                #region Braintree Test Cards

                case "****************":
                    response.CvvCode = "M";
                    response.AvsCode = "M";
                    response.CavvCode = "Y";
                    if (IsCardExpiryDateValid(request))
                    {
                        response.ProviderResponseCode = "succeeded";
                        response.ProviderResponseMessage = "APPROVED";
                    }
                    else
                    {
                        response.ProviderResponseCode = "generic_decline";
                        response.ProviderResponseMessage = "GENERIC DECLINE";
                    }

                    break;

                #endregion

                case "****************" when request.CreditCard.VerificationValue == "112":
                    response.AddError("generic_decline");
                    response.CvvCode = "M";
                    response.AvsCode = "N";
                    response.ProviderResponseCode = "generic_decline";
                    response.ProviderResponseMessage = "GENERIC DECLINE";
                    break;
                case "****************" when request.CreditCard.VerificationValue == "113":
                    response.AddError("generic_decline");
                    response.CvvCode = "N";
                    response.AvsCode = "M";
                    response.ProviderResponseCode = "generic_decline";
                    response.ProviderResponseMessage = "GENERIC DECLINE";
                    break;
                case "****************" when request.CreditCard.VerificationValue != string.Empty:
                    response.AddError("generic_decline");
                    response.CvvCode = "M";
                    response.AvsCode = "M";
                    response.ProviderResponseCode = "generic_decline";
                    response.ProviderResponseMessage = "GENERIC DECLINE";
                    break;
                case "2303779951000446":
                case "2303779951000453":
                case "2303779951000420":
                case "2303779951000537":
                case "2303779951000511":
                    response.CvvCode = request.CreditCard.VerificationValue == "999" ? "M" : "N";
                    response.AvsCode = "M";
                    response.ProviderResponseCode = "succeeded";
                    response.ProviderResponseMessage = "APPROVED";
                    break;
                case "****************":
                    response.AddError("insufficient_funds");
                    response.CvvCode = request.CreditCard.VerificationValue == "999" ? "M" : "N";
                    response.AvsCode = "M";
                    response.ProviderResponseCode = "insufficient_funds";
                    response.ProviderResponseMessage = "INSUFFICIENT FUNDS";
                    break;

                case "****************":
                    response.AddError("do_not_honor");
                    response.CvvCode = request.CreditCard.VerificationValue == "999" ? "M" : "N";
                    response.AvsCode = "M";
                    response.ProviderResponseCode = "do_not_honor";
                    response.ProviderResponseMessage = "DO NOT HONOR";
                    break;

                case "****************":
                case "****************":
                //  case "****************":
                //case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                case "****************":
                    response.AddError("au_test_invalid_card");
                    response.CvvCode = request.CreditCard.VerificationValue == "999" ? "M" : "N";
                    response.AvsCode = "M";
                    response.ProviderResponseCode = "au_test_invalid_card";
                    response.ProviderResponseMessage = "AU TEST INVALID CARD";
                    break;
                case "****************":
                case "****************":
                    response.CvvCode = request.CreditCard.VerificationValue == "999" ? "M" : "N";
                    response.AvsCode = "M";
                    response.ProviderResponseCode = "succeeded";
                    response.ProviderResponseMessage = "AU TEST VALID CARD";
                    break;

                case "****************":
                    response.AddError("stolen_card");
                    response.CvvCode = request.CreditCard.VerificationValue == "999" ? "M" : "N";
                    response.AvsCode = "N";
                    response.ProviderResponseCode = "stolen_card";
                    response.ProviderResponseMessage = "STOLEN CARD";
                    break;

                case "****************":
                    response.AddError("do_not_try_again");
                    response.CvvCode = request.CreditCard.VerificationValue == "999" ? "M" : "N";
                    response.AvsCode = "N";
                    response.ProviderResponseCode = "do_not_try_again";
                    response.ProviderResponseMessage = "DO NOT TRY AGAIN";
                    break;

                case "****************":
                    response.AddError("exception");
                    throw new FlexChargeException("Exception occurred");
                    break;

                default:
                    response.AddError("generic_decline");
                    response.CvvCode = "N";
                    response.AvsCode = "N";
                    response.ProviderResponseCode = "generic_decline";
                    response.ProviderResponseMessage = "GENERIC DECLINE";
                    break;
            }

            //AU TEST with updated expiry dates , update returns different dates
            if (request.CreditCard.Number == "****************")
            {
                if (request.CreditCard.Month == 12 &&
                    (request.CreditCard.Year == 25 || request.CreditCard.Year == 2025))
                {
                    response.AddError("au_test_invalid_card");
                    response.CvvCode = request.CreditCard.VerificationValue == "999" ? "M" : "N";
                    response.AvsCode = "M";
                    response.ProviderResponseCode = "au_test_invalid_card";
                    response.ProviderResponseMessage = "AU TEST INVALID CARD";
                }
                else
                {
                    response.CvvCode = request.CreditCard.VerificationValue == "999" ? "M" : "N";
                    response.AvsCode = "M";
                    response.ProviderResponseCode = "succeeded";
                    response.ProviderResponseMessage = "AU TEST VALID CARD";
                }
            }

            response.RawResult = JsonConvert.SerializeObject(response);
            if (response.RawResult != null) Workspan.Current?.Log.Information(response.RawResult);

            var internalResponse = await DummyResponseMapper.GetMappedResponseAsync(response.ProviderResponseCode,
                response.ProviderResponseMessage);

            response.InternalResponseCode = internalResponse.MappedResponseCode;
            response.InternalResponseMessage = internalResponse.MappedResponseMessage;
            response.InternalResponseGroup = internalResponse.MappedResponseGroup.ToString();

            if (request.Modifiers != null)
            {
                if (request.Modifiers.RedactCvv)
                    response.CvvCode = "P";

                if (request.Modifiers.RedactAvs)
                    response.AvsCode = "U";
            }
        }
        catch (Exception e)
        {
            var internalResponse = DummyResponseMapper.ProviderExceptionError;
            response.InternalResponseCode = internalResponse.MappedResponseCode;
            response.InternalResponseMessage = internalResponse.MappedResponseMessage;
            response.InternalResponseGroup = internalResponse.MappedResponseGroup.ToString();
        }
    }

    bool IsCardExpiryDateValid(IAuthorizationRequest request)
    {
        var normalizedYear = request.CreditCard.Year > 99 ? request.CreditCard.Year : request.CreditCard.Year + 2000;

        return new DateTime(normalizedYear, request.CreditCard.Month, 1).AddMonths(1).AddDays(-1).Date >=
               DateTime.Now.Date;
    }

    public override async Task<IVoidPaymentResult> VoidAsync(IVoidPaymentRequest request, CancellationToken token)
    {
        using var workspan = Workspan.Start<DummyPaymentProvider>()
            .Baggage("Mid", request.Mid)
            .Baggage("OrderId", request.OrderId)
            .Baggage("GatewayId", request.Gateway?.Id);

        workspan.Log.Information("VoidAsync");
        workspan.Log.Information(JsonConvert.SerializeObject(request));

        var response = new VoidPaymentResult();

        try
        {
            response.ProviderResponseCode = "0";
            response.ProviderResponseMessage = "APPROVED";
            response.Provider = CurrentPaymentProvider;
            response.ProviderTransactionToken = Guid.NewGuid().ToString();
            response.RawResult = JsonConvert.SerializeObject(response);

            if (response.RawResult != null) workspan.Log.Information(response.RawResult);

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Payload: {SerializeObject}", JsonConvert.SerializeObject(request));
            throw;
        }
    }

    public override async Task<ICreditPaymentResult> CreditAsync(ICreditPaymentRequest request, CancellationToken token)
    {
        using var workspan = Workspan.Start<DummyPaymentProvider>()
            .Baggage("Mid", request.Mid)
            .Baggage("OrderId", request.OrderId)
            .Baggage("GatewayId", request.Gateway?.Id);


        workspan.Log.Information("Started: CreditAsync Payload: {SerializeObject}",
            JsonConvert.SerializeObject(request));

        var response = new CreditPaymentResult();
        try
        {
            if (request.Reason == "101")
            {
                response.ProviderResponseCode = "20010";
                response.ProviderResponseMessage = "DECLINED";
                response.AddError("generic_decline");
            }
            else
            {
                response.ProviderResponseCode = "0";
                response.ProviderResponseMessage = "APPROVED";
            }

            response.Provider = CurrentPaymentProvider;
            response.ProviderTransactionToken = Guid.NewGuid().ToString();
            response.RawResult = JsonConvert.SerializeObject(response);

            if (response.RawResult != null) workspan.Log.Information(response.RawResult);

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Payload: {SerializeObject}", JsonConvert.SerializeObject(request));
            throw;
        }
    }

    public override async Task<(bool CanHandle, Guid? SupportedGatewayId)> CanHandleWebhookEventAsync(string body,
        IHeaderDictionary headers, CancellationToken token)
    {
        return (false, null);
    }

    //public bool IsSandbox { get; }
    public override bool SupportCapture => true;
    public override bool SupportPartiallyRefund => true;
    public override bool SupportRefund => true;
    public override bool SupportVoid => true;
    public override bool SupportTokenization => true;
    public override bool RequirePostProcessPayment => false;

    public override bool SupportPayouts => false;

    //public override bool SupportSubMerchants => false;
    public override string CurrentPaymentProvider => GatewayTypesConstants.FlexChargeDummy;
    public override bool SupportsAch => false;
    public override bool SupportsCreditCards => true;
    public override bool SupportsCreditCardVerification => false;
    public override bool SupportsExternalThreeDS => true;
    public override bool SupportsSubscription => false;
    public override bool SupportsStandaloneCredit => false;
    public override CardBrand[] NetworkTokenCardBrands => null;
}