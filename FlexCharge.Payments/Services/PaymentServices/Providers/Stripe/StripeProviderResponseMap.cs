using FlexCharge.Payments.Services.PaymentServices.ResponseCodeMapping;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Stripe;

public class StripeProviderResponseMap : ProviderResponseMapBase
{
    protected override string MapName => "Stripe";

    public override MappingType MappingType => MappingType.ResponseCodeOnly;

    public override bool IsApprovedResponse(ProviderResponse providerResponse)
    {
        return providerResponse.ResponseCode == "succeeded";
    }
}