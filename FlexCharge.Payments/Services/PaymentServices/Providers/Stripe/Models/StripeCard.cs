namespace FlexCharge.Stripe.Models;

public class StripeCard
{
    /// <summary>
    /// The card's CVC. It is highly recommended to always include this value.
    /// </summary>
    public string Cvc { get; set; }

    /// <summary>
    /// Two-digit number representing the card's expiration month.
    /// </summary>

    public long? ExpMonth { get; set; }

    /// <summary>
    /// Four-digit number representing the card's expiration year.
    /// </summary>

    public long? ExpYear { get; set; }

    /// <summary>
    /// The card number, as a string without any separators.
    /// </summary>

    public string Number { get; set; }


    public string Token { get; set; }
}