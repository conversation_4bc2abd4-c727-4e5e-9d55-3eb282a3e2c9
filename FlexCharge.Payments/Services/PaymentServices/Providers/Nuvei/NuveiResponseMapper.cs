using System.Collections.Generic;
using System.ComponentModel;
using FlexCharge.Common.Shared.Payments;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Nuvei;

public class NuveiResponseMapper : IndexTableProviderResponseMapperBase
{
    public static void Initialize()
    {
    }

    static NuveiResponseMapper()
    {
        IndexTable = new Dictionary<string, MappedResponse>
        {
            {"APPROVED", InternalResponseMapper.GetMappedResponse("0")},
            {"1002", InternalResponseMapper.GetMappedResponse("1")},
            {"1156", InternalResponseMapper.GetMappedResponse("3")},
            {"1157", InternalResponseMapper.GetMappedResponse("4")},
            {"1158", InternalResponseMapper.GetMappedResponse("5")},
            {"1159", InternalResponseMapper.GetMappedResponse("7")},
            {"1162", InternalResponseMapper.GetMappedResponse("12")},
            {"1153", InternalResponseMapper.GetMappedResponse("13")},
            {"1101", InternalResponseMapper.GetMappedResponse("14")},
            {"1102", InternalResponseMapper.GetMappedResponse("14")},
            {"1103", InternalResponseMapper.GetMappedResponse("14")},
            {"1164", InternalResponseMapper.GetMappedResponse("15")},
            {"1165", InternalResponseMapper.GetMappedResponse("25")},
            {"1166", InternalResponseMapper.GetMappedResponse("28")},
            {"1167", InternalResponseMapper.GetMappedResponse("N7")},
            {"1168", InternalResponseMapper.GetMappedResponse("39")},
            {"1169", InternalResponseMapper.GetMappedResponse("41")},
            {"1170", InternalResponseMapper.GetMappedResponse("43")},
            {"2015", InternalResponseMapper.GetMappedResponse("51")},
            {"1171", InternalResponseMapper.GetMappedResponse("52")},
            {"1172", InternalResponseMapper.GetMappedResponse("53")},
            {"1005", InternalResponseMapper.GetMappedResponse("54")},
            {"1173", InternalResponseMapper.GetMappedResponse("55")},
            {"1174", InternalResponseMapper.GetMappedResponse("57")},
            {"1175", InternalResponseMapper.GetMappedResponse("58")},
            {"1176", InternalResponseMapper.GetMappedResponse("59")},
            {"1177", InternalResponseMapper.GetMappedResponse("61")},
            {"1233", InternalResponseMapper.GetMappedResponse("62")},
            {"1178", InternalResponseMapper.GetMappedResponse("86")},
            {"1179", InternalResponseMapper.GetMappedResponse("65")},
            {"1180", InternalResponseMapper.GetMappedResponse("2")},
            {"1181", InternalResponseMapper.GetMappedResponse("74")},
            {"1182", InternalResponseMapper.GetMappedResponse("75")},
            {"1246", InternalResponseMapper.GetMappedResponse("93")},
            {"1196", InternalResponseMapper.GetMappedResponse("N5")},
            {"1004", InternalResponseMapper.GetMappedResponse("6")},
            {"1023", InternalResponseMapper.GetMappedResponse("82")},
            {"1200", InternalResponseMapper.GetMappedResponse("R0")},
            {"1202", InternalResponseMapper.GetMappedResponse("R3")},
            {"FC-0006", InternalResponseMapper.GetMappedResponse("6")},
            {"FC-0070", InternalResponseMapper.GetMappedResponse("70")},
            {"FC-0091", InternalResponseMapper.GetMappedResponse("91")},
            {"FC-0090", InternalResponseMapper.GetMappedResponse("92")},
            {"FC-0005", InternalResponseMapper.GetMappedResponse("5")}
        };
    }

    public static MappedResponse GetMappedResponse(string providerResponseCode)
    {
        return GetMappedResponse(providerResponseCode, IndexTable);
    }

    private static Dictionary<string, MappedResponse> IndexTable { get; }
}