using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc.Validators;

namespace FlexCharge.Payments.Services.PaymentServices.ResponseCodeMapping;

public record ProviderResponseToInternalResponseRecord
{
    [RequiredIf(nameof(ProviderResponseMessage), null)]
    public string ProviderResponseCode { get; init; }

    [RequiredIf(nameof(ProviderResponseCode), null)]
    public string ProviderResponseMessage { get; init; }

    [Required] public string InternalResponseCode { get; init; }
    [Required] public string InternalResponseMessage { get; init; }
}