using System;
using System.Threading;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using System.Threading.Tasks;
using FlexCharge.Common.Response;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Models.ExternalTokenPayments;
using Microsoft.AspNetCore.Http;

namespace FlexCharge.Payments.Services.PaymentServices
{
    public interface IPaymentProvider
    {
        /// <summary>
        /// Makes an authorization request
        /// </summary>
        /// <param name="authRequest"></param>
        /// <param name="token"></param>
        /// <param name="AuthRequest">Authentication Request</param>
        /// <returns>Authentication Result</returns>
        Task<AuthResult> AuthorizeAsync(AuthorizationRequest authRequest, CancellationToken token = default);

        /// <summary>
        /// Captures payment
        /// </summary>
        /// <param name="request"></param>
        /// <param name="token"></param>
        /// <param name="capturePaymentRequest">Capture payment request</param>
        /// <returns>Capture payment result</returns>
        Task<CapturePaymentResult> CaptureAsync(CapturePaymentRequest request, CancellationToken token = default);

        /// <summary>
        /// Makes a credit card / debit card sale
        /// </summary>
        /// <param name="SaleRequest">Sale request</param>
        /// <returns>Sale Result result</returns>
        Task<SaleResult> SaleAsync(SaleRequest request, CancellationToken token = default);

        /// <summary>
        /// Makes a Echeck Credit
        /// </summary>
        /// <param name="EcheckCreditRequest">Echeck credit request</param>
        /// <returns>Credit Result result</returns>
        Task<AchTransferResult> AchCreditAsync(IAchTransferRequest request, CancellationToken token = default);

        /// <summary>
        /// Makes a ACH Debit
        /// </summary>
        /// <returns>Debit Result</returns>
        Task<AchTransferResult> AchDebitAsync(IAchTransferRequest request, CancellationToken token = default);

        Task<AchCancelResult> AchCancelAsync(IAchCancelPaymentRequest payload,
            CancellationToken token = default);

        /// <summary>
        /// Refunds a payment
        /// </summary>
        /// <param name="refundPaymentRequest">Request</param>
        /// <returns>Result</returns>
        Task<ICreditPaymentResult> CreditAsync(ICreditPaymentRequest request, CancellationToken token = default);

        /// <summary>
        /// Refunds a payment
        /// </summary>
        /// <param name="request"></param>
        /// <param name="supportedGateway"></param>
        /// <param name="token"></param>
        /// <param name="refundPaymentRequest">Request</param>
        /// <returns>Result</returns>
        Task<ICreditPaymentResult> StandaloneCreditAsync(ICreditPaymentRequest request,
            SupportedGateway supportedGateway,
            CancellationToken token = default);

        /// <summary>
        /// Voids a payment
        /// </summary>
        /// <param name="voidPaymentRequest">Request</param>
        /// <returns>Result</returns>
        Task<IVoidPaymentResult> VoidAsync(IVoidPaymentRequest voidPaymentRequest, CancellationToken token = default);

        // /// <summary>
        // /// Process recurring payment
        // /// </summary>
        // /// <param name="processPaymentRequest">Payment info required for an order processing</param>
        // /// <returns>Process payment result</returns>
        // ProcessPaymentResult ProcessRecurringPayment(ProcessPaymentRequest processPaymentRequest);
        //
        // /// <summary>
        // /// Cancels a recurring payment
        // /// </summary>
        // /// <param name="cancelPaymentRequest">Request</param>
        // /// <returns>Result</returns>
        // CancelRecurringPaymentResult CancelRecurringPayment(CancelRecurringPaymentRequest cancelPaymentRequest);

        /// <summary>
        /// Verify a payment instrument
        /// </summary>
        /// <param name="IVerifyInstrumentRequest">payload</param>
        /// <returns>Result</returns>
        Task<IVerifyInstrumentResult> VerifyAsync(IVerifyInstrumentRequest payload, CancellationToken token = default);

        /// <summary>
        /// Verify a payment instrument
        /// </summary>
        /// <param name="IVerifyInstrumentRequest">payload</param>
        /// <returns>Result</returns>
        //Task<IVerifyInstrumentResult> Verify3dAsync(IVerifyInstrumentRequest payload, CancellationToken token = default);


        /// <summary>
        /// Registers a new token/vault
        /// </summary>
        /// <param name="Token Request Model">Request</param>
        /// <returns>Result</returns>
        Task<ITokenResult> RegisterTokenAsync(ITokenRequest tokenRequest, CancellationToken token = default);

        Task<CreateSubscriptionResult> CreateSubscriptionAsync(CreateSubscriptionRequest request,
            CancellationToken token);

        Task<RequestResult<CancelExternalSubscriptionResult>> CancelSubscriptionAsync(SubscriptionGetRequest getRequest,
            CancellationToken token);

        Task<bool> UpdateCardAsync(Guid externalSubId, (string number, string year, string month) card);
        Task<bool> SetSubscriptionStatusAsync(Guid externalSubId, bool isSuccess, bool isActive);

        Task<SubscriptionAndLastInvoiceStatus> GetSubscriptionStatusAsync(SubscriptionGetRequest subscriptionGetRequest,
            CancellationToken token);


        Task<SubscriptionAndLastInvoiceStatus> GetAndSyncSubscriptionAsync(
            SubscriptionGetRequest request,
            CancellationToken token);

        Task<(bool CanHandle, Guid? SupportedGatewayId)> CanHandleWebhookEventAsync(string body,
            IHeaderDictionary headers, CancellationToken token);

        Task<IWebHookResult> HandleWebhookEventAsync(string body, Guid gatewayId, CancellationToken token);

        Task<IRefundCancelResult>
            CancelRefundAsync(ICancelRefundRequest payload, CancellationToken token);

        #region EXTERNAL TOKEN PAYMENTS

        /// <summary>
        /// Makes an external token debit payment
        /// </summary>
        /// <param name="request">Sale request</param>
        /// <param name="token"></param>
        /// <returns>Sale Result result</returns>
        Task<RequestResult<ChargePaymentResult>> ChargeAsync(ChargePaymentRequest request,
            CancellationToken token = default);

        Task<RequestResult<CanChargeExternalTokenResult>> CanChargeExternalTokenAsync(
            CanChargeExternalTokenRequest payload, CancellationToken token);

        Task<RequestResult<MarkInvoiceAsPaidOutOfBandResult>> MarkInvoiceAsPaidOutOfBandAsync(
            MarkInvoiceAsPaidOutOfBandRequest request,
            CancellationToken token = default);

        #endregion


        #region Properties

        /// <summary>
        /// Gets a value indicating whether capture is supported
        /// </summary>
        bool SupportCapture { get; }

        /// <summary>
        /// Gets a value indicating whether partial refund is supported
        /// </summary>
        bool SupportPartiallyRefund { get; }

        /// <summary>
        /// Gets a value indicating whether refund is supported
        /// </summary>
        bool SupportRefund { get; }

        /// <summary>
        /// Gets a value indicating whether void is supported
        /// </summary>
        bool SupportVoid { get; }

        /// <summary>
        /// Gets a value indicating whether tokenization/vault is supported
        /// </summary>
        bool SupportTokenization { get; }

        /// <summary>
        /// Gets a value indicating whether redirecting to third party URL is required
        /// </summary>
        bool RequirePostProcessPayment { get; }

        /// <summary>
        /// Gets a value indicating whether payouts is supported
        /// </summary>
        bool SupportPayouts { get; }

        /// <summary>
        /// Gets a payment method type
        /// </summary>
        string CurrentPaymentProvider => "adyen";

        public bool SupportsAch { get; }
        public bool SupportsCreditCards => true;

        public bool SupportsCreditCardVerification { get; }

        public bool SupportsExternalThreeDS => true;
        public bool SupportsSubscription { get; }
        public bool SupportsStandaloneCredit { get; }
        CardBrand[] NetworkTokenCardBrands => new[] {CardBrand.Visa, CardBrand.MasterCard};

        #endregion
    }
}