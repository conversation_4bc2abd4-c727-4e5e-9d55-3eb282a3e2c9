using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Contracts.Commands.Common;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Contracts.Common;
using FlexCharge.Contracts.Vault;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using BillingAddress = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.BillingAddress;
using ShippingAddress = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.ShippingAddress;

namespace FlexCharge.Payments.Services.PaymentServices.Interfaces
{
    public interface IChargePaymentRequest : IMerchantRequest
    {
        public string Token { get; set; }

        public bool? IsRebilling { get; set; }

        public Guid PaymentInstrumentId { get; set; }
        public string SenseKey { get; set; }

        public bool IsCit { get; set; }
    }
}