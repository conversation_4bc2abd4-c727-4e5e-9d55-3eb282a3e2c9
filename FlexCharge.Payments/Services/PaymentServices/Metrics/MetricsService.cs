using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using EntityFramework.Exceptions.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Payments.Entities;
using Microsoft.EntityFrameworkCore;
using Org.BouncyCastle.Crypto.Tls;

namespace FlexCharge.Payments.Services.PaymentServices;

public class MetricsService
{
    private readonly PostgreSQLDbContext _context;

    public MetricsService(PostgreSQLDbContext context)
    {
        _context = context;
    }

    public async Task UpdateMetrics(MetricsUpdateDTO message, CancellationToken cancellationToken)
    {
        try
        {
            Workspan
                .Current!
                .Baggage("Mid", message.Mid)
                .Baggage("OrderId", message.OrderId)
                .LogEnterAndExit();

            var supportedGateway = await GetSupportedGateway(message, cancellationToken);

            var paymentInstrument = message.PaymentInstrumentId.HasValue
                ? await _context.PaymentInstruments
                    .SingleOrDefaultAsync(x => x.Id == message.PaymentInstrumentId)
                : null;

            //first day of the month
            var start = DateTime.UtcNow.Date.AddDays(-DateTime.UtcNow.Date.Day + 1);
            //00:00:00 of the day of next month
            var end = start.AddMonths(1).AddSeconds(-1).ToUniversalTime();

            var bin = paymentInstrument?.Bin;

            var metricsId = supportedGateway.MetricsId ??
                            await InitiateCycleMetrics(cancellationToken, start, end, supportedGateway);

            // If gateway is quarantined, reset the error count and remove quarantine (approved transaction)
            supportedGateway.Metrics.Quarantine_ErrorsCount = 0;
            supportedGateway.Metrics.Quarantine_AddedOn = null;

            await _context.Metrics.Where(m => m.Id == metricsId)
                .ExecuteUpdateAsync(settings => settings
                        .SetProperty(a => a.LastTransactionTimeStamp, a => message.TransactionTimestamp)
                        .SetProperty(a => a.CycleStartDate,
                            a => a.CycleStartDate < start ? start : a.CycleStartDate)
                        .SetProperty(a => a.CycleEndDate,
                            a => a.CycleEndDate < end ? end : a.CycleEndDate)
                        .SetProperty(a => a.TotalTransactionCount,
                            a => a.CycleStartDate < start
                                ? 1
                                : a.TotalTransactionCount + 1)
                        .SetProperty(a => a.TotalTransactionAmount,
                            a => a.CycleStartDate < start ? message.Amount : a.TotalTransactionAmount + message.Amount)
                        .SetProperty(a => a.TotalDailyTransactionCount,
                            a => a.TotalDailyTransactionCount + 1)
                        .SetProperty(a => a.TotalDailyTransactionAmount,
                            a => a.TotalDailyTransactionAmount + message.Amount)
                        .SetProperty(a => a.MasterCardCurrentTransactionsCount,
                            a => bin != null && (bin.StartsWith("5") || bin.StartsWith("2"))
                                ? (a.CycleStartDate < start ? 1 : a.MasterCardCurrentTransactionsCount + 1)
                                : a.MasterCardCurrentTransactionsCount)
                        .SetProperty(a => a.MasterCardCurrentTransactionsValue,
                            a => bin != null && (bin.StartsWith("5") || bin.StartsWith("2"))
                                ? (a.CycleStartDate < start
                                    ? message.Amount
                                    : a.MasterCardCurrentTransactionsValue + message.Amount)
                                : a.MasterCardCurrentTransactionsValue)
                        .SetProperty(a => a.VisaCurrentTransactionsCount,
                            a => bin != null && bin.StartsWith("4")
                                ? (a.CycleStartDate < start ? 1 : a.VisaCurrentTransactionsCount + 1)
                                : a.VisaCurrentTransactionsCount)
                        .SetProperty(a => a.VisaCurrentTransactionsValue,
                            a => bin != null && bin.StartsWith("4")
                                ? (a.CycleStartDate < start
                                    ? message.Amount
                                    : a.VisaCurrentTransactionsValue + message.Amount)
                                : a.VisaCurrentTransactionsValue)
                        .SetProperty(a => a.Quarantine_ErrorsCount,
                            a => supportedGateway.Metrics.Quarantine_ErrorsCount)
                        .SetProperty(a => a.Quarantine_AddedOn, a => supportedGateway.Metrics.Quarantine_AddedOn)
                        .SetProperty(a => a.ModifiedOn, a => DateTime.UtcNow),
                    cancellationToken);
        }
        catch (Exception e)
        {
            Workspan.Current!.RecordFatalException(e);
        }
    }

    private async Task<SupportedGateway> GetSupportedGateway(MetricsUpdateDTO message,
        CancellationToken cancellationToken)
    {
        var gateway = await _context.SupportedGateways
            .Include(x => x.Metrics)
            .Where(x => x.Id == message.SupportedGatewayId)
            .SingleOrDefaultAsync(cancellationToken);
        return gateway;
    }

    /// <summary>
    /// Updates only declined transaction metrics in case of a failed authorization.
    /// </summary>
    public async Task UpdateDeclinedMetrics(MetricsUpdateDTO message, CancellationToken cancellationToken)
    {
        try
        {
            Workspan
                .Current!
                .Baggage("Mid", message.Mid)
                .Baggage("OrderId", message.OrderId)
                .LogEnterAndExit();

            var gateway = await GetSupportedGateway(message, cancellationToken);

            var paymentInstrument = message.PaymentInstrumentId.HasValue
                ? await _context.PaymentInstruments
                    .SingleOrDefaultAsync(x => x.Id == message.PaymentInstrumentId)
                : null;

            //first day of the month
            var start = DateTime.UtcNow.Date.AddDays(-DateTime.UtcNow.Date.Day + 1);
            //00:00:00 of the day of next month
            var end = start.AddMonths(1).AddSeconds(-1).ToUniversalTime();

            var bin = paymentInstrument?.Bin;

            var metricsId = gateway.MetricsId ?? await InitiateCycleMetrics(cancellationToken, start, end, gateway);

            if (metricsId == Guid.Empty)
                throw new ArgumentException("Invalid Metrics ID", nameof(metricsId));

            Workspan.Current!.Log.Information(
                $"Updating declined metrics for MetricsID: {metricsId}, Declined Amount: {message.Amount}");

            await _context.Metrics.Where(m => m.Id == metricsId)
                .ExecuteUpdateAsync(settings => settings
                        .SetProperty(a => a.TotalFailureCount, a => a.TotalFailureCount + 1)
                        .SetProperty(a => a.Quarantine_ErrorsCount,
                            a => 0) // Reset quarantine error count on declined transaction so its not comulative
                        .SetProperty(a => a.Quarantine_AddedOn,
                            a => null) // Reset quarantine error count on declined transaction so its not comulative
                        .SetProperty(a => a.TotalFailureAmount, a => a.TotalFailureAmount + message.Amount)
                        .SetProperty(a => a.ModifiedOn, a => DateTime.UtcNow),
                    cancellationToken);
        }
        catch (Exception e)
        {
            Workspan.Current!.RecordFatalException(e);
        }
    }

    public async Task RemoveQuarantine(MetricsUpdateDTO message, CancellationToken cancellationToken)
    {
        try
        {
            Workspan
                .Current!
                .Baggage("Mid", message.Mid)
                .Baggage("OrderId", message.OrderId)
                .LogEnterAndExit();

            var gateway = await GetSupportedGateway(message, cancellationToken);

            var paymentInstrument = message.PaymentInstrumentId.HasValue
                ? await _context.PaymentInstruments
                    .SingleOrDefaultAsync(x => x.Id == message.PaymentInstrumentId)
                : null;

            //first day of the month
            var start = DateTime.UtcNow.Date.AddDays(-DateTime.UtcNow.Date.Day + 1);
            //00:00:00 of the day of next month
            var end = start.AddMonths(1).AddSeconds(-1).ToUniversalTime();

            var bin = paymentInstrument?.Bin;

            var metricsId = gateway.MetricsId ?? await InitiateCycleMetrics(cancellationToken, start, end, gateway);

            if (metricsId == Guid.Empty)
                throw new ArgumentException("Invalid Metrics ID", nameof(metricsId));

            Workspan.Current!.Log.Information(
                $"Updating declined metrics for MetricsID: {metricsId}, Declined Amount: {message.Amount}");

            await _context.Metrics.Where(m => m.Id == metricsId)
                .ExecuteUpdateAsync(settings => settings
                        .SetProperty(a => a.TotalFailureCount, a => a.TotalFailureCount + 1)
                        .SetProperty(a => a.TotalFailureAmount, a => a.TotalFailureAmount + message.Amount)
                        .SetProperty(a => a.ModifiedOn, a => DateTime.UtcNow),
                    cancellationToken);
        }
        catch (Exception e)
        {
            Workspan.Current!.RecordFatalException(e);
        }
    }

    public async Task UpdateErrorMetrics(MetricsUpdateDTO message, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<MetricsService>()
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId)
            .LogEnterAndExit();

        try
        {
            var supportedGateway = await GetSupportedGateway(message, cancellationToken);

            var paymentInstrument = message.PaymentInstrumentId.HasValue
                ? await _context.PaymentInstruments
                    .SingleOrDefaultAsync(x => x.Id == message.PaymentInstrumentId)
                : null;

            //first day of the month
            var start = DateTime.UtcNow.Date.AddDays(-DateTime.UtcNow.Date.Day + 1);
            //00:00:00 of the day of next month
            var end = start.AddMonths(1).AddSeconds(-1).ToUniversalTime();

            var bin = paymentInstrument?.Bin;

            var metricsId = supportedGateway.MetricsId ??
                            await InitiateCycleMetrics(cancellationToken, start, end, supportedGateway);

            if (metricsId == Guid.Empty)
                throw new ArgumentException("Invalid Metrics ID", nameof(metricsId));

            supportedGateway.Metrics.Quarantine_ErrorsCount++;
            supportedGateway.Metrics.TotalErrorCount++;
            supportedGateway.Metrics.TotalErrorAmount += message.Amount;

            var nowUtc = DateTime.UtcNow;
            var quarantineAddedOn = supportedGateway.Metrics.Quarantine_AddedOn;

            // Check if gateway is in quarantine
            if (quarantineAddedOn.HasValue)
            {
                var elapsed = nowUtc - quarantineAddedOn.Value;

                if (elapsed.TotalHours >= 12)
                {
                    // If 12+ hours elapsed and a new failure occurs, extend by 1 hour
                    supportedGateway.Metrics.Quarantine_AddedOn = nowUtc.AddHours(1);
                }
                // Else: Do nothing, keep the quarantine as is.
            }
            else if (supportedGateway.Metrics.Quarantine_ErrorsCount >= 10)
            {
                // If error count reaches threshold, start quarantine
                supportedGateway.Metrics.Quarantine_AddedOn = nowUtc;
            }

            workspan.Log.Information(
                "Updating declined metrics for MetricsID: {MetricsId}, Declined Amount: {MessageAmount}", metricsId,
                message.Amount);

            await _context.Metrics
                .Where(m => m.Id == supportedGateway.Metrics.Id)
                .ExecuteUpdateAsync(settings => settings
                        .SetProperty(a => a.TotalErrorCount, a => supportedGateway.Metrics.TotalErrorCount)
                        .SetProperty(a => a.TotalErrorAmount, a => supportedGateway.Metrics.TotalErrorAmount)
                        .SetProperty(a => a.Quarantine_AddedOn, a => supportedGateway.Metrics.Quarantine_AddedOn)
                        .SetProperty(a => a.Quarantine_ErrorsCount,
                            a => supportedGateway.Metrics.Quarantine_ErrorsCount),
                    cancellationToken);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
        }
    }

    private async Task<Guid> InitiateCycleMetrics(CancellationToken cancellationToken, DateTime start, DateTime end,
        SupportedGateway gateway)
    {
        try
        {
            var metrics = new CycleMetrics()
            {
                //month start date
                CycleStartDate = start,
                CycleEndDate = end,
                TotalTransactionCount = 0,
                TotalTransactionAmount = 0,
                SupportGatewayId = gateway.Id
            };

            gateway!.Metrics = metrics;
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (UniqueConstraintException ue)
        {
            Workspan.Current!.Log.Information("metrics already exists");
        }

        var supportedGateway = await _context.SupportedGateways
            .Where(x => x.Id == gateway.Id)
            .SingleOrDefaultAsync(cancellationToken);

        var metricsId = supportedGateway.MetricsId;

        if (!metricsId.HasValue || metricsId == Guid.Empty)
            throw new Exception("Cannot create metrics");

        return metricsId.Value;
    }
}