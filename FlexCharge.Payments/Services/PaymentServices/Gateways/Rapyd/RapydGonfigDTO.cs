using System.ComponentModel.DataAnnotations;
using FlexCharge.Contracts.Common;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace FlexCharge.Payments.Services.PaymentServices.Gateways;

public class RapydGonfigDTO
{
    [Required] public string SecretKey { get; set; }
    [Required] public string WalletId { get; set; }
    [Required] public string AccessKey { get; set; }

    [Required]
    [Newtonsoft.Json.JsonConverter(typeof(StringEnumConverter))]
    public CurrencyEnum BalanceCurrency { get; set; }

    [JsonProperty("SupportedCurrencies", ItemConverterType = typeof(StringEnumConverter))]
    [Required]
    public CurrencyEnum[] SupportedCurrencies { get; set; }
}