using System.Collections.Generic;
using System.Text.Json;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd;
using FlexCharge.PaymentsUtils;

namespace FlexCharge.Payments.Services.PaymentServices.Gateways;

public class RapydGatewayService : BaseGatewayService<RapydGonfigDTO>
{
    public RapydGatewayService(PostgreSQLDbContext context) : base(context)
    {
    }


    protected override string NameIdentifier => GatewayTypesConstants.Rapyd;

    protected override void MapConfigToGateway(RapydGonfigDTO dto, SupportedGateway gateway)
    {
        gateway.SecretKey = dto.SecretKey;
        RapydConfiguration configuration = new()
        {
            SecretKey = dto.SecretKey,
            AccessKey = dto.AccessKey,
            BalanceCurrency = dto.BalanceCurrency,
            SupportedCurrencies = dto.SupportedCurrencies,
            WalletId = dto.WalletId
        };
        gateway.Configuration = JsonSerializer.Serialize(configuration, _jsonOptions);
    }

    protected override void MapGatewayToConfig(SupportedGateway gateway, RapydGonfigDTO dto)
    {
        var configuration =
            JsonSerializer.Deserialize<RapydConfiguration>(gateway.Configuration, _jsonOptions);

        dto.SecretKey = configuration.SecretKey;
        dto.WalletId = configuration.WalletId;
        dto.AccessKey = configuration.AccessKey;
        dto.BalanceCurrency = configuration.BalanceCurrency.Value; //todo: check if this is correct
        dto.SupportedCurrencies = configuration.SupportedCurrencies;
    }


    protected override List<ListLookup> GetListsInternal()
    {
        return null;
    }
}