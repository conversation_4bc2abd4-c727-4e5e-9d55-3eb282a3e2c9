using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.CustomAttributes;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Contracts.Common;
using Swashbuckle.AspNetCore.Annotations;

namespace FlexCharge.Payments.Services.PaymentServices.Gateways;

public class CapabilitiesDTO
{
    [Required]
    // [DefaultValue("Visa, MasterCard")]
    public CardBrand[] CardBrands { get; set; }

    // public string? CardTypes { get; set; }
    // public string? PaymentMethods { get; set; }
    // public string? PaymentTypes { get; set; }

    [Required]
    //[DefaultValue("USD")]
    public CurrencyEnum[] SupportedCurrencies { get; set; }

    [Required]
    //[DefaultValue("USA")] 
    public string[] SupportedCountries { get; set; }


    [Required] public bool SupportMIT { get; set; }
    [Required] public bool SupportCIT { get; set; }
    [Required] public bool Support3DS { get; set; }
    [Required] public bool Support3DS2 { get; set; }

    [DefaultValue(false)] public bool UseDefaultDupCheckSeconds { get; set; }

    [Required] [DefaultValue(true)] public bool UseDynamicDescriptor { get; set; }
    [Required] [DefaultValue(false)] public bool UseDynamicDescriptorSuffix { get; set; }

    public bool SupportTokenization { get; set; }

    [DefaultValue(false)] public bool SupportNetworkTokenization { get; set; }

    public bool SupportNetworkTokenization3DS { get; set; }

    public bool SupportExternalNetworkTokenPayment { get; set; }
    public int? MasterCard_MonthlyMaxTransactionsCount { get; set; }
    public int? MasterCard_MonthlyMaxValue { get; set; }

    public int? Visa_MonthlyMaxTransactionsCount { get; set; }
    public int? Visa_MonthlyMaxValue { get; set; }

    public int? TotalMonthlyMaxTransactionsCount { get; set; }

    public int? TotalMonthlyMaxTransactionsAmount { get; set; }

    public int? TotalDailyMaxTransactionsCount { get; set; }
    public int? TotalDailyMaxTransactionsValue { get; set; }

    public bool SupportStandaloneCredit { get; set; }
}