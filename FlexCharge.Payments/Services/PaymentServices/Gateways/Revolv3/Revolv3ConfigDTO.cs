using System.ComponentModel.DataAnnotations;
using FlexCharge.Payments.Services.PaymentServices.Providers.Revolv3.Model;

namespace FlexCharge.Payments.Services.PaymentServices.Gateways.Revolv;

public class Revolv3ConfigDTO
{
    public string SecretKey { get; set; }
    [Required] public string ClientId { get; set; }
    [Required] public string StaticKey { get; set; }
    [Url] public string WebhookUrl { get; set; }
    public string WebhookSecret { get; set; }
    [Required] public Revolv3Processor Processor { get; set; }
}