using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Configuration;
using FlexCharge.Payments.Entities.JsonbModels;
using Swashbuckle.AspNetCore.Annotations;

namespace FlexCharge.Payments.Services.PaymentServices.Gateways;

public class GatewayDTO<TConfig> where TConfig : class, new()
{
    [SwaggerSchema(ReadOnly = true)]
    [ReadOnly(true)]
    [Required]
    public Guid Id { get; set; }

    [Required] [MinLength(3)] public string Name { get; set; }

    // [Required]
    // public abstract string NameIdentifier { get; }

    [Required] [StringLength(4)] public string Mcc { get; set; }

    [DefaultValue(true)] public bool IsActive { get; set; }

    [Required] public bool Sandbox { get; set; }

    // [RegularExpression(@"^[{]?[0-9a-fA-F]{8}-([0-9a-fA-F]{4}-){3}[0-9a-fA-F]{12}[}]?$",
    //     ErrorMessage = "Guid is not valid")]
    [Required] public Guid? PartnerId { get; set; }


    [MinLength(3)] public string ProcessorId { get; set; }

    public string ProcessorPlatform { get; set; }

    // public string User { get; set; }
    // public string Password { get; set; }
    // public string SecretKey { get; set; }
    //  public string AuthenticationType { get; set; }

    //[DefaultValue(false)] public bool IsOrchestratorGateway { get; set; }


    // In case prefix is set be the processor and we can't change it
    // public string FixedDescriptorPrefix { get; set; }
    public string MasterCardDescriptorPrefix { get; set; }
    public string VisaDescriptorPrefix { get; set; }

    // [RegularExpression(@"^[a-zA-Z''-'\s]{1,40}$", 
    //     ErrorMessage = "Characters are not allowed.")]
    // [RegularExpression(@"^[{]?[0-9a-fA-F]{8}-([0-9a-fA-F]{4}-){3}[0-9a-fA-F]{12}[}]?$",
    //     ErrorMessage = "Guid is not valid")]
    // public string? MetricsId { get; set; }

    // [RegularExpression(@"^[{]?[0-9a-fA-F]{8}-([0-9a-fA-F]{4}-){3}[0-9a-fA-F]{12}[}]?$",
    //     ErrorMessage = "Guid is not valid")]

    public Guid? MetaId { get; set; }


    //
    // public string MidNumber { get; set; }
    // public string CAID { get; set; }
    // public string BIN { get; set; }
    //
    // public string Domain { get; set; }
    // public string Category { get; set; }

    //public string Version { get; set; }


    [Required] public CapabilitiesDTO Capabilities { get; set; }

    [Required] public TConfig Configuration { get; set; }
}