using System.Collections.Generic;
using FlexCharge.Common.Shared.Payments;

namespace FlexCharge.Payments.Services.PaymentServices;

public abstract class IndexTableProviderResponseMapperBase
{
    protected static MappedResponse GetMappedResponse(string providerResponseCode,
        Dictionary<string, MappedResponse> indexTable)
    {
        if (providerResponseCode == "-3")
            return InternalResponseMapper.GetMappedResponse("-3");

        if (providerResponseCode == null)
            return InternalResponseMapper.GetMappedResponse("-2");

        return indexTable.TryGetValue(providerResponseCode, out var response)
            ? response
            : InternalResponseMapper.GetMappedResponse("-1");
    }
}