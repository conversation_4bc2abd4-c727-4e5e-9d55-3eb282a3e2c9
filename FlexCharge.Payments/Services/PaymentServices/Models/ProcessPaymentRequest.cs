using FlexCharge.Payments.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FlexCharge.Common.SensitiveData.Obfuscation;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels
{
    [Serializable]
    public class ProcessPaymentRequest
    {
        public ProcessPaymentRequest()
        {
            CustomValues = new Dictionary<string, object>();
        }

        #region Payment method specific properties

        /// <summary>
        /// Gets or sets a credit card type (Visa, Master Card, etc...). We leave it empty if not used by a payment gateway
        /// </summary>
        public string CreditCardType { get; set; }

        /// <summary>
        /// Gets or sets a credit card owner name
        /// </summary>
        public string CreditCardName { get; set; }

        /// <summary>
        /// Gets or sets a credit card number
        /// </summary>
        [SensitiveData(ObfuscationType.CreditCardMaskAllDigits)]
        public string CreditCardNumber { get; set; }

        /// <summary>
        /// Gets or sets a credit card expire year
        /// </summary>
        [SensitiveData(ObfuscationType.CreditCardMaskAllDigits)]
        public int CreditCardExpireYear { get; set; }

        /// <summary>
        /// Gets or sets a credit card expire month
        /// </summary>
        [SensitiveData(ObfuscationType.CreditCardMaskAllDigits)]
        public int CreditCardExpireMonth { get; set; }

        /// <summary>
        /// Gets or sets a credit card CVV2 (Card Verification Value)
        /// </summary>
        [SensitiveData(ObfuscationType.MaskAllChars)]
        public string CreditCardCvv2 { get; set; }

        #endregion

        /// <summary>
        /// You can store any custom value in this property
        /// </summary>
        public Dictionary<string, object> CustomValues { get; set; }
    }
}