using FlexCharge.Contracts.Common;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;

public class AddressBase
{
    public string FirstName { get; set; }
    public string LastName { get; set; }

    public string Address1 { get; set; }
    public string Address2 { get; set; }
    public string City { get; set; }
    public string State { get; set; }
    public string Zip { get; set; }

    /// <summary>
    /// Country code
    /// </summary>
    /// ISO 3166-1 alpha-2 country code (2-letter)
    public string Country { get; set; }

    public string PhoneNumber { get; set; }
}

public static class AddressBaseExtensions
{
    public static T InitializeFrom<T>(this T address, Contracts.Common.BillingAddress initializeFrom)
        where T : BillingAddress
    {
        address.FirstName = initializeFrom.FirstName;
        address.LastName = initializeFrom.LastName;
        address.Address1 = initializeFrom.Address1;
        address.Address2 = initializeFrom.Address2;
        address.City = initializeFrom.City;
        address.State = initializeFrom.State;
        address.Zip = initializeFrom.Zip;
        address.Country = initializeFrom.Country;
        address.PhoneNumber = initializeFrom.Phone;
        return address;
    }
}