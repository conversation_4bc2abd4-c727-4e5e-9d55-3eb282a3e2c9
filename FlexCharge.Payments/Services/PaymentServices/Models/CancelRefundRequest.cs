using System;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;

namespace FlexCharge.Payments.Services.PaymentServices.Models;

public class CancelRefundRequest : ICancelRefundRequest
{
    public Guid Mid { get; set; }
    public Guid RefundTransactionId { get; set; }
    public string ProviderToken { get; set; }
    public bool IsStandaloneRefund { get; set; }
    public Guid GatewayId { get; set; }
    public Guid SupportedGatewayId { get; set; }
    public Guid OrderId { get; set; }
    public string Reason { get; set; }
    public int Amount { get; set; }
}