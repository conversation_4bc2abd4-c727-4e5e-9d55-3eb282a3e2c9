using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;


namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels
{
    public class FingerprintInstrumentRequest
    {
        public string Token { get; set; }
        public Guid PaymentInstrumentId { get; set; }
        public bool UseLatestInstrument { get; set; }
    }
}