using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Common.SensitiveData.Obfuscation;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;


namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels
{
    public class TokenRequest : ITokenRequest
    {
        public TokenRequest()
        {
        }

        public TokenRequest(string RequestId)
        {
            _requestId = RequestId;
        }

        public string _requestId { get; private set; }
        public string Id { get; set; }
        public string OrderId { get; set; }
        public string MerchantId { get; set; }
        public string ReportGroup { get; set; }
        public bool IsCheck21 { get; set; }
        public AccountForToken AccountForToken { get; set; }
        public CardForToken CardForToken { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public virtual string Name { get; set; }
    }

    public class AccountForToken
    {
        [SensitiveData(ObfuscationType.HeadVisible, LeaveUnobfuscatedSymbols = 4)]
        public string AccountNumber { get; set; }

        [SensitiveData(ObfuscationType.HeadVisible, LeaveUnobfuscatedSymbols = 4)]
        public string RoutingNumber { get; set; }

        public string AccountType { get; set; }
        public string AccountName { get; set; }

        public string Last4Digits
        {
            get { return AccountNumber.Substring(AccountNumber.Length - 4); }
        }
    }

    public class CardForToken
    {
        [SensitiveData(ObfuscationType.CreditCardMaskAllDigits)]
        public string AccountNumber { get; set; }

        [SensitiveData(ObfuscationType.MaskAllChars)]
        public string Expire { get; set; }

        [SensitiveData(ObfuscationType.MaskAllChars)]
        public string CardValidationNumber { get; set; }

        public string Last4Digits
        {
            get { return AccountNumber.Substring(AccountNumber.Length - 4); }
        }
    }
}