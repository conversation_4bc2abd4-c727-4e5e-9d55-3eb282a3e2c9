using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FlexCharge.Common.Response;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels
{
    public class CapturePaymentResult : BaseResult, ICapturePaymentResult
    {
        public Guid TransactionId { get; set; }

        public Guid PaymentInstrumentId { get; set; }
        
    }
}
