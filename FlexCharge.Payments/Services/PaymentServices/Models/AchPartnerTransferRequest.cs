using System;
using FlexCharge.Common.SensitiveData.Obfuscation;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.SVBAchServices.Models;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;

public class AchPartnerTransferRequest : PartnerBaseRequest, IAchTransferRequest
{
    public bool IsVerifiedAch { get; set; }

    public string IdentificationNumber { get; set; }

    public string Processor { get; set; }
    public string Provider { get; set; }

    public Guid ProviderId { get; set; }
    public string Currency { get; set; }
    public string CompanyEntryDescription { get; set; }
    public string EffectiveEntryDate { get; set; } = DateTime.Now.Date.ToString("yyyy-MM-dd");

    public SecCodeEnum SecType { get; set; }
    public IAchAccount Sender { get; set; }
    public IAchAccount Receiver { get; set; }
}

public class AchTransferPartnerResponse
{
    public Guid TransactionId { get; set; }
    public bool Success { get; set; }
    public string Message { get; set; }
    public string ResponseCode { get; set; }
    public string Gateway { get; set; }
    public string Provider { get; set; }
    public string Processor { get; set; }
}