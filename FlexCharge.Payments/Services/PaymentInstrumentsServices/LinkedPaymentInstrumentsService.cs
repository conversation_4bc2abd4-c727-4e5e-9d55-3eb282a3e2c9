using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Commands.Payments.Common;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.OpenBanking;
using FlexCharge.Payments.Services.OpenBanking.Models;
using FlexCharge.Payments.Services.PaymentInstrumentsServices.Models;
using FlexCharge.Utils;
using Going.Plaid.Accounts;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.PaymentInstrumentsServices;

class LinkedPaymentInstrumentsService : ILinkedPaymentInstrumentsService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IOpenBankingService _openBankingService;

    public LinkedPaymentInstrumentsService(PostgreSQLDbContext dbContext, IOpenBankingService openBankingService)
    {
        _dbContext = dbContext;
        _openBankingService = openBankingService;
    }

    public async Task<LinkedPaymentInstruments> GetLinkedPaymentInstruments(List<Guid> ownerIds,
        List<LinkedPaymentInstrumentType> instrumentTypesToGet,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<LinkedPaymentInstrumentsService>()
            .Baggage("OwnerIds", string.Join(',', ownerIds));

        List<LinkedPaymentInstrument> linkedPaymentInstruments = new();
        if (instrumentTypesToGet.Contains(LinkedPaymentInstrumentType.Card))
        {
            var linkedCards = await _dbContext.PaymentInstruments
                .Where(pi => ownerIds.Contains(pi.OwnerId) &&
                             (pi.Type == PaymentMethodType.Credit || pi.Type == PaymentMethodType.Debit) &&
                             pi.IsActive)
                .Select(pi => new
                {
                    PaymentInstrumentId = pi.Id,
                    Token = pi.Token,
                    CardNumberMasked = pi.CardNumberMasked,
                    AccountLastUpdatedAt = pi.AccountLastUpdatedAt,
                    ModifiedOn = pi.ModifiedOn,
                    Bin = pi.Bin,
                    Last4 = pi.Last4
                })
                .OrderByDescending(x => x.AccountLastUpdatedAt ?? x.ModifiedOn)
                .ToListAsync(cancellationToken);


            foreach (var linkedCard in linkedCards)
            {
                linkedPaymentInstruments.Add(new LinkedPaymentInstrument(
                    linkedCard.PaymentInstrumentId,
                    linkedCard.Token,
                    linkedCard.CardNumberMasked,
                    linkedCard.AccountLastUpdatedAt ?? linkedCard.ModifiedOn,
                    linkedCard.Bin, linkedCard.Last4));
            }
        }

        List<LinkedBankAccount> linkedBankAccounts = new();
        if (instrumentTypesToGet.Contains(LinkedPaymentInstrumentType.BankAccount))
        {
            var linkedAccounts = await _dbContext.OpenBankingData
                .Where(x => ownerIds.Contains(x.OwnerId) && x.DataType == nameof(OpenBankingDataType.ACCOUNTS))
                .Where(x => x.AccessToken.Expiration == null || x.AccessToken.Expiration > DateTime.UtcNow)
                .Include(x => x.AccessToken)
                .Select(x => new
                {
                    OpenBankingDataId = x.Id,
                    EncryptedData = x.Data,
                    ModifiedOn = x.ModifiedOn,
                    AccessToken = x.AccessToken
                })
                .OrderByDescending(x => x.ModifiedOn)
                .ToListAsync(cancellationToken);

            HashSet<string> alreadyAddedAccounts = new();
            foreach (var linkedBankAccount in linkedAccounts)
            {
                var data = await _openBankingService.DecryptDataAsync(linkedBankAccount.EncryptedData);

                PlaidAccountsInformation plaidAccounts = null;

                if (!string.IsNullOrWhiteSpace(data))
                {
                    try
                    {
                        plaidAccounts = JsonConvert.DeserializeObject<PlaidAccountsInformation>(data);
                    }
                    catch (Exception e)
                    {
                        workspan.Log.Error(e, "Failed to deserialize Plaid accounts data");
                    }
                }

                if (plaidAccounts?.Accounts != null)
                {
                    foreach (var account in plaidAccounts.Accounts)
                    {
                        string accountKey =
                            $"{linkedBankAccount.AccessToken.InstitutionName}-{account.AccountId}-{account.Type}-{account.Subtype}";

                        if (alreadyAddedAccounts.Contains(accountKey))
                            continue;

                        alreadyAddedAccounts.Add(accountKey);

                        linkedBankAccounts.Add(new LinkedBankAccount(
                            linkedBankAccount.OpenBankingDataId,
                            account.AccountId,
                            account.Type.ToString(),
                            account.Subtype.ToString(),
                            account.Name,
                            account.OfficialName,
                            account.Mask,
                            linkedBankAccount.ModifiedOn,
                            linkedBankAccount.AccessToken.AccessToken,
                            linkedBankAccount.AccessToken.InstitutionName,
                            linkedBankAccount.AccessToken.Expiration));
                    }
                }
            }
        }

        workspan.Log.Information(
            "Found {LinkedPaymentInstrumentsCount} linked payment instruments and {LinkedBankAccountsCount} linked bank accounts",
            linkedPaymentInstruments.Count, linkedBankAccounts.Count);

        return new LinkedPaymentInstruments(linkedPaymentInstruments, linkedBankAccounts);
    }
}