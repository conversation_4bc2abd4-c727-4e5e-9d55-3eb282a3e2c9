using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.FinancialAccounts.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.BackgroundServices;

public class ResetCycleMetricsBackgroundCommand : BackgroundWorkerCommand
{
    protected override async Task ExecuteAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ResetCycleMetricsBackgroundCommand>()
            .LogEnterAndExit();

        try
        {
            await using var dbContext = serviceProvider.GetRequiredService<PostgreSQLDbContext>();

            var today = DateTime.UtcNow;
            var startDateTime = new DateTime(today.Year, today.Month, 1).ToUniversalTime();
            var endDatetime = startDateTime.AddMonths(1).AddSeconds(-1).ToUniversalTime();

            var metricsUpdated = await dbContext.Metrics
                .Where(m => !m.IsDeleted && m.CycleEndDate <= DateTime.UtcNow)
                .ExecuteUpdateAsync(settings => settings
                    .SetProperty(a => a.VisaCurrentTransactionsCount, a => 0)
                    .SetProperty(a => a.VisaCurrentTransactionsValue, a => 0)
                    .SetProperty(a => a.MasterCardCurrentTransactionsCount, a => 0)
                    .SetProperty(a => a.MasterCardCurrentTransactionsValue, a => 0)
                    .SetProperty(a => a.TotalTransactionCount, a => 0)
                    .SetProperty(a => a.TotalTransactionAmount, a => 0)
                    .SetProperty(a => a.TotalFailureCount, a => 0)
                    .SetProperty(a => a.TotalFailureAmount, a => 0)
                    .SetProperty(a => a.TotalErrorCount, a => 0)
                    .SetProperty(a => a.TotalErrorAmount, a => 0)
                    .SetProperty(a => a.TotalRefundCount, a => 0)
                    .SetProperty(a => a.TotalRefundAmount, a => 0)
                    .SetProperty(a => a.CycleStartDate, a => startDateTime)
                    .SetProperty(a => a.CycleEndDate, a => endDatetime)
                    .SetProperty(a => a.ModifiedOn, a => DateTime.UtcNow));

            await dbContext.Metrics
                .Where(m => !m.IsDeleted && m.CycleStartDate == startDateTime)
                .ExecuteUpdateAsync(settings => settings
                    .SetProperty(a => a.CycleEndDate, a => endDatetime));


            workspan.Log.Information("Updated metrics {MetricsCount}", metricsUpdated);
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Error while updating cycle metrics");
        }
    }
}