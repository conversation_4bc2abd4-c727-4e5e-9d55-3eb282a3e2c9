using System.Collections.Generic;
using System.Linq;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Entities.Extensions;

namespace FlexCharge.Payments.Domain.Payments.IntegrityManager;

public class OrderPayments
{
    private readonly IReadOnlyList<Payment> _payments;

    public OrderPayments(IEnumerable<Transaction> orderTransactions)
    {
        _payments = orderTransactions.GetPayments();
    }

    public int TotalRefundableAmount()
    {
        return _payments.TotalRefundableAmount();
    }
}