using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Reporting;
using FlexCharge.Common.Response;
using FlexCharge.Payments.Activities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace FlexCharge.Payments.Controllers;

[Route("[controller]")]
[ApiController]
[JwtAuth]
public class ReportingController : BaseController
{
    private readonly AppOptions _globalData;
    private readonly ILogger _logger;
    private readonly IReportingService _reportingService;
    private readonly IEnumerable<ILookupService> _lookupServices;
    private readonly IActivityService _activityService;

    public ReportingController(
        ILogger<ReportingController> logger,
        IOptions<AppOptions> globalData,
        IReportingService reportingService, IEnumerable<ILookupService> lookupServices,
        IActivityService activityService) : base()
    {
        _globalData = globalData.Value;
        _logger = logger;
        _reportingService = reportingService;
        _lookupServices = lookupServices;
        _activityService = activityService;
    }


    [HttpPost]
    [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
    [Route("transactionReport")]
    [ProducesResponseType(typeof(BaseResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> PostTransactionsReport(
        Guid reportId,
        DateTime? from,
        DateTime? to,
        Guid? mid,
        Guid? pid,
        string timezone,
        string transactionType,
        string paymentType,
        string providerName,
        CancellationToken token)
    {
        try
        {
            _logger.LogInformation(
                $"ENTERED: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString}");

            var reportRequestValidated = _reportingService.GenerateAndSendReport(reportId, from, to,
                new TransactionReportParam()
                {
                    Mid = mid,
                    Timezone = timezone,
                    TransactionType = transactionType?.ToLower(),
                    PaymentType = paymentType,
                    ProviderName = providerName?.ToLower(),
                    Pid = pid
                },
                new ModelStateWrapper(ModelState),
                User.FindFirstValue(ClaimTypes.Email)
                //"<EMAIL>"
                , out var message);
            if (!reportRequestValidated)
            {
                await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                    set => set
                        .TenantId(GetMID())
                        .Meta(meta => meta.SetValue("ReportId", reportId)));
                return ValidationProblem();
            }

            return Ok(new
            {
                Result = message,
                Status = "",
                StatusCode = 200.ToString()
            });
        }
        catch (Exception e)
        {
            _logger.LogError(e,
                $"EXCEPTION: POST {_globalData.Name} => API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
            await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                set => set
                    .TenantId(GetMID())
                    .Meta(meta => meta.SetValue("ReportId", reportId)));

            return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching FlexCharge statistics");
        }
    }

    [HttpGet]
    [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
    [Route("getLookups")]
    //[AllowAnonymous]
    //[ProducesResponseType(typeof(OrdersReport), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetLookups()
    {
        try
        {
            _logger.LogInformation(
                $"ENTERED: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString}");

            var retvals = new List<Lookup>();
            foreach (var lookupservice in _lookupServices)
            {
                var lookup = await lookupservice.GetLookup();
                retvals.Add(lookup);
            }

            var ret = retvals.ToDictionary(r => r.Title, r => r.IdToValue);

            //var retval = await Task.WhenAll(retvalTasks);
            return Ok(ret);
        }
        catch (Exception e)
        {
            _logger.LogError(e,
                $"EXCEPTION: POST {_globalData.Name} => API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
            await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                set => set
                    .TenantId(GetMID()));

            return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching FlexCharge statistics");
        }
    }
}