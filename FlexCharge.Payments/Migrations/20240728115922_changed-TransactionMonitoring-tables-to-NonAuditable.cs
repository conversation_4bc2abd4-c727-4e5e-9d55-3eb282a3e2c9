using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class changedTransactionMonitoringtablestoNonAuditable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "TransactionMonitoring");

            migrationBuilder.DropColumn(
                name: "CreatedOn",
                table: "TransactionMonitoring");

            migrationBuilder.DropColumn(
                name: "ModifiedBy",
                table: "TransactionMonitoring");

            migrationBuilder.DropColumn(
                name: "ModifiedOn",
                table: "TransactionMonitoring");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "PartnerTransactionMonitoring");

            migrationBuilder.DropColumn(
                name: "CreatedOn",
                table: "PartnerTransactionMonitoring");

            migrationBuilder.DropColumn(
                name: "ModifiedBy",
                table: "PartnerTransactionMonitoring");

            migrationBuilder.DropColumn(
                name: "ModifiedOn",
                table: "PartnerTransactionMonitoring");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "TransactionMonitoring",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedOn",
                table: "TransactionMonitoring",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "ModifiedBy",
                table: "TransactionMonitoring",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ModifiedOn",
                table: "TransactionMonitoring",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "PartnerTransactionMonitoring",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedOn",
                table: "PartnerTransactionMonitoring",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "ModifiedBy",
                table: "PartnerTransactionMonitoring",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ModifiedOn",
                table: "PartnerTransactionMonitoring",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        }
    }
}
