using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class addalertProviderstosupportedGaytwaystable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ChargebacksManagementProvider",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "EthocaProvider",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "RdrProvider",
                table: "SupportedGateways");

            migrationBuilder.AddColumn<Guid>(
                name: "ChargebacksManagementProviderId",
                table: "SupportedGateways",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "EthocaProviderId",
                table: "SupportedGateways",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "RdrProviderId",
                table: "SupportedGateways",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_SupportedGateways_ChargebacksManagementProviderId",
                table: "SupportedGateways",
                column: "ChargebacksManagementProviderId");

            migrationBuilder.CreateIndex(
                name: "IX_SupportedGateways_EthocaProviderId",
                table: "SupportedGateways",
                column: "EthocaProviderId");

            migrationBuilder.CreateIndex(
                name: "IX_SupportedGateways_RdrProviderId",
                table: "SupportedGateways",
                column: "RdrProviderId");

            migrationBuilder.AddForeignKey(
                name: "FK_SupportedGateways_AlertProviders_ChargebacksManagementProvi~",
                table: "SupportedGateways",
                column: "ChargebacksManagementProviderId",
                principalTable: "AlertProviders",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SupportedGateways_AlertProviders_EthocaProviderId",
                table: "SupportedGateways",
                column: "EthocaProviderId",
                principalTable: "AlertProviders",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SupportedGateways_AlertProviders_RdrProviderId",
                table: "SupportedGateways",
                column: "RdrProviderId",
                principalTable: "AlertProviders",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SupportedGateways_AlertProviders_ChargebacksManagementProvi~",
                table: "SupportedGateways");

            migrationBuilder.DropForeignKey(
                name: "FK_SupportedGateways_AlertProviders_EthocaProviderId",
                table: "SupportedGateways");

            migrationBuilder.DropForeignKey(
                name: "FK_SupportedGateways_AlertProviders_RdrProviderId",
                table: "SupportedGateways");

            migrationBuilder.DropIndex(
                name: "IX_SupportedGateways_ChargebacksManagementProviderId",
                table: "SupportedGateways");

            migrationBuilder.DropIndex(
                name: "IX_SupportedGateways_EthocaProviderId",
                table: "SupportedGateways");

            migrationBuilder.DropIndex(
                name: "IX_SupportedGateways_RdrProviderId",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "ChargebacksManagementProviderId",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "EthocaProviderId",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "RdrProviderId",
                table: "SupportedGateways");

            migrationBuilder.AddColumn<string>(
                name: "ChargebacksManagementProvider",
                table: "SupportedGateways",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EthocaProvider",
                table: "SupportedGateways",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RdrProvider",
                table: "SupportedGateways",
                type: "text",
                nullable: true);
        }
    }
}
