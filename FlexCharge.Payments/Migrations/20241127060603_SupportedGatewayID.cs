using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class SupportedGatewayID : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.AddColumn<Guid>(
            //     name: "GatewayId",
            //     table: "Transactions",
            //     type: "uuid",
            //     nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "SupportedGatewayId",
                table: "Transactions",
                type: "uuid",
                nullable: true);
            //
            // migrationBuilder.CreateIndex(
            //     name: "IX_Transactions_GatewayId",
            //     table: "Transactions",
            //     column: "GatewayId");
            //
            // migrationBuilder.AddForeignKey(
            //     name: "FK_Transactions_Gateways_GatewayId",
            //     table: "Transactions",
            //     column: "GatewayId",
            //     principalTable: "Gateways",
            //     principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.DropForeignKey(
            //     name: "FK_Transactions_Gateways_GatewayId",
            //     table: "Transactions");
            //
            // migrationBuilder.DropIndex(
            //     name: "IX_Transactions_GatewayId",
            //     table: "Transactions");
            //
            // migrationBuilder.DropColumn(
            //     name: "GatewayId",
            //     table: "Transactions");

            migrationBuilder.DropColumn(
                name: "SupportedGatewayId",
                table: "Transactions");
        }
    }
}
