using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class addSupportACHandSupportWiretofinantialAccounttable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "SupportACH",
                table: "FinancialAccounts",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "SupportWire",
                table: "FinancialAccounts",
                type: "boolean",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SupportACH",
                table: "FinancialAccounts");

            migrationBuilder.DropColumn(
                name: "SupportWire",
                table: "FinancialAccounts");
        }
    }
}
