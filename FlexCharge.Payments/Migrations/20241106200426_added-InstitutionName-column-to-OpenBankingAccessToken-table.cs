using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class addedInstitutionNamecolumntoOpenBankingAccessTokentable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "InstitutionName",
                table: "OpenBankingAccessTokens",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_OpenBankingAccessTokens_AccessToken",
                table: "OpenBankingAccessTokens",
                column: "AccessToken");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_OpenBankingAccessTokens_AccessToken",
                table: "OpenBankingAccessTokens");

            migrationBuilder.DropColumn(
                name: "InstitutionName",
                table: "OpenBankingAccessTokens");
        }
    }
}
