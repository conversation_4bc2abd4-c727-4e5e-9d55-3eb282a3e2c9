using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class altersupportedgatewaysaddmerchantMCCprocessorId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "MCC",
                table: "Gateways",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProcessorId",
                table: "Gateways",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "SupportedGatewayId",
                table: "Gateways",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MCC",
                table: "Gateways");

            migrationBuilder.DropColumn(
                name: "ProcessorId",
                table: "Gateways");

            migrationBuilder.DropColumn(
                name: "SupportedGatewayId",
                table: "Gateways");
        }
    }
}
