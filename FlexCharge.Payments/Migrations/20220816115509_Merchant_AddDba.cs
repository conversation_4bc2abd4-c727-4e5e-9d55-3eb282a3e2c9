using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    public partial class Merchant_AddDba : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CompanyName",
                table: "Merchants",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "<PERSON>ba",
                table: "Merchants",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LegalEntityName",
                table: "Merchants",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Locked",
                table: "Merchants",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CompanyName",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON>",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "LegalEntityName",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "Locked",
                table: "Merchants");
        }
    }
}
