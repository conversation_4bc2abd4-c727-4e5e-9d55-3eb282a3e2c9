using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Entities.JsonbModels;

namespace FlexCharge.PaymentsUtils;

public static class CapabilitiesHelper
{
    static bool ResolveBool(bool? gatewayValue, bool? supportedGatewayValue, bool defaultValue)
    {
        return (gatewayValue, supportedValue: supportedGatewayValue) switch
        {
            (true, _) => true,
            (false, _) => false,
            (_, true) => true,
            (_, false) => false,
            _ => defaultValue
        };
    }


    public static bool CannotSetDupCheckValue(bool? gatewayValue, bool? supportedGatewayValue)
    {
        return ResolveBool(gatewayValue, supportedGatewayValue, false);
    }

    public static bool CanSetDynamicDescriptorValue(bool? gatewayValue, bool? supportedGatewayValue)
    {
        return ResolveBool(gatewayValue, supportedGatewayValue, true);
    }

    public static bool UseDynamicDescriptorSuffix(bool? gatewayValue, bool? supportedGatewayValue)
    {
        return ResolveBool(gatewayValue, supportedGatewayValue, false);
    }

    public static bool CanPayWithNetworkToken(bool? gatewayValue, bool? supportedGatewayValue)
    {
        return ResolveBool(gatewayValue, supportedGatewayValue, false);
    }

    public static bool CanSetUserDefinedValues(bool? gatewayValue, bool? supportedGatewayValue)
    {
        return ResolveBool(gatewayValue, supportedGatewayValue, false);
    }

    /// <summary>
    /// Need this to be on a supported gateway level without override (gatewayValue will turn null)
    /// </summary>
    /// <param name="gatewayValue"></param>
    /// <param name="supportedGatewayValue"></param>
    /// <returns></returns>
    public static bool CanSetDescriptorAddress(bool? gatewayValue, bool? supportedGatewayValue)
    {
        return ResolveBool(null, supportedGatewayValue, false);
    }

    /// <summary>
    /// Need this to be on a supported gateway level without override (gatewayValue will turn null)
    /// </summary>
    /// <param name="gatewayValue"></param>
    /// <param name="supportedGatewayValue"></param>
    /// <returns></returns>
    public static bool CanSetDescriptorUrl(bool? gatewayValue, bool? supportedGatewayValue)
    {
        return ResolveBool(null, supportedGatewayValue, false);
    }
}