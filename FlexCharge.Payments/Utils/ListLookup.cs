using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Components.Web;

namespace FlexCharge.PaymentsUtils;

public class ListLookup : IListLookup
{
    public string Title { get; set; }
    public List<string> Values { get; set; }

    public ListLookup(string title, List<string> values)
    {
        Title = title;
        Values = values;
    }

    public ListLookup(Type type)
    {
        Title = type.Name;
        Values = new List<string>(System.Enum.GetNames(type));
    }
}

public interface IListLookup
{
    string Title { get; set; }
    List<string> Values { get; set; }
}