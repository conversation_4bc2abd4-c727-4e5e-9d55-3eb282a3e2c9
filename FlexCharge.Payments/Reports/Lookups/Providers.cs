using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Reporting;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query.SqlExpressions;

namespace FlexCharge.Payments.Reports.Lookups;

public class Providers : ILookupService
{
    private readonly DbContext _dbContext;

    public Providers(DbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Lookup> GetLookup()
    {
        var idNames = await (_dbContext as PostgreSQLDbContext).Gateways
            .Select(t => new {t.Id, t.SupportedGateway.Name})
            .Distinct().ToListAsync();

        var result = new Lookup()
        {
            Title = "paymentProviders",
            IdToValue = idNames.AsEnumerable().DistinctBy(p => p.Id).ToDictionary(p => p.Id.ToString(), p => p.Name)
        };
        return result;
    }
}