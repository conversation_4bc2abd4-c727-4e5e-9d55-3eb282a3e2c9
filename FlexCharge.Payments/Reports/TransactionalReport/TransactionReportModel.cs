using System;
using CsvHelper.Configuration.Attributes;
using FlexCharge.Payments.Entities;

namespace FlexCharge.Payments;

public class TransactionReportModel
{
    public DateTime CreatedOn { get; set; }
    [Ignore] public Guid Id { get; set; }
    [Name("Provider")] public string ProviderName { get; set; }
    public string ProviderTransactionToken { get; set; }

    public string DynamicDescriptor { get; set; }
    public Guid OrderId { get; set; }
    [Ignore] public decimal AmountOriginal { get; set; }
    public string Currency { get; set; }

    [Name("Amount")] public string Amount => (AmountOriginal / 100).ToString("0.00");

    //public TransactionStatus Status { get; set; }
    [Name("Transaction Type")] public string Type { get; set; }

    [Name("Payment Type")] public string PaymentType { get; set; }
    [Name("Status")] public string Status { get; set; }
    [Name("Response Code")] public string ResponseCode { get; set; }
    [Name("Response Message")] public string ResponseMessage { get; set; }
    [Name("Cavv Result Code")] public string CAvvResultCode { get; set; }
    [Name("Cvv Result Code")] public string CvvResultCode { get; set; }
    [Name("Avs Result Code")] public string AvsResultCode { get; set; }
    [Name("AuthorizationId")] public string AuthorizationId { get; set; }

    [Name("Processor Name")] public string ProcessorName { get; set; }
    [Name("Processor Id")] public string ProcessorId { get; set; }
    [Name("Cardholder Name")] public string CardHolderName { get; set; }

    [Name("Bin")] public string Bin { get; set; }
    [Name("Last 4 digits of card")] public string Last4 { get; set; }
    [Name("Merchant Id")] public Guid Mid { get; set; }
    [Name("Doing business as")] public string Dba { get; set; }
    [Name("Company name")] public string CompanyName { get; set; }
    [Name("Partner Id")] public Guid Pid { get; set; }
}