using System;
using FlexCharge.Common.Activities.Attributes;
using FlexCharge.Common.Reporting;

namespace FlexCharge.Payments;

public class TransactionReportParam
{
    public string ProviderName { get; set; }
    public Guid? Mid { get; set; }
    public string PaymentType { get; set; }
    public string Timezone { get; set; }
    public Guid? Pid { get; set; }

    public string TransactionType
    {
        get { return _transactionType; }
        set { _transactionType = value?.ToLower(); }
    }

    private string _transactionType;
}