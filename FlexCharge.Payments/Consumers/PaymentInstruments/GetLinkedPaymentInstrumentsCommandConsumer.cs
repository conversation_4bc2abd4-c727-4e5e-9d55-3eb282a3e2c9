using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Consumers.PaymentInstruments;

public class GetLinkedPaymentInstrumentsCommandConsumer :
    CommandConsumer<GetLinkedPaymentInstrumentsCommand, GetLinkedPaymentInstrumentsCommandResponse>
{
    private readonly ILinkedPaymentInstrumentsService _linkedPaymentInstrumentsService;

    public GetLinkedPaymentInstrumentsCommandConsumer(IServiceScopeFactory serviceScopeFactory,
        ILinkedPaymentInstrumentsService linkedPaymentInstrumentsService
    )
        : base(serviceScopeFactory)
    {
        _linkedPaymentInstrumentsService = linkedPaymentInstrumentsService;
    }

    protected override async Task<GetLinkedPaymentInstrumentsCommandResponse> ConsumeCommand(
        GetLinkedPaymentInstrumentsCommand command, CancellationToken cancellationToken)
    {
        var linedPaymentInstruments =
            await _linkedPaymentInstrumentsService.GetLinkedPaymentInstruments(command.OwnerIds,
                command.InstrumentTypesToGet, cancellationToken);

        return new GetLinkedPaymentInstrumentsCommandResponse(linedPaymentInstruments.PaymentInstruments,
            linedPaymentInstruments.BankAccounts);
    }
}