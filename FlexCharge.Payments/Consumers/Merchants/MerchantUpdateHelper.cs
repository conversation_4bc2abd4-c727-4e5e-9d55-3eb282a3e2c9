using System;
using FlexCharge.Contracts;
using Merchant = FlexCharge.Payments.Entities.Merchant;

namespace FlexCharge.Payments.Consumers;

public static class MerchantUpdateHelper
{
    public static void UpdateMerchant(Merchant merchant, MerchantCreatedEvent message)
    {
        UpdateMerchantInternal(merchant, message);
    }

    public static void UpdateMerchant(Merchant merchant, MerchantUpdatedEvent message)
    {
        UpdateMerchantInternal(merchant, message);
    }

    private static void UpdateMerchantInternal(Merchant merchant, MerchantCreateOrUpdateEventBase message)
    {
        merchant.Mid = message.MerchantId;

        merchant.CompanyName = message.CompanyName;
        merchant.Dba = message.Dba;
        merchant.LegalEntityName = message.LegalEntityName;

        //TODO: Refactor. !!!For now, in case null sent dont override existing values !!!
        merchant.Descriptor = message.Descriptor ?? merchant.Descriptor;
        merchant.Descriptor_City = message.Descriptor_City ?? merchant.Descriptor_City;
        merchant.Descriptor_Address = message.Descriptor_Address ?? merchant.Descriptor_Address;
        merchant.Descriptor_Country = message.Descriptor_Country ?? merchant.Descriptor_Country;
        merchant.Descriptor_Mcc = message.Descriptor_Mcc ?? merchant.Descriptor_Mcc;
        merchant.Descriptor_Merchant_Id = message.Descriptor_Merchant_Id ?? merchant.Descriptor_Merchant_Id;
        merchant.Descriptor_Phone = message.Descriptor_Phone ?? merchant.Descriptor_Phone;
        merchant.Descriptor_Postal = message.Descriptor_Postal ?? merchant.Descriptor_Postal;
        merchant.Descriptor_State = message.Descriptor_State ?? merchant.Descriptor_State;
        merchant.Descriptor_Url = message.Descriptor_Url ?? merchant.Descriptor_Url;

        merchant.IsActive = message.IsActive;
        merchant.Locked = message.IsLocked;
        merchant.PayoutsEnabled = message.PayoutsEnabled;
        merchant.AllowBinCheckOnTokenization = message.AllowBinCheckOnTokenization;
        merchant.EnableGlobalNetworkTokenization = message.EnableGlobalNetworkTokenization;

        merchant.TransactionBaseFee = message.TransactionBaseFee;
        merchant.TransactionBaseFeeType = message.TransactionBaseFeeType;
        merchant.TransactionFee = message.TransactionFee;
        merchant.TransactionFeeType = message.TransactionFeeType;
        merchant.ChargebackFee = message.ChargebackFee;
        merchant.ChargebackFeeType = message.ChargebackFeeType;
        merchant.RefundFee = message.RefundFee;
        merchant.RefundFeeType = message.RefundFeeType;

        if (message.PartnerId != null && message.PartnerId != Guid.Empty)
        {
            merchant.Pid = message.PartnerId.Value;
        }

        if (message.IntegrationPartnerId != null && message.IntegrationPartnerId != Guid.Empty)
        {
            merchant.IntegrationPartnerId = message.IntegrationPartnerId.Value;
        }
    }
}