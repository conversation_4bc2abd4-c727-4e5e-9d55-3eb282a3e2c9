using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Services.PaymentServices;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Consumers;

public class PerformPayoutCommandConsumer :
    IdempotentCommandConsumer<PerformPayoutCommand, PerformPayoutCommandResponse>
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IPartnerPaymentServices _partnerPaymentServices;

    public PerformPayoutCommandConsumer(
        PostgreSQLDbContext dbContext,
        IServiceScopeFactory serviceScopeFactory,
        IPartnerPaymentServices partnerPaymentServices) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
        _partnerPaymentServices = partnerPaymentServices;
    }

    protected override async Task<PerformPayoutCommandResponse> ConsumeCommand(PerformPayoutCommand command,
        CancellationToken cancellationToken)
    {
        try
        {
            return new PerformPayoutCommandResponse
            {
                Success = false,
                ResponseMessage = "ACH debit not implemented yet"
            };
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);
            throw;
        }
    }
}