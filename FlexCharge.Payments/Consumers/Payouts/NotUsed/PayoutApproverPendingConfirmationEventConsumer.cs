// using System;
// using System.Linq;
// using System.Threading;
// using System.Threading.Tasks;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Contracts;
// using FlexCharge.Contracts.Payouts;
// using FlexCharge.Payments.Entities;
// using FlexCharge.Payments.Services.PayoutServices;
// using FlexCharge.Payments.Services.SpreedlyService;
// using Hangfire.Server;
// using MassTransit;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Logging;
// using Newtonsoft.Json;
// using Merchant = FlexCharge.Payments.Entities.Merchant;
//
// namespace FlexCharge.Payments.Consumers;
//
// /// <summary>
// /// Once merchant is created we create a merchant on the payment MS as well
// /// </summary>
// public class PayoutApproverPendingConfirmationEventConsumer : IConsumer<PayoutBatchCreatedEvent>
// {
//     private PostgreSQLDbContext _context;
//     private readonly IPublishEndpoint _publishEndpoint;
//
//     public PayoutApproverPendingConfirmationEventConsumer(
//         PostgreSQLDbContext context, IPublishEndpoint publishEndpoint)
//     {
//         _context = context;
//         _publishEndpoint = publishEndpoint;
//     }
//
//     public async Task Consume(ConsumeContext<PayoutBatchCreatedEvent> context)
//     {
//         // using var workspan = Workspan.Start<PayoutApproverPendingConfirmationEventConsumer>()
//         //     .Request(context.Message);
//         //
//         // try
//         // {
//         //     await _publishEndpoint.Publish<PayoutApproverConfirmedEvent>(new {});
//         // }
//         // catch (Exception e)
//         // {
//         //     workspan.RecordException(e, true);
//         // }
//     }
// }