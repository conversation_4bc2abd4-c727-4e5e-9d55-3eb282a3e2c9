using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CsvHelper;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Exports;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services.FundsReserveService;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace FlexCharge.Orders.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class FundsReserveController : BaseController
    {
        private readonly AppOptions _globalData;
        private readonly IFundsReserveService _fundsReserveService;

        public FundsReserveController(
            IOptions<AppOptions> globalData,
            IFundsReserveService fundsReserveService)
        {
            _globalData = globalData.Value;
            _fundsReserveService = fundsReserveService;
        }

        [HttpGet("{mid}/details")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(IPagedList<FundsReserve>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get([FromQuery] FundsReserveQueryDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<FundsReserveController>(this, payload, _globalData);

            try
            {
                IPagedList<FundsReserve> response;
                if (HttpContext.User.Claims.Any(x =>
                        x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _fundsReserveService.GetReserveByMidAsync(payload.Mid, null, payload.PageNumber,
                        payload.PageSize, "");
                }
                else
                {
                    response = await _fundsReserveService.GetReserveByMidAsync(GetMID(), GetPID(), payload.PageNumber,
                        payload.PageSize, "");
                }

                return Ok(response.ToPagedList(payload.PageNumber, payload.PageSize));
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }
        
        [HttpGet("{mid}/export-details")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(IPagedList<FundsReserve>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ExportFundsReserveDetails([FromQuery] FundsReserveDetailsExportDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<FundsReserveController>(this, payload, _globalData);

            try
            {
                IPagedList<FundsReserve> response;
                if (HttpContext.User.Claims.Any(x =>
                        x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _fundsReserveService.GetReserveByMidAsync(payload.Mid, null, payload.PageNumber,
                        payload.PageSize, "");
                }
                else
                {
                    response = await _fundsReserveService.GetReserveByMidAsync(GetMID(), GetPID(), payload.PageNumber,
                        payload.PageSize, "");
                }
                
                var stream = CSVExport.GenerateCSVStreamFromRows(response.ToList());


                return File(stream, "text/csv", "funds_reserve_export.csv");
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpGet]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(IPagedList<FundsReserve>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetSummary([FromQuery] FundsReserveQueryDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<FundsReserveController>(this, payload, _globalData);

            try
            {
                IPagedList<FundsReserveSummaryDTO> response;
                if (HttpContext.User.Claims.Any(x =>
                        x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _fundsReserveService.GetReservesSummaryAsync(
                        payload.Mid,
                        Guid.Empty,
                        payload.PageNumber,
                        payload.PageSize,
                        "");
                }
                else
                {
                    response = await _fundsReserveService.GetReservesSummaryAsync(
                        GetMID(),
                        GetPID(),
                        payload.PageNumber,
                        payload.PageSize,
                        "");
                }

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        // TODO: Incomplete method missing the flow of how this method is used without a related batch id
        // Utilize reserve > needs to know the related batch but if batch doesnt exist yet, we cannot add new fundsReserve records
        // we maybe need to add fundsReserve candidates and once batch is generated we can use candidates for utilization

        // [HttpPost]
        // [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        // [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        // [ProducesResponseType(StatusCodes.Status404NotFound)]
        // public async Task<IActionResult> UtilizeReserve(UtilizeFundsReserveDTO payload,
        //     CancellationToken token)
        // {
        //     using var workspan = Workspan.StartEndpoint<FundsReserveController>(this, payload, _globalData);
        //
        //     try
        //     {
        //         await _fundsReserveService.UtilizeReserveAsync(payload.Mid, payload.RelatedBatchId,
        //             payload.BatchRecordId, Utils.Formatters.DecimalToInt(payload.Amount), payload.Reason);
        //
        //         return Ok();
        //     }
        //     catch (FlexChargeValidationException e)
        //     {
        //         ModelState.AddModelError(e.Property, e.Message);
        //         return BadRequest(ModelState);
        //     }
        //     catch (Exception e)
        //     {
        //         workspan.RecordException(e);
        //         return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
        //     }
        // }
    }
}