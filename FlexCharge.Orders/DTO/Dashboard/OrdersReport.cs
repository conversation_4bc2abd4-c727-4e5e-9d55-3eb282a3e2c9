using System;
using System.Collections.Generic;

namespace FlexCharge.Orders.DTO;

public class OrdersReport
{
    public OrdersReport()
    {
        PaymentProcessingStatistics = new PaymentProcessingStatistics();
        FlexChargeDeclinedPaymentsReceived = new DeclinedPaymentsReceived();
    }
    
    public PaymentProcessingStatistics PaymentProcessingStatistics { get; set; }
    public DeclinedPaymentsReceived FlexChargeDeclinedPaymentsReceived { get; set; }

    public string CurrencyCode { get; set; }
    public string CurrencySymbol { get; set; }
    public string TimeFrameInterval { get; set; }
}

public class PaymentProcessingStatistics
{

    #region Commented

    // public int Daily_Processed_Payments_Count { get; set; }
    // public decimal Daily_Processed_Payments_TotalAmount { get; set; }

    // public int Monthly_Processed_Payments_Count { get; set; }
    // public decimal Monthly_Processed_Payments_TotalAmount { get; set; }
    //
    // public int Yearly_Processed_Payments_Count { get; set; }
    // public decimal Yearly_Processed_Payments_TotalAmount { get; set; }
    
    // public IEnumerable<(string year, decimal sum)> Yearly_Processed_Payments { get; set; }
    // public IEnumerable<(string month, decimal sum)> Monthly_Processed_Payments { get; set; }

    #endregion

    public int Processed_Payments_Count { get; set; }
    public decimal Processed_Payments_TotalAmount { get; set; }

    public IEnumerable<GraphPoint> Daily_Processed_Payments { get; set; }
    
    
    public int Successful_Payments_Count { get; set; }
    public decimal Successful_Payments_TotalAmount { get; set; }

    #region Commented

    // public int Daily_Successful_Payments_Count { get; set; }
    // public decimal Daily_Successful_Payments_TotalAmount { get; set; }
    //
    // public int Monthly_Successful_Payments_Count { get; set; }
    // public decimal Monthly_Successful_Payments_TotalAmount { get; set; }
    //
    // public int Yearly_Successful_Payments_Count { get; set; }
    // public decimal Yearly_Successful_Payments_TotalAmount { get; set; }

    // public IEnumerable<(string month, decimal)> Monthly_Successful_Payments { get; set; }
    // public IEnumerable<(string year, decimal)> Yearly_Successful_Payments { get; set; }

    #endregion

    public IEnumerable<GraphPoint> Daily_Successful_Payments { get; set; } 

    public int Declined_Payments_Count { get; set; }
    public decimal Declined_Payments_TotalAmount { get; set; }

    #region Commented

    // public int Daily_Declined_Payments_Count { get; set; }
    // public decimal Daily_Declined_Payments_TotalAmount { get; set; }
    //
    // public int Monthly_Declined_Payments_Count { get; set; }
    // public decimal Monthly_Declined_Payments_TotalAmount { get; set; }
    //
    // public int Yearly_Declined_Payments_Count { get; set; }
    // public decimal Yearly_Declined_Payments_TotalAmount { get; set; }

    // public IEnumerable<(string month, decimal)> Monthly_Declined_Payments { get; set; }
    // public IEnumerable<(string year, decimal)> Yearly_Declined_Payments { get; set; }

    #endregion
    public IEnumerable<GraphPoint> Daily_Declined_Payments { get; set; }
    
 
    //Lost orders because of declined payments
    public int Lost_Orders_DueToDeclines_Count { get; set; }
    public decimal Lost_Orders_DueToDeclines_Amount { get; set; }

    //Average value lost order
    public decimal Avg_LostValue_DueToDeclines_Amount { get; set; }

    //Lost clients because of declined payment
    public int Lost_Clients_DueToDeclines_Count { get; set; }
    public decimal Lost_Clients_DueToDeclines_Amount { get; set; }

    //Average value lost client
    public decimal Avg_LostValue_LostClients_DueToDeclines_Amount { get; set; }

    //Total revenue lost because of declined payments
    public decimal LostRevenue_DueToDeclines_Amount { get; set; }
    public decimal LostRevenue_DueToDeclines_Percentage { get; set; }
}

public class DeclinedPaymentsReceived
{
    public int Declined_Payments_Count { get; set; }
    public decimal Declined_Payments_TotalAmount { get; set; }

    #region Commented

    // public int Daily_Declined_Payments_Count { get; set; }
    // public decimal Daily_Declined_Payments_TotalAmount { get; set; }

    // public int Monthly_Declined_Payments_Count { get; set; }
    // public decimal Monthly_Declined_Payments_TotalAmount { get; set; }
    //
    // public int Yearly_Declined_Payments_Count { get; set; }
    // public decimal Yearly_Declined_Payments_TotalAmount { get; set; }

    // public IEnumerable<(string month, decimal sum)> Monthly_Declined_Payments { get; set; }
    // public IEnumerable<(string year, decimal sum)> Yearly_Declined_Payments { get; set; }
    #endregion

    public IEnumerable<GraphPoint> Declined_Payments { get; set; } //evaluated_payments_amount


    public int Declined_EligiblePayments_Count { get; set; }
    public decimal Declined_EligiblePayments_TotalAmount { get; set; }

    #region Commented

    // public int Daily_Declined_EligiblePayments_Count { get; set; }
    // public decimal Daily_Declined_EligiblePayments_TotalAmount { get; set; }
    //
    // public int Monthly_Declined_EligiblePayments_Count { get; set; }
    // public decimal Monthly_Declined_EligiblePayments_TotalAmount { get; set; }
    //
    // public int Yearly_Declined_EligiblePayments_Count { get; set; }
    // public decimal Yearly_Declined_EligiblePayments_TotalAmount { get; set; }

    // public IEnumerable<(string month, decimal)> Monthly_DeclinedEligible_Payments { get; set; }
    // public IEnumerable<(string year, decimal)> Yearly_DeclinedEligible_Payments { get; set; }
    

    #endregion
    
    public IEnumerable<GraphPoint> DeclinedEligible_Payments { get; set; } //eligible_offers_amount


    public int Declined_PaymentsProcessed_Count { get; set; } //approved_offers_count
    public decimal Declined_PaymentsProcessed_TotalAmount { get; set; } //approved_offers_amount

    #region Commented

    // public int Daily_Declined_PaymentsProcessed_Count { get; set; }
    // public decimal Daily_Declined_PaymentsProcessed_TotalAmount { get; set; }
    //
    // public int Monthly_Declined_PaymentsProcessed_Count { get; set; }
    // public decimal Monthly_Declined_PaymentsProcessed_TotalAmount { get; set; }
    //
    // public int Yearly_Declined_PaymentsProcessed_Count { get; set; }
    // public decimal Yearly_Declined_PaymentsProcessed_TotalAmount { get; set; }
    // public IEnumerable<(string month, decimal)> MonthlyDeclinedPaymentsProcessed { get; set; }
    // public IEnumerable<(string year, decimal)> YearlyDeclinedPaymentsProcessed { get; set; }

    #endregion
    
    public IEnumerable<GraphPoint> DeclinedPaymentsProcessed { get; set; } //approved_offers_amount

    //Total revenue saved by FlexCharge
    public decimal SavedRevenue_By_FlexCharge_Amount { get; set; } //saved_revenue_total_amount
    public decimal SavedRevenue_By_FlexCharge_Percentage { get; set; } //saved_revenue_percentage
}

public class GraphPoint
{
    public int ItemsCount { get; set; }
    public  DateTime Date { get; set; }
    public  decimal Value { get; set; }
}