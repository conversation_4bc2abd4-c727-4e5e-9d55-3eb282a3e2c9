using FlexCharge.Orders.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.HateosLinks;
using FlexCharge.Common.Response;
using FlexCharge.Utils;

namespace FlexCharge.Orders.DTO
{
    public interface IDTO
    {
        public Guid CorrelationId { get; set; }
    }

    // public class ContactDTO
    // {
    //     public string FirstName { get; set; }
    //     public string LastName { get; set; }
    //     public string Email { get; set; }
    //     public string SecondaryEmail { get; set; }
    //     public string Phone { get; set; }
    //     public Guid? ContactId { get; set; }
    //     public Guid? UserId { get; set; }
    //     public string? ProfileUrl { get; set; }
    // }

    public class OrderCreateDTO : IDTO
    {
        public Guid Id { get; set; }

        public bool IsMIT { get; set; }
        public bool IsGhostMode { get; set; }
        public bool IsTestOrder { get; set; }


        /// <summary>
        /// Payee ID
        /// </summary>
        [Required]
        public Guid MerchantId { get; set; }

        public string MerchantName { get; set; }

        /// <summary>
        /// Payer ID
        /// </summary>
        [Required]
        public Guid PayerId { get; set; }

        [Required] public string FirstName { get; set; }
        [Required] public string LastName { get; set; }

        public string Email { get; set; }
        [Required] public string Phone { get; set; }
        public string? ProfileUrl { get; set; }

        public string? SecondaryEmail { get; set; }

        public Guid CorrelationId { get; set; }

        public int Amount { get; set; }
        public string? Currency { get; set; }
        public string PaymentInstrumentToken { get; set; }

        public string ReferenceNumber { get; set; }
        public string StoreName { get; set; }
        public string StoreId { get; set; }
        public Guid? SiteId { get; set; }
        public OrderStatusCategory StatusCategory { get; set; }
        public string StatusSubCategory { get; set; }
        public string StatusDescription { get; set; }

        public DateTime? ExpiryDate { get; set; }

        public AddressDTO BillingAddress { get; set; }
        public AddressDTO? ShippingAddress { get; set; }

        public IEnumerable<OrderItemDTO> OrderItems { get; set; }
        public IEnumerable<OrderActivityDTO> Activity { get; set; }

        public string? Subscription { get; set; }

        public string? Bin { get; set; }
        public string? Last4 { get; set; }

        public Dictionary<string, string>? AdditionalFields { get; set; }

        public Dictionary<string, string>? Meta { get; set; }
    }

    public class OrderUpdateDTO : IDTO
    {
        public Guid CorrelationId { get; set; }

        /// <summary>
        /// Order ID
        /// </summary>
        [Required]
        public Guid Id { get; set; }

        public bool IsGhostMode { get; set; }

        /// <summary>
        /// Payee ID
        /// </summary>
        [Required]
        public Guid MerchantId { get; set; }

        /// <summary>
        /// Payer ID
        /// </summary>
        [Required]
        public Guid ContactId { get; set; }

        [Required] public string FirstName { get; set; }
        [Required] public string LastName { get; set; }

        public string Email { get; set; }
        [Required] public string Phone { get; set; }
        public string ProfileUrl { get; set; }
        public string SecondaryEmail { get; set; }


        public string ReferenceNumber { get; set; }
        public string StoreName { get; set; }
        public string StoreId { get; set; }
        public OrderStatusCategory Status { get; set; }

        public int Amount { get; set; }
        public string Currency { get; set; }

        public DateTime OrderPlacedDate { get; set; }
        public DateTime? ExpiryDate { get; set; }

        //public List<xContactAccounts> ContactAccounts { get; set; }
        public AddressDTO BillingAddress { get; set; }
        public AddressDTO ShippingAddress { get; set; }

        public IEnumerable<OrderItemDTO> OrderItems { get; set; }
    }

    public class OrderQueryDTO : BaseResponse, IDTO
    {
        public Guid Id { get; set; }

        public bool IsGhostMode { get; set; }

        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string ProfileUrl { get; set; }
        public string SecondaryEmail { get; set; }

        public Guid CorrelationId { get; set; }

        public string ReferenceNumber { get; set; }
        public string StoreName { get; set; }
        public string StoreId { get; set; }

        public Guid? SideId { get; set; }

        public OrderStatusCategory Status { get; set; }
        public string StatusName => Enum.GetName(typeof(OrderStatusCategory), Status);

        public int Amount { get; set; }
        public string Currency { get; set; }
        public string CurrencyCode { get; set; }
        public string CurrencySymbol { get; set; }

        public DateTime OrderPlacedDate { get; set; }
        public DateTime? ExpiryDate { get; set; }

        public AddressDTO BillingAddress { get; set; }
        public AddressDTO ShippingAddress { get; set; }


        public IEnumerable<OrderItemDTO> OrderItems { get; set; }
        public IEnumerable<OrderActivityDTO> Activity { get; set; }
    }


    public class OrdersQueryResponse : BaseResponse, IHateoasLinkSupport
    {
        public OrdersQueryResponse()
        {
            OrderStatuses = new Dictionary<int, string>();

            foreach (OrderStatusCategory status in Enum.GetValues(typeof(OrderStatusCategory)))
            {
                var statusName = status.ToString();
                OrderStatuses.Add((int) status, char.ToUpper(statusName[0]) + statusName.Substring(1));
            }
        }

        public PagedDTO<OrderItemQueryDTO> Orders { get; set; }
        public Dictionary<int, string> OrderStatuses { get; set; }

        public class OrderItemQueryDTO
        {
            public Guid Id { get; set; }

            public bool IsGhostMode { get; set; }

            public string PayerName { get; set; }
            public string MerchantName { get; set; }
            public Guid MerchantId { get; set; }

            public string ReferenceNumber { get; set; }
            public string StoreName { get; set; }
            public string StoreId { get; set; }
            public OrderStatusCategory Status { get; set; }
            public string StatusName => Enum.GetName(typeof(OrderStatusCategory), Status);

            public decimal Amount { get; set; }
            public string FormattedAmount => $"{CurrencySymbol}{Amount}";
            public string Currency { get; set; }
            public int CurrencyCode { get; set; }
            public string CurrencySymbol { get; set; }

            public DateTime CreatedOn { get; set; }
            public DateTime OrderPlacedDate { get; set; }
            public DateTime? ExpiryDate { get; set; }

            public string PayoutStatus { get; set; }
            public decimal RefundsAmount { get; set; }
            public string Descriptor { get; set; }
            public string PaymentInstrumentToken { get; set; }
            public bool IsMIT { get; set; }
            public Guid? SiteId { get; set; }
        }

        public class OrderExportItemQueryDTO
        {
            public Guid Id { get; set; }
            public string ReferenceNumber { get; set; }
            public DateTime CreatedOn { get; set; }
            public DateTime OrderPlacedDate { get; set; }
            public string MerchantName { get; set; }
            public string PayerName { get; set; }
            public bool IsMIT { get; set; }
            public string Descriptor { get; set; }
            public OrderStatusCategory Status { get; set; }
            public decimal Amount { get; set; }
            public decimal RefundsAmount { get; set; }
            public Guid? SiteId { get; set; }
            public string Bin { get; set; }
            public string Last4 { get; set; }
        }

        public List<HateoasLink> Links { get; set; }
    }

    public class OrderConfirmationIdDTO
    {
        [Required] public string ConfirmationId { get; set; }
        public string Token { get; set; }
    }

    public class OrdersQueryResponseDTO : IDTO, IHateoasLinkSupport
    {
        public Guid Id { get; set; }

        public bool IsGhostMode { get; set; }

        public Guid CorrelationId { get; set; }

        public string ReferenceNumber { get; set; }
        public string StoreName { get; set; }
        public string StoreId { get; set; }

        public OrderStatusCategory Status { get; set; }
        public string StatusName => Enum.GetName(typeof(OrderStatusCategory), Status);

        public DateTime? CreatedOn { get; set; }
        public DateTime OrderPlacedDate { get; set; }
        public DateTime? ExpiryDate { get; set; }

        public AddressDTO BillingAddress { get; set; }
        public AddressDTO ShippingAddress { get; set; }

        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string ProfileUrl { get; set; }
        public string SecondaryEmail { get; set; }

        public string Currency { get; set; }
        public int CurrencyCode { get; set; }
        public string CurrencySymbol { get; set; }

        public decimal Amount { get; set; }
        public string FormattedAmount => $"{CurrencySymbol}{Amount}";

        public bool PaidOutOfBand { get; set; }


        public IEnumerable<OrderItemDTO> OrderItems { get; set; }
        public IEnumerable<OrderActivityDTO> ActivityItems { get; set; }

        public Guid? RefundVoidTransactionId { get; set; }
        public decimal TotalBalancePaid { get; set; } //=> CalculateTotal();
        public string TotalBalancePaidFormatted => $"{CurrencySymbol}{TotalBalancePaid}";

        public decimal BalanceToBePaid => this.Amount - TotalBalancePaid - RefundsAmount - DiscountsAmount;
        public string BalanceToBePaidFormatted => $"{CurrencySymbol}{BalanceToBePaid}";

        public string PayoutStatus { get; set; }
        public decimal RefundsAmount { get; set; }
        public decimal DiscountsAmount { get; set; }

        // public decimal CalculateTotal()
        // {
        //     var sumDebits = ActivityItems?.Where(x => x.Type is "debit" or "capture" or "Debit" or "Capture").Sum(x => x.TotalAmount);
        //     var sumCredits = ActivityItems?.Where(x => x.Type is "credit" or "Credit").Sum(x => x.TotalAmount);
        //
        //     return Utils.Formatters.IntToDecimal(sumDebits.Value - sumCredits.Value);
        // }
        public string PaymentInstrumentToken { get; set; }
        public string SiteName { get; set; }
        public string SiteDescriptor { get; set; }
        public string MerchantName { get; set; }
        public Guid MerchantId { get; set; }
        public bool IsCancelable { get; set; }

        public List<HateoasLink> Links { get; set; }
    }

    public enum OrderState
    {
        Processing,
        Cancelled,
        CaptureRequired,
        Completed,
    }

    /// <summary>
    /// To be used in future
    /// </summary>
    public enum OrderPayInState
    {
        PaidIn,
        PartiallyRefunded,
        Refunded
    }

    public class PublicOrderQueryResponseDTO : IDTO
    {
        public Guid Id { get; set; }

        public Guid CorrelationId { get; set; }

        public string ReferenceNumber { get; set; }

        public OrderState State { get; set; }

        // /// <summary>
        // /// To be used in future
        // /// </summary>
        // public OrderPayInState PayInState  { get; set; }

        /// <summary>
        /// !!! Should be deprecated in next version (replaced by <see cref="State"/>)
        /// </summary>
        public OrderStatusCategory Status { get; set; }

        /// <summary>
        /// !!! Should be deprecated in next version (replaced by <see cref="State"/>)
        /// </summary>
        public string StatusName => Enum.GetName(typeof(OrderStatusCategory), Status)?.ToUpper();

        public DateTime OrderPlacedDate { get; set; }
        public DateTime? ExpiryDate { get; set; }

        public AddressDTO BillingAddress { get; set; }
        public AddressDTO ShippingAddress { get; set; }

        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string SecondaryEmail { get; set; }

        public string Currency { get; set; }
        public int CurrencyCode { get; set; }
        public string CurrencySymbol { get; set; }

        public decimal Amount { get; set; }
        public string FormattedAmount => $"{CurrencySymbol}{Amount}";

        public IEnumerable<OrderItemDTO> OrderItems { get; set; }
        public IEnumerable<OrderActivityDTO> ActivityItems { get; set; }
        public decimal TotalBalancePaid { get; set; } //=> CalculateTotal();
        public string TotalBalancePaidFormatted => $"{CurrencySymbol}{TotalBalancePaid}";

        public decimal BalanceToBePaid => this.Amount - TotalBalancePaid - RefundsAmount;
        public string BalanceToBePaidFormatted => $"{CurrencySymbol}{BalanceToBePaid}";
        public decimal RefundsAmount { get; set; }

        public string PaymentInstrumentToken { get; set; }

        public string MerchantName { get; set; }
        public Guid MerchantId { get; set; }
        public Guid SiteId { get; set; }
    }

    public class OrderItemDTO
    {
        public Guid Id { get; set; }

        [Required] public string Name { get; set; }
        public string ReferenceId { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Discount { get; set; }
        public decimal Tax { get; set; }
        public int Quantity { get; set; }

        public decimal Amount => UnitPrice * Quantity - Discount + Tax;
    }

    public class ContactUpdateImageDTO
    {
        public Guid Id { get; set; }
        [Required] public string ProfileUrl { get; set; }
    }


    //public class RoomDTOValidator : AbstractValidator<RoomDTO>
    //{
    //    public RoomDTOValidator()
    //    {

    //    }
    //}

    public class SubscriptionDTO
    {
        public string SubscriptionId { get; set; }
        public string Interval { get; set; }
        public string Price { get; set; }
        public string Currency { get; set; }
        public string PaymentNumber { get; set; }
        public string TotalPayments { get; set; }
    }
}