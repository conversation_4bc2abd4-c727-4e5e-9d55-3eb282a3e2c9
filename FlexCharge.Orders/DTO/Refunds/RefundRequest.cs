using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc;
using FlexCharge.Common.Mvc.Validators;

namespace FlexCharge.Orders.DTO.Refunds
{
    public class RefundRequest
    {
        public bool IsConsumerRequestedRefund { get; set; } = false;

        /// <summary>
        /// Gets or sets an amount
        /// </summary>
        //[RequiredIf(nameof(IsPartialRefund), true)]
        [Display(Name = "Refund amount")]
        public decimal AmountToRefund { get; set; }

        public int AmountToRefundInCents => Utils.Formatters.DecimalToInt(AmountToRefund);

        [NoUnicodeNullCharacters] public string RefundMessage { get; set; }

        public Guid? SupportedGatewayId { get; set; }

        // /// <summary>
        // /// Gets or sets a value indicating whether it's a partial refund; otherwise, full refund
        // /// </summary>
        // [DefaultValue(false)]
        // public bool IsPartialRefund { get; set; } = false;

        //[Required] public Guid OrderId { get; set; } // It's passed in request url
        public Guid? TransactionId { get; set; }
    }
}