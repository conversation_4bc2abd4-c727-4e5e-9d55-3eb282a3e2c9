using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Correlation;
using FlexCharge.Common.Dependencies;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Swagger;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Athena;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cloud.BI.Amazon;
using FlexCharge.Common.DataBase;
using FlexCharge.Common.DataStreaming;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.RepaymentStrategies;
using FlexCharge.Orders.Services;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Recaptcha;
using FlexCharge.Common.Telemetry.PerformanceCounters;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Commands.Orders.MerchantInfo;
using FlexCharge.Orders.Services.ActivityService;
using FlexCharge.Orders.Services.PayoutServices;
using Newtonsoft.Json;
using FlexCharge.Common.DistributedLock.Implementations.RedLock;
using FlexCharge.Common.Grpc;
using FlexCharge.Common.HateosLinks;
using FlexCharge.Common.Reporting;
using FlexCharge.Common.Settings.SharedSettings;
using FlexCharge.Common.Shared.Common;
using FlexCharge.Common.Shared.CommonSettings;
using FlexCharge.Common.Shared.Partners;
using FlexCharge.Common.Shared.UrlShortener;
using FlexCharge.Common.Sms;
using FlexCharge.Common.Telemetry.HttpRequests;
using FlexCharge.Contracts.Commands.Orders;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Orders.ConsumerLinks.UserInteractions;
using FlexCharge.Orders.GRPC;
using FlexCharge.Orders.Services.ActivityService.MetadataEnrichment;
using FlexCharge.Orders.Services.AdministratorTools;
using FlexCharge.Orders.Services.ConsumerNotifications;
using FlexCharge.Orders.Services.FinancialAccountsServices;
using FlexCharge.Orders.Services.FundsReserveService;
using FlexCharge.Orders.Services.Links;
using FlexCharge.Orders.Services.Links.ClickToRefund;
using FlexCharge.Orders.Services.MerchantNotifications;
using FlexCharge.Orders.Services.Merchants;
using FlexCharge.Orders.Services.PartnersServices;
using FlexCharge.Orders.Services.PartnersServices.FeeCollectionServices;
using FlexCharge.Orders.Services.Settlements;

namespace FlexCharge.Orders
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddTelemetry();
            services.AddCloudWatchPerformanceCountersTelemetry<Startup>();

            services.AddActivities<OrdersActivityService>();

            services.AddTransient<IAthenaService, AthenaService>();
            services.AddTransient<IClickToRefundService, ClickToRefundService>();
            services.AddTransient<ILinksService, LinksService>();
            services.AddTransient<ISecurityCheckService, SecurityCheckService>();
            services.AddTransient<IMetadataEnrichmentService, MetadataEnrichmentService>();
            services.AddTransient<IConsumerNotificationsService, ConsumerNotificationsService>();
            services.AddTransient<IMerchantNotificationsService, MerchantNotificationsService>();
            services.AddTransient<IMerchantsService, MerchantsService>();
            services.AddTransient<IPartnerService, PartnerService>();
            services.AddTransient<IOrderService, OrderService>();
            services.AddTransient<IBatchService, BatchService>();
            services.AddTransient<IPayoutService, PayoutService>();
            services.AddTransient<IRepaymentStrategyFactoryService, RepaymentStrategiesFactoryService>();
            services.AddTransient<IRepaymentStrategyManagerService, RepaymentStrategyManagerService>();
            services.AddTransient<IOrderReportingService, OrderReportingService>();
            services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient<ISettlementsService, SettlementsService>();
            services.AddTransient<IAdministratorToolsService, AdministratorToolsService>();
            services.AddTransient<IFundsReserveService, FundsReserveService>();
            services.AddTransient<IFinancialAccountsService, FinancialAccountsService>();
            services.AddTransient<IFeesCollectionService, FeesCollectionService>();

            services.AddTransient(typeof(IOptions<>), typeof(OptionsManager<>));

            #region Repayment Strategies

            services.PopulateAndAddTransients<IRepaymentStrategy>();
            services.AddTransient<ServiceCollectionExtensions.RepaymentStrategiesResolver>(serviceProvider =>
                strategyId =>
                {
                    var repaymentStrategies = serviceProvider.GetServices<IRepaymentStrategy>();

                    var repaymentStrategy = repaymentStrategies.FirstOrDefault(x => x.StrategyId == strategyId);
                    if (repaymentStrategy is null)
                        throw new KeyNotFoundException($"Repayment strategy {strategyId} not found");

                    return repaymentStrategy;
                });

            #endregion

            services.AddReporting();

            services.AddHateoasLinksSupport();

            services.AddUserInteractions();

            services.Configure<AppOptions>(Configuration.GetSection("app"));
            services.AddOptions();

            var connectionString =
                $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';";
#if DEBUG
            connectionString =
                "Host=localhost;port=5432;Database=fc.orders;Username=orders-service-staging;Password=*****";
#endif

            services.AddEntityFrameworkNpgsql()
                .AddNpgsqlDbContext<PostgreSQLDbContext>(connectionString)
                .AddNpgsqlDbContext<ReadOnlyPostgreSQLDbContext>(
                    ReadOnlyConnectionString.MakeReadOnly(connectionString));
            //.AddNpgsqlDbContext<PostgreSQLDbContext>("Host=localhost;port=5432;Database=fc.orders;Username=orders-service-staging;Password=*****");

            services.AddJwt();
            services.AddAuthorization(options =>
                {
                    options.AddPolicy(MyPolicies.SUPER_ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SUPER_ADMIN));

                    options.AddPolicy(MyPolicies.ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.MERCHANT_ADMIN));

                    options.AddPolicy(MyPolicies.USERS,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.USER));

                    options.AddPolicy(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN, SuperAdminGroups.MERCHANT_ADMIN);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS_AND_MERCHANT_FINANCE,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN,
                                MerchantGroups.MERCHANT_FINANCE);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS_AND_MERCHANT_FINANCE,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                MerchantGroups.MERCHANT_FINANCE);
                        });
                }
            );

            services.AddMassTransit<Startup>(
                x => { x.AddFastRequestClient<GetByVaultIdCommand>(Microservices.Vault); });

            #region GRPC

            services.AddFlexGrpc();

            // services.AddFlexGrpcClient<Grpc.Vault.GrpcVaultService.GrpcVaultServiceClient, VaultGrpcOptions>(
            //     nameof(VaultGrpcOptions.VaultEndpoint));

            #endregion

            services.AddRecaptcha();

            services.AddAutoMapper(typeof(Startup));

            services
                .AddControllers()
                .AddNewtonsoftJson(x =>
                    x.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore);
            //.AddNewtonsoftJson(x => x.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Serialize);

            //Added to fix circular reference errors (somehow configuration above was not enough)
            Newtonsoft.Json.JsonConvert.DefaultSettings = () => new JsonSerializerSettings()
                {ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore};

            services.AddSwaggerDocs();


            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", cors =>
                    cors.AllowAnyMethod()
                        .AllowAnyOrigin()
                        .AllowAnyHeader());
            });

            services.AddEmailClient();
            services.AddSmsClient();
            services.AddUrlShortener();

            services.AddDataStreamingClient();

            services.AddRedisCache();
            services.AddBigPayloadSupport();
            services.AddExternalRequestsCache();

            services.AddRedLockDistributedLock();

            services.AddBackgroundWorkerService(Configuration);

            #region Registering Shared Flex Services

            services.AddSharedPartnerSettings();

            #endregion

            services.AddAthenaDB();
            services.AddAmazonQuickSight();

            // To see consumer real IP
            // see: https://learn.microsoft.com/en-us/aspnet/core/host-and-deploy/proxy-load-balancer?view=aspnetcore-7.0#forwarded-headers-middleware-options
            services.Configure<ForwardedHeadersOptions>(options =>
            {
                //options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostApplicationLifetime applicationLifetime,
            IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                // Enable middleware to serve generated Swagger as a JSON endpoint.
            }

            //app.UseHttpsRedirection();
            app.UseSwaggerDocs();
            app.UseAutoMigrations<PostgreSQLDbContext>();
            app.UseCors("CorsPolicy");

            app.UseRouting();
            app.UseAuthorization();
            app.UseCorrelationId();

            app.UsePublicHttpRequestsTelemetry(); // Should be right before UseEndpoints to work correctly
            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });

            #region GRPC

            app.UseGrpcEndpoints(endpoints =>
                endpoints
                    .Map<GrpcOrdersService>()
            );

            #endregion

            app.UseMassTransit();
        }
    }
}