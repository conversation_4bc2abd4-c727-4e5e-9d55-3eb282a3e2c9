using System;

namespace FlexCharge.Orders.Entities
{
    public class MerchantFee : AuditableEntity
    {
        public string Name { get; set; }
        public string FeeType { get; set; } //FIXED, PERCENTAGE
        public string ChargeType { get; set; } //PER_TRANSACTION, ONE_TIME, PER_CHARGEBACK_TRANSACTION
        public int Amount { get; set; }

        public int? MinimumFeeAmount { get; set; }

        public bool IsActive { get; set; } = true;
        public Guid MerchantId { get; set; }
    }
}