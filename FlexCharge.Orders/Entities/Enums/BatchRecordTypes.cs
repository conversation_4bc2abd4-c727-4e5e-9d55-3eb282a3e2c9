using System.ComponentModel;
using System.Runtime.Serialization;

namespace FlexCharge.Orders.Entities;

public enum BatchRecordTypes
{
    [Description("SALE")] Order = 1,
    [Description("CREDIT")] Credit,
    [Description("RETURN")] Return,
    [Description("CHARGEBACK")] Chargeback,
    [Description("BATCH ADJUSTMENT")] BatchAdjustment,
    [Description("RESERVE HOLD")] ReserveHold,
    [Description("RESERVE RELEASE")] ReserveRelease,
    [Description("RESERVE CANCEL")] ReserveCancel,
    [Description("RESERVE UTILIZATION")] ReserveUtilization,
    [Description("PROCESSING FEE")] FlexFee,
    [Description("PROCESSING FEE")] FlexPartnerFee,
    [Description("CHARGEBACK FEE")] FlexChargebackFee,
    [Description("RETURN FEE")] FlexReturnFee,
    [Description("INTERCHANGE FEE")] FlexInterchangeFee
}

public enum DirectionEnum
{
    /// <summary>
    /// Enum CREDIT for value: CREDIT
    /// </summary>
    Credit = 1,

    /// <summary>
    /// Enum DEBIT for value: DEBIT
    /// </summary>
    Debit = 2
}