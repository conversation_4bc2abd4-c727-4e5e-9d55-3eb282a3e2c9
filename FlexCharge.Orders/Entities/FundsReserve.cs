using System;
using FlexCharge.Orders.Services.FundsReserveService;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Orders.Entities;

[Index(nameof(Parent))]
public class FundsReserve : AuditableEntity
{
    public FundsReserve()
    {
    }

    //add constructor and make this emutable
    public FundsReserve(
        Guid? mid,
        Guid? financialAccountId,
        Guid? orderId,
        DateTime? orderCompletedDate,
        Guid parent,
        Guid? batchId,
        Guid? batchRecordId,
        Guid fundsReserveConfigId,
        int amount,
        string reason,
        string type,
        ReserveStatus status)
    {
        Mid = mid;
        FinancialAccountId = financialAccountId;
        OrderId = orderId;
        OrderCompletedDate = orderCompletedDate;
        Parent = parent;
        BatchId = batchId;
        BatchRecordId = batchRecordId;
        FundsReserveConfigId = fundsReserveConfigId;
        Amount = amount;
        Reason = reason;
        Type = type;
        Status = status.ToString();
    }

    // public FundsReserve(
    //     Guid financialAccountId, 
    //     Guid? reserveOrderId, 
    //     Guid batchId, 
    //     Guid batchRecordId,
    //     Guid reserveFundsReserveConfigId,
    //     int fundsReserveConfigId,
    //     string type,
    //     string reason,
    //     ReserveStatus utilized)
    // {
    //     FinancialAccountId = financialAccountId;
    //     OrderId = reserveOrderId;
    //     BatchId = batchId;
    //     BatchRecordId = batchRecordId;
    //     FundsReserveConfigId = reserveFundsReserveConfigId;
    //     Amount = fundsReserveConfigId;
    //     Reason = reason;
    //     Type = type;
    //     Status = utilized.ToString();
    // }

    public Guid? FinancialAccountId { get; set; }
    public FinancialAccount FinancialAccount { get; set; }
    public Guid? Mid { get; private set; }
    public Guid Parent { get; private set; }
    public Guid? OrderId { get; private set; }
    public DateTime? OrderCompletedDate { get; private set; }
    public int Amount { get; private set; }
    public string Status { get; private set; } // Held, Released
    public string Reason { get; private set; } // Pending, Approved, Declined
    public string Type { get; private set; } // Fixed or Rolling

    public string Note { get; private set; }
    public Guid FundsReserveConfigId { get; private set; }
    public Guid? BatchId { get; private set; }
    public Guid? BatchRecordId { get; private set; }
}