namespace FlexCharge.Orders.Entities;

public class PaymentTransactionResponse
{
    public PaymentTransactionResponse(
        TransactionType transactionType,
        string responseCodeGateway,
        string responseCodeProcessor,
        string responseCode,
        string cvv = null,
        string avs = null,
        int? usedGatewayOrder = null,
        string internalResponseCode = null,
        string internalResponseMessage = null,
        string internalResponseGroup = null
    )
    {
        TransactionType = transactionType.ToString();
        ResponseCodeGateway = responseCodeGateway;
        ResponseCodeProcessor = responseCodeProcessor;
        ResponseCode = responseCode;
        NormalizedResponseCode = internalResponseCode;
        NormalizedResponseMessage = internalResponseMessage;
        NormalizedResponseCodeGroup = internalResponseGroup;
        CVV = cvv;
        AVS = avs;

        if (responseCodeGateway != null &&
            transactionType != FlexCharge.Orders.Entities.TransactionType.InitialResponseCode)
        {
            UsedGatewayOrder = usedGatewayOrder;
        }
    }


    public string TransactionType { get; set; }
    public string ResponseCodeGateway { get; set; }
    public string ResponseCodeProcessor { get; set; }
    public string ResponseCode { get; set; }

    public string NormalizedResponseCodeGroup { get; set; }
    public string NormalizedResponseCode { get; set; }
    public string NormalizedResponseMessage { get; set; }

    public string CVV { get; set; }

    public string AVS { get; set; }

    //public bool? ResponseCodeCertain { get; set; }
    public int? UsedGatewayOrder { get; set; }
}