using System.Collections.Generic;
using System.Linq;

namespace FlexCharge.Orders.Entities;

public static class PaymentsExtensions
{
    public static int TotalPendingDebitAmount(this IEnumerable<Payment> payments)
    {
        return payments.Sum(x => x.PendingDebitAmount);
    }

    public static int TotalSettledDebitAmount(this IEnumerable<Payment> payments)
    {
        return payments.Sum(x => x.SettledDebitAmount);
    }

    public static int TotalPendingCreditAmount(this IEnumerable<Payment> payments)
    {
        return payments.Sum(x => x.PendingCreditAmount);
    }

    public static int TotalSettledCreditAmount(this IEnumerable<Payment> payments)
    {
        return payments.Sum(x => x.SettledCreditAmount);
    }

    public static int TotalSettledAmount(this IEnumerable<Payment> payments)
    {
        return payments.TotalSettledDebitAmount() - payments.TotalSettledCreditAmount();
    }

    public static int TotalPendingAmount(this IEnumerable<Payment> payments)
    {
        return payments.TotalPendingDebitAmount() - payments.TotalPendingCreditAmount();
    }

    public static int TotalPendingOrSettledAmount(this IEnumerable<Payment> payments)
    {
        return payments.TotalPendingAmount() + payments.TotalSettledAmount();
    }

    public static int TotalDiscountedAmount(this IEnumerable<Payment> payments)
    {
        return payments.Sum(x => x.DiscountsAmount);
    }
}