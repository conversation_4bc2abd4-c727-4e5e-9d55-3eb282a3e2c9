using System;
using FlexCharge.Utils;

namespace FlexCharge.Orders.Entities.Extensions;

public static class LinkExtension
{
    public static LinkStatus ToLinkStatus(this Link link) =>
        EnumHelpers.ParseEnum<LinkStatus>(link.Status);

    public static bool IsExpired(this Link link) =>
        link.Expiry <= DateTime.UtcNow;

    public static bool IsActive(this Link link) =>
        link.ToLinkStatus() == LinkStatus.Active;
}