using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Orders.Consumers;
using FlexCharge.Orders.Entities;
using MassTransit;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Org.BouncyCastle.Crypto.Tls;

namespace FlexCharge.Orders.RepaymentStrategies.Implementations;

public class S2002_ChargeWithGateway2Strategy : ChargeWithSpecificGatewayStrategyBase
{
    protected override int? GetGatewayOrder() => 0;

    protected override async Task ExecuteStrategyAsync()
    {
        using var workspan = Workspan.Start<S2002_ChargeWithGateway2Strategy>();

        await base.ExecuteStrategyAsync();
    }
}