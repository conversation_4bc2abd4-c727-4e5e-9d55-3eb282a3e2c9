using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Threading.Tasks;
using FlexCharge.Common.RuntimeEnvironment;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.Services;

public class SecurityCheckService : ISecurityCheckService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public void EnsureHasAccessToOrdersPublicApi(Guid? requestMid, Guid? partnerId)
    {
        //TODO D: Ensure merchant has access to offers
    }

    public void EnsurePartnerHasAccessToMerchant(Guid pid, Guid mid)
    {
        //TODO D: Ensure merchant has access to offers
    }
}