using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Autofac.Features.Metadata;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.Contracts;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.RepaymentStrategies;
using FlexCharge.Orders.Services.PCSMService;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Merchant = FlexCharge.Orders.Entities.Merchant;

namespace FlexCharge.Orders.Services;

public class RepaymentStrategyManagerService : IRepaymentStrategyManagerService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IActivityService _activityService;
    private readonly IPublishEndpoint _publisher;
    private readonly IRepaymentStrategyFactoryService _repaymentStrategyFactoryService;
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public RepaymentStrategyManagerService(PostgreSQLDbContext dbContext,
        IActivityService activityService,
        IPublishEndpoint publisher,
        IRepaymentStrategyFactoryService repaymentStrategyFactoryService,
        IServiceScopeFactory serviceScopeFactory)
    {
        _dbContext = dbContext;
        _activityService = activityService;
        _publisher = publisher;
        _repaymentStrategyFactoryService = repaymentStrategyFactoryService;
        _serviceScopeFactory = serviceScopeFactory;
    }

    public async Task ProcessNotFullyPaidInOrdersAsync(CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<RepaymentStrategyManagerService>();


        try
        {
            var notFullyPaidInOrders = await _dbContext.Orders
                .Where(x =>
                    x.StatusCategory == nameof(OrderStatusCategory.completed) &&
                    x.OrderPayinStatus != PayInStatuses.Completed && x.OrderPayinStatus != PayInStatuses.Stopped)
                .AsNoTracking()
                .ToListAsync();

            workspan.Log.Information("Orders to try to charge money for: {OrdersCount}", notFullyPaidInOrders.Count);


            #region Creating Activity

            await _activityService.CreateActivityAsync(PayInActivities.PayIn_ProcessNotFullyPaidInOrders_Started,
                set => set
                    .Meta(meta => meta
                        .SetValue("Count", notFullyPaidInOrders.Count)));

            #endregion

            foreach (var order in notFullyPaidInOrders)
            {
                try
                {
                    await _publisher.Publish(new TryToRepayOrderCommand(order.Id));
                }
                catch (Exception e)
                {
                    workspan.RecordException(e);
                }
            }
        }
        catch (TaskCanceledException)
        {
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }
    }

    public async Task ProcessNotFullyPaidInOrderAsync(Guid orderId)
    {
        using var workspan = Workspan.Start<RepaymentStrategyManagerService>("Processing order pay-in")
            .Baggage("OrderId", orderId);

        using var serviceScope = _serviceScopeFactory.CreateScope();
        // Creating via scope to avoid circular dependency
        var orderService = serviceScope.ServiceProvider.GetRequiredService<IOrderService>();

        Order order = null;

        try
        {
            var orderAndMerchant = await _dbContext.Orders
                .Include(x => x.ActivityItems)
                .Where(o => o.Id == orderId)
                .Join(_dbContext.Merchants, o => o.MerchantId, m => m.Mid,
                    (ord, merchant) => new
                    {
                        Order = ord,
                        Merchant = merchant
                    })
                .SingleAsync();

            order = orderAndMerchant.Order;

            workspan.Baggage("Mid", order.MerchantId);

            // we only can try to get amount that not already authorized or debited or captured
            if (!order.IsFullyPaidIn(calculateOnlySettledAmount: false))
            {
                #region Creating Activity

                await _activityService.CreateActivityAsync(PayInActivities.PayIn_ProcessingNotFullyPaidInOrder,
                    set => set
                        .TenantId(order.MerchantId)
                        .CorrelationId(order.Id));

                #endregion

                using (var tx = await _dbContext.Database.BeginTransactionAsync())
                {
                    order.InProcessing = true; //locking Order to avoid concurrent repayment processing
                    try
                    {
                        #region [Commented]Not using order.IsExpired() in Orders microservice

                        // if (order.IsExpired())
                        // {
                        //     #region Creating Activity
                        //
                        //     await _activityService.CreateActivityAsync(
                        //         PayInActivities.PayIn_StoppingRepayments_OrderIsExpired,
                        //         set => set
                        //             .TenantId(order.MerchantId)
                        //             .CorrelationId(order.Id));
                        //
                        //     #endregion
                        //
                        //     //Order is expired so we must stop repayments
                        //     workspan.Log.Information("ORDER IS EXPIRED: Stop repayments");
                        //     await StopRepayments(order, orderService);
                        // }
                        // else

                        #endregion

                        //{
                        var repaymentInstructions =
                            await GetRepaymentInstructionsFromRuleEngineAsync(order, orderAndMerchant.Merchant);

                        if (repaymentInstructions.StopRepayments)
                        {
                            #region Creating Activity

                            await _activityService.CreateActivityAsync(
                                PayInActivities.PayIn_StoppingRepayments_RuleEngine,
                                set => set
                                    .TenantId(order.MerchantId)
                                    .CorrelationId(order.Id));

                            #endregion

                            workspan.Log.Information("RULE ENGINE: Stop repayments");
                            await StopRepayments(order, orderService);
                        }
                        else if (string.IsNullOrWhiteSpace(repaymentInstructions.StrategyToExecute))
                        {
                            #region Creating Activity

                            await _activityService.CreateActivityAsync(
                                PayInActivities.PayIn_RuleEngineResult_DoNothing,
                                set => set
                                    .TenantId(order.MerchantId)
                                    .CorrelationId(order.Id));

                            #endregion

                            workspan.Log.Information("RULE ENGINE: Do nothing");
                        }
                        else if (order.IsTestOrder)
                        {
                            #region Creating Activity

                            await _activityService.CreateActivityAsync(
                                PayInActivities.PayIn_SkippingTestOrder,
                                set => set
                                    .TenantId(order.MerchantId)
                                    .CorrelationId(order.Id));

                            #endregion

                            workspan.Log.Information("RULE ENGINE: Test order detected - skipping");
                        }
                        else
                        {
                            #region Creating Activity

                            await _activityService.CreateActivityAsync(
                                PayInActivities.PayIn_RuleEngineResult_ExecuteStrategy,
                                set => set
                                    .TenantId(order.MerchantId)
                                    .CorrelationId(order.Id)
                                    .Subcategory(repaymentInstructions.StrategyToExecute)
                                    .Meta(meta => meta
                                        .SetValue("Strategy", repaymentInstructions.StrategyToExecute)));

                            #endregion

                            //!!! Order repayment functionality is disabled for now and should be re-tested before production use!!!
                            throw new FlexChargeException("ORDER REPAYMENT: Repayment is disabled");

                            // workspan.Log.Information("RULE ENGINE: Execute {StrategyToExecute}",
                            //     repaymentInstructions.StrategyToExecute);
                            //
                            // Dictionary<string, string> strategyParameters = new Dictionary<string, string>();
                            // var strategy = await _repaymentStrategyFactoryService.CreateRepaymentStrategyAsync(
                            //     repaymentInstructions.StrategyToExecute,
                            //     Guid.NewGuid(), order,
                            //     strategyParameters);
                            //
                            // #region Creating Activity
                            //
                            // await _activityService.CreateActivityAsync(
                            //     PayInActivities.PayIn_Strategy_ExecutionRequested,
                            //     set => set
                            //         .TenantId(order.MerchantId)
                            //         .CorrelationId(order.Id)
                            //         .Subcategory(repaymentInstructions.StrategyToExecute)
                            //         .Meta(meta => meta
                            //             .SetValue("Strategy", repaymentInstructions.StrategyToExecute)));
                            //
                            // #endregion
                            //
                            // await strategy.ExecuteAsync();
                        }
                        //}
                    }
                    finally
                    {
                        order.InProcessing = false;
                        await _dbContext.SaveChangesAsync();

                        await tx.CommitAsync();
                    }
                }
            }
            else // Already fully paid-in or all amount to pay in is authorized or captured, but waiting for settlement
            {
                if (order.OrderPayinStatus != PayInStatuses.Completed)
                {
                    if (order.GetPayments().TotalPendingOrSettledAmount() > order.Amount)
                    {
                        #region Creating Activity

                        await _activityService.CreateActivityAsync(
                            PayInErrorActivities.PayIn_ChargedAmountIsGreatherThanAmountError,
                            set => set
                                .TenantId(order.MerchantId)
                                .CorrelationId(order.Id));

                        #endregion

                        workspan.Log.Error("Order TotalPendingOrSettledAmount > order.Amount");
                    }

                    if (order.IsFullyPaidIn(calculateOnlySettledAmount: true))
                    {
                        #region Creating Activity

                        await _activityService.CreateActivityAsync(PayInErrorActivities.PayIn_WrongPayInStatus,
                            set => set
                                .TenantId(order.MerchantId)
                                .CorrelationId(order.Id));

                        #endregion

                        workspan.Log.Warning("Order is fully paid in, but not marked as Complete");

                        //Forces recalculation of OrderPayInStatus
                        await orderService.UpdateOrderStateAsync(order.MerchantId, order.Id);
                    }
                }
            }
        }
        catch (Exception e)
        {
            #region Creating Activity

            await _activityService.CreateActivityAsync(PayInErrorActivities.PayIn_StrategyExecution_Error,
                set => set
                    .TenantId(order?.MerchantId)
                    .CorrelationId(orderId)
                    .Subcategory(order?.CurrentStrategyId)
                    .Meta(meta => meta
                        .SetValue("Strategy", order?.CurrentStrategyId)));

            #endregion

            workspan.RecordFatalException(e, "Error processing order: {OrderId}", orderId);
            throw;
        }
    }

    private async Task StopRepayments(Order order, IOrderService orderService)
    {
        using var workspan = Workspan.Start<RepaymentStrategyManagerService>("Stopping repayments")
            .Baggage("OrderId", order.Id);

        if (order.OrderPayinStatus == PayInStatuses.Completed)
        {
            workspan.Log.Fatal(
                $"Cannot set {nameof(order.OrderPayinStatus)} to stopped. It's already in completed state");
            return;
        }

        if (order.OrderPayinStatus == PayInStatuses.Stopped)
            return;

        order.OrderPayinStatus = PayInStatuses.Stopped;

        if (order.IsOrderCompleteOnlyWhenFullyPaidIn())
        {
            await orderService.SetOrderStatusAsync(order.MerchantId, order.Id,
                OrderStatusCategory.cancelled, "", "");
        }

        await _publisher.Publish(new OrderWrittenOffEvent
        {
            OrderId = order.Id,
            Mid = order.MerchantId,
            WrittenOffAmount = order.AmountLeftToPayIn(calculateOnlySettledAmount: true)
        });
    }

    private async Task<(bool StopRepayments, string StrategyToExecute)> GetRepaymentInstructionsFromRuleEngineAsync(
        Order order, Merchant merchant)
    {
        var lastTransactionResponse = order.GetPaymentTransactionsResponses().LastOrDefault();

        int daysSinceOrderFirstPlaced = (int) Math.Truncate((DateTime.UtcNow - order.OrderPlacedDate).TotalDays);
        Stage4Request stage4Request = new()
        {
            IsMIT = order.IsMIT,
            FirstDeclineDateDaysOld = (decimal) daysSinceOrderFirstPlaced,
            ResponseCodeSource = lastTransactionResponse?.ResponseCodeGateway ?? "",
            ResponseCode = lastTransactionResponse?.ResponseCode ?? ""
        };

        #region Create Activity

        await _activityService.CreateActivityAsync(
            PayInActivities.RuleEngine_Stage4_Starting,
            data: stage4Request,
            set => set
                .TenantId(order.MerchantId)
                .CorrelationId(order.Id));

        #endregion

        //var stage4Result = await _pcsmService.InvokeStage4Async(stage4Request);

        var stage4Result = new Stage4Response
        {
            StopRepayments = false,
            ChargeRetryIntervalInDays = -1
        };

        stage4Result.ChargeRetryIntervalInDays = daysSinceOrderFirstPlaced switch
        {
            >= 0 and <= 10 => 1,
            > 10 and <= 25 => 3,
            > 25 and <= 30 => 5,
            > 30 and <= 60 => 7,
            _ => -1
        };

        stage4Result.StopRepayments = stage4Result.ChargeRetryIntervalInDays == -1;

        #region Create Activity

        await _activityService.CreateActivityAsync(
            PayInActivities.RuleEngine_Stage4_ResponseReceived,
            data: stage4Result,
            set => set
                .TenantId(order.MerchantId)
                .CorrelationId(order.Id));

        #endregion

        if (PRODUCTION_TEST_MODE.IS_PRODUCTION_TEST_MODE(merchant, order))
        {
            await _activityService.CreateActivityAsync(
                PayInActivities.PayIn_StoppingRepayments_TestOrderDetected,
                set => set
                    .TenantId(order.MerchantId)
                    .CorrelationId(order.Id));

            stage4Result = new Stage4Response
            {
                StopRepayments = true,
            };
        }

        bool stopRepayments = stage4Result.StopRepayments;

        string strategyToExecute = null;
        if (!stopRepayments)
        {
            if (stage4Result.ChargeRetryIntervalInDays == 0)
            {
                throw new Exception("PCSM Stage 4 error: ChargeRetryIntervalInDays can't be 0");
            }


            var tryToExecuteToday =
                daysSinceOrderFirstPlaced > 0 && //do not execute repayment strategies on the day of order placement
                stage4Result.ChargeRetryIntervalInDays > 0 && // if -1 -> do nothing
                daysSinceOrderFirstPlaced % stage4Result.ChargeRetryIntervalInDays == 0;

            if (tryToExecuteToday)
            {
                strategyToExecute = "S1000";
            }
        }

        return (stopRepayments, strategyToExecute);
    }


    public async Task<IRepaymentStrategy?> ExecuteStrategyAsync(string strategyName, Order order,
        IReadOnlyDictionary<string, string> strategyParameters,
        PostgreSQLDbContext dbContext,
        IRepaymentStrategyFactoryService repaymentStrategyFactoryService)
    {
        IRepaymentStrategy? repaymentStrategy = null;

        if (!string.IsNullOrWhiteSpace(strategyName))
        {
            Guid orderId = order.Id;

            order.StrategySequenceCounter++;
            order.CurrentStrategyId = strategyName;
            await dbContext.SaveChangesAsync();

            Guid schedulerJobId = Guid.NewGuid();

            repaymentStrategy =
                await repaymentStrategyFactoryService.CreateRepaymentStrategyAsync(
                    order.CurrentStrategyId, schedulerJobId, order, strategyParameters);


            await repaymentStrategy.ExecuteAsync();
        }

        return repaymentStrategy;
    }
}