using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon;
using Amazon.Athena;
using Amazon.Athena.Model;
using Amazon.Internal;
using Amazon.Runtime;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Utils;
using Newtonsoft.Json;
using SixLabors.ImageSharp.ColorSpaces.Conversion;


namespace FlexCharge.Orders.Services;

public class AthenaService : IAthenaService
{
    private readonly IAthenaDBService _athenaDbService;

    public AthenaService(IAthenaDBService athenaDbService)
    {
        _athenaDbService = athenaDbService;
    }

    public async Task<DashboardDataDTO> ExecuteDashboardQueryAsync(string query,
        string athenaDatabaseName,
        string athenaOutputBucket,
        int executionTimeout,
        int retryTimeInterval)
    {
        DashboardDataDTO data = new DashboardDataDTO
        {
            MerchantId = default,
            StoreName = null,
            StoreId = null
        };

        await _athenaDbService.ExecuteQueryAsync(query, athenaDatabaseName, athenaOutputBucket,
            async (row, columnInfoList, endOfBatch) =>
            {
                var dailyKPIs = ProcessDashboardRow(row, columnInfoList);
                data.DailyKPIs.Add(dailyKPIs);
                await Task.CompletedTask;
            },
            executionTimeout, retryTimeInterval);

        return data;
    }

    public async Task<DashboardAggregatedDataDTO> ExecuteDashboardAggregatedKPIsQueryAsync(string query,
        string athenaDatabaseName,
        string athenaOutputBucket,
        int executionTimeout,
        int retryTimeInterval)
    {
        DashboardAggregatedDataDTO data = new DashboardAggregatedDataDTO
        {
            MerchantId = default,
            StoreName = null,
            StoreId = null
        };

        await _athenaDbService.ExecuteQueryAsync(query, athenaDatabaseName, athenaOutputBucket,
            async (row, columnInfoList, endOfBatch) =>
            {
                ProcessAggregatedDashboardRow(row, columnInfoList, data);
                await Task.CompletedTask;
            },
            executionTimeout, retryTimeInterval);

        return data;
    }

    private DailyKPIs ProcessDashboardRow(Row row, List<ColumnInfo> columnInfoList)
    {
        DailyKPIs kpis = new DailyKPIs();

        for (int i = 0; i < columnInfoList.Count; ++i)
        {
            try
            {
                var rowValue = row.Data[i]?.VarCharValue;
                switch (columnInfoList[i].Name)
                {
                    case "timestamp":
                        if (rowValue != null)
                            kpis.CreatedDate = DateTime.Parse(rowValue);
                        break;

                    #region Transmitted payments

                    case "total_payments_count":
                        kpis.TotalPaymentsCount = ParseCount(rowValue);
                        break;
                    case "total_payments_amount":
                        kpis.TotalPaymentsAmount = ParseAmountToLong(rowValue);
                        break;

                    case "declined_payments_count":
                        kpis.DeclinedPaymentsCount = ParseCount(rowValue);
                        break;
                    case "declined_payments_amount":
                        kpis.DeclinedPaymentsAmount = ParseAmountToLong(rowValue);
                        break;

                    case "successful_payments_count":
                        kpis.SuccessfulPaymentsCount = ParseCount(rowValue);
                        break;
                    case "successful_payments_amount":
                        kpis.SuccessfulPaymentsAmount = ParseAmountToLong(rowValue);
                        break;

                    case "credited_payments_count":
                        kpis.CreditedPaymentsCount = ParseCount(rowValue);
                        break;
                    case "credited_payments_amount":
                        kpis.CreditedPaymentsAmount = ParseAmountToLong(rowValue);
                        break;

                    #endregion

                    case "lost_payments_count":
                        kpis.LostPaymentsOrdersCount = ParseCount(rowValue);
                        break;
                    case "lost_payments_amount":
                        kpis.LostPaymentsAmount = ParseAmountToLong(rowValue);
                        break;

                    case "lost_payments_average_amount":
                        kpis.LostPaymentsAverageAmount = ParseAmountToLong(rowValue);
                        break;

                    case "lost_customer_payments_count":
                        kpis.LostCustomerPaymentsOrdersCount = ParseCount(rowValue);
                        break;
                    case "lost_customer_payments_amount":
                        kpis.LostCustomerPaymentsAmount = ParseAmountToLong(rowValue);
                        break;
                    case "lost_customer_payments_average_amount":
                        kpis.LostCustomerPaymentsAverageAmount = ParseAmountToLong(rowValue);
                        break;
                    case "lost_revenue_total_amount":
                        kpis.RevenueLostTotal = ParseAmountToLong(rowValue);
                        break;
                    case "lost_revenue_percentage":
                        kpis.RevenueLostTotalPercentage = ParsePercentage(rowValue);
                        break;


                    case "evaluated_payments_count":
                        kpis.EvaluatedOffersCount = ParseCount(rowValue);
                        break;
                    case "evaluated_payments_amount":
                        kpis.EvaluatedOffersAmount = ParseAmountToLong(rowValue);
                        break;

                    case "eligible_offers_count":
                        kpis.EligibleOffersCount = ParseCount(rowValue);
                        break;
                    case "eligible_offers_amount":
                        kpis.EligibleOffersAmount = ParseAmountToLong(rowValue);
                        break;

                    case "approved_offers_count":
                        kpis.ApprovedOrdersCount = ParseCount(rowValue);
                        break;
                    case "approved_offers_amount":
                        kpis.ApprovedOrdersAmount = ParseAmountToLong(rowValue);
                        break;

                    case "distinct_days": break;
                    case "lost_customer_method": break;
                    case "churn_rate": break;
                    case "clv": break;
                    case "saved_revenue_total_amount": break;
                    case "saved_revenue_percentage": break;
                    case "merchant_name": break;
                    case "mid": break;
                    case "year": break;
                    case "month": break;
                    case "day": break;


                    default:
#if DEBUG
                        Console.WriteLine($"case \"{columnInfoList[i].Name}\": break;");
#endif
                        break;
                }
            }
            catch
            {
                continue;
            }
        }

        return kpis;
    }

    private void ProcessAggregatedDashboardRow(Row row, List<ColumnInfo> columnInfoList,
        DashboardAggregatedDataDTO data)
    {
        for (int i = 0; i < columnInfoList.Count; ++i)
        {
            try
            {
                var rowValue = row.Data[i]?.VarCharValue;

                string rangePostfix = "";
                int postfixStartIndex = columnInfoList[i].Name.LastIndexOf("_");
                if (postfixStartIndex >= 0 && postfixStartIndex + 1 < columnInfoList[i].Name.Length)
                {
                    rangePostfix = columnInfoList[i].Name.Substring(postfixStartIndex + 1);
                }

                PaymentProcessingStatistics merchantKPIs = null;
                DeclinedPaymentsReceived fcKPIs = null;
                switch (rangePostfix)
                {
                    case "1days":
                        merchantKPIs = data.MerchantDayKPIs;
                        fcKPIs = data.FlexChargeDayKPIs;
                        break;
                    case "7days":
                        merchantKPIs = data.MetchantWeekKPIs;
                        fcKPIs = data.FlexChargeWeekKPIs;
                        break;
                    case "30days":
                        merchantKPIs = data.MerchantMonthKPIs;
                        fcKPIs = data.FlexChargeMonthKPIs;
                        break;
                    case "360days":
                        merchantKPIs = data.MerchantYearKPIs;
                        fcKPIs = data.FlexChargeYearKPIs;
                        break;
                    default:
                        continue;
                }

                string dataName = columnInfoList[i].Name.Substring(0, postfixStartIndex);

                switch (dataName)
                {
                    // case "timestamp":
                    //     if (rowValue != null)
                    //         data.DayKPIs.TimeStamp = DateTime.Parse(rowValue);
                    //     break;

                    case "lost_payments_count":
                        merchantKPIs.Lost_Orders_DueToDeclines_Count = ParseCount(rowValue);
                        break;

                    case "total_payments_count":
                        merchantKPIs.Processed_Payments_Count = ParseCount(rowValue);
                        break;

                    case "total_payments_amount":
                        merchantKPIs.Processed_Payments_TotalAmount = ParseAmountToDecimal(rowValue);
                        break;
                    case "successful_payments_count":
                        merchantKPIs.Successful_Payments_Count = ParseCount(rowValue);
                        break;
                    case "successful_payments_amount":
                        merchantKPIs.Successful_Payments_TotalAmount = ParseAmountToDecimal(rowValue);
                        break;
                    case "declined_payments_count":
                        merchantKPIs.Declined_Payments_Count = ParseCount(rowValue);
                        break;

                    case "declined_payments_amount":
                        merchantKPIs.Declined_Payments_TotalAmount = ParseAmountToDecimal(rowValue);
                        break;

                    case "lost_customer_payments_average_amount":
                        merchantKPIs.Avg_LostValue_LostClients_DueToDeclines_Amount = ParseAmountToDecimal(rowValue);
                        break;

                    // case "lost_payments_count":
                    //     kpis.Lost = ParseCount(rowValue);
                    //     break;

                    case "lost_payments_amount":
                        merchantKPIs.Lost_Orders_DueToDeclines_Amount = ParseAmountToDecimal(rowValue);
                        break;

                    case "lost_payments_average_amount":
                        merchantKPIs.Avg_LostValue_DueToDeclines_Amount = ParseAmountToDecimal(rowValue);
                        break;


                    // case "lost_payments_average_amount":
                    //     kpis.Aver = ParseAmount(rowValue);
                    //     break;

                    case "lost_customer_payments_count":
                        merchantKPIs.Lost_Clients_DueToDeclines_Count = ParseCount(rowValue);
                        break;

                    case "lost_customer_payments_amount":
                        merchantKPIs.Lost_Clients_DueToDeclines_Amount = ParseAmountToDecimal(rowValue);
                        break;

                    // case "lost_customer_payments_average_amount":
                    //     kpis.Aver= ParseAmount(rowValue);
                    //     break;

                    case "lost_revenue_total_amount":
                        merchantKPIs.LostRevenue_DueToDeclines_Amount = ParseAmountToDecimal(rowValue);
                        break;

                    case "lost_revenue_percentage":
                        merchantKPIs.LostRevenue_DueToDeclines_Percentage =
                            ParsePercentage(rowValue);
                        break;

                    case "distinct_days": break;

                    case "credited_payments_count": break;
                    case "credited_payments_amount": break;

                    case "lost_customer_method": break;
                    case "churn_rate": break;
                    case "clv": break;

                    case "evaluated_payments_count":
                        fcKPIs.Declined_Payments_Count = ParseCount(rowValue);
                        break;
                    case "evaluated_payments_amount":
                        fcKPIs.Declined_Payments_TotalAmount = ParseAmountToDecimal(rowValue);
                        break;

                    case "eligible_offers_count":
                        fcKPIs.Declined_EligiblePayments_Count = ParseCount(rowValue);
                        break;
                    case "eligible_offers_amount":
                        fcKPIs.Declined_EligiblePayments_TotalAmount = ParseAmountToDecimal(rowValue);
                        break;

                    case "approved_offers_count":
                        fcKPIs.Declined_PaymentsProcessed_Count = ParseCount(rowValue);
                        break;
                    case "approved_offers_amount":
                        fcKPIs.Declined_PaymentsProcessed_TotalAmount = ParseAmountToDecimal(rowValue);
                        break;

                    case "saved_revenue_total_amount":
                        fcKPIs.SavedRevenue_By_FlexCharge_Amount = ParseAmountToDecimal(rowValue);
                        break;
                    case "saved_revenue_percentage":
                        fcKPIs.SavedRevenue_By_FlexCharge_Percentage = ParsePercentage(rowValue);
                        break;


                    default:
#if DEBUG
                        Console.WriteLine($"case \"{dataName}\": break;");
#endif
                        break;
                }
            }
            catch
            {
                continue;
            }
        }
    }

    private static decimal ParsePercentage(string rowValue)
    {
        return rowValue is null ? 0 : decimal.Parse(rowValue);
    }

    private static int ParseCount(string rowValue)
    {
        return rowValue is null
            ? 0
            : int.Parse(rowValue);
    }

    private static long ParseAmountToLong(string rowValue)
    {
        return rowValue is null
            ? 0
            : long.Parse(rowValue);
    }


    private static decimal ParseAmountToDecimal(string rowValue)
    {
        return Formatters.LongToDecimal(ParseAmountToLong(rowValue));
    }

    #region Commented

// private static void ProcessRow(Row row, List<ColumnInfo> columnInfoList)
// {
//     for (int i = 0; i < columnInfoList.Count; ++i)
//     {
//         switch (columnInfoList[i].Type)
//         {
//             case "varchar":
//                 // Convert and Process as String
//                 break;
//             case "tinyint":
//                 // Convert and Process as tinyint
//                 break;
//             case "smallint":
//                 // Convert and Process as smallint
//                 break;
//             case "integer":
//                 // Convert and Process as integer
//                 break;
//             case "bigint":
//                 // Convert and Process as bigint
//                 break;
//             case "double":
//                 // Convert and Process as double
//                 break;
//             case "boolean":
//                 // Convert and Process as boolean
//                 break;
//             case "date":
//                 // Convert and Process as date
//                 break;
//             case "timestamp":
//                 // Convert and Process as timestamp
//                 break;
//             default:
//                 throw new Exception("Unexpected Type is not expected" + columnInfoList[i].Type);
//         }
//     }
//}

    #endregion
}