using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Shared.Orders;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.Contracts;
using FlexCharge.Orders.DistributedLock;
using FlexCharge.Orders.DTO.Refunds;
using FlexCharge.Orders.Services.PayoutServices;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Merchant = FlexCharge.Orders.Entities.Merchant;

namespace FlexCharge.Orders.Services
{
    public partial class OrderService : IOrderService
    {
        private IHttpContextAccessor _httpContext { get; set; }
        private PostgreSQLDbContext _dbContext;

        private IMapper _mapper;

        private AppOptions _globalData;
        private readonly IPublishEndpoint _publisher;

        private readonly IRepaymentStrategyManagerService _repaymentStrategyManagerService;
        private readonly IActivityService _activityService;
        private readonly IPayoutService _payoutService;
        private readonly IDistributedLockService _distributedLockService;
        private readonly ISecurityCheckService _securityCheckService;

        private readonly IRepaymentStrategyFactoryService _repaymentStrategyFactoryService;
        private readonly IRequestClient<CreditPaymentCommand> _creditPaymentClient;
        private readonly ReadOnlyPostgreSQLDbContext _readOnlyDbContext;


        public OrderService(IHttpContextAccessor httpContext, PostgreSQLDbContext dbContext,
            IMapper mapper,
            IOptions<AppOptions> globalData,
            IPublishEndpoint publisher,
            IRepaymentStrategyFactoryService repaymentStrategyFactoryService,
            IRepaymentStrategyManagerService repaymentStrategyManagerService,
            IActivityService activityService,
            IPayoutService payoutService,
            IDistributedLockService distributedLockService,
            ISecurityCheckService securityCheckService,
            ReadOnlyPostgreSQLDbContext readOnlyDbContext,
            IRequestClient<CreditPaymentCommand> creditPaymentClient)
        {
            _httpContext = httpContext;
            _dbContext = dbContext;
            _mapper = mapper;
            //_logger = logger;
            _publisher = publisher;
            _repaymentStrategyManagerService = repaymentStrategyManagerService;
            _activityService = activityService;
            _payoutService = payoutService;
            _distributedLockService = distributedLockService;
            _securityCheckService = securityCheckService;
            _creditPaymentClient = creditPaymentClient;
            _repaymentStrategyFactoryService = repaymentStrategyFactoryService;
            _globalData = globalData.Value;
            _readOnlyDbContext = readOnlyDbContext;
        }

        public async Task<OrderCreateResponse> CreateAsync(OrderCreateDTO payload)
        {
            using var workspan = Workspan.Start<OrderService>();

            try
            {
                workspan.Log.Information(
                    $"ENTERED: OrderService => CreateAsync {JsonConvert.SerializeObject(payload)}");
                var response = new OrderCreateResponse();

                var order = _mapper.Map<Entities.Order>(payload);

                order.MerchantAdditionalFields = payload.AdditionalFields != null
                    ? JsonConvert.SerializeObject(payload.AdditionalFields)
                    : null;

                await _dbContext.Orders.AddAsync(order);
                await _dbContext.SaveChangesAsync();

                response.Order = _mapper.Map<OrderQueryDTO>(order);

                workspan.Log.Information(
                    $"SUCCESS: OrderService => CreateAsync {JsonConvert.SerializeObject(response.Order)}");
                await _publisher.Publish(_mapper.Map<OrderCreatedEvent>(order));

                // else
                // {
                //     workspan.Log.Information(
                //         $"FAILED: OrderService => CreateAsync {JsonConvert.SerializeObject(response)}");
                //     await _publisher.Publish<Entities.Order>(_mapper.Map<OrderCreateFailedEvent>(order));
                // }

                return response;
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "EXCEPTION: OrderService => Unable to create order");
                throw;
            }
        }

        public async Task<OrderUpdateResponse> UpdateAsync(OrderUpdateDTO payload)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Tag("Payload", payload)
                .LogEnterAndExit();

            var response = new OrderUpdateResponse();

            try
            {
                //var order = await _dbContext.FindAsync<Entities.Order>(payload.Id);

                var orderAndMerchant = await _dbContext.Orders
                    .Where(o => o.Id == payload.Id)
                    .Join(_dbContext.Merchants, o => o.MerchantId, m => m.Mid,
                        (ord, merchant) => new
                        {
                            Order = ord,
                            Merchant = merchant
                        })
                    .SingleOrDefaultAsync();

                var order = orderAndMerchant?.Order;
                var merchant = orderAndMerchant?.Merchant;

                if (order == null)
                {
                    response.AddError($"order not found for id:{payload.Id}");
                    return response;
                }

                var updatedOrderEntity = _dbContext.Orders.Update(_mapper.Map(payload, order));
                await _dbContext.SaveChangesAsync();

                if (updatedOrderEntity.Entity.Id == Guid.Empty)
                    response.AddError("Unable to update order");

                response.Order = _mapper.Map<OrderUpdateDTO>(updatedOrderEntity.Entity);

                await _publisher.Publish(new OrderUpdatedEvent
                {
                    OrderId = order.Id,
                    ExternalOrderId = order.ReferenceNumber,
                    ConfirmationId = order.ConfirmationId,
                    //OldOrder = JsonConvert.SerializeObject(oldOrder),
                    UpdatedOrder = JsonConvert.SerializeObject(order),
                    Mid = order.MerchantId,
                    Pid = merchant.Pid,
                    StatusCategory = order.StatusCategory
                });

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                await _publisher.Publish<OrderUpdateFailedEvent>(new {OrderId = payload.Id});
                throw;
            }
        }

        public async Task<OrderUpdateResponse> SetOrderStatusAsync(Guid mid, Guid orderId,
            OrderStatusCategory status, string statusSubCategory, string statusDescription,
            Action<Order> updateOrderAction = null)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Baggage("Mid", mid)
                .Baggage("OrderId", orderId)
                .Baggage("Status", status)
                .Tag("SubCategory", statusSubCategory)
                .Tag("Description", statusDescription);


            var response = new OrderUpdateResponse();

            try
            {
                workspan.Log.Information("Updating order status to {Status}", status);

                var orderAndMerchant = await _dbContext.Orders
                    .Where(o => o.Id == orderId && o.MerchantId == mid)
                    .Join(_dbContext.Merchants, o => o.MerchantId, m => m.Mid,
                        (ord, merchant) => new
                        {
                            Order = ord,
                            Merchant = merchant
                        })
                    .SingleOrDefaultAsync();

                var order = orderAndMerchant?.Order;
                if (order == null)
                {
                    workspan.Log.Fatal(
                        "NOTFOUND: Order > {Id} status: {Status}, message: {Message}",
                        orderId, status, statusDescription);

                    response.AddError($"Order not found for id: {orderId}");
                    return response;
                }

                var merchant = orderAndMerchant?.Merchant;

                return await SetOrderStatusAsync(mid, merchant.Pid, order, status, statusSubCategory, statusDescription,
                    updateOrderAction: updateOrderAction);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Unable to set order status");
                await _publisher.Publish<OrderUpdateFailedEvent>(new {OrderId = orderId});
                throw;
            }
        }

        public async Task<OrderUpdateResponse> SetOrderStatusAsync(Guid mid, Guid? pid, Order order,
            OrderStatusCategory status, string statusSubCategory, string statusDescription, bool saveChanges = true,
            Action<Order> updateOrderAction = null)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Baggage("Mid", mid)
                .Baggage("Pid", pid)
                .Baggage("OrderId", order.Id)
                .Tag("Status", status)
                .Tag("SubCategory", statusSubCategory)
                .Tag("Description", statusDescription)
                .LogEnterAndExit();


            var response = new OrderUpdateResponse();

            try
            {
                if (!order.IsNotInTerminalState())
                {
                    // can't update order status - order is in some terminal state
                    // can be in case outdated update event came from SQS queue after
                    // order is finished

                    if ((order.IsOrderCompleted() && status != OrderStatusCategory.completed) ||
                        (status == OrderStatusCategory.completed && !order.IsOrderCompleted()))
                    {
                        workspan.Log.Fatal(
                            "Order is in completed terminal state - skipping status update ({OrderStatus}) ",
                            status);

                        #region Creating Activity

                        await _activityService.CreateActivityAsync(
                            OrdersProcessingErrorActivities.Order_AlreadyInTerminalState_Error,
                            set => set
                                .TenantId(order.MerchantId)
                                .CorrelationId(order.Id));

                        #endregion
                    }
                    else
                    {
                        workspan.Log.Information(
                            "Order is in terminal state ({OrderStatus}) - skipping status update",
                            status);
                    }

                    return response;
                }

                var newStatusCategory = status.ToString();
                bool statusChanged = order.StatusCategory != newStatusCategory;

                order.StatusCategory = newStatusCategory;
                order.StatusSubCategory = statusSubCategory;
                order.StatusDescription = statusDescription;

                if (updateOrderAction != null) updateOrderAction(order);

                _dbContext.Orders.Update(order);

                if (saveChanges) await _dbContext.SaveChangesAsync();

                workspan.Log.Information("Order status updated to {Status}", status);

                if (statusChanged)
                {
                    // Sending order.expired webhook instead of cancelled
                    // Maybe it's better to add expired order status category in the future
                    var orderStatusCategory = order.StatusSubCategory == "NOT_ELIGIBLE_EXPIRED"
                        ? "expired"
                        : order.StatusCategory.ToString();

                    await _publisher.Publish(new OrderUpdatedEvent
                    {
                        OrderId = order.Id,
                        ExternalOrderId = order.ReferenceNumber,
                        ConfirmationId = order.ConfirmationId,
                        //OldOrder = JsonConvert.SerializeObject(oldOrder),
                        UpdatedOrder = JsonConvert.SerializeObject(order),
                        Mid = mid,
                        Pid = pid,
                        StatusCategory = orderStatusCategory
                    });

                    try
                    {
                        await _activityService.CreateActivityAsync(OrdersProcessingActivities.Order_StatusUpdated,
                            order,
                            set => set.TenantId(mid).CorrelationId(order.Id)
                                .Meta(meta => meta
                                    .SetValue("StatusCategory", order.StatusCategory)
                                    .SetValue("SubCategory", order.StatusSubCategory)
                                    .SetValue("Description", order.StatusDescription)));
                    }
                    catch (Exception e)
                    {
                        workspan.RecordFatalException(e);
                    }
                }

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Unable to set order status");
                await _publisher.Publish<OrderUpdateFailedEvent>(new {OrderId = order.Id});
                throw;
            }
        }

        public async Task UpdateOrderStateAsync(Guid mid, Guid orderId)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Tag("Mid", mid)
                .Tag(nameof(orderId), orderId);

            try
            {
                var order = await _dbContext.Orders.Where(x => x.MerchantId == mid && x.Id == orderId)
                    .Include(x => x.ActivityItems)
                    .SingleOrDefaultAsync();
                if (order == null)
                {
                    workspan.AddError($"NOTFOUND: Order not found");

                    return;
                }

                // we mark as completed only orders with all paid in amount settled 
                if (order.IsFullyPaidIn(calculateOnlySettledAmount: true))
                    await SetOrderPayInStatusCompleted(order);

                _dbContext.Orders.Update(order);

                await _dbContext.SaveChangesAsync();
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Unable to update order state");
                throw;
            }
        }

        public async Task<ApplyOrderActivityResponse> ApplyOrderActivityAsync(OrderActivityDTO payload)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Payload(payload);

            try
            {
                var response = new ApplyOrderActivityResponse();

                var order = await _dbContext.Orders
                    .Where(x => x.Id == payload.OrderId)
                    .Include(x => x.ActivityItems)
                    .SingleOrDefaultAsync();

                if (order == null)
                {
                    var error = $"Order not found for OrderId: {payload.OrderId}";
                    workspan.Log.Fatal(error);

                    throw new FlexChargeException(message: error);
                }

                var transaction = _mapper.Map<Entities.Transaction>(payload);

                if (!string.IsNullOrWhiteSpace(payload.Bin) && !string.IsNullOrWhiteSpace(payload.Last4))
                {
                    order.Bin = payload.Bin;
                    order.Last4 = payload.Last4;
                }

                _dbContext.Orders.Update(order);
                _dbContext.Transactions.Add(transaction);
                await _dbContext.SaveChangesAsync();


                if (order.IsOrderCompleteOnlyWhenFullyPaidIn() &&
                    order.IsFullyPaidIn(calculateOnlySettledAmount: true))
                {
                    await SetOrderStatusAsync(order.MerchantId, order.Id,
                        OrderStatusCategory.completed, "", "");
                }

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                throw;
            }
        }

        public async Task ApplyOrderDiscountAsync(Guid mid, Guid orderId, int amount, string description)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Tag("OrderId", orderId)
                .Tag("Amount", amount);

            try
            {
                // being invoked after eligibility approval (recurring payments strategy, etc.)

                var order = await _dbContext.Orders.Where(x => x.MerchantId == mid && x.Id == orderId)
                    .Include(x => x.ActivityItems).SingleOrDefaultAsync();

                if (order == null)
                {
                    workspan.Log.Fatal(
                        "NOTFOUND: OrderService => ApplyOrderDiscountAsync > {OrderId} mid: {Mid}",
                        orderId, mid);

                    return;
                }

                order.DiscountsAmount += amount;

                _dbContext.Orders.Update(order);
                await _dbContext.SaveChangesAsync();
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
            }
        }

        public async Task ProcessCancelledRefundAsync(Guid mid, Guid orderId, int amount, string message,
            DateTime cancelRefundDateTime)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Tag(nameof(mid), mid)
                .Tag(nameof(orderId), orderId)
                .Tag(nameof(amount), amount)
                .Tag(nameof(message), message);

            try
            {
                // being invoked after eligibility approval (recurring payments strategy, etc.)

                var order = await _dbContext.Orders.Where(x => x.MerchantId == mid && x.Id == orderId)
                    .Include(x => x.ActivityItems).SingleOrDefaultAsync();
                if (order == null)
                {
                    workspan.Log.Fatal(
                        "NOTFOUND: OrderService => ProcessCancelledRefundAsync > {orderId} mid: {mid}",
                        orderId, mid);

                    return;
                }

                order.RefundsAmount -= amount;

                _dbContext.Orders.Update(order);
                await _dbContext.SaveChangesAsync();
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
            }
        }

        public async Task<ApplyOrderActivityResponse> ApplyChargebackToOrderAsync(OrderActivityDTO payload)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Payload(payload);

            try
            {
                var response = new ApplyOrderActivityResponse();

                var orderAndMerchant = await GetOrderAndMerchantIncludingActivityAsync(payload.OrderId);

                var order = orderAndMerchant.Order;
                var merchant = orderAndMerchant.Merchant;

                await ApplyActivityAsync(payload, order);

                await _publisher.Publish(new OrderChargedBackEvent
                {
                    OrderId = order.Id,
                    ExternalOrderId = order?.ReferenceNumber,
                    Mid = order.MerchantId,
                    Pid = merchant.Pid,
                    Amount = order.Amount,
                    DisputeDateTime = payload.PaymentDate,
                    Reason = payload.Description,
                    TransactionId = payload.PaymentTransactionId,
                    Currency = order.Currency
                });

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                throw;
            }
        }

        public async Task<ApplyOrderActivityResponse> ApplyAlertToOrderAsync(OrderActivityDTO payload)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Payload(payload);

            try
            {
                var response = new ApplyOrderActivityResponse();

                var orderAndMerchant = await GetOrderAndMerchantIncludingActivityAsync(payload.OrderId);

                var order = orderAndMerchant.Order;

                await ApplyActivityAsync(payload, order);

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                throw;
            }
        }

        private async Task<(Order Order, Merchant Merchant)> GetOrderAndMerchantIncludingActivityAsync(Guid orderId)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Baggage("OrderId", orderId);

            // var order = await _dbContext.Orders
            //     .Where(x => x.Id == payload.OrderId)
            //     .Include(x => x.ActivityItems)
            //     .SingleOrDefaultAsync();

            var orderAndMerchant = await _dbContext.Orders
                .Where(o => o.Id == orderId)
                .Include(x => x.ActivityItems)
                .Join(_dbContext.Merchants, o => o.MerchantId, m => m.Mid,
                    (ord, merchant) => new
                    {
                        Order = ord,
                        Merchant = merchant
                    })
                .SingleOrDefaultAsync();

            var order = orderAndMerchant?.Order;


            if (order == null)
            {
                var error = $"order not found for OrderId: {orderId}";
                workspan.Log.Fatal(error);

                throw new FlexChargeException("ORDER_NOT_FOUND", "Order not found");
            }

            return (order, orderAndMerchant.Merchant);
        }

        public async Task<ApplyOrderActivityResponse> ApplyActivityAsync(OrderActivityDTO payload, Order order)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Payload(payload);

            try
            {
                var response = new ApplyOrderActivityResponse();

                var transaction = _mapper.Map<Entities.Transaction>(payload);
                _dbContext.Transactions.Add(transaction);
                await _dbContext.SaveChangesAsync();


                if (order.IsOrderCompleteOnlyWhenFullyPaidIn() &&
                    order.IsFullyPaidIn(calculateOnlySettledAmount: true))
                {
                    await SetOrderStatusAsync(order.MerchantId, order.Id,
                        OrderStatusCategory.completed, "", "");
                }

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                throw;
            }
        }

        public async Task<Merchant> GetMerchantAsync(Guid mid)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Tag(nameof(mid), mid)
                .LogEnterAndExit();

            try
            {
                return await _dbContext.Merchants.SingleOrDefaultAsync(x => x.Mid == mid);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "EXCEPTION: orderService => Unable to get merchant ");
                throw;
            }
        }

        private async Task SetOrderPayInStatusCompleted(Order order)
        {
            if (order.OrderPayinStatus == PayInStatuses.Completed) return;

            var workspan = Workspan.Current;

            workspan.Log.Information("Marking order as Completed");

            #region Creating Activity

            await _activityService.CreateActivityAsync(PayInActivities.PayIn_MarkedOrderAsCompleted,
                set => set
                    .TenantId(order.MerchantId)
                    .CorrelationId(order.Id));

            #endregion

            _dbContext.Attach(order);
            order.OrderPayinStatus = PayInStatuses.Completed;
            await _dbContext.SaveChangesAsync();

            await _publisher.Publish<OrderFullyPaidInEvent>(new
            {
                OrderId = order.Id,
                Mid = order.MerchantId
            });
        }

        public async Task ProcessApprovedOfferAsync(Guid mid, Guid orderId, Guid? siteId,
            bool isCit,
            bool isTestOrder,
            bool isInitial,
            string paymentInstrumentToken, string scaAuthenticationToken,
            string lastResponseCodeGateway,
            string lastResponseCode,
            string cvvCheckResult,
            string merchantDescriptor,
            string? orderConfirmationId,
            bool isPaidOutOfBand)
        {
            using var workspan = Workspan.Start<OrderService>();
            try
            {
                // Being invoked after eligibility approval (recurring payments strategy, etc.)

                // Note: same status can be received more than once - it's normal in distributed systems
                workspan.Log.Information("Processing approved order");

                var orderAndMerchant = await _dbContext.Orders
                    .Include(x => x.ActivityItems)
                    .Where(x => x.MerchantId == mid && x.Id == orderId)
                    .Join(_dbContext.Merchants, o => o.MerchantId, m => m.Mid,
                        (ord, merchant) => new
                        {
                            Order = ord,
                            Merchant = merchant
                        })
                    .SingleOrDefaultAsync();

                var order = orderAndMerchant?.Order;
                var merchant = orderAndMerchant?.Merchant;

                if (order == null)
                {
                    workspan.Log.Fatal(
                        "NOTFOUND: OrderService => ProcessCompletedOrder > {OrderId} mid: {Mid}",
                        orderId, mid);

                    return;
                }

                if (order.OrderPlacedDate > DateTime.MinValue) // skipping duplicate status update
                {
                    // Note: same status can be received more than once - it's normal in distributed systems
                    workspan.Log.Information("Order is already placed - skipping status update");
                    return;
                }

                //var merchantSite = await _dbContext.Sites.Where(x => x.Id == siteId).SingleOrDefaultAsync();


                OrderStatusCategory status = order.IsOrderCompleteOnlyWhenFullyPaidIn()
                    ? OrderStatusCategory.processing
                    : OrderStatusCategory.completed;

                order.IsTestOrder = isTestOrder;

                order.PaymentInstrumentToken = paymentInstrumentToken;
                order.SCAAuthenticationToken = scaAuthenticationToken;
                order.AddPaymentTransactionResponse(new PaymentTransactionResponse(
                    TransactionType.InitialResponseCode, lastResponseCodeGateway, "", lastResponseCode));

                order.CVVCheckResult = cvvCheckResult;

                order.MerchantDescriptor = merchantDescriptor;

                order.ConfirmationId = orderConfirmationId;
                order.OrderPlacedDate = DateTime.UtcNow;
                order.PaidOutOfBand = isPaidOutOfBand;

                order.SiteId = siteId;

                if (order.IsMIT)
                {
                    //MIT orders are always fully paid in and captured as we are not taking risk
                    order.OrderPayinStatus = PayInStatuses.Completed;
                }

                await SetOrderStatusAsync(mid, merchant.Pid, order, status, "", "");

                var voidTransactionIds = order.ActivityItems.FilterVoidTransactions()
                    .Select(x => x.RelatedTransactionId).ToHashSet();

                var captureTransactionIds = order.ActivityItems.FilterCaptureTransactions()
                    .Select(x => x.RelatedTransactionId).ToHashSet();


                var activeAuthorizationTransactions = order.ActivityItems.FilterAuthorizationTransactions()
                    .Where(x =>
                        !voidTransactionIds.Contains(x.PaymentTransactionId) &&
                        !captureTransactionIds.Contains(x.PaymentTransactionId)).ToList();

                var activeSaleTransactions = order.ActivityItems.FilterSaleTransactions()
                    .Where(x => !voidTransactionIds.Contains(x.PaymentTransactionId)).ToList();

                var activeCaptureTransactions = order.ActivityItems.FilterCaptureTransactions()
                    .Where(x => !voidTransactionIds.Contains(x.PaymentTransactionId)).ToList();


                #region Processing Approved Order

                //Only CIT orders can use repayment strategies in Orders microservice

                if (activeSaleTransactions.Count > 0)
                {
                    #region Processing order with active sale transaction

                    if (activeSaleTransactions.Count == 1)
                    {
                        // don't do anything if there is only one sale transaction
                    }
                    else
                    {
                        #region Error: More than one active sale transaction found

                        workspan.Log.Fatal(
                            "More than one active sale transaction found - cannot proceed with Payment Capture");

                        await _activityService.CreateActivityAsync(
                            PayInErrorActivities
                                .PayIn_ProcessingApprovedOrder_MoreThanOneActiveSaleTransaction_Error,
                            set => set.TenantId(order.MerchantId).CorrelationId(order.Id));

                        #endregion
                    }

                    if (activeAuthorizationTransactions.Count > 0)
                    {
                        #region Error: Sale transaction is active and there is at least one active authorization transaction

                        workspan.Log.Fatal(
                            "Sale transaction is active and there is at least one active authorization transaction");

                        await _activityService.CreateActivityAsync(
                            PayInErrorActivities
                                .PayIn_ProcessingApprovedOrder_SaleAndAuthorizationInSame_Order_Error,
                            set => set.TenantId(order.MerchantId).CorrelationId(order.Id));

                        #endregion
                    }

                    #endregion
                }

                #region [Commented] Active authorizations now captured in Eligibility MS

                // else if (activeAuthorizationTransactions.Count == 1) // Add Capture if authorization was successful
                // {
                //     var lastAndOnlyTransaction = activeAuthorizationTransactions.Single();
                //     // if (lastAndOnlyTransaction.Type is "authorization" or "Authorization")
                //     // {
                //     var strategyParameters = new Dictionary<string, string>();
                //     strategyParameters["authTransId"] =
                //         lastAndOnlyTransaction.PaymentTransactionId.ToString();
                //     strategyParameters["isCIT"] = isCit.ToString();
                //     strategyParameters["isInitial"] = isInitial.ToString();
                //
                //     await _repaymentStrategyManagerService.ExecuteStrategyAsync(
                //         "S0001",
                //         order,
                //         strategyParameters,
                //         _dbContext,
                //         _repaymentStrategyFactoryService);
                //
                //     #region Commented
                //
                //     // }
                //     // else
                //     // {
                //     //     workspan.Log.Information(
                //     //         "No Authorization transaction - cannot proceed with Payment Capture");
                //     //
                //     //     await _activityService.CreateActivityAsync(
                //     //         PayInActivities.PayIn_ProcessingApprovedOrder_NoAuthorizationTransactionFound,
                //     //         set => set.TenantId(order.MerchantId).CorrelationId(order.Id));
                //     // }
                //
                //     #endregion
                // }

                #endregion

                else if (activeAuthorizationTransactions.Count > 0)
                {
                    #region Error: An active authorization transaction found

                    workspan.Log.Fatal("An active authorization transaction found");

                    await _activityService.CreateActivityAsync(
                        PayInErrorActivities
                            .PayIn_ProcessingApprovedOrder_ActiveAuthorizationTransactionFound_Error,
                        set => set.TenantId(order.MerchantId).CorrelationId(order.Id));

                    #endregion
                }
                else // No active sale or authorization transactions found
                {
                }

                #endregion


                await _activityService.CreateActivityAsync(OrdersProcessingActivities.Order_Placed, order,
                    set => set.TenantId(mid).CorrelationId(orderId)
                        .Meta(meta => meta
                            .SetValue("Amount", order.Amount)
                            .SetValue("MIT", order.IsMIT == true)
                            .SetValue("PaidOutOfBand", order.PaidOutOfBand == true)));
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Cannot process approved order");

                await _activityService.CreateActivityAsync(
                    OrdersProcessingErrorActivities.Order_ProcessingApprovedOrder_Error,
                    data: e,
                    set => set.TenantId(mid).CorrelationId(orderId));
            }
        }

        public async Task ProcessReturnAsync(Guid mid, Guid orderId, int refundAmount, string refundMessageDescription,
            DateTime returnDateTime)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Tag(nameof(mid), mid)
                .Tag(nameof(orderId), orderId)
                .Tag(nameof(refundAmount), refundAmount)
                .Tag(nameof(refundMessageDescription), refundMessageDescription);

            try
            {
                // being invoked after eligibility approval (recurring payments strategy, etc.)

                var order = await _dbContext.Orders.Where(x => x.MerchantId == mid && x.Id == orderId)
                    .Include(x => x.ActivityItems).SingleOrDefaultAsync();
                if (order == null)
                {
                    workspan.Log.Fatal(
                        "NOTFOUND: OrderService => ProcessCompletedOrder > {OrderId} mid: {Mid}",
                        orderId, mid);

                    return;
                }

                order.RefundsAmount += refundAmount;

                _dbContext.Orders.Update(order);
                await _dbContext.SaveChangesAsync();

                // await _payoutService.AddOrderReturnToPayoutAsync(mid, orderId, refundAmount, refundMessageDescription,
                //     returnDateTime);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
            }
        }

        public async Task ProcessDisputeAsync(Guid mid, Guid orderId, int amount, string message,
            DateTime disputeDateTime)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Tag(nameof(mid), mid)
                .Tag(nameof(orderId), orderId)
                .Tag(nameof(amount), amount)
                .Tag(nameof(message), message);

            try
            {
                // being invoked after eligibility approval (recurring payments strategy, etc.)

                var order = await _dbContext.Orders.Where(x => x.MerchantId == mid && x.Id == orderId)
                    .Include(x => x.ActivityItems).SingleOrDefaultAsync();
                if (order == null)
                {
                    workspan.Log.Fatal(
                        "NOTFOUND: OrderService => ProcessDisputeAsync > {OrderId} mid: {Mid}",
                        orderId, mid);

                    throw new FlexChargeException("OrderNotFound", "Order not found");
                }

                order.RefundsAmount += amount;

                _dbContext.Orders.Update(order);
                await _dbContext.SaveChangesAsync();

                //await _payoutService.AddOrderChargebackToPayoutAsync(mid, orderId, message, disputeDateTime);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
            }
        }


        public async Task<RefundResult> RefundAsync(Guid mid, Guid orderId, RefundRequest payload,
            CancellationToken token)
        {
            return await RefundImplementationAsync(mid, orderId, payload, false, token);
        }

        public async Task<RefundResult> StandaloneRefundAsync(Guid mid, Guid orderId, RefundRequest payload,
            CancellationToken token)
        {
            return await RefundImplementationAsync(mid, orderId, payload, true, token);
        }

        private async Task<RefundResult> RefundImplementationAsync(Guid mid, Guid orderId, RefundRequest payload,
            bool isStandalone,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Baggage("Mid", mid)
                .Baggage("OrderId", orderId)
                .Request(payload)
                .LogEnterAndExit();

            var result = new RefundResult();
            Order order = null;
            try
            {
                await using var @lock = await _distributedLockService
                    .AcquireLockAsync(LockKeyFactory.CreateOrderKey(orderId),
                        TimeSpan.FromMinutes(1),
                        maxRetryDuration: TimeSpan.FromMinutes(1));

                var orderAndMerchant = await GetOrderAndMerchantIncludingActivityAsync(orderId);
                order = orderAndMerchant.Order;
                var merchant = orderAndMerchant.Merchant;

                #region Observability

                await _activityService.CreateActivityAsync(
                    OrdersProcessingActivities.Order_Refund_Initiated,
                    set => set
                        .TenantId(order.MerchantId)
                        .CorrelationId(order.Id)
                        .Meta(meta => meta
                            .SetValue("Amount", payload.AmountToRefundInCents)
                            .SetValue("Reason", payload.RefundMessage)
                        ));

                #endregion

                EnsureRefundAmountIsCorrect(order, payload.AmountToRefundInCents);

                //Get only authorized transactions that are not voided or refunded (partially refunded transactions are not implemented yet)
                // var refundedTransactionIds = order.ActivityItems.FilterRefundedTransactions()
                //     .Select(x => x.RelatedTransactionId).ToHashSet();

                var voidTransactionIds = order.ActivityItems.FilterVoidTransactions()
                    .Select(x => x.RelatedTransactionId).ToHashSet();

                var activeAuthorizationTransactions = order.ActivityItems.FilterCaptureTransactions()
                    .Where(x =>
                        !voidTransactionIds.Contains(x.PaymentTransactionId))
                    //&& !refundedTransactionIds.Contains(x.PaymentTransactionId))
                    .ToList();

                if (!activeAuthorizationTransactions.Any())
                    throw new FlexChargeException("TRANSACTION_NOT_FOUND", "No transactions to refund");

                var authorization = activeAuthorizationTransactions.FirstOrDefault();

                //Calculate the amount that was actually discounted from the order
                // if (authorization?.DiscountAmount > 0)
                // {
                //     var discountedAmount = authorization.TotalAmount + authorization.DiscountAmount;
                //     if (discountedAmount == payload.AmountToRefundInCents)
                //     {
                //         payload.AmountToRefund = Formatters.IntToDecimal(authorization.TotalAmount);
                //     }
                // }

                var creditPaymentCommand = new CreditPaymentCommand
                {
                    Mid = mid,
                    Reason = payload.RefundMessage,
                    AmountToRefund = payload.AmountToRefund,
                    SupportedGatewayId = payload.SupportedGatewayId,
                    //IsPartialRefund = payload.IsPartialRefund,
                    TransactionId = authorization.PaymentTransactionId,
                    OrderId = authorization.OrderId,
                    StandaloneCredit = isStandalone,
                    PaymentInstrumentId = Guid.TryParse(order.PaymentInstrumentToken, out var paymentInstrumentId)
                        ? paymentInstrumentId
                        : Guid.Empty,
                    IsConsumerRequestedRefund = payload.IsConsumerRequestedRefund
                };

                #region Observability

                await _activityService.CreateActivityAsync(
                    OrdersProcessingActivities.Order_Refund_Requested,
                    data: creditPaymentCommand,
                    set => set
                        .TenantId(order.MerchantId)
                        .CorrelationId(order.Id)
                        .Meta(meta => meta
                            .SetValue("Amount", payload.AmountToRefundInCents)
                            .SetValue("Reason", payload.RefundMessage)
                            .SetValue("TransactionId", authorization?.PaymentTransactionId ?? Guid.Empty)
                        ));

                #endregion


                var responseFromProcessor = await _creditPaymentClient.GetResponse<CreditPaymentCommandResponse>(
                    creditPaymentCommand, token);

                #region Observability

                if (responseFromProcessor.Message.Success)
                {
                    await _activityService.CreateActivityAsync(
                        OrdersProcessingActivities.Order_Refund_Succeeded,
                        data: responseFromProcessor,
                        set => set
                            .TenantId(order.MerchantId)
                            .CorrelationId(order.Id)
                            .Meta(meta => meta
                                .SetValue("TransactionId", responseFromProcessor.Message.TransactionId)
                            ));
                }
                else
                {
                    await _activityService.CreateActivityAsync(
                        OrdersProcessingActivities.Order_Refund_Failed,
                        data: responseFromProcessor,
                        set => set
                            .TenantId(order.MerchantId)
                            .CorrelationId(order.Id)
                            .Meta(meta => meta
                                .SetValue("TransactionId", responseFromProcessor.Message.TransactionId)
                            ));
                }

                #endregion

                // if (responseFromProcessor.Message.Success)
                // {
                //     await _payoutService.AddOrderReturnToPayoutAsync(order,
                //         Utils.Formatters.DecimalToInt(payload.AmountToRefund), payload.RefundMessage);
                // }

                //TODO: Move publishing events to PaymentCredited(CreditFailed)Events
                // To do this we need to pass RefundMessage, ExternalOrderId, InitialTransactionReference
                // to this events in case of refund

                if (!responseFromProcessor.Message.Success)
                {
                    result.AddError(responseFromProcessor.Message.ResponseMessage,
                        responseFromProcessor.Message.ResponseCode, responseFromProcessor.Message.ResponseCode);

                    result.Status = nameof(RefundStatus.FAILED);
                }
                else
                {
                    result.TransactionId = responseFromProcessor.Message?.TransactionId;

                    result.Status = nameof(RefundStatus.SUCCESS);

                    await _publisher.Publish(new OrderRefundedEvent
                    {
                        Timestamp = DateTime.UtcNow,
                        OrderId = order.Id,
                        ExternalOrderId = order.ReferenceNumber,
                        Mid = mid,
                        Pid = merchant.Pid,
                        Amount = payload.AmountToRefundInCents,
                        Currency = "USD",
                        Message = payload.RefundMessage,
                        TransactionId = responseFromProcessor.Message.TransactionId,
                        InitialTransactionReference = authorization.PaymentTransactionId,
                    });
                }

                result.ResponseCode = responseFromProcessor.Message.ResponseCode;
                result.ResponseMessage = responseFromProcessor.Message.ResponseMessage;

                return result;
            }
            catch (Exception e)
            {
                if (e is FlexChargeException && order != null)
                {
                    workspan.Log.Warning(e, "EXPECTED_ERROR: OrderService => RefundAsync");

                    await _activityService.CreateActivityAsync(
                        OrdersProcessingActivities.Order_Refund_ExpectedError,
                        data: e.Message,
                        set => set
                            .TenantId(order.MerchantId)
                            .CorrelationId(order.Id));
                }
                else
                {
                    workspan.RecordFatalException(e, "OrderService => RefundAsync");

                    await _activityService.CreateActivityAsync(
                        OrdersProcessingErrorActivities.Order_Refund_Error,
                        data: e.Message,
                        set => set
                            .TenantId(order?.MerchantId)
                            .CorrelationId(order?.Id));
                }

                throw;
            }
        }

        public async Task<RefundResult> AdminRefundAsync(Guid orderId, RefundRequest payload,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Baggage("OrderId", orderId)
                .Request(payload)
                .LogEnterAndExit();

            var result = new RefundResult();
            Order order = null;
            try
            {
                await using var @lock = await _distributedLockService
                    .AcquireLockAsync(LockKeyFactory.CreateOrderKey(orderId),
                        TimeSpan.FromMinutes(1),
                        maxRetryDuration: TimeSpan.FromMinutes(1));

                var orderAndMerchant = await GetOrderAndMerchantIncludingActivityAsync(orderId);

                order = orderAndMerchant.Order;
                var merchant = orderAndMerchant.Merchant;

                workspan
                    .Baggage("Mid", order.MerchantId);

                #region Observability

                await _activityService.CreateActivityAsync(
                    OrdersProcessingActivities.Order_Refund_Initiated,
                    set => set
                        .TenantId(order.MerchantId)
                        .CorrelationId(order.Id)
                        .Meta(meta => meta
                            .SetValue("Amount", payload.AmountToRefundInCents)
                            .SetValue("Reason", payload.RefundMessage)
                        ));

                #endregion

                EnsureRefundAmountIsCorrect(order, payload.AmountToRefundInCents);

                //Get only authorized transactions that are not voided or refunded (partially refunded transactions are not implemented yet)
                // var refundedTransactionIds = order.ActivityItems.FilterRefundedTransactions()
                //     .Select(x => x.RelatedTransactionId).ToHashSet();

                var voidTransactionIds = order.ActivityItems.FilterVoidTransactions()
                    .Select(x => x.RelatedTransactionId).ToHashSet();

                var activeAuthorizationTransactions = order.ActivityItems.FilterCaptureTransactions()
                    .Where(x =>
                        !voidTransactionIds.Contains(x.PaymentTransactionId))
                    //&& !refundedTransactionIds.Contains(x.PaymentTransactionId))
                    .ToList();

                if (!activeAuthorizationTransactions.Any())
                    throw new FlexChargeException("TRANSACTION_NOT_FOUND", "No transactions to refund");

                var authorization = activeAuthorizationTransactions.FirstOrDefault();

                //Calculate the amount that was actually discounted from the order
                // if (authorization?.DiscountAmount > 0)
                // {
                //     var discountedAmount = authorization.TotalAmount + authorization.DiscountAmount;
                //     if (discountedAmount == payload.AmountToRefundInCents)
                //     {
                //         payload.AmountToRefund = Formatters.IntToDecimal(authorization.TotalAmount);
                //     }
                // }

                var creditPaymentCommand = new CreditPaymentCommand
                {
                    Reason = payload.RefundMessage,
                    AmountToRefund = payload.AmountToRefund,
                    //IsPartialRefund = payload.IsPartialRefund,
                    TransactionId = authorization.PaymentTransactionId,
                    OrderId = authorization.OrderId,
                    Mid = order.MerchantId,
                    IsConsumerRequestedRefund = payload.IsConsumerRequestedRefund,
                    PaymentInstrumentId = Guid.TryParse(order.PaymentInstrumentToken, out var paymentInstrumentId)
                        ? paymentInstrumentId
                        : Guid.Empty,
                };

                #region Observability

                await _activityService.CreateActivityAsync(
                    OrdersProcessingActivities.Order_Refund_Requested,
                    data: creditPaymentCommand,
                    set => set
                        .TenantId(order.MerchantId)
                        .CorrelationId(order.Id)
                        .Meta(meta => meta
                            .SetValue("Amount", payload.AmountToRefundInCents)
                            .SetValue("Reason", payload.RefundMessage)
                            .SetValue("TransactionId", authorization?.PaymentTransactionId ?? Guid.Empty)
                        ));

                #endregion


                var responseFromProcessor = await _creditPaymentClient.GetResponse<CreditPaymentCommandResponse>(
                    creditPaymentCommand, token);

                #region Observability

                if (responseFromProcessor.Message.Success)
                {
                    await _activityService.CreateActivityAsync(
                        OrdersProcessingActivities.Order_Refund_Succeeded,
                        data: responseFromProcessor,
                        set => set
                            .TenantId(order.MerchantId)
                            .CorrelationId(order.Id)
                            .Meta(meta => meta
                                .SetValue("TransactionId", responseFromProcessor.Message.TransactionId)
                            ));
                }
                else
                {
                    await _activityService.CreateActivityAsync(
                        OrdersProcessingActivities.Order_Refund_Failed,
                        data: responseFromProcessor,
                        set => set
                            .TenantId(order.MerchantId)
                            .CorrelationId(order.Id)
                            .Meta(meta => meta
                                .SetValue("TransactionId", responseFromProcessor.Message.TransactionId)
                            ));
                }

                #endregion

                // if (responseFromProcessor.Message.Success)
                // {
                //     await _payoutService.AddOrderReturnToPayoutAsync(order,
                //         Utils.Formatters.DecimalToInt(payload.AmountToRefund), payload.RefundMessage);
                // }

                //TODO: Move publishing events to PaymentCredited(CreditFailed)Events
                // To do this we need to pass RefundMessage, ExternalOrderId, InitialTransactionReference
                // to this events in case of refund

                if (!responseFromProcessor.Message.Success)
                {
                    result.AddError(responseFromProcessor.Message.ResponseMessage,
                        responseFromProcessor.Message.ResponseCode, responseFromProcessor.Message.ResponseCode);

                    result.Status = nameof(RefundStatus.FAILED);
                }
                else
                {
                    result.TransactionId = responseFromProcessor.Message?.TransactionId;

                    result.Status = nameof(RefundStatus.SUCCESS);

                    await _publisher.Publish(new OrderRefundedEvent
                    {
                        Timestamp = DateTime.UtcNow,
                        OrderId = order.Id,
                        ExternalOrderId = order.ReferenceNumber,
                        Mid = order.MerchantId,
                        Pid = merchant.Pid,
                        Amount = payload.AmountToRefundInCents,
                        Currency = "USD",
                        Message = payload.RefundMessage,
                        TransactionId = responseFromProcessor.Message.TransactionId,
                        InitialTransactionReference = authorization.PaymentTransactionId,
                    });
                }

                result.ResponseCode = responseFromProcessor.Message.ResponseCode;
                result.ResponseMessage = responseFromProcessor.Message.ResponseMessage;

                return result;
            }
            catch (Exception e)
            {
                if (e is FlexChargeException && order != null)
                {
                    workspan.Log.Warning(e, "EXPECTED_ERROR: OrderService => RefundAsync");

                    await _activityService.CreateActivityAsync(
                        OrdersProcessingActivities.Order_Refund_ExpectedError,
                        data: e.Message,
                        set => set
                            .TenantId(order.MerchantId)
                            .CorrelationId(order.Id));
                }
                else
                {
                    workspan.RecordFatalException(e, "OrderService => RefundAsync");

                    await _activityService.CreateActivityAsync(
                        OrdersProcessingErrorActivities.Order_Refund_Error,
                        data: e.Message,
                        set => set
                            .TenantId(order?.MerchantId)
                            .CorrelationId(order?.Id));
                }

                throw;
            }
        }

        public async Task<(ConsumerNotificationChannel? Channel, bool IsMitOrder)>
            ShouldSendConsumerNotificationsForOrderAsync(Guid orderId)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Baggage("OrderId", orderId);

            //load merchant and order from _dbContext in one roundtrip to db
            var merchantAndOrder = await _dbContext
                .Orders
                .Where(order => order.Id == orderId)
                .Join(_dbContext.Merchants, order => order.MerchantId, merchant => merchant.Mid,
                    (order, merchant) => new {Order = order, Merchant = merchant})
                .SingleAsync();

            var order = merchantAndOrder.Order;
            var merchant = merchantAndOrder.Merchant;

            workspan
                .Baggage("Mid", merchant.Mid);

            if (order.AreConsumerNotificationsEnabled(merchant))
            {
                if (string.IsNullOrWhiteSpace(merchant.ConsumerOrderNotificationChannel))
                    return (ConsumerNotificationChannel.Email, order.IsMIT); // default channel

                if (EnumHelpers.TryParseEnum<ConsumerNotificationChannel>(merchant.ConsumerOrderNotificationChannel,
                        out var channel))
                {
                    return (channel, order.IsMIT);
                }
                else
                {
                    workspan.Log.Fatal(
                        "Unknown ConsumerNotificationChannel({NotificationChannel}) - notifications not sent",
                        merchant.ConsumerOrderNotificationChannel);
                }
            }

            return (null, order.IsMIT);
        }

        public async Task<(MerchantNotificationChannel? Channel, bool IsMitOrder)>
            ShouldSendMerchantNotificationsForOrderAsync(Guid orderId)
        {
            using var workspan = Workspan.Start<OrderService>()
                .Baggage("OrderId", orderId);

            //load merchant and order from _dbContext in one roundtrip to db
            var merchantAndOrder = await _dbContext
                .Orders
                .Where(order => order.Id == orderId)
                .Join(_dbContext.Merchants, order => order.MerchantId, merchant => merchant.Mid,
                    (order, merchant) => new {Order = order, Merchant = merchant})
                .SingleAsync();

            var order = merchantAndOrder.Order;
            var merchant = merchantAndOrder.Merchant;

            workspan
                .Baggage("Mid", merchant.Mid);

            if (order.AreMerchantNotificationsEnabled(merchant))
            {
                return (MerchantNotificationChannel.Email, order.IsMIT); // default channel
            }

            return (null, order.IsMIT);
        }

        private static void EnsureRefundAmountIsCorrect(Order order, int amountToRefund)
        {
            var availableBalance = order.GetPayments().TotalPendingOrSettledAmount();
            var totalDiscountedAmount = order.GetPayments().TotalDiscountedAmount();

            if (availableBalance < totalDiscountedAmount)
            {
                throw new FlexValidationException(2002,
                    $"Available balance ({Formatters.IntToDecimal(availableBalance):F2}) is less than discounts amount ({Formatters.IntToDecimal(order.DiscountsAmount):F2})");
            }

            //This is only for a case where we applied a discount and amount to refund will be greater than available balance
            var maxRefundAmount = availableBalance + totalDiscountedAmount;
            if (amountToRefund > maxRefundAmount)
            {
                throw new FlexValidationException(2001,
                    $"Amount to refund ({Formatters.IntToDecimal(amountToRefund):F2}) is greater than maximum refund amount in ({Formatters.IntToDecimal(maxRefundAmount):F2}))");
            }
        }

        #region Commented

        // public async Task NotifyApprovedOrder()
        // {
        // }

        // public async Task ScheduleStrategyExecution(string strategyName, Order order,
        //     IReadOnlyDictionary<string, string> strategyParameters, TimeSpan? delayExecution = null)
        // {
        //     if (!string.IsNullOrWhiteSpace(strategyName))
        //     {
        //         Guid orderId = order.Id;
        //
        //         order.StrategySequenceCounter++;
        //         order.CurrentStrategyId = strategyName;
        //         await _context.SaveChangesAsync();
        //
        //         Guid schedulerJobId = Guid.NewGuid();
        //
        //         var repaymentStrategy =
        //             await _repaymentStrategyFactoryService.CreateRepaymentStrategyAsync(
        //                 order.CurrentStrategyId, schedulerJobId, order, strategyParameters);
        //
        //         await repaymentStrategy.ScheduleAsync(schedulerJobId);
        //     }
        // }

        #endregion
    }
}