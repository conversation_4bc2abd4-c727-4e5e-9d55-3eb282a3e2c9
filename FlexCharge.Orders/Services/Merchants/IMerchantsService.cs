using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Activities;
using FlexCharge.Contracts.Commands.Tracking;
using FlexCharge.Orders.Entities;

namespace FlexCharge.Orders.Services.Merchants;

public interface IMerchantsService
{
    Task InsertMerchantAsync(Guid merchantId, MerchantCreatedEvent payload);
    Task UpdateMerchantAsync(Guid merchantId, MerchantUpdatedEvent payload);

    Task UpdateMerchantFeesAsync(Guid merchantId, IEnumerable<FlexCharge.Contracts.MerchantFee> merchantFeeHistory);
    Task<GetMerchantSiteConfigurationCommandResponse> GetMerchantConfigurationAsync(Guid mid, Guid siteId);

    Task CreateMerchantFinancialAccountAsync(Guid merchantId, FinancialAccount payload);
    Task UpdateMerchantFinancialAccountAsync(Guid merchantId, FinancialAccount payload);
    Task UpdateFinancialAccountDbaAsync(Guid financialAccountId, string dba);

    //get financial account by merchantId
    Task<FinancialAccount> GetFinancialAccountAsync(Guid merchantId);
}