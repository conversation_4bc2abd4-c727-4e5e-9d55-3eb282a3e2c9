using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Activities.Exceptions;
using FlexCharge.Common.Response;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Orders.Services.ActivityService.MetadataEnrichment;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using IActivity = FlexCharge.Contracts.Activities.IActivity;

namespace FlexCharge.Orders.Services.ActivityService;

/// <summary>
/// This service is responsible for enriching the activity with metadata.
/// </summary>
public class OrdersActivityService : Common.Activities.ActivityService
{
    private readonly IMetadataEnrichmentService _metadataEnrichmentService;

    public OrdersActivityService(IServiceScopeFactory serviceScopeFactory,
        IMetadataEnrichmentService metadataEnrichmentService) : base(serviceScopeFactory)
    {
        _metadataEnrichmentService = metadataEnrichmentService;
    }


    protected override async ValueTask EnrichActivityAsync(IActivity activity)
    {
        await _metadataEnrichmentService.EnrichMetadataAsync(activity);
    }
}