using System;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Contracts.Activities;
using FlexCharge.Orders.Entities;

namespace FlexCharge.Orders.Services.ActivityService.MetadataEnrichment.Enrichers;

public interface IMetadataEnricher
{
    void ResetMerchantInformation(Guid merchantId);
    Task EnrichMetadataAsync(IActivity activity, PostgreSQLDbContext dbContext, IMapper map);
}