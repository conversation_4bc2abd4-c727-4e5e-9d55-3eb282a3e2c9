using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Contracts.Payouts;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;

namespace FlexCharge.Orders.Services.PayoutServices;

public interface IPayoutService
{
    /// <summary>
    /// Ensures that all orders are added to batches
    /// If it finds an order that is not added to any batch, it adds it to the current batch
    /// </summary>
    /// <returns></returns>
    //Task EnsureAllOrdersAreAddedToBatchesAsync();

    /// <summary>
    /// Adds missing orders to batches and double-checks total amount value of orders in batches 
    /// </summary>
    /// <returns></returns>
    //Task RecalculateFinishedBatchesAsync();

    /// <summary>
    /// Generates funding instructions for approved (IsPosted is true) batches in database
    /// </summary>
    /// <returns></returns>
    Task ExecuteBatchProcessingAsync();

    //Task AddCompletedOrderToPayoutAsync(Order order);
    Task AddOrderReturnToPayoutAsync(Guid mid, Guid orderId, int refundAmount, string message,
        DateTime? returnDateTime = null);

    Task AddOrderReturnToPayoutAsync(Order order, int refundAmount, string message, DateTime? returnDateTime = null);

    Task AddOrderReturnToPayoutAsync(Merchant merchant, Order order, int refundAmount, string message,
        DateTime? returnDateTime = null);

    Task AddOrderChargebackToPayoutAsync(Guid mid, Guid orderId, string message, DateTime? chargebackDateTime = null);
    Task AddOrderChargebackToPayoutAsync(Order order, string message, DateTime? chargebackDateTime = null);

    Task AddOrderChargebackToPayoutAsync(Merchant merchant, Order order, string message,
        DateTime? chargebackDateTime = null);

    Task<string> ExportUnpostedAsync(Guid senderId, Guid receiverId, Guid? partnerId, string? batchType, DateTime? from,
        DateTime? to,
        string format = "csv");

    Task<string> ExportPendingAsync(Guid senderId, Guid receiverId, Guid? partnerId, string? batchType, DateTime? from,
        DateTime? to,
        string format = "csv");

    Task<string> ExportShelvedAsync(Guid senderId, Guid receiverId, Guid? partnerId, string? batchType, DateTime? from,
        DateTime? to,
        string format = "csv");

    Task<string> ExportDetailsAsync(Guid senderId, Guid receiverId, Guid id, int pageSize, int pageNumber,
        string format = "csv");

    Task AddAdjustmentAsync(Guid senderId, Guid receiverId, Guid id, AdjustmentCreateRequestDTO payload);
    Task ApplyReserveAsync(Guid senderId, Guid receiverId, Guid id, ApplyReserveRequestDTO payload);

    Task ApplyReserveUtilizationAsync(Guid senderId, Guid receiverId, Guid batchId, UtilizeFundsReserveDTO payload);
    Task RemoveReserveUtilizationAsync(Guid senderId, Guid receiverId, Guid batchId);
    Task AddCommentAsync(Guid senderId, Guid receiverId, Guid batchId, string comment);

    Task<List<PayoutDTO>> GetPayoutsAsync(Guid senderId, Guid receiverId, Guid? partnerId, PayoutStatus? status,
        string? batchType, DateTime? from, DateTime? to);
}