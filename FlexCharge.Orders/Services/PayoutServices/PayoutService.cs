using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Activities;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Exports;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.PostgreSql.Interceptors;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Payouts;
using FlexCharge.Orders.Contracts;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.DTO.Batches;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Entities.Extensions;
using FlexCharge.Orders.Services.FundsReserveService;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;


namespace FlexCharge.Orders.Services.PayoutServices;

public class PayoutService : IPayoutService
{
    private PostgreSQLDbContext _dbContext;
    private readonly IPublishEndpoint _publisher;
    private readonly IBatchService _batchService;
    private readonly IActivityService _activityService;
    private readonly IDistributedLockService _distributedLockService;
    private readonly IRequestClient<PerformPayoutCommand> _performPayoutCommandRequestClient;
    private readonly IRequestClient<PerformPartnerDebitCommand> _performPartnerDebitCommandRequestClient;
    private readonly IFundsReserveService _fundsReserveService;
    private readonly IMapper _mapper;


    public PayoutService(PostgreSQLDbContext context,
        IPublishEndpoint publisher,
        IActivityService activityService,
        IRequestClient<PerformPayoutCommand> performPayoutCommandRequestClient,
        IDistributedLockService distributedLockService,
        IBatchService batchService, IFundsReserveService fundsReserveService, IMapper mapper,
        IRequestClient<PerformPartnerDebitCommand> performPartnerDebitCommandRequestClient)
    {
        _dbContext = context;
        _publisher = publisher;
        _activityService = activityService;
        _performPayoutCommandRequestClient = performPayoutCommandRequestClient;
        _distributedLockService = distributedLockService;
        _batchService = batchService;
        _fundsReserveService = fundsReserveService;
        _mapper = mapper;
        _performPartnerDebitCommandRequestClient = performPartnerDebitCommandRequestClient;
    }

    // public async Task EnsureAllOrdersAreAddedToBatchesAsync()
    // {
    //     using var workspan = Workspan.Start<PayoutService>()
    //         .LogEnterAndExit();
    //
    //     var todayUtc = DateTime.UtcNow.ToUtcDate();
    //
    //     var merchants = await _dbContext.Merchants.Where(x => x.Locked == false).ToListAsync();
    //     foreach (var merchant in merchants)
    //     {
    //         workspan.Log.Information("Staring > Processing payouts for merchant {Mid}", merchant.Mid);
    //
    //         //TODO D: Acquire some distributed lock to ensure no parallel/duplicate processing of the batches occurs
    //         try
    //         {
    //             await using (await _dbContext.Database.BeginTransactionAsync())
    //             {
    //                 var ordersToPayout = await _dbContext.Orders
    //                     .Where(x =>
    //                         x.MerchantId == merchant.Mid &&
    //                         x.StatusCategory == nameof(OrderStatusCategory.completed) &&
    //                         x.OrderPayoutStatus == null)
    //                     .ToListAsync();
    //
    //                 if (ordersToPayout.Any()) // are there any orders that are not in a batch?
    //                 {
    //                     workspan.Log.Information("Merchant {Mid} has {OrdersCount} orders to add to current batch",
    //                         merchant.Mid, ordersToPayout.Count);
    //
    //                     var payoutFrequency = merchant.ToPayoutFrequencyType();
    //
    //                     var batchStartDate = payoutFrequency.GetCurrentBatchPayoutRangeStartDate(merchant, todayUtc);
    //                     var batchEndDate = payoutFrequency.GetCurrentBatchPayoutRangeEndDate(merchant, todayUtc);
    //
    //                     var batch = await _batchService.GetOrCreateBatch(merchant, batchStartDate, batchEndDate,
    //                         includeBatchOrders: true); // batch orders will be loaded later
    //
    //                     var merchantFees = await _dbContext.MerchantFees
    //                         .Where(x => x.MerchantId == merchant.Mid && x.IsActive)
    //                         .ToListAsync();
    //
    //                     foreach (var order in ordersToPayout)
    //                     {
    //                         workspan.Log.Information("Adding order {OrderId} to batch {BatchId}", order.Id, batch.Id);
    //
    //                         //Not already in batch? -> adding it 
    //                         if (order.IsOrderCompleted() &&
    //                             !batch.BatchOrders.Any(x => x.OrderId == order.Id))
    //                         {
    //                             batch.AddCompletedOrder(order, merchantFees);
    //                         }
    //
    //                         order.OrderPayoutStatus = OrderPayOutStatuses.InBatch;
    //                     }
    //                 }
    //
    //                 await _dbContext.SaveChangesAsync();
    //                 await _dbContext.Database.CommitTransactionAsync();
    //             }
    //         }
    //         catch (Exception e)
    //         {
    //             workspan.Log.Error(
    //                 "PAYOUTS: <<< SKIPPING MERCHANT >>> Error while adding orders to batch for merchant {Mid}: {Error}",
    //                 merchant.Mid, e.Message);
    //         }
    //     }
    // }

    // public async Task RecalculateFinishedBatchesAsync()
    // {
    //     using var workspan = Workspan.Start<PayoutService>()
    //         .LogEnterAndExit();
    //
    //     workspan.Log.Information("Staring > Recalculating posted batches");
    //
    //     var batchesToPayout = await _dbContext.Batches
    //         .Include(x => x.Beneficiary)
    //         .Where(x =>
    //             x.IsPosted == true &&
    //             x.PayoutStatus == nameof(BatchPayoutStatus.UNPROCESSED)).ToListAsync();
    //
    //     workspan.Log.Information("Found {BatchesCount} batches to recalculate", batchesToPayout.Count);
    //
    //     Dictionary<Guid, List<MerchantFee>> merchantFeesCache = new();
    //     foreach (var batch in batchesToPayout)
    //     {
    //         await using var @lock = await _distributedLockService
    //             .AcquireLockAsync(LockKeyFactory.CreatePayoutBatchKey(batch.Id),
    //                 TimeSpan.FromSeconds(30),
    //                 maxRetryDuration: TimeSpan.FromMinutes(1));
    //
    //         try
    //         {
    //             if (batch.Beneficiary is null)
    //             {
    //                 workspan.RecordError("PAYOUTS: Can't process batch {BatchId} MERCHANT NOT FOUND ID: {Mid}",
    //                     batch.Id, batch.Beneficiary.Mid);
    //
    //                 throw new FlexChargeException(
    //                     $"PAYOUTS: Can't process batch {batch.Id} MERCHANT NOT FOUND ID: {batch.Beneficiary.Mid}");
    //             }
    //
    //             await RecalculateBatchAsync(batch, merchantFeesCache);
    //         }
    //         catch (Exception e)
    //         {
    //             batch.PayoutStatus = nameof(BatchPayoutStatus.ERROR);
    //             batch.PayoutStatusMessage = e.Message;
    //             workspan.RecordError("PAYOUTS: Error while recalculating batch {BatchId}: {Error}", batch.Id,
    //                 e.Message);
    //         }
    //
    //         _dbContext.Update(batch);
    //         await _dbContext.SaveChangesAsync();
    //     }
    // }

    public async Task ExecuteBatchProcessingAsync()
    {
        using var workspan = Workspan.Start<PayoutService>()
            .LogEnterAndExit();

        var executedPayouts = new List<Payout>();

        await using var @lock = await _distributedLockService
            .AcquireLockAsync(
                "ExecutePayoutsAsync",
                TimeSpan.FromMinutes(15),
                TimeSpan.FromSeconds(30));

        await using (await _dbContext.Database.BeginTransactionAsync())
        {
            //TODO: move to another Command + BG job for better performance
            var batchesToPayout = await _dbContext.Batches
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.BatchRecords)
                .TagWith(SelectForUpdateCommandInterceptor.SelectForUpdateBlockingTag)
                .Where(x =>
                    (x.BatchType == nameof(FIMovementType.FISC) ||
                     x.BatchType == nameof(FIMovementType.FIPAD)) &&
                    x.IsPosted == true &&
                    x.PayoutStatus == nameof(BatchStatus.UNPROCESSED))
                .ToListAsync();

            foreach (var batch in batchesToPayout)
            {
                workspan
                    .Tag("BatchId", batch.Id)
                    .Tag("BatchType", batch.BatchType)
                    .Log.Information("Processing batch {BatchId}", batch.Id);

                if (batch.Receiver is null)
                {
                    workspan.Log.Fatal("PAYOUTS: Can't process batch {BatchId} RECEIVER IS NULL", batch.Id);
                    continue;
                }

                if (batch.Sender is null)
                {
                    workspan.Log.Fatal("PAYOUTS: Can't process batch {BatchId} SENDER IS NULL", batch.Id);
                    continue;
                }

                try
                {
                    switch (batch.BatchType)
                    {
                        //based on batch type process the batch
                        case nameof(FIMovementType.FISC):
                            await Process_FISC_Batch(batch);
                            break;
                        case nameof(FIMovementType.FIPAD):
                            await Process_FIPAD_Batch(batch);
                            break;
                        default:
                            workspan.Log.Fatal("PAYOUTS: Can't process batch {BatchId} UNKNOWN BATCH TYPE {BatchType}",
                                batch.Id, batch.BatchType);
                            continue;
                    }
                }
                catch (Exception e)
                {
                    // log exception here to be sure that failed batch id property is set
                    workspan
                        .Tag("BatchId", batch.Id)
                        .RecordFatalException(e);
                }
            }

            await _dbContext.SaveChangesAsync();
            await _dbContext.Database.CommitTransactionAsync();
        }

        await _publisher.Publish(new PayoutExecutedEvent
        {
            Payouts = executedPayouts
        });
    }

    private async Task Process_FISC_Batch(Batch batch)
    {
        Workspan.Current!
            .Baggage("BatchId", batch.Id)
            .LogEnterAndExit();

        if (!batch.IsOffline)
        {
            #region Sending ACH Debit Payment Request For All Batches At Once

            // Send all batches at once to avoid situation when some is started processing and others don't
            bool payoutFailed = true;
            try
            {
                throw new NotImplementedException();
                // // batch.PayoutStatus = nameof(PayoutStatus.PROCESSING);
                // // await _dbContext.SaveChangesAsync();
                //
                // //Proceed with the ACH debit payment
                // var performPayoutCommandResponse = await _performPayoutCommandRequestClient
                //     .GetResponse<PerformPayoutCommandResponse>(
                //         new PerformPayoutCommand
                //         {
                //             Mid = batch.Beneficiary.Mid,
                //             OrderId = batch.Id,
                //             Amount = batch.CalculatePayoutAmount(),
                //             Currency = "USD"
                //         }
                //     );
                //
                // if (performPayoutCommandResponse.Message.Success)
                // {
                //     payoutFailed = false;
                //     batch.RelatedTransaction = performPayoutCommandResponse.Message.TransactionId;
                //     // Success status must be set after ACH debit payment is verified
                // }
                // else
                // {
                //     workspan.RecordError(
                //         "PAYOUTS: Can't process payout for batchID: {BatchId} MID: {Mid} ERROR: {Error}",
                //         batch.Id, batch.Beneficiary.Mid,
                //         performPayoutCommandResponse.Message.ResponseMessage);
                // }
            }
            catch (NotImplementedException e)
            {
                Workspan.Current!.RecordError("FISC: Process offline batch");
            }
            catch (Exception e)
            {
                Workspan.Current!.RecordException(e, "Payout failed for batch");
            }

            if (payoutFailed)
            {
                batch.PayoutStatus = nameof(BatchStatus.FAILED);
            }

            #endregion
        }
        else
        {
            batch.PayoutStatus = nameof(BatchStatus.SUCCESS);

            await ProcessBatch(batch);
        }
    }

    private async Task Process_FIPAD_Batch(Batch batch)
    {
        using var workspan = Workspan.Start<PayoutService>()
            .Baggage("BatchId", batch.Id)
            .LogEnterAndExit();

        if (!batch.IsOffline)
        {
            try
            {
                if (batch.BatchType != nameof(FIMovementType.FIPAD))
                    throw new FlexChargeException($"Batch type is not supported: {batch.BatchType}");

                if (batch.PayoutStatus != BatchStatus.UNPROCESSED.ToString())
                    throw new FlexChargeException("Batch status is not unprocessed");

                if (batch.IsPosted != true)
                    throw new FlexChargeException("Cannot process unposted batch");

                if (batch.Sender?.RelatedEntityType != nameof(FinancialAccountsRelatedEntityType.Partner))
                    throw new FlexChargeException("Sender is not a partner");

                if (batch.Receiver?.RelatedEntityType != nameof(FinancialAccountsRelatedEntityType.MasterAccount))
                    throw new FlexChargeException("Receiver is not a master account");

                if (batch.RelatedTransaction != null)
                    throw new FlexChargeException("Batch already has a related transaction");

                var batchAmount = batch.CalculatePayoutAmount();
                switch (batchAmount)
                {
                    case < 0:
                        throw new FlexChargeException("Batch amount is less than to zero");
                    case 0:
                        batch.PayoutStatus = nameof(BatchStatus.SUCCESS);
                        break;
                    default:
                    {
                        var transactionIdentificationNumber = UniqueIdsHelper.GeneratePseudoUniqueId(batch.Id);

                        //Proceed with the ACH debit payment
                        var performPayoutCommandResponse = await _performPartnerDebitCommandRequestClient
                            .RunCommandAsync<PerformPartnerDebitCommand, PerformPartnerDebitCommandResponse>(
                                new PerformPartnerDebitCommand
                                {
                                    CompanyEntryDescription =
                                        $"{batch.Sender.RelatedEntityDba}".Trim(), // will appear in the statement memo
                                    FundSenderPid = batch.Sender.RelatedEntityId,
                                    Amount = batchAmount,
                                    Currency = batch.Currency,
                                    TransactionIdentificationString = transactionIdentificationNumber,
                                }
                            );

                        if (performPayoutCommandResponse.Message.Success)
                        {
                            batch.RelatedTransaction = performPayoutCommandResponse.Message.TransactionId;
                            batch.PayoutStatus = nameof(BatchStatus.PROCESSING);

                            workspan.Log.Information("ACH TRANSFER RESULT: {MessageResponseMessage}",
                                performPayoutCommandResponse.Message.ResponseMessage);
                        }
                        else
                        {
                            batch.PayoutStatus = nameof(BatchStatus.FAILED);
                            batch.PayoutStatusMessage = performPayoutCommandResponse.Message.ResponseMessage;
                            workspan.Log.Fatal(
                                "PAYOUTS: Can't process fee debit payment for batchID: {BatchId} PID: {Pid} ERROR: {Error}",
                                batch.Id, batch.Sender.RelatedEntityId,
                                performPayoutCommandResponse.Message.ResponseMessage);
                        }

                        break;
                    }
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Payout failed for batch");
            }
        }
        else
        {
            workspan.RecordError("FIPAD: Process offline batch");
            throw new NotImplementedException();
        }
    }

    // private async Task RecalculateBatchAsync(Batch batch, Dictionary<Guid, List<MerchantFee>> merchantFeesCache = null)
    // {
    //     if (merchantFeesCache == null) merchantFeesCache = new();
    //
    //     List<MerchantFee> merchantFees;
    //
    //     #region Getting Merchant Fees
    //
    //     var beneficiaryMid = batch.Beneficiary.Mid;
    //     if (!merchantFeesCache.TryGetValue(beneficiaryMid, out merchantFees))
    //     {
    //         merchantFees = await _dbContext.MerchantFees
    //             .Where(x =>
    //                 x.MerchantId == beneficiaryMid && x.IsActive).ToListAsync();
    //
    //         merchantFeesCache.Add(beneficiaryMid, merchantFees);
    //     }
    //
    //     #endregion
    //
    //     #region Adding missing orders Recalculating Payout Amounts (double-check)
    //
    //     await _dbContext.Entry(batch).Collection(x => x.BatchOrders).LoadAsync();
    //
    //     if (!batch.BatchOrders.Any())
    //     {
    //         Console.WriteLine("PAYOUTS: Can't process batch {BatchId} NO ORDERS FOUND", batch.Id);
    //         return;
    //     }
    //
    //     Batch recalculatedBatch = new Batch()
    //     {
    //         ConcurrencyUniqueBatchId = batch.ConcurrencyUniqueBatchId,
    //         Beneficiary = batch.Beneficiary,
    //         From = batch.From,
    //         To = batch.To,
    //         IsOffline = batch.IsOffline,
    //         IsPosted = batch.IsPosted,
    //         PayoutStatus = batch.PayoutStatus,
    //         BatchOrders = new List<BatchOrder>()
    //     };
    //
    //     // adding all orders from batch range to double-check batch creation
    //     foreach (var batchOrder in batch.BatchOrders)
    //     {
    //         var order = await _dbContext.Orders.SingleAsync(x => x.Id == batchOrder.OrderId);
    //
    //         if (order != null)
    //         {
    //             //TODO: Double-check if order is completed
    //             recalculatedBatch.AddCompletedOrder(order, merchantFees);
    //         }
    //     }
    //
    //     // we do not double-check (at least for now) chargebacks and returns
    //     var payoutTotalAmount = batch.TotalAmount;
    //     var recalculatedTotalAmount = recalculatedBatch.TotalAmount;
    //     if (payoutTotalAmount != recalculatedTotalAmount)
    //     {
    //         throw new FlexChargeException(
    //             $"Payout batch recalculation error. Total amounts are not equal after recalculation: {payoutTotalAmount} != {recalculatedTotalAmount}");
    //     }
    //
    //     #endregion
    // }

    private async Task ProcessBatch(Batch batch)
    {
        try
        {
            if (batch.BatchRecords == null)
                await _dbContext.Entry(batch).Collection(x => x.BatchRecords).LoadAsync();

            if (batch.BatchRecords == null || !batch.BatchRecords.Any())
            {
                Workspan.Current?.RecordError(
                    "PAYOUTS: Can't update orders status for batch {BatchId} NO RECORDS FOUND",
                    batch.Id);
                return;
            }

            //filter out batch adjustment records which are not related to any order
            var groupedItems = batch.BatchRecords
                .Where(x => x.Type != nameof(BatchRecordTypes.BatchAdjustment))
                .GroupBy(x => x.RelatedOrderId);

            foreach (var groupedItem in groupedItems)
            {
                var orderId = groupedItem.Key;
                var records = groupedItem.ToList();

                //sum all credit records - all debit records
                //  The following condition is backward compatible with the old code (type was credit and debit)
                //  x.Type is nameof(BatchRecordTypes.Order) or nameof(BatchRecordTypes.Credit)) 

                var totalCredits = records.Where(x => x.Direction == nameof(DirectionEnum.Credit) &&
                                                      x.Type is nameof(BatchRecordTypes.Order)
                                                          or nameof(BatchRecordTypes.Credit))
                    .Sum(x => x.Amount);

                var totalDebits = records.Where(x => x.Direction == nameof(DirectionEnum.Debit))
                    .Sum(x => x.Amount);

                var totalPayoutAmount = totalCredits - totalDebits;

                var orderToUpdate = await _dbContext.Orders.SingleOrDefaultAsync(x => x.Id == orderId);

                Workspan.Current?.Log.Information("Updating order {OrderId}", orderId);
                Workspan.Current?.Log.Information("Total payout amount: {TotalPayoutAmount}", totalPayoutAmount);
                Workspan.Current?.Log.Information("Total credits: {TotalCredits}", totalCredits);

                if (orderToUpdate.Amount >= totalPayoutAmount)
                {
                    if (orderToUpdate.PayoutBatchId != batch.Id)
                        Workspan.Current?.Log.Information("Order already has a payout batch id {BatchId}", batch.Id);

                    orderToUpdate.OrderPayoutStatus = OrderPayOutStatuses.Completed;
                    orderToUpdate.PayoutBatchId = batch.Id;
                }
                else if ((orderToUpdate.OrderPayoutStatus
                             is nameof(OrderPayOutStatuses.Completed)
                             or nameof(OrderPayOutStatuses.Failed) && totalPayoutAmount < 0))
                {
                    // need to record this somewhere. in case order was refunded 
                    // At the moment we only mark paidout orders, we have no state for refunded orders
                }
                else
                {
                    throw new FlexChargeException(
                        $"Error while setting order payout status. Total payout amount is greater than order amount. OrderId: {orderId}");
                }
            }

            await StoreReserves(batch.BatchRecords);

            async Task StoreReserves(List<BatchRecord> brecords)
            {
                Workspan.Current?.Log.Information("Storing reserves for batch {BatchId}", batch.Id);

                var reserveBatchRecords = brecords
                    .Where(x => x.BatchId == batch.Id &&
                                x.Type == nameof(BatchRecordTypes.ReserveHold) ||
                                x.Type == nameof(BatchRecordTypes.ReserveRelease) ||
                                x.Type == nameof(BatchRecordTypes.ReserveCancel) ||
                                x.Type == nameof(BatchRecordTypes.ReserveUtilization)
                    )
                    .ToList();

                // Add Funds reserve to funds reserve table
                if (reserveBatchRecords.Any())
                {
                    foreach (var reserveBatchRecord in reserveBatchRecords)
                    {
                        Workspan.Current?.Log.Information("Storing reserve for record: {RecordId}, {OrderId}",
                            reserveBatchRecord.ToString(), reserveBatchRecord.RelatedOrderId);

                        var relatedOrder =
                            await _dbContext.Orders.SingleOrDefaultAsync(x =>
                                x.Id == reserveBatchRecord.RelatedOrderId);

                        switch (reserveBatchRecord.Type)
                        {
                            case nameof(BatchRecordTypes.ReserveHold):
                                await _fundsReserveService.ApplyReserveAsync(
                                    batch.Receiver.RelatedEntityId,
                                    batch.Receiver.Id,
                                    reserveBatchRecord.RelatedOrderId,
                                    relatedOrder.OrderPlacedDate,
                                    batch.Id,
                                    reserveBatchRecord.Id,
                                    batch.FundsReserveConfig ?? Guid.Empty,
                                    reserveBatchRecord.Amount,
                                    reserveBatchRecord.Description,
                                    "");
                                break;
                            case nameof(BatchRecordTypes.ReserveRelease):
                                await _fundsReserveService.ReleaseOrderReserveAsync(reserveBatchRecord.RelatedOrderId,
                                    reserveBatchRecord.Description);
                                break;
                            case nameof(BatchRecordTypes.ReserveCancel):
                                await _fundsReserveService.CancelReserveAsync(reserveBatchRecord.RelatedOrderId,
                                    reserveBatchRecord.Description);
                                break;
                            case nameof(BatchRecordTypes.ReserveUtilization):
                                await _fundsReserveService.UtilizeReserveAsync(
                                    batch.Receiver.RelatedEntityId,
                                    batch.Receiver.Id,
                                    batch.Id,
                                    reserveBatchRecord.Id,
                                    reserveBatchRecord.RelatedOrderId,
                                    ReserveUtilizationReason.LossCoverage);
                                break;
                            default:
                                throw new FlexChargeException("Unknown reserve type");
                        }
                    }
                }
            }
        }
        catch (FlexChargeException e)
        {
            Workspan.Current?.RecordException(e);
            throw;
        }
    }

    public async Task AddOrderReturnToPayoutAsync(Guid mid, Guid orderId, int refundAmount, string message,
        DateTime? returnDateTime = null)
    {
        var order = await _dbContext.Orders.SingleAsync(x => x.Id == orderId && x.MerchantId == mid);

        await AddOrderReturnToPayoutAsync(order, refundAmount, message, returnDateTime);
    }

    public async Task AddOrderReturnToPayoutAsync(Order order, int refundAmount, string message,
        DateTime? returnDateTime = null)
    {
        var merchant = await _dbContext.Merchants.SingleAsync(x => x.Mid == order.MerchantId);

        await AddOrderReturnToPayoutAsync(merchant, order, refundAmount, message, returnDateTime);
    }

    public async Task AddOrderReturnToPayoutAsync(Merchant merchant, Order order, int refundAmount, string message,
        DateTime? returnDateTime = null)
    {
        var batch = await _batchService.GetCurrentBatch(merchant);
        var merchantFees = await GetMerchantFees(merchant);

        batch.AddReturn(order, returnDateTime, refundAmount, message, merchantFees);
        await _dbContext.SaveChangesAsync();
    }

    public async Task AddOrderChargebackToPayoutAsync(Guid mid, Guid orderId, string message,
        DateTime? chargebackDateTime = null)
    {
        var order = await _dbContext.Orders.SingleAsync(x => x.Id == orderId && x.MerchantId == mid);
        await AddOrderChargebackToPayoutAsync(order, message, chargebackDateTime);
    }

    public async Task AddOrderChargebackToPayoutAsync(Order order, string message, DateTime? chargebackDateTime = null)
    {
        var merchant = await _dbContext.Merchants.SingleAsync(x => x.Mid == order.MerchantId);
        await AddOrderChargebackToPayoutAsync(merchant, order, message, chargebackDateTime);
    }

    public async Task AddOrderChargebackToPayoutAsync(Merchant merchant, Order order, string message,
        DateTime? chargebackDateTime = null)
    {
        var batch = await _batchService.GetCurrentBatch(merchant);
        var merchantFees = await GetMerchantFees(merchant);

        batch.AddChargeback(order, chargebackDateTime, message, merchantFees);
        await _dbContext.SaveChangesAsync();
    }

    private async Task<List<MerchantFee>> GetMerchantFees(Merchant merchant)
    {
        var merchantFees = await _dbContext.MerchantFees
            .Where(x => x.MerchantId == merchant.Mid && x.IsActive)
            .ToListAsync();
        return merchantFees;
    }

    public async Task<string> ExportUnpostedAsync(Guid senderId, Guid receiverId, Guid? partnerId, string? batchType,
        DateTime? from, DateTime? to,
        string format = "csv")
    {
        using var workspan = Workspan.Start<PayoutService>();
        try
        {
            var batches = await _batchService.GetUnPostedAsync(senderId, receiverId, partnerId, batchType, from, to);

            return format switch
            {
                "json" => JsonConvert.SerializeObject(batches.OrderByDescending(x => x.PayoutDate).ToList()),
                "csv" => CSVExport.GenerateCSVFromRows<BatchDTO, BatchDTOMap>(batches
                    .OrderByDescending(x => x.PayoutDate).ToList()),
                _ => throw new FlexChargeException("Invalid format")
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<string> ExportPendingAsync(Guid senderId, Guid receiverId, Guid? partnerId, string? batchType,
        DateTime? from, DateTime? to,
        string format = "csv")
    {
        using var workspan = Workspan.Start<PayoutService>();
        try
        {
            var batches = await _batchService.GetPendingAsync(senderId, receiverId, partnerId, batchType, from, to);

            return format switch
            {
                "json" => JsonConvert.SerializeObject(batches.OrderByDescending(x => x.PayoutDate).ToList()),
                "csv" => CSVExport.GenerateCSVFromRows(batches.OrderByDescending(x => x.PayoutDate).ToList()),
                _ => throw new FlexChargeException("Invalid format")
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<string> ExportShelvedAsync(Guid senderId, Guid receiverId, Guid? partnerId, string? batchType,
        DateTime? from, DateTime? to,
        string format = "csv")
    {
        using var workspan = Workspan.Start<PayoutService>();
        try
        {
            var batches = await _batchService.GetShelvedAsync(senderId, receiverId, partnerId, batchType, from, to);

            return format switch
            {
                "json" => JsonConvert.SerializeObject(batches.OrderByDescending(x => x.PayoutDate).ToList()),
                "csv" => CSVExport.GenerateCSVFromRows(batches.OrderByDescending(x => x.PayoutDate).ToList()),
                _ => throw new FlexChargeException("Invalid format")
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<string> ExportDetailsAsync(Guid senderId, Guid receiverId, Guid id, int pageSize,
        int pageNumber, string format = "csv")
    {
        using var workspan = Workspan.Start<PayoutService>();
        try
        {
            var payoutDetails = _dbContext.Batches
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Where(x => x.Id == id);

            if (senderId != Guid.Empty)
            {
                payoutDetails = payoutDetails.Where(x => x.SenderId == senderId);
            }

            if (receiverId != Guid.Empty)
            {
                payoutDetails = payoutDetails.Where(x => x.ReceiverId == receiverId);
            }

            var payoutDetails2 = await payoutDetails.Where(x => x.Id == id)
                .SelectMany(x => x.BatchRecords)
                .Join(_dbContext.Orders, x => x.RelatedOrderId, x => x.Id,
                    (r, o) => new PayoutDetailsDTO()
                    {
                        OrderDate = o.OrderPlacedDate,
                        ExternalOrderId = o.ReferenceNumber,
                        MerchantId = o.MerchantId,
                        Type = r.Type, //nameof(PayoutDetailsType.Payment),
                        PaymentType = r.Direction,
                        Amount = Formatters.IntToDecimal(r.Amount),
                        FlexChargeFee = Formatters.IntToDecimal(r.Fee),
                        Payout = Formatters.IntToDecimal(r.Amount),
                        OrderId = o.Id,
                        Adjustment = Formatters.IntToDecimal(r.Adjustment),
                        Reserve = Formatters.IntToDecimal(r.Type == BatchRecordTypes.ReserveHold.ToString()
                            ? r.Amount
                            : 0),
                    }).OrderByDescending(x => x.OrderDate)
                .ToListAsync();


            if (pageNumber != null && pageSize != null)
            {
                payoutDetails2 = pageNumber == 1
                    ? payoutDetails2.Skip(0).Take((int) pageSize).ToList()
                    : payoutDetails2.Skip(((int) pageNumber - 1) * (int) pageSize).Take((int) pageSize).ToList();
            }

            return format switch
            {
                "json" => JsonConvert.SerializeObject(payoutDetails2),
                "csv" => CSVExport.GenerateCSVFromRows(payoutDetails2),
                _ => throw new FlexChargeException("Invalid format")
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task AddAdjustmentAsync(Guid senderId, Guid receiverId, Guid id, AdjustmentCreateRequestDTO payload)
    {
        using var workspan = Workspan.Start<PayoutService>()
            .LogEnterAndExit();

        try
        {
            var batch = _dbContext.Batches
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.BatchRecords)
                .Where(x => x.Id == id);

            if (senderId != Guid.Empty)
            {
                batch = batch.Where(x => x.SenderId == senderId);
            }

            if (receiverId != Guid.Empty)
            {
                batch = batch.Where(x => x.ReceiverId == receiverId);
            }

            var queryResult = await batch.SingleOrDefaultAsync();

            if (queryResult == null)
                throw new Exception("Not found");

            if (queryResult.IsPosted)
                throw new Exception("Can't change posted batch");

            queryResult.ApplyAdjustment(payload.PaymentType, Formatters.DecimalToInt(payload.Amount));

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task RemoveReserveUtilizationAsync(Guid senderId, Guid receiverId, Guid batchId)
    {
        using var workspan = Workspan.Start<PayoutService>()
            .LogEnterAndExit();

        try
        {
            var batch = _dbContext.Batches
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.BatchRecords)
                .Where(x => x.Id == batchId);

            if (senderId != Guid.Empty)
                batch = batch.Where(x => x.SenderId == senderId);

            if (receiverId != Guid.Empty)
                batch = batch.Where(x => x.ReceiverId == receiverId);

            var queryResult = await batch.SingleOrDefaultAsync();

            if (queryResult == null)
                throw new FlexNotFoundException("Not found");

            queryResult.RemoveReserveUtilization();

            _dbContext.Batches.Update(queryResult);

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task AddCommentAsync(Guid senderId, Guid receiverId, Guid batchId, string comment)
    {
        using var workspan = Workspan.Start<PayoutService>()
            .LogEnterAndExit();

        try
        {
            var batch = _dbContext.Batches
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.BatchRecords)
                .Where(x => x.Id == batchId);

            if (senderId != Guid.Empty)
            {
                batch = batch.Where(x => x.SenderId == senderId);
            }

            if (receiverId != Guid.Empty)
            {
                batch = batch.Where(x => x.ReceiverId == receiverId);
            }

            var queryResult = await batch.SingleOrDefaultAsync();

            if (queryResult == null)
                throw new FlexNotFoundException("Not found");

            queryResult.Comment = comment;

            _dbContext.Batches.Update(queryResult);

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task ApplyReserveAsync(Guid senderId, Guid receiverId, Guid id, ApplyReserveRequestDTO payload)
    {
        using var workspan = Workspan.Start<PayoutService>()
            .LogEnterAndExit();
        try
        {
            var batch = _dbContext.Batches
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.BatchRecords)
                .Where(x => x.Id == id);

            if (senderId != Guid.Empty)
            {
                batch = batch.Where(x => x.SenderId == senderId);
            }

            if (receiverId != Guid.Empty)
            {
                batch = batch.Where(x => x.ReceiverId == receiverId);
            }

            var queryResult = await batch.SingleOrDefaultAsync();

            if (queryResult == null)
                throw new Exception("Not found");

            if (queryResult.IsPosted)
                throw new Exception("Can't modify posted batch");

            queryResult.Reserve = Formatters.DecimalToInt(payload.Amount);

            queryResult.ApplyReserve(null, queryResult.Reserve, payload.Description);

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    /// <summary>
    /// applies reserve utilization to the batch based on available funds in reserve
    /// </summary>
    /// <param name="senderId"></param>
    /// <param name="receiverId"></param>
    /// <param name="id"></param>
    /// <param name="payload"></param>
    /// <exception cref="FlexChargeException"></exception>
    public async Task ApplyReserveUtilizationAsync(Guid senderId, Guid receiverId, Guid id,
        UtilizeFundsReserveDTO payload)
    {
        using var workspan = Workspan.Start<PayoutService>()
            .LogEnterAndExit();
        try
        {
            var dbset = _dbContext.Batches
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.BatchRecords)
                .Where(x => x.Id == id);

            if (senderId != Guid.Empty)
                dbset = dbset.Where(x => x.SenderId == senderId);

            if (receiverId != Guid.Empty)
                dbset = dbset.Where(x => x.ReceiverId == receiverId);

            var batch = await dbset.SingleOrDefaultAsync();

            if (batch == null)
                throw new FlexChargeException("Not found");

            if (batch.IsPosted)
                throw new FlexChargeException("Can't modify posted batch");

            if (batch.TotalAmount >= 0)
                throw new FlexChargeException("Can't apply reserve utilization to positive amount batch");

            //check available funds in reserve
            var availableReserve = await _fundsReserveService.GetReserveBalanceAsync(receiverId);

            if (availableReserve.Amount < payload.Amount)
                throw new FlexChargeException("Not enough funds in reserve there is only " + availableReserve);

            foreach (var reserve in availableReserve.Records)
            {
                batch.AddReserveUtilization(reserve.OrderId, payload.AmountInCents, payload.Reason.ToString());
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<List<PayoutDTO>> GetPayoutsAsync(
        Guid senderId,
        Guid receiverId,
        Guid? partnerId,
        PayoutStatus? status,
        string? batchType,
        DateTime? from,
        DateTime? to)
    {
        using var workspan = Workspan.Start<PayoutService>()
            .LogEnterAndExit();
        try
        {
            var results = new List<BatchDTO>();

            switch (status)
            {
                case PayoutStatus.Pending:
                    results = await _batchService.GetPendingAsync(senderId, receiverId, partnerId, batchType, from, to);
                    break;
                case PayoutStatus.Processing:
                    break;
                case PayoutStatus.Failed:
                    break;
                case PayoutStatus.Error:
                    break;
                case PayoutStatus.Canceled:
                    break;
                case PayoutStatus.Success:
                    break;
                default:
                    results = await _batchService.GetUnPostedAsync(senderId, receiverId, partnerId, batchType, from,
                        to);
                    break;
            }

            return _mapper.Map<List<BatchDTO>, List<PayoutDTO>>(results);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }
}