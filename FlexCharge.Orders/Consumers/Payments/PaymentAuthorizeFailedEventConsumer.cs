using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Orders.Consumers;

public class PaymentAuthorizeFailedEventConsumer : ConsumerBase<PaymentAuthorizeFailedEvent>
{
    private readonly IOrderService _orderService;
    private readonly IActivityService _activityService;

    public PaymentAuthorizeFailedEventConsumer(IOrderService orderService,
        IActivityService activityService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _orderService = orderService;
        _activityService = activityService;
    }

    protected override async Task ConsumeMessage(PaymentAuthorizeFailedEvent message,
        CancellationToken cancellationToken)
    {
        Workspan
            .Context(Context)
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId)
            .LogEnterAndExit();
    }
}