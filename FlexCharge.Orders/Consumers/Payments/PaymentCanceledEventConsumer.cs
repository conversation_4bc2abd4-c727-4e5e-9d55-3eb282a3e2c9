using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.DistributedLock;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Orders.Consumers;

public class PaymentCanceledEventConsumer : IdempotentEventConsumer<PaymentCanceledEvent>
{
    private readonly IOrderService _orderService;
    private readonly IActivityService _activityService;
    private readonly IDistributedLockService _distributedLockService;

    public PaymentCanceledEventConsumer(IOrderService orderService, IActivityService activityService,
        IServiceScopeFactory serviceScopeFactory,
        IDistributedLockService distributedLockService) : base(serviceScopeFactory)
    {
        _orderService = orderService;
        _activityService = activityService;
        _distributedLockService = distributedLockService;
    }

    protected override async Task ConsumeEvent(PaymentCanceledEvent message, CancellationToken cancellationToken)
    {
        Workspan
            .Context(Context)
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId)
            .LogEnterAndExit();

        try
        {
            await using var @lock = await _distributedLockService
                .AcquireLockAsync(LockKeyFactory.CreateOrderKey(message.OrderId),
                    TimeSpan.FromSeconds(15),
                    maxRetryDuration: TimeSpan.FromMinutes(1));

            await _orderService.ApplyOrderActivityAsync(new OrderActivityDTO
            {
                OrderId = message.OrderId,
                PaymentTransactionId = message.TransactionId,
                RelatedTransactionId = message.RelatedTransactionId,
                Type = message.PaymentType,
                Description = message.Description,
                TotalAmount = message.Amount,
                DiscountAmount = message.DiscountAmount,
                PaymentDate = message.PaymentDate,
                Bin = message.Bin,
                Last4 = message.Last4,
            });

            // TODO: Set Returned status explicitly on Refund button
            // await _orderService.SetOrderStatusAsync(context.Message.Mid, context.Message.OrderId,
            //     OrderStatusCategory.Returned, string.Empty, context.Message.Description);

            await _activityService.CreateActivityAsync(PayInActivities.PayIn_Payment_Cancel_Succeeded,
                set => set
                    .TenantId(message.Mid)
                    .CorrelationId(message.OrderId)
                    .Data(message)
                    .Meta(meta => meta
                        .SetValue("Amount", message.Amount)
                        .SetValue("DiscountAmount", message.DiscountAmount)));
        }
        catch (Exception e)
        {
            Workspan.RecordException(e,
                "EXCEPTION: OrdersService > PaymentVoidedEventConsumer > PaymentAuthorizedEvent{Event}",
                JsonConvert.SerializeObject(message));
        }
    }
}