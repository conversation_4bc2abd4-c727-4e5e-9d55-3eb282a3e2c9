// using System;
// using System.Collections.Generic;
// using System.Threading.Tasks;
// using FlexCharge.Common.Cache;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Contracts.Payouts;
// using FlexCharge.Orders.Contracts;
// using MassTransit;
// using Microsoft.EntityFrameworkCore;
//
// namespace FlexCharge.Orders.Consumers;
//
// public class PayoutProcessingFailedEventConsumer : IConsumer<PayoutProcessingFailedEvent>
// {
//     private readonly PostgreSQLDbContext _dbContext;
//     private readonly IBigPayloadService _bigPayloadService;
//     private readonly IPublishEndpoint _publisher;
//
//     public PayoutProcessingFailedEventConsumer(
//         PostgreSQLDbContext dbContext,
//         IBigPayloadService bigPayloadService,
//         IPublishEndpoint publisher)
//     {
//         _dbContext = dbContext;
//         _bigPayloadService = bigPayloadService;
//         _publisher = publisher;
//     }
//
//     public async Task Consume(ConsumeContext<PayoutProcessingFailedEvent> context)
//     {
//         using var workspan = Workspan.Start<PayoutProcessingFailedEventConsumer>(logEnterAndExit: true)
//             .Context(context);
//
//         try
//         {
//             var payoutOrdersPayload = new BigPayload<List<PayoutOrderItem>>(context.Message.PayoutOrders.Key);
//             var ordersInPayoutProcessing = await payoutOrdersPayload.GetValueAsync(_bigPayloadService);
//
//             foreach (var orderInPayoutProcessing in ordersInPayoutProcessing)
//             {
//                 var order = await _dbContext.Orders.SingleAsync(x => x.Id == orderInPayoutProcessing.Id);
//                 order.OrderPayoutStatus = PayOutStatuses.Failed;
//             }
//
//             await _dbContext.SaveChangesAsync();
//         }
//         catch (Exception e)
//         {
//             workspan.RecordException(e);
//             throw;
//         }
//     }
// }