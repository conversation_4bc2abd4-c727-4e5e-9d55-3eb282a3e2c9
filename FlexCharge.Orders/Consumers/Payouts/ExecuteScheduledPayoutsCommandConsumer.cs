using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cache;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Common;
using FlexCharge.Contracts.Payouts;
using FlexCharge.Orders;
using FlexCharge.Orders.Contracts;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services.PayoutServices;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Orders.Consumers;

public class ExecuteScheduledPayoutsCommandConsumer : IdempotentCommandConsumer<ExecuteScheduledPayoutsCommand>
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;

    public ExecuteScheduledPayoutsCommandConsumer(
        PostgreSQLDbContext dbContext,
        IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
        _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
    }

    protected override async Task ConsumeCommand(ExecuteScheduledPayoutsCommand command,
        CancellationToken cancellationToken)
    {
        _backgroundWorkerCommandQueue.Enqueue(new ProcessPayoutsCommand());

        await Task.CompletedTask;
    }
}