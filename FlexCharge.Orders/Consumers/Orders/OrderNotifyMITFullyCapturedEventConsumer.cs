using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Emails;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services;
using FlexCharge.Orders.Services.ConsumerNotifications;
using FlexCharge.Orders.Services.Links;
using FlexCharge.Orders.Services.Links.ClickToRefund;
using FlexCharge.Orders.Services.MerchantNotifications;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace FlexCharge.Orders.Consumers;

public class OrderNotifyMITFullyCapturedEventConsumer : ConsumerBase<OrderNotifyMITFullyCapturedEvent>
{
    private readonly IConsumerNotificationsService _consumerNotificationsService;
    private readonly IMerchantNotificationsService _merchantNotificationsService;
    private IOrderService _orderService;
    private readonly IClickToRefundService _clickToRefundService;
    private readonly ILinksService _linksService;
    private IEmailSender _emailSender;
    private IConfiguration _configuration;
    private readonly IActivityService _activityService;
    private readonly IOptions<SendGridOptions> _emailOptions;


    public OrderNotifyMITFullyCapturedEventConsumer(
        IConsumerNotificationsService consumerNotificationsService,
        IMerchantNotificationsService merchantNotificationsService,
        IOrderService orderService,
        IClickToRefundService clickToRefundService,
        ILinksService linksService,
        IMapper mapper,
        IEmailSender emailSender,
        IConfiguration configuration,
        IServiceScopeFactory serviceScopeFactory,
        IActivityService activityService,
        IOptions<SendGridOptions> emailOptions) : base(serviceScopeFactory)
    {
        _consumerNotificationsService = consumerNotificationsService;
        _merchantNotificationsService = merchantNotificationsService;
        _orderService = orderService;
        _clickToRefundService = clickToRefundService;
        _linksService = linksService;
        _emailSender = emailSender;
        _configuration = configuration;
        _activityService = activityService;
        _emailOptions = emailOptions;
    }

    protected override async Task ConsumeMessage(OrderNotifyMITFullyCapturedEvent message,
        CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("OrderId", message.OrderId)
            .Baggage("Mid", message.Mid);

        string last4digits = !string.IsNullOrWhiteSpace(message.CardLast4Digits) ? "*" + message.CardLast4Digits : null;

        #region Sending Consumer Notification

        string clickToRefundLinkUrl = null;
        var clickToRefundLink = await _clickToRefundService.CreateClickToRefundLinkAsync(message.Mid, message.OrderId);

        if (clickToRefundLink != null)
            clickToRefundLinkUrl = _linksService.GetLinkUrl(clickToRefundLink.Value.LinkId);

        var merchantSupportEmail = message.MerchantSupportEmail;

        await _consumerNotificationsService.SendConsumerNotificationAsync(message.Mid, message.OrderId,
            message.To, message.Phone,
            "order_confirmation_after_initial_auth_captured",
            new ConsumerNotificationTemplateData(
                message.Subject,
                message.Name,
                message.OrderAmount,
                message.PaymentDescriptor,
                message.MerchantName,
                message.MerchantSiteUrl,
                message.MerchantSupportUrl,
                message.MerchantSupportEmail,
                message.MerchantSupportPhone,
                message.MerchantLogoUrl,
                message.ExternalOrderId,
                message.OrderDate.ToString("MMMM dd, yyyy"),
                message.Currency,
                clickToRefundLink?.IsActive == true,
                clickToRefundLinkUrl,
                message.CaptureDate.ToString("MMMM dd, yyyy"),
                last4digits,
                (string) null)
            , merchantSupportEmail);

        #endregion

        #region Sending Merchant Notification

        await _merchantNotificationsService.SendMerchantNotificationAsync(message.Mid, message.OrderId,
            merchantSupportEmail,
            "merchant_order_notification_after_full_capture",
            new MerchantNotificationTemplateData(
                message.Subject,
                message.Name,
                message.OrderAmount,
                message.PaymentDescriptor,
                message.MerchantName,
                message.MerchantSiteUrl,
                message.MerchantSupportUrl,
                message.MerchantSupportPhone,
                message.MerchantLogoUrl,
                message.ExternalOrderId,
                message.OrderDate.ToString("MMMM dd, yyyy"),
                message.Currency,
                false,
                null,
                message.CaptureDate.ToString("MMMM dd, yyyy"),
                last4digits,
                (string) null)
        );

        #endregion
    }
}