using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.Consumers;

public class MerchantDeactivatedEventConsumer : ConsumerBase<MerchantDeactivatedEvent>
{
    private readonly PostgreSQLDbContext _dbContext;


    public MerchantDeactivatedEventConsumer(PostgreSQLDbContext dbContext,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
    }


    protected override async Task ConsumeMessage(MerchantDeactivatedEvent message, CancellationToken cancellationToken)
    {
        try
        {
            var existingMerchant = await _dbContext.Merchants.SingleOrDefaultAsync(x => x.Mid == message.Mid);
            if (existingMerchant is not null)
            {
                existingMerchant.Status = "Inactive";
                _dbContext.Merchants.Update(existingMerchant);
                await _dbContext.SaveChangesAsync();

                Workspan.Log.Information("Merchant deactivated: {mid}", message.Mid);
            }
            else
            {
                Workspan.Log.Fatal(
                    "Can't deactivate Merchant - Merchant doesn't exist. Merchant: {MerchantId}",
                    message.Mid);
            }
        }
        catch (Exception e)
        {
            Workspan.RecordException(e,
                "Failed deactivating merchant {Mid}", message.Mid);
        }
    }
}