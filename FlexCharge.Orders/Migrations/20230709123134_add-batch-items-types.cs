using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Orders.Migrations
{
    /// <inheritdoc />
    public partial class addbatchitemstypes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Returns",
                table: "Batches",
                newName: "TotalCount");

            migrationBuilder.RenameColumn(
                name: "FlexChargeFees",
                table: "Batches",
                newName: "ReturnsCount");

            migrationBuilder.RenameColumn(
                name: "Chargebacks",
                table: "Batches",
                newName: "ReturnsAmount");

            migrationBuilder.AddColumn<int>(
                name: "ChargebacksAmount",
                table: "Batches",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "ChargebacksCount",
                table: "Batches",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "FlexChargeChargebackFeesAmount",
                table: "Batches",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "FlexChargeFeesAmount",
                table: "Batches",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "FlexChargeFeesCount",
                table: "Batches",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "FlexChargeInterchangeFeesAmount",
                table: "Batches",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "FlexChargeReturnFeesAmount",
                table: "Batches",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "BatchRecord",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<string>(type: "text", nullable: true),
                    BatchId = table.Column<Guid>(type: "uuid", nullable: false),
                    RelatedOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    Amount = table.Column<int>(type: "integer", nullable: false),
                    Fee = table.Column<int>(type: "integer", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BatchRecord", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BatchRecord_Batches_BatchId",
                        column: x => x.BatchId,
                        principalTable: "Batches",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BatchRecord_BatchId",
                table: "BatchRecord",
                column: "BatchId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BatchRecord");

            migrationBuilder.DropColumn(
                name: "ChargebacksAmount",
                table: "Batches");

            migrationBuilder.DropColumn(
                name: "ChargebacksCount",
                table: "Batches");

            migrationBuilder.DropColumn(
                name: "FlexChargeChargebackFeesAmount",
                table: "Batches");

            migrationBuilder.DropColumn(
                name: "FlexChargeFeesAmount",
                table: "Batches");

            migrationBuilder.DropColumn(
                name: "FlexChargeFeesCount",
                table: "Batches");

            migrationBuilder.DropColumn(
                name: "FlexChargeInterchangeFeesAmount",
                table: "Batches");

            migrationBuilder.DropColumn(
                name: "FlexChargeReturnFeesAmount",
                table: "Batches");

            migrationBuilder.RenameColumn(
                name: "TotalCount",
                table: "Batches",
                newName: "Returns");

            migrationBuilder.RenameColumn(
                name: "ReturnsCount",
                table: "Batches",
                newName: "FlexChargeFees");

            migrationBuilder.RenameColumn(
                name: "ReturnsAmount",
                table: "Batches",
                newName: "Chargebacks");
        }
    }
}
