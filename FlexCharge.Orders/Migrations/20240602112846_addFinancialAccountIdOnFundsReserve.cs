using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Orders.Migrations
{
    /// <inheritdoc />
    public partial class addFinancialAccountIdOnFundsReserve : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<Guid>(
                name: "Mid",
                table: "FundsReserves",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<Guid>(
                name: "FinancialAccountId",
                table: "FundsReserves",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_FundsReserves_FinancialAccountId",
                table: "FundsReserves",
                column: "FinancialAccountId");

            migrationBuilder.AddForeignKey(
                name: "FK_FundsReserves_FinancialAccounts_FinancialAccountId",
                table: "FundsReserves",
                column: "FinancialAccountId",
                principalTable: "FinancialAccounts",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FundsReserves_FinancialAccounts_FinancialAccountId",
                table: "FundsReserves");

            migrationBuilder.DropIndex(
                name: "IX_FundsReserves_FinancialAccountId",
                table: "FundsReserves");

            migrationBuilder.DropColumn(
                name: "FinancialAccountId",
                table: "FundsReserves");

            migrationBuilder.AlterColumn<Guid>(
                name: "Mid",
                table: "FundsReserves",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("********-0000-0000-0000-********0000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);
        }
    }
}
