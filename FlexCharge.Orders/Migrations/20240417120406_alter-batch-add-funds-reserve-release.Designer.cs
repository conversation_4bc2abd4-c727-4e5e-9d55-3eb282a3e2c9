// <auto-generated />
using System;
using FlexCharge.Orders;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Orders.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20240417120406_alter-batch-add-funds-reserve-release")]
    partial class alterbatchaddfundsreserverelease
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Orders.Entities.Address", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Line1")
                        .HasColumnType("text");

                    b.Property<string>("Line2")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("ZipCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Addresses");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.Batch", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Adjustment")
                        .HasColumnType("integer");

                    b.Property<string>("BatchType")
                        .HasColumnType("text");

                    b.Property<Guid?>("BeneficiaryId")
                        .HasColumnType("uuid");

                    b.Property<int>("ChargebacksAmount")
                        .HasColumnType("integer");

                    b.Property<int>("ChargebacksCount")
                        .HasColumnType("integer");

                    b.Property<string>("ConcurrencyUniqueBatchId")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("FlexChargeChargebackFeesAmount")
                        .HasColumnType("integer");

                    b.Property<int>("FlexChargeFeesAmount")
                        .HasColumnType("integer");

                    b.Property<int>("FlexChargeFeesCount")
                        .HasColumnType("integer");

                    b.Property<int>("FlexChargeInterchangeFeesAmount")
                        .HasColumnType("integer");

                    b.Property<int>("FlexChargeReturnFeesAmount")
                        .HasColumnType("integer");

                    b.Property<DateTime>("From")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("FundsReserveConfig")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsOffline")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPosted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("IsShelved")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PayoutStatus")
                        .HasColumnType("text");

                    b.Property<string>("PayoutStatusMessage")
                        .HasColumnType("text");

                    b.Property<Guid?>("RelatedTransaction")
                        .HasColumnType("uuid");

                    b.Property<int>("Reserve")
                        .HasColumnType("integer");

                    b.Property<int>("ReserveCount")
                        .HasColumnType("integer");

                    b.Property<int>("ReserveRelease")
                        .HasColumnType("integer");

                    b.Property<int>("ReserveReleaseCount")
                        .HasColumnType("integer");

                    b.Property<int>("ReturnsAmount")
                        .HasColumnType("integer");

                    b.Property<int>("ReturnsCount")
                        .HasColumnType("integer");

                    b.Property<DateTime>("To")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("TotalAmount")
                        .HasColumnType("integer");

                    b.Property<int>("TotalCount")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("BeneficiaryId");

                    b.HasIndex("ConcurrencyUniqueBatchId")
                        .IsUnique();

                    b.ToTable("Batches");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.BatchOrder", b =>
                {
                    b.Property<Guid>("BatchId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("BatchId", "OrderId");

                    b.HasIndex("OrderId");

                    b.ToTable("BatchOrder");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.BatchRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Adjustment")
                        .HasColumnType("integer");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<Guid>("BatchId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Direction")
                        .HasColumnType("text");

                    b.Property<int>("Fee")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("RelatedOrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("UniqueHash")
                        .HasColumnType("text");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("BatchId");

                    b.HasIndex("UniqueHash")
                        .IsUnique();

                    b.ToTable("BatchRecords");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.ExecutedStrategy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("Payload")
                        .HasColumnType("text");

                    b.Property<Guid>("SchedulerJobId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("SchedulerJobId");

                    b.ToTable("ExecutedStrategies");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.FundsReserve", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<Guid?>("BatchId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("BatchRecordId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FundsReserveConfigId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("Parent")
                        .HasColumnType("uuid");

                    b.Property<string>("Reason")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("FundsReserves");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.Link", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrentUserInteraction")
                        .HasColumnType("text");

                    b.Property<string>("Data")
                        .HasColumnType("jsonb");

                    b.Property<int>("ExecutedUserInteractions")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("Expiry")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("InitialData")
                        .HasColumnType("jsonb");

                    b.Property<string>("InitialUserInteraction")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<bool>("UseIframes")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("Links");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.Merchant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("CITClickToRefundEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("CITConsumerNotificationsEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DashboardReportingBucketName")
                        .HasColumnType("text");

                    b.Property<string>("Dba")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Locked")
                        .HasColumnType("boolean");

                    b.Property<bool>("MITClickToRefundEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("MITConsumerNotificationsEnabled")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PayoutFrequency")
                        .HasColumnType("text");

                    b.Property<bool>("PayoutsEnabled")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("Pid")
                        .HasColumnType("uuid");

                    b.Property<string>("SupportUrl")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Merchants");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.MerchantFee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("ChargeType")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FeeType")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<int?>("MinimumFeeAmount")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("MerchantFees");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Adjustment")
                        .HasColumnType("integer");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<Guid?>("BillingAddressId")
                        .HasColumnType("uuid");

                    b.Property<string>("CVVCheckResult")
                        .HasColumnType("text");

                    b.Property<string>("ConfirmationId")
                        .HasColumnType("text");

                    b.Property<Guid>("ContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("CurrentStrategyId")
                        .HasColumnType("text");

                    b.Property<int>("DiscountsAmount")
                        .HasColumnType("integer");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ExternalSubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<bool>("InProcessing")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGhostMode")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMIT")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsProcessingByExternalRecyclingEngine")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsSenseKeyMatched")
                        .HasColumnType("boolean");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<string>("MerchantDescriptor")
                        .HasColumnType("text");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("MerchantName")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<string>("OrderPayinStatus")
                        .HasColumnType("text");

                    b.Property<string>("OrderPayoutStatus")
                        .HasColumnType("text");

                    b.Property<DateTime>("OrderPlacedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PaymentInstrumentToken")
                        .HasColumnType("text");

                    b.Property<string>("PaymentTransactionsResponses")
                        .HasColumnType("text");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.Property<string>("ProfileUrl")
                        .HasColumnType("text");

                    b.Property<string>("RecyclingEngine")
                        .HasColumnType("text");

                    b.Property<string>("ReferenceNumber")
                        .HasColumnType("text");

                    b.Property<int>("RefundsAmount")
                        .HasColumnType("integer");

                    b.Property<string>("SCAAuthenticationToken")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("ShippingAddressId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SiteId")
                        .HasColumnType("uuid");

                    b.Property<string>("StatusCategory")
                        .HasColumnType("text");

                    b.Property<string>("StatusDescription")
                        .HasColumnType("text");

                    b.Property<string>("StatusSubCategory")
                        .HasColumnType("text");

                    b.Property<string>("StoreId")
                        .HasColumnType("text");

                    b.Property<string>("StoreName")
                        .HasColumnType("text");

                    b.Property<int>("StrategySequenceCounter")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("BillingAddressId");

                    b.HasIndex("CreatedOn");

                    b.HasIndex("Email");

                    b.HasIndex("FirstName");

                    b.HasIndex("LastName");

                    b.HasIndex("ShippingAddressId");

                    b.HasIndex("StatusCategory");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.OrderItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Discount")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("text");

                    b.Property<int>("Tax")
                        .HasColumnType("integer");

                    b.Property<int>("UnitPrice")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("OrderItems");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.Site", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerSupportEmail")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportLink")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportName")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportPhone")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<string>("DescriptorCity")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Mid");

                    b.ToTable("Sites");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.Transaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("BatchId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("DiscountAmount")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("PaymentTransactionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RelatedTransactionId")
                        .HasColumnType("uuid");

                    b.Property<int>("TotalAmount")
                        .HasColumnType("integer");

                    b.Property<string>("TotalAmountFormatted")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("Transactions");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.Batch", b =>
                {
                    b.HasOne("FlexCharge.Orders.Entities.Merchant", "Beneficiary")
                        .WithMany()
                        .HasForeignKey("BeneficiaryId");

                    b.Navigation("Beneficiary");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.BatchOrder", b =>
                {
                    b.HasOne("FlexCharge.Orders.Entities.Batch", "Batch")
                        .WithMany()
                        .HasForeignKey("BatchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FlexCharge.Orders.Entities.Order", "Order")
                        .WithMany("BatchOrders")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Batch");

                    b.Navigation("Order");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.BatchRecord", b =>
                {
                    b.HasOne("FlexCharge.Orders.Entities.Batch", "Batch")
                        .WithMany("BatchRecords")
                        .HasForeignKey("BatchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Batch");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.ExecutedStrategy", b =>
                {
                    b.HasOne("FlexCharge.Orders.Entities.Order", null)
                        .WithMany("ExecutedStrategies")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.Order", b =>
                {
                    b.HasOne("FlexCharge.Orders.Entities.Address", "BillingAddress")
                        .WithMany()
                        .HasForeignKey("BillingAddressId");

                    b.HasOne("FlexCharge.Orders.Entities.Address", "ShippingAddress")
                        .WithMany()
                        .HasForeignKey("ShippingAddressId");

                    b.Navigation("BillingAddress");

                    b.Navigation("ShippingAddress");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.OrderItem", b =>
                {
                    b.HasOne("FlexCharge.Orders.Entities.Order", "Order")
                        .WithMany("OrderItems")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.Transaction", b =>
                {
                    b.HasOne("FlexCharge.Orders.Entities.Order", "Order")
                        .WithMany("ActivityItems")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.Batch", b =>
                {
                    b.Navigation("BatchRecords");
                });

            modelBuilder.Entity("FlexCharge.Orders.Entities.Order", b =>
                {
                    b.Navigation("ActivityItems");

                    b.Navigation("BatchOrders");

                    b.Navigation("ExecutedStrategies");

                    b.Navigation("OrderItems");
                });
#pragma warning restore 612, 618
        }
    }
}
