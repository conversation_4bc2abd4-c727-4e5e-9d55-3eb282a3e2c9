using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Orders.Migrations
{
    public partial class amountUpdate2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Amount",
                table: "Transactions",
                newName: "TotalAmountFormatted");

            migrationBuilder.AddColumn<int>(
                name: "TotalAmount",
                table: "Transactions",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TotalAmount",
                table: "Transactions");

            migrationBuilder.RenameColumn(
                name: "TotalAmountFormatted",
                table: "Transactions",
                newName: "Amount");
        }
    }
}
