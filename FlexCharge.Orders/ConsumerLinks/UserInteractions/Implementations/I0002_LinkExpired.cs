using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Shared.UIBuilder;
using FlexCharge.Eligibility.Controllers;

namespace FlexCharge.Orders.ConsumerLinks.UserInteractions.Implementations.ClickToRefund;

public class I0002_LinkExpired : PopupUserInteraction
{
    public override bool IsImplemented => true;


    protected override async Task CreateAsync()
    {
        UI.Title()
            .Text($"Unfortunately this link has now expired.");


        UI.HorizontalSeparator();

        UI.Text()
            .Text(
                $"Sorry for your inconvenience but requested operation can no longer be supported through this link.");
    }


    protected override async Task<UserInteractionResult> ExecuteAsync(NextPutRequest request)
    {
        return await CloseThisPopupAsync();
    }
}