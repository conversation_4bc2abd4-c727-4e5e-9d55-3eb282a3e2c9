using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Shared.UIBuilder;
using FlexCharge.Eligibility.Controllers;

namespace FlexCharge.Orders.ConsumerLinks.UserInteractions.Implementations.ClickToRefund;

public class I0003_LinkInactive : PopupUserInteraction
{
    public override bool IsImplemented => true;


    protected override async Task CreateAsync()
    {
        UI.Title()
            .Text($"Unfortunately this link is now inactive");


        UI.HorizontalSeparator();

        UI.Text()
            .Text(
                $"Sorry for your inconvenience but requested operation can not be supported through this link.");
    }

    protected override async Task<UserInteractionResult> ExecuteAsync(NextPutRequest request)
    {
        return await CloseThisPopupAsync();
    }
}