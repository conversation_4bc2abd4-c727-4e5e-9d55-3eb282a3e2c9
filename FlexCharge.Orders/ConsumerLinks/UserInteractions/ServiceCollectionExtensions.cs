using System.Collections.Generic;
using System.Linq;
using System.Text;
using FlexCharge.Common.Dependencies;
using FlexCharge.Common.Shared.UIBuilder;
using FlexCharge.Orders.Services.UserInteractions;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.ConsumerLinks.UserInteractions;

public static class ServiceCollectionExtensions
{
    public delegate IUserInteraction UserInteractionResolver(string userInteractionName, bool throwExceptionIfNotFound);

    public static void AddUserInteractions(this IServiceCollection services)
    {
        services.AddUIBuilder();
        
        services.AddTransient<IUserInteractionService, UserInteractionService>();
        
        services.PopulateAndAddTransients<IUserInteraction>();
        services.AddTransient<UserInteractionResolver>(serviceProvider =>
            (key, throwExceptionIfNotFound) =>
            {
                var userInteractions = serviceProvider.GetServices<IUserInteraction>();

                var userInteraction = userInteractions.SingleOrDefault(x => x.UserInteractionId.ToUpper() == key.ToUpper());

#if DEBUG && CREATE_USER_INTERACTIONS_LIST
                StringBuilder userInteractionsList = new StringBuilder();
                foreach (var userInteraction in userInteractions)
                {
                    userInteractionsList.AppendLine(userInteraction.GetType().Name.Replace("_", " "));
                }
#endif

                if (userInteraction is null && throwExceptionIfNotFound)
                    throw new KeyNotFoundException($"User Interaction #{key} not found");

                return userInteraction;
            });
    }
}