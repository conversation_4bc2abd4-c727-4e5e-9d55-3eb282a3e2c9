{"app": {"name": "activity-service", "version": "0.0.1"}, "tablepart": {"TableSettings": [{"PartitionTableName": "AggregatedActivities", "NumberOfDailyPartitions": 95, "NumberOfDailyPartitionsToCreateAhead": 5, "ToMerge": false}], "Timeout": 20000}, "jwt": {"Provider": "Cognito", "secretKey": "JLBMU2VbJZmt42sUwByUpJJF6Y5mG2gPNU9sQFUpJFcGFJdyKxskR3bxh527kax2UcXHvB", "expiryMinutes": 30, "issuer": "identity-service", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "6jl6laoa1jtk2hgqdu79bu3m7d", "Region": "us-east-1", "UserPoolId": "us-east-1_7Snu9L5Rt", "AppClientId": "6jl6laoa1jtk2hgqdu79bu3m7d"}, "aggregatedActivitiesAthenaDB": {"databaseName": "sandbox_activity_archive_db", "outputBucket": "s3://sandbox-activity-archive-db-query-results/", "executionTimeout": 30000, "retryTimeInterval": 3000}, "cache": {"connectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379"}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/audit-{0}.txt"}, "dataStream": {"provider": "kinesis"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "email": {"provider": "sendgrid", "key": "*********************************************************************", "supportEmail": "<EMAIL>", "senderEmail": "<EMAIL>", "senderName": "FlexFactor"}, "swagger": {"enabled": true, "commentsEnabled": true, "reDocEnabled": false, "name": "v1", "title": "audit-service", "version": "v1", "routePrefix": "", "includeSecurity": true}, "jaeger": {"agentHost": "**************", "agentPort": 6831}, "internalReporting": {"emailTemplateId": "d-d3185eabd44c4469b2f804f55b0fddd2"}, "backgroundWorkerService": {"executionInterval": 500}, "AllowedHosts": "*"}