using System;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using EntityFramework.Exceptions.PostgreSQL;
using FlexCharge.Activity.Entities;
using FlexCharge.Common.PostgreSql.Interceptors;
using FlexCharge.Utils;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata;

namespace FlexCharge.Activity
{
    public class PostgreSQLDbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public PostgreSQLDbContext(DbContextOptions<PostgreSQLDbContext> options,
            IHttpContextAccessor httpContextAccessor)
            : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
            // NpgsqlConnection.GlobalTypeMapper
            //    .MapEnum<OrderStatusCategory>();
        }

        protected PostgreSQLDbContext(DbContextOptions options)
            : base(options)
        {
        }


        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.AddInterceptors(
                new SelectForUpdateCommandInterceptor()
            );

            optionsBuilder.UseExceptionProcessor();
        }


        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.Entity<Entities.Activity>().Property<bool>("IsDeleted");
            builder.Entity<Entities.Activity>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
        }


        public DbSet<Entities.Activity> AggregatedActivities { get; set; }

        public override int SaveChanges()
        {
            UpdateSoftDeleteStatuses();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UpdateSoftDeleteStatuses();
            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        private void UpdateSoftDeleteStatuses()
        {
            var user = _httpContextAccessor?.HttpContext?.User?.Claims.SingleOrDefault(x =>
                x.Type == ClaimTypes.NameIdentifier);

            foreach (var entry in ChangeTracker.Entries())
            {
                switch (entry.State)
                {
                    case EntityState.Modified:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                entry.Property("ModifiedBy").CurrentValue = user.Value;
                        }

                        if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                            entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();

                        entry.CurrentValues["IsDeleted"] = false;

                        break;
                    }
                    case EntityState.Added:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "CreatedBy"))
                                entry.Property("CreatedBy").CurrentValue = user.Value;

                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                entry.Property("ModifiedBy").CurrentValue = user.Value;
                        }

                        if (entry.Properties.Any(o => o.Metadata.Name == "CreatedOn"))
                            entry.Property("CreatedOn").CurrentValue = DateTime.Now.ToUniversalTime();

                        if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                            entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();

                        entry.CurrentValues["IsDeleted"] = false;

                        break;
                    }

                    case EntityState.Deleted:
                        entry.State = EntityState.Modified;
                        entry.CurrentValues["IsDeleted"] = true;

                        CascadeSoftDelete(entry);

                        break;
                }
            }
        }

        // /// <summary>
        // /// Cascades Soft Delete to Related Entities
        // /// </summary>
        // private void CascadeSoftDelete()
        // {
        //     //See: https://github.com/aspnetboilerplate/aspnetboilerplate/issues/3543
        //     
        //     foreach (var entry in ChangeTracker.Entries().ToList())
        //     {
        //         if (entry.State == EntityState.Deleted && entry.Entity is IEntity)
        //         {
        //             CascadeSoftDelete(entry);
        //         }
        //     }
        // }

        /// Cascades Soft Delete to Related Entities
        private void CascadeSoftDelete(EntityEntry entry)
        {
            var dependentToPrincipalNavigations =
                entry.Navigations.Where(x => !((IReadOnlyNavigation) x.Metadata).IsOnDependent).ToList();

            foreach (var navigationEntry in dependentToPrincipalNavigations)
            {
                //see: https://github.com/dotnet/efcore/issues/23283
                var foreignKey = ((IReadOnlyNavigation) navigationEntry.Metadata).ForeignKey;
                if (typeof(IEntity).IsAssignableFrom(foreignKey.DeclaringEntityType.ClrType))
                {
                    if (foreignKey.DeleteBehavior == DeleteBehavior.Cascade)
                    {
                        if (!navigationEntry.IsLoaded)
                        {
                            navigationEntry.Load();
                        }

                        if (navigationEntry.CurrentValue != null)
                        {
                            switch (navigationEntry)
                            {
                                case CollectionEntry collectionEntry:
                                    foreach (var x in collectionEntry.CurrentValue)
                                    {
                                        ((IEntity) Entry(x).Entity).IsDeleted = true;
                                        Entry(x).State = EntityState.Modified;

                                        //SetDeletionAuditProperties(entry.Entity, userId);
                                        //changeReport.ChangedEntities.Add(new EntityChangeEntry(entry.Entity, EntityChangeType.Deleted));
                                    }

                                    break;
                                case ReferenceEntry referenceEntry:
                                    ((IEntity) Entry(referenceEntry.CurrentValue).Entity).IsDeleted = true;
                                    Entry(referenceEntry.CurrentValue).State = EntityState.Modified;

                                    //SetDeletionAuditProperties(entry.Entity, userId);
                                    //changeReport.ChangedEntities.Add(new EntityChangeEntry(entry.Entity, EntityChangeType.Deleted));

                                    break;
                            }
                        }
                    }
                }
            }
        }
    }
}