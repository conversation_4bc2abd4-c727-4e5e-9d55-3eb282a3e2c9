#if DEBUG
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Activity.Services.AggregatedActivities;
using FlexCharge.Common.Activities;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Activity.Services.InternalReporting;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Logging;
using FlexCharge.Common.Logging.LogSuppression;
using FlexCharge.Common.Partitions;
using FlexCharge.Contracts.Activities;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using ActionResult = Microsoft.AspNetCore.Mvc.ActionResult;

//using Transaction = FlexCharge.Activity.Entities.Transaction;

namespace FlexCharge.Activity.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IPublishEndpoint _publisher;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly IInternalReportingService _internalReportingService;
        private readonly IMapper _mapper;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IEnumerable<ITablePartitionService> _activityPartitionServices;

        private readonly IEmailSender _emailSender;

        private IAggregatedActivitiesService _aggregatedActivitiesService;
        //private readonly AppOptions _globalData;
        //private readonly IAthenaService _biServices;


        public TestController(ILogger<TestController> logger,
            IPublishEndpoint publisher,
            PostgreSQLDbContext dbContext,
            IInternalReportingService internalReportingService,
            IMapper mapper,
            IServiceScopeFactory serviceScopeFactory,
            IEmailSender emailSender,
            IAggregatedActivitiesService aggregatedActivitiesService,
            IEnumerable<ITablePartitionService> activityPartitionServices
        )
        {
            _logger = logger;
            _publisher = publisher;
            _dbContext = dbContext;

            _internalReportingService = internalReportingService;
            _mapper = mapper;
            _serviceScopeFactory = serviceScopeFactory;
            _activityPartitionServices = activityPartitionServices;
            _aggregatedActivitiesService = aggregatedActivitiesService;

            // _biServices = biServices;
        }


        //private readonly AppOptions _globalData;
        //private readonly IAthenaService _biServices;


        [HttpGet("TestPartitions")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestPartitions(CancellationToken token)
        {
            foreach (var partServices in _activityPartitionServices)
            {
                await partServices.MaintainTablePartitionsAsync(token);
            }

            return Ok();
        }


        [HttpGet]
        [ProducesResponseType(200)]
        public async Task<ActionResult> Get()
        {
            if (!EnvironmentHelper.IsInStagingOrDevelopment) throw new NotSupportedException();

            // try
            // {
            //     //_logger.LogInformation(
            //     //  $"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");
            //
            //     var mid = new Guid("76e105a7-4d60-457b-82f2-648d46aba287");
            //     var orderId = Guid.NewGuid();
            //     var activityId = new Guid("033430d2-8549-4a90-b014-1f6a1fd17183");
            //     //await FireActivityCreatedEvent(mid, orderId, activityId);
            //     await FireActivityCreatedEvent(mid, orderId, activityId);
            //     await FireActivityCreatedEvent(mid, orderId, Guid.NewGuid());
            // }
            // catch (Exception ex)
            // {
            //     throw;
            // }

            return Ok();
        }

        async Task FireActivityCreatedEvent(Guid tenantId, Guid? correlationId = null, Guid? id = null)
        {
            using var workspan = Workspan.Start<TestController>();

            try
            {
                var activityDto = new ActivityDTO
                {
                    Id = id ?? Guid.NewGuid(),
                    Value = "Value",
                    Category = "Order",
                    //Id= Guid.NewGuid(),
                    ActionResult = "ar",
                    Data = "{}",
                    CorrelationId = correlationId ?? Guid.NewGuid(),
                    Domain = "gggg",
                    AccessLevel = "al",
                    ActionTimestamp = DateTime.UtcNow,
                    InformationLevel = 1,
                    Event = "ev",
                    SubCategory = "sg",
                    Name = "Order",
                    PreviousActionId = Guid.Empty,
                    Meta = "{}",
                    Source = "src",
                    Version = "version",
                    IsRoot = false,
                    TenantId = tenantId
                };


                await _publisher.Publish(new ActivityCreatedEvent
                {
                    Activity = activityDto
                });
            }
            catch (Exception e)
            {
                throw;
                //workspan.RecordException(e);
            }
        }


        [HttpPost("test-activity-storage-retry-queue")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestActivityStorageRetryQueuePost()
        {
            if (!EnvironmentHelper.IsInStagingOrDevelopment) throw new NotSupportedException();

            await _publisher.Publish(new ActivityStorageFailedRetryEvent
            {
                Activity = new ActivityDTO
                {
                    Value = "Value",
                    Category = "Order",
                    Id = Guid.NewGuid(),
                    ActionResult = "ar",
                    Data = "{}",
                    CorrelationId = Guid.NewGuid(),
                    Domain = "gggg",
                    AccessLevel = "al",
                    ActionTimestamp = DateTime.UtcNow,
                    InformationLevel = 1,
                    Event = "ev",
                    SubCategory = "sg",
                    Name = "Order",
                    PreviousActionId = Guid.NewGuid(),
                    Meta = "{}",
                    Source = "src",
                    Version = "version",
                    IsRoot = false,
                    TenantId = Guid.NewGuid()
                },
                Storage = ActivityStorageFailedRetryEvent.StorageType.Kinesis
            });

            return Ok();
        }

        // [HttpPost("test-select-for-update-skip-locked-db-interceptor")]
        // [ProducesResponseType(200)]
        // public async Task<ActionResult> TestSelectForUpdateSkipLockedCommandInterceptor()
        // {
        //     await SelectForUpdateCommandInterceptorUsageExample.TestQueueStyleLock(_serviceScopeFactory);
        //
        //     return Ok();
        // }
        //
        // [HttpPost("test-select-for-update-blocking-db-interceptor")]
        // [ProducesResponseType(200)]
        // public async Task<ActionResult> TestSelectForUpdateBlockingCommandInterceptor()
        // {
        //     await SelectForUpdateCommandInterceptorUsageExample.TestResourceLockStyleBlock(_serviceScopeFactory);
        //
        //     return Ok();
        // }

        [HttpPost("test-athena")]
        public async Task<IActionResult> TestAthena(
            [FromQuery] Guid correlationId,
            [FromQuery] string[]? category,
            [FromQuery] string? search,
            [FromQuery] ActivityInformationLevelFlags[]? infolevel,
            [FromQuery] DateTime from,
            [FromQuery] DateTime to,
            CancellationToken token = default)
        {
            using var workspan = Workspan.Start<TestDataController>();

            const int MAX_QUERY_DAYS = 90;

            try
            {
                if (from == default || to == default)
                    return BadRequest("From and To dates are required");

                //Ensure that range is no more than MAX_QUERY_DAYS
                //if (to.ToDateTime(new TimeOnly(23, 59)) - from.ToDateTime(new TimeOnly(0, 0)) > TimeSpan.FromDays(MAX_QUERY_DAYS))
                if (to - from > TimeSpan.FromDays(MAX_QUERY_DAYS))
                {
                    return BadRequest($"Date range should not be more than {MAX_QUERY_DAYS} days");
                }

                var request = new AggregatedActivityQuery
                {
                    CorrelationId = correlationId,
                    FromDate = new DateOnly(from.Year, from.Month, from.Day),
                    ToDate = new DateOnly(to.Year, to.Month, to.Day),
                    InformationLevelFilter = infolevel,
                    ActivityCategoriesFilter = category,
                    SearchFilter = search
                };

                return Ok(await _aggregatedActivitiesService.GetAthenaActivitiesAsync(request, token));
            }
            catch (Exception e)
            {
                return BadRequest(e);
            }
        }
    }
}

#endif