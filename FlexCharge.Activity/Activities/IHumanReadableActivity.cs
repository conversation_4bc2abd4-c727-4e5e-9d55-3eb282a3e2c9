using System;


namespace FlexCharge.Activity.Activities
{
    public interface IHumanReadableActivity
    {
        DateTime CreatedOn { get; set; }
        string Name { get; set; }
        string Data { get; set; }
        string Meta { get; set; }
        int InformationLevel { get; set; }
    }


    public class HumanReadableActivity : IHumanReadableActivity
    {
        public DateTime CreatedOn { get; set; }
        public string Name { get; set; }
        public string Data { get; set; }
        public string Meta { get; set; }
        public int InformationLevel { get; set; }
    }
}