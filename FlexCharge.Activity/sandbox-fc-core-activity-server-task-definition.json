{"containerDefinitions": [{"name": "core-activity", "image": "556663010871.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-activity:8f31fb9993e42e8ef513fd02312a27dc8cd8a701", "cpu": 0, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "activity_service_sandbox"}, {"name": "DB_DATABASE", "value": "fc_activity"}, {"name": "DB_HOST", "value": "flexcharge-sandbox.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Sandbox"}, {"name": "AWS_COGNITO_USER_POOL_ID", "value": "us-east-1_7Snu9L5Rt"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}, {"name": "NEW_RELIC_APP_NAME", "value": "Activity-sandbox"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_DB_ACTIVITY_PASSWORD-ZwyItu"}, {"name": "AWS_IAM_COGNITO_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_IAM_COGNITO_KEY-SVc81A"}, {"name": "AWS_IAM_COGNITO_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_IAM_COGNITO_SECRET-7KOA9j"}, {"name": "AWS_IAM_COGNITO_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_COGNITO_CLIENT_ID-flykbv"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_SNS_IAM_ACCESS_KEY-vt5dWo"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_SNS_IAM_SECRET_KEY-iDGfTK"}, {"name": "API_CLIENT_JWT_SIGNING_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX-API-CLIENT-JWT-SIGNING-KEY-HlgILt"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-activity-server-sandbox", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-activity-server-sandbox", "taskRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Sandbox-Role", "executionRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Sandbox-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024"}