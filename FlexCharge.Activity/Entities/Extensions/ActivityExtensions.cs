using System.Linq;
using System.Text;
using FlexCharge.Contracts.Activities;
using FlexCharge.Utils;

namespace FlexCharge.Activity.Entities;

public static class ActivityExtensions
{
    static readonly string[] ErrorWords = {"failed", "error", "rejected", "decline", "notpassed", "noteligible"};

    public static int GetHumanReadableInformationLevel(this Activity activity)
    {
        string activityNameLower = activity.Name.ToLower();


        if (ErrorWords.Any(x => activityNameLower.Contains(x)))
        {
            return (int) ActivityInformationLevelFlags.Error;
        }
        else if ((activity.InformationLevel & (int) ActivityInformationLevelFlags.Error) != 0)
        {
            return (int) ActivityInformationLevelFlags.Error;
        }
        else return activity.InformationLevel;
    }

    public static string GetHumanReadableDescription(this Activity activity)
    {
        StringBuilder description = new();

        string subcategory = !string.IsNullOrEmpty(activity.SubCategory) ? activity.SubCategory : "None";

        description.AppendLine($"Timestamp: {activity.ActionTimestamp.ToString("O")}");
        description.AppendLine($"Subcategory: {subcategory}");
        description.AppendLine();
        description.AppendLine("Data:");


        if (!string.IsNullOrEmpty(activity.Data))
        {
            var json = RemoveTypeInformationFromJson(activity.Data);
            json = Formatters.JsonFormatAsIndentedString(json);
            json = RemoveValueInformationFromJson(json);

            description.AppendLine( //"\"data\" : {\n" + 
                json
                    .Replace("\\\"", "\"")
                //+ "\n},\n" +
                //"\"meta\" : {\n" + Formatters.JsonFormatAsIndentedString(src.Meta).Replace("\\\"", "\"") +
                //"\n}"
            );
        }

        var descriptionString = description.ToString();
        description.Clear();

        return descriptionString;
    }

    /// <summary>
    /// Strips "$type:" : "..." information from json
    /// </summary>
    /// <param name="jsonText"></param>
    /// <returns></returns>
    private static string RemoveTypeInformationFromJson(string jsonText)
    {
        const string typeInfoStartText = "\"$type\": \"";
        string json = jsonText;
        int indexOfTypeInfoStart = -1;
        while ((indexOfTypeInfoStart = json.IndexOf(typeInfoStartText)) >= 0)
        {
            int indexOfTypeInfoEnd = json.IndexOf("\"", indexOfTypeInfoStart + typeInfoStartText.Length);
            if (indexOfTypeInfoEnd > 0)
            {
                var jsonStartPart = json.Substring(0, indexOfTypeInfoStart);

                string jsonEndPart = json.Substring(indexOfTypeInfoEnd + 1);
                if (jsonEndPart.StartsWith(",")) jsonEndPart = jsonEndPart.Substring(1);
                if (jsonEndPart.StartsWith("\n")) jsonEndPart = jsonEndPart.Substring(1);

                json = jsonStartPart + jsonEndPart;
            }
            else break;
        }

        return json;
    }

    /// <summary>
    /// Strips "$values": from json
    /// </summary>
    /// <param name="jsonText"></param>
    /// <returns></returns>
    private static string RemoveValueInformationFromJson(string jsonText)
    {
        const string valuesText = "\"$values\": [";

        return jsonText.Replace(valuesText, "[");
    }
}