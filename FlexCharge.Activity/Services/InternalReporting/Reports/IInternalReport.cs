using System;
using System.Linq;
using System.Threading.Tasks;

namespace FlexCharge.Activity.Services.InternalReporting.Reports;

public interface IInternalReport
{
    Guid? ReportId { get; }
    string? ReportName { get; }

    Task<string> GenerateAsync(ReportGenerationStatistics reportGenerationStatistics, IQueryable<FlexCharge.Activity.Entities.Activity> activities, DateTime startDate, DateTime endDate, Guid? mid = null,
        int? mcc = null);
}