using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using CsvHelper.Configuration;
using CsvHelper.Configuration.Attributes;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils;

namespace FlexCharge.Activity.Services.InternalReporting.Reports;

abstract class InternalReportBase : IInternalReport
{
    #region Report Id and Name

    [Ignore]
    public Guid? ReportId
    {
        get
        {
            var reportAttribute = this.GetType().GetCustomAttribute(typeof(ReportAttribute)) as ReportAttribute;
            return reportAttribute?.ReportId;
        }
    }

    [Ignore]
    public string? ReportName
    {
        get
        {
            var reportAttribute = this.GetType().GetCustomAttribute(typeof(ReportAttribute)) as ReportAttribute;
            return reportAttribute?.Name;
        }
    }

    #endregion

    #region Calculation and Formatting Helpers

    private static CultureInfo _invariantCulture = CultureInfo.InvariantCulture;

    protected static string CalculatePercent(float fraction, float total)
    {
        if (fraction >= 0 && total > 0)
        {
            return (fraction / total * 100).ToString("0.##", _invariantCulture) + "%";
        }
        else return null;
    }

    protected static string CalculatePercentOrZero(decimal fraction, decimal total)
    {
        if (fraction >= 0 && total > 0)
        {
            return (fraction / total * 100).ToString("0.##", _invariantCulture) + "%";
        }
        else return "0%";
    }

    protected static DateOnly GetDateOnly(DateTime dateTime)
    {
        var universalDateTime = dateTime.ToUniversalTime();
        return new DateOnly(universalDateTime.Year, universalDateTime.Month, universalDateTime.Day);
    }

    protected static string FormatDate(DateTime date)
    {
        return date.Date.ToString("yyyy/MM/dd");
    }

    #endregion

    #region Report Generation

    public async Task<string> GenerateAsync(ReportGenerationStatistics reportGenerationStatistics,
        IQueryable<FlexCharge.Activity.Entities.Activity> activities, DateTime startDate, DateTime endDate,
        Guid? mid = null, int? mcc = null)
    {
        using (var workspan = Workspan.Start<InternalReportBase>())
        {
            return await GenerateReportAsync(workspan, reportGenerationStatistics, activities, startDate, endDate, mid,
                mcc);
        }
    }

    protected static string GenerateCSVFromRows<TRow>(IList<TRow> report)
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
        };

        String result;
        using (MemoryStream mem = new MemoryStream())
        {
            using (StreamWriter sw = new StreamWriter(mem))
            {
                CSVHelper.Create(report, sw, config);
            }

            var stringBytes = mem.ToArray();
            result = Encoding.UTF8.GetString(stringBytes, 0, (int) stringBytes.Length);
        }

        return result;
    }

    #endregion

    public abstract Task<string> GenerateReportAsync(Workspan workspan,
        ReportGenerationStatistics reportGenerationStatistics,
        IQueryable<FlexCharge.Activity.Entities.Activity> activities, DateTime startDate,
        DateTime endDate,
        Guid? mid = null, int? mcc = null);
}