// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Text;
// using System.Threading.Tasks;
// using CsvHelper.Configuration.Attributes;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Orders.Activities;
// using FlexCharge.Orders.Entities;
// using FlexCharge.Utils;
// using Microsoft.EntityFrameworkCore;
//
// namespace FlexCharge.Activity.Services.InternalReporting.Reports.Implementations;
//
// [Report("Vintage Report", "A4750856-A7C7-4FAF-B0F7-A637A883AFD2")]
// class VintageReport : InternalReportBase
// {
//     [Name("Day")] public string Date { get; set; }
//     [Name("# Total Orders Processed")] public int TotalOrdersProcessed { get; set; }
//
//     [Ignore] public decimal TotalOrdersProcessedValue { get; set; }
//
//     [Name("Value Total Orders Processed")]
//     public string TotalOrdersProcessedValueString => TotalOrdersProcessedValue.ToString("0.##");
//
//     [Ignore] public decimal RepaidOrdersValue { get; set; }
//     [Name("Value Repaid orders")] public string RepaidOrdersValueString => RepaidOrdersValue.ToString("0.##");
//
//     [Name("% Repaid orders")] public string RepaidOrdersPercent { get; set; }
//
//     [Ignore] public decimal RepaidAt0DayValue { get; set; }
//     [Name("% At 0 Day")] public string RepaidAt0DayPercent { get; set; }
//
//
//     [Ignore] public decimal RepaidAt1DayValue { get; set; }
//     [Name("% At 1 Day")] public string RepaidAt1DayPercent { get; set; }
//
//     [Ignore] public decimal RepaidAt3DayValue { get; set; }
//     [Name("% At 3 Day")] public string RepaidAt3DayPercent { get; set; }
//
//     [Ignore] public decimal RepaidAt5DayValue { get; set; }
//     [Name("% At 5 Day")] public string RepaidAt5DayPercent { get; set; }
//
//     [Ignore] public decimal RepaidAt10DayValue { get; set; }
//     [Name("% At 10 Day")] public string RepaidAt10DayPercent { get; set; }
//
//     [Ignore] public decimal RepaidAt15DayValue { get; set; }
//     [Name("% At 15 Day")] public string RepaidAt15DayPercent { get; set; }
//
//     [Ignore] public decimal RepaidAt20DayValue { get; set; }
//     [Name("% At 20 Day")] public string RepaidAt20DayPercent { get; set; }
//
//     [Ignore] public decimal RepaidAt25DayValue { get; set; }
//     [Name("% At 25 Day")] public string RepaidAt25DayPercent { get; set; }
//
//     [Ignore] public decimal RepaidAt30DayValue { get; set; }
//     [Name("% At 30 Day")] public string RepaidAt30DayPercent { get; set; }
//
//     [Ignore] public decimal RepaidAt45DayValue { get; set; }
//     [Name("% At 45 Day")] public string RepaidAt45DayPercent { get; set; }
//
//     [Ignore] public decimal RepaidAt60DayValue { get; set; }
//     [Name("% At 60 Day")] public string RepaidAt60DayPercent { get; set; }
//
//     #region Report Generation
//
//     public override async Task<string> GenerateReportAsync(Workspan workspan,
//         ReportGenerationStatistics reportGenerationStatistics, IQueryable<Entities.Activity> activities, DateTime startDate,
//         DateTime endDate,
//         Guid? mid = null,
//         int? mcc = null)
//     {
//         int daysBack = (int)Math.Ceiling((endDate - startDate).TotalDays);
//         
//         var vintageReport = new List<VintageReport>();
//
//         DateTime reportEndTime = endDate.ToUniversalTime().Date;
//         DateTime reportStartTime = endDate.ToUniversalTime().AddDays(-daysBack);
//
//         var activitiesToSelect = activities
//             .FilterByMid(mid)
//             .Where(x => x.ActionTimestamp >= reportStartTime);
//
//
//         var successfulPaymentCaptures = await activitiesToSelect
//             .Where(x => x.Name == ActivityNames.PayIn_Payment_Capture_Succeeded)
//             .ToListAsync();
//
//
//         //int totalOrdersProcessed = 0;
//         //double totalOrdersProcessedValue = 0;
//         DateTime day = reportEndTime.ToUniversalTime().Date;
//         do
//         {
//
//             try
//             {
//                 DateTime dayToSelect = day;
//
//                 var activitiesInThatDay = activities
//                     .Where(x => x.ActionTimestamp.Date == dayToSelect.Date);
//
//                 var processedOffers =
//                     await activitiesInThatDay.Where(x => x.Name == ActivityNames.Offer_Created).ToListAsync();
//
//                 //totalOrdersProcessed += processedOffers.Count;
//                 //totalOrdersProcessedValue +=
//                 //    processedOffers.Sum(x => new PayloadMetadata(x.Meta, true).AsIntegerOrDefault("Amount")) / 100f;
//
//                 var vintageReportRow = new VintageReport()
//                 {
//                     Date = FormatDate(dayToSelect),
//                     TotalOrdersProcessed = processedOffers.Count(),
//                     TotalOrdersProcessedValue =
//                         processedOffers.Sum(x => Formatters.LongToDecimal(new FlexCharge.Common.Activities.PayloadMetadata(x.Meta, true).AsLong("Amount"))),
//                     // RepaidOrdersValue = activitiesInDay.Where(x => x.Name == "Capture")
//                     //     .Sum(x => new PayloadMetadata(x.Meta).GetValueAsInteger("Amount")),
//                 };
//
//                 foreach (var orderActivity in processedOffers)
//                 {
//                     var orderCorrelationId = orderActivity.CorrelationId;
//                     var orderDate = orderActivity.ActionTimestamp.Date;
//
//                     var successfulCapturesRelatedToOrder =
//                         successfulPaymentCaptures.Where(x => x.CorrelationId == orderCorrelationId);
//
//
//                     var successfulCaptures = successfulCapturesRelatedToOrder.Where(x =>
//                         x.Name == ActivityNames.PayIn_Payment_Capture_Succeeded).ToList();
//
//                     if (HasNDaysPassedSinceOrder(reportEndTime, orderDate, 0))
//                     {
//                         vintageReportRow.RepaidAt0DayValue +=
//                             successfulCaptures.FilterByRepaymentRange(day, 0).CalculateTotalAmount();
//                     }
//
//                     if (HasNDaysPassedSinceOrder(reportEndTime, orderDate, 1))
//                     {
//                         vintageReportRow.RepaidAt1DayValue +=
//                             successfulCaptures.FilterByRepaymentRange(day, 1).CalculateTotalAmount();
//                     }
//
//                     if (HasNDaysPassedSinceOrder(reportEndTime, orderDate, 3))
//                     {
//                         vintageReportRow.RepaidAt3DayValue +=
//                             successfulCaptures.FilterByRepaymentRange(day, 3).CalculateTotalAmount();
//                     }
//
//                     if (HasNDaysPassedSinceOrder(reportEndTime, orderDate, 5))
//                     {
//                         vintageReportRow.RepaidAt5DayValue +=
//                             successfulCaptures.FilterByRepaymentRange(day, 5).CalculateTotalAmount();
//                     }
//
//                     if (HasNDaysPassedSinceOrder(reportEndTime, orderDate, 10))
//                     {
//                         vintageReportRow.RepaidAt10DayValue +=
//                             successfulCaptures.FilterByRepaymentRange(day, 10).CalculateTotalAmount();
//                     }
//
//                     if (HasNDaysPassedSinceOrder(reportEndTime, orderDate, 15))
//                     {
//                         vintageReportRow.RepaidAt15DayValue +=
//                             successfulCaptures.FilterByRepaymentRange(day, 15).CalculateTotalAmount();
//                     }
//
//                     if (HasNDaysPassedSinceOrder(reportEndTime, orderDate, 20))
//                     {
//                         vintageReportRow.RepaidAt20DayValue +=
//                             successfulCaptures.FilterByRepaymentRange(day, 20).CalculateTotalAmount();
//                     }
//
//                     if (HasNDaysPassedSinceOrder(reportEndTime, orderDate, 25))
//                     {
//                         vintageReportRow.RepaidAt25DayValue +=
//                             successfulCaptures.FilterByRepaymentRange(day, 25).CalculateTotalAmount();
//                     }
//
//                     if (HasNDaysPassedSinceOrder(reportEndTime, orderDate, 30))
//                     {
//                         vintageReportRow.RepaidAt30DayValue +=
//                             successfulCaptures.FilterByRepaymentRange(day, 30).CalculateTotalAmount();
//                     }
//
//                     if (HasNDaysPassedSinceOrder(reportEndTime, orderDate, 45))
//                     {
//                         vintageReportRow.RepaidAt45DayValue +=
//                             successfulCaptures.FilterByRepaymentRange(day, 45).CalculateTotalAmount();
//                     }
//
//                     if (HasNDaysPassedSinceOrder(reportEndTime, orderDate, 60))
//                     {
//                         vintageReportRow.RepaidAt60DayValue +=
//                             successfulCaptures.FilterByRepaymentRange(day, 60).CalculateTotalAmount();
//                     }
//
//                     vintageReportRow.RepaidOrdersValue += successfulCaptures.CalculateTotalAmount();
//                 }
//
//                 vintageReportRow.RepaidAt0DayPercent = CalculatePercentageIfHasNDaysPassedSinceOrder(reportEndTime, day,
//                     vintageReportRow.RepaidAt0DayValue, vintageReportRow.TotalOrdersProcessedValue, 0);
//
//                 vintageReportRow.RepaidAt1DayPercent = CalculatePercentageIfHasNDaysPassedSinceOrder(reportEndTime, day,
//                     vintageReportRow.RepaidAt1DayValue, vintageReportRow.TotalOrdersProcessedValue, 1);
//                 vintageReportRow.RepaidAt3DayPercent = CalculatePercentageIfHasNDaysPassedSinceOrder(reportEndTime, day,
//                     vintageReportRow.RepaidAt3DayValue, vintageReportRow.TotalOrdersProcessedValue, 3);
//                 vintageReportRow.RepaidAt5DayPercent = CalculatePercentageIfHasNDaysPassedSinceOrder(reportEndTime, day,
//                     vintageReportRow.RepaidAt5DayValue, vintageReportRow.TotalOrdersProcessedValue, 5);
//                 vintageReportRow.RepaidAt10DayPercent = CalculatePercentageIfHasNDaysPassedSinceOrder(reportEndTime, day,
//                     vintageReportRow.RepaidAt10DayValue, vintageReportRow.TotalOrdersProcessedValue, 10);
//                 vintageReportRow.RepaidAt15DayPercent = CalculatePercentageIfHasNDaysPassedSinceOrder(reportEndTime, day,
//                     vintageReportRow.RepaidAt15DayValue, vintageReportRow.TotalOrdersProcessedValue, 15);
//                 vintageReportRow.RepaidAt20DayPercent = CalculatePercentageIfHasNDaysPassedSinceOrder(reportEndTime, day,
//                     vintageReportRow.RepaidAt20DayValue, vintageReportRow.TotalOrdersProcessedValue, 20);
//                 vintageReportRow.RepaidAt25DayPercent = CalculatePercentageIfHasNDaysPassedSinceOrder(reportEndTime, day,
//                     vintageReportRow.RepaidAt25DayValue, vintageReportRow.TotalOrdersProcessedValue, 25);
//                 vintageReportRow.RepaidAt30DayPercent = CalculatePercentageIfHasNDaysPassedSinceOrder(reportEndTime, day,
//                     vintageReportRow.RepaidAt30DayValue, vintageReportRow.TotalOrdersProcessedValue, 30);
//                 vintageReportRow.RepaidAt45DayPercent = CalculatePercentageIfHasNDaysPassedSinceOrder(reportEndTime, day,
//                     vintageReportRow.RepaidAt45DayValue, vintageReportRow.TotalOrdersProcessedValue, 45);
//                 vintageReportRow.RepaidAt60DayPercent = CalculatePercentageIfHasNDaysPassedSinceOrder(reportEndTime, day,
//                     vintageReportRow.RepaidAt60DayValue, vintageReportRow.TotalOrdersProcessedValue, 60);
//
//
//                 vintageReportRow.RepaidOrdersPercent = CalculatePercentOrZero(vintageReportRow.RepaidOrdersValue,
//                     vintageReportRow.TotalOrdersProcessedValue);
//
//                 vintageReport.Add(vintageReportRow);
//
//                 day = day.AddDays(-1);
//                 reportGenerationStatistics.RowsActuallyGenerated++;
//             }
//             catch (Exception e)
//             {
//                 workspan.RecordException(e, "Cannot create report row");
//             }
//
//             reportGenerationStatistics.TotalRowsRequested++;
//         } while (day >= reportStartTime);
//
//
//         return GenerateCSVFromRows(vintageReport);
//     }
//
//     #endregion
//
//     #region Helper Methods
//
//     private string CalculatePercentageIfHasNDaysPassedSinceOrder(DateTime utcNow, DateTime orderUtcDate,
//         decimal fraction,
//         decimal total, int repaymentAfterDay)
//     {
//         if (HasNDaysPassedSinceOrder(utcNow, orderUtcDate, repaymentAfterDay))
//         {
//             return CalculatePercentOrZero(fraction, total);
//         }
//         else return null;
//     }
//
//
//     private bool HasNDaysPassedSinceOrder(DateTime utcNow, DateTime orderDateUtc, int days)
//     {
//         return orderDateUtc.AddDays(days).Date <= utcNow.Date;
//     }
//
//     #endregion
// }