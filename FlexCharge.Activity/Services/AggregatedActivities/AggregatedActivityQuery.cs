using System;
using FlexCharge.Contracts.Activities;
using FluentValidation;

namespace FlexCharge.Activity.Services.AggregatedActivities;

public class AggregatedActivityQuery
{
    public Guid CorrelationId { get; set; }
    public DateOnly FromDate { get; set; }
    public DateOnly ToDate { get; set; }
    public ActivityInformationLevelFlags[]? InformationLevelFilter { get; set; }
    public string[] ActivityCategoriesFilter { get; set; }
    public string? SearchFilter { get; set; }

    public class Validator : AbstractValidator<AggregatedActivityQuery>
    {
        public Validator()
        {
            RuleFor(x => x.FromDate).LessThanOrEqualTo(x => x.ToDate);
        }
    }
}