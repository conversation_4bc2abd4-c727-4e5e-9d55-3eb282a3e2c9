using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Contracts.Activities;
using FlexCharge.Activity.Activities;

namespace FlexCharge.Activity.Services.AggregatedActivities;

public interface IAggregatedActivitiesService
{
    Task StoreActivitiesToDatabaseAsync(List<IActivity> activitiesToSave);

    Task StoreSingleActivityToDatabaseAsync(IActivity activitiesToSave);

    Task<(IEnumerable<IHumanReadableActivity> Activity, bool ActivitiesCouldBeArchived)> GetActivitiesAsync(
        Guid? id,
        ActivityInformationLevelFlags[]? informationLevelFilter,
        string[] activityCategoriesFilter,
        string? searchFilter);

    Task<IEnumerable<IHumanReadableActivity>> GetAthenaActivitiesAsync(
        AggregatedActivityQuery request,
        CancellationToken cancellationToken);


    Task SendToKinesisAsync(List<IActivity> activitiesToSave);
}