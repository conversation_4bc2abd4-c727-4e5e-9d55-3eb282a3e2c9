using FlexCharge.Common.Partitions;
using Microsoft.Extensions.Options;

namespace FlexCharge.Activity.Services.Partitions;

public class ActivityTablePartitionService : TablePartitionServiceBase<PostgreSQLDbContext>
{
    public ActivityTablePartitionService(PostgreSQLDbContext context, IOptions<TablePartitionOptions> partitionOptions)
        : base(context, partitionOptions)
    {
    }

    protected override string PartitionedTableName =>
        "AggregatedActivities"; // nameof(PostgreSQLDbContext.AggregatedActivities);

    protected override string PartitionedColumnName => "ActionTimestamp";
}