#################################
##### SET SERVICE NAME HERE #####
#################################
$microServiceName = "onboarding"
$dbName = "Onboarding" # capital letter 
$dbcon = "" #Example "**************"
$dbuser = "" 
$dbpass = "" 
$logName = "" 

# Get file path
$filePath = ".\appsettings.json"
$filePathDevelopemnt = ".\appsettings.Development.json"
$filePathStaging = ".\appsettings.Staging.json"
$filePathProduction = ".\appsettings.Production.json"

Write-Host '$microServiceName is' $microServiceName 

$hashTable = @{
    'template_microservice' = $microServiceName
    '{{DBNAME}}' = $dbName
    '{{DBCON}}' = $dbcon
    '{{DBUSER}}' = $dbuser
    '{{DBPASS}}' = $dbpass
    '{{LOGNAME}}' = $logName
}

foreach ($key in $hashTable.Keys) {
    $sourceFile = Get-Content $filePath
    $sourceFile -replace $key, $hashTable[$key] | Set-Content $filePath
    Write-Host 'Replaced key' $key 'with value' $hashTable[$key] 'in' $filePath

    $sourceFile = Get-Content $filePathDevelopemnt
    $sourceFile -replace $key, $hashTable[$key] | Set-Content $filePathDevelopemnt
    Write-Host 'Replaced key' $key 'with value' $hashTable[$key] 'in' $filePathDevelopemnt

    $sourceFile = Get-Content $filePathStaging
    $sourceFile -replace $key, $hashTable[$key] | Set-Content $filePathStaging
    Write-Host 'Replaced key' $key 'with value' $hashTable[$key] 'in' $filePathStaging

    $sourceFile = Get-Content $filePathProduction
    $sourceFile -replace $key, $hashTable[$key] | Set-Content $filePathProduction
    Write-Host 'Replaced key' $key 'with value' $hashTable[$key] 'in' $filePathProduction
}