using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Tracking.Services.MerchantsInformationCacheService;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Tracking.Consumers;

public class MerchantSiteCreatedEventConsumer : IConsumer<MerchantSiteCreatedEvent>
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IMerchantInformationCacheService _merchantInformationCacheService;


    public MerchantSiteCreatedEventConsumer(PostgreSQLDbContext dbContext,
        IMerchantInformationCacheService merchantInformationCacheService)
    {
        _dbContext = dbContext;
        _merchantInformationCacheService = merchantInformationCacheService;
    }

    public async Task Consume(ConsumeContext<MerchantSiteCreatedEvent> context)
    {
        using var workspan = Workspan.Start<MerchantSiteCreatedEventConsumer>();
        try
        {
            workspan.Log.Information("ENTERED: Tracking > MerchantSiteCreatedEventConsumer > {SerializeObject}",
                JsonConvert.SerializeObject(context));

            var message = context.Message;
            _dbContext.Configurations.Add(new Entities.Configuration
            {
                MerchantId = message.Mid,
                SiteId = message.SiteId,
                ScrapingData = await CreateScrapingDataAsync(message.Mid),
                SubmitData = await CreateSubmitDataAsync(message.Mid),
                CheckoutUrl = await CreateCheckoutUrlData(message.Mid),
                SuccessUrl = "",
                RejectUrl = "",
                CancelUrl = "",
                ConsentFlowConfiguration = 1,
                Logo = null,
                PrimaryColor = "#000000",
                SecondaryColor = "#000000",
                TokenizationPublicKey = null,
                FontFamily = null,
                IsLogoVisible = !string.IsNullOrWhiteSpace(""),
                ButtonsCornersRadius = "0",
                ModalCornersRadius = "0"
            });

            await _dbContext.SaveChangesAsync();

            await _merchantInformationCacheService.InvalidateMerchantSiteConfigurationCacheAsync(message.Mid,
                message.SiteId);

            workspan.Log.Information("EXIT: Tracking > ApplicationConvertedEventConsumer > {SerializeObject}",
                JsonConvert.SerializeObject(context));
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: Merchants > ApplicationConvertedEventConsumer > {SerializeObject}",
                JsonConvert.SerializeObject(context));
            await context.Publish(new MerchantConfigurationCreateFailedEvent
            {
                MerchantId = context.Message.Mid,
                SiteId = context.Message.SiteId,
                Message = e.Message
            });
        }
    }

    private async Task<JsonDocument> CreateCheckoutUrlData(Guid mid)
    {
        return await ParseAndCheckJsonDocumentAsync<List<string>>("[]");
        // string jsonData = await ReadConfigurationFromResourceAsync("DefaultCheckoutUrlData.json");
        // return await ParseAndCheckJsonDocumentAsync<List<string>>(jsonData);
    }


    private async Task<JsonDocument> CreateScrapingDataAsync(Guid mid)
    {
        return await ParseAndCheckJsonDocumentAsync<List<string>>("[]");
        // string jsonData = await ReadConfigurationFromResourceAsync("DefaultScrapingData.json");
        // return await ParseAndCheckJsonDocumentAsync<List<ScrapingField>>(jsonData);
    }

    private async Task<JsonDocument> CreateSubmitDataAsync(Guid mid)
    {
        return await ParseAndCheckJsonDocumentAsync<List<string>>("[]");
        // string jsonData = await ReadConfigurationFromResourceAsync("DefaultSubmitData.json");
        // return await ParseAndCheckJsonDocumentAsync<List<SubmitData>>(jsonData);
    }

    private async Task<JsonDocument> ParseAndCheckJsonDocumentAsync<T>(string json)
    {
        T converted = JsonConvert.DeserializeObject<T>(json);

        using (MemoryStream scrapingDataStream = new MemoryStream(Encoding.UTF8.GetBytes(json)))
        {
            return await JsonDocument.ParseAsync(scrapingDataStream);
        }
    }

    private async Task<string> ReadConfigurationFromResourceAsync(string resourcePath)
    {
        var assembly = typeof(MerchantSiteCreatedEventConsumer).Assembly;
        using (Stream resourceStream =
               assembly.GetManifestResourceStream(
                   $"{nameof(FlexCharge)}.{nameof(FlexCharge.Tracking)}.Configuration.{resourcePath}"))
        {
            using (StreamReader sr = new StreamReader(resourceStream))
            {
                return await sr.ReadToEndAsync();
            }
        }
    }
}