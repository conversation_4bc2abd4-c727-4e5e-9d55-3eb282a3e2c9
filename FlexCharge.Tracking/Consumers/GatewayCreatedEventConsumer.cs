// using System;
// using System.Threading.Tasks;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Contracts;
// using FlexCharge.Tracking.DistributedCache;
// using MassTransit;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Caching.Distributed;
//
// namespace FlexCharge.Tracking.Consumers;
//
// public class GatewayCreatedEventConsumer : IConsumer<GatewayCreatedEvent>
// {
//     private readonly PostgreSQLDbContext _dbContext;
//     private readonly IDistributedCache _distributedCache;
//
//
//     public GatewayCreatedEventConsumer(PostgreSQLDbContext dbContext, IDistributedCache distributedCache)
//     {
//         _dbContext = dbContext;
//         _distributedCache = distributedCache;
//     }
//
//     public async Task Consume(ConsumeContext<GatewayCreatedEvent> context)
//     {
//         using var workspan = Workspan.Start<GatewayCreatedEventConsumer>()
//             .Context(context);
//
//         try
//         {
//             #region Invalidating configuration in distributed cache
//
//             var merchantConfigurationKey = CacheKeyFactory.CreateMerchantConfigurationKey(context.Message.MerchantId);
//             await _distributedCache.RemoveAsync(merchantConfigurationKey.Key);
//
//             #endregion
//
//             var merchantConfiguration =
//                 await _dbContext.Configurations.SingleAsync(x => x.MerchantId == context.Message.MerchantId);
//             merchantConfiguration.TokenizationPublicKey = context.Message.TokenizationPublicKey;
//             await _dbContext.SaveChangesAsync();
//         }
//         catch (Exception e)
//         {
//             workspan.RecordException(e);
//         }
//     }
// }