<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
		<UserSecretsId>99bbd081-7f55-4d77-9823-a712a2c8e7e0</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<!--<DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>-->
		<Configurations>Debug;Release;Staging</Configurations>
		<WarningsAsErrors>CS4014</WarningsAsErrors>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>bin\$(Configuration)\$(AssemblyName).xml</DocumentationFile>
		<NoWarn>1701;1702;1591;</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="DeviceDetector.NET" Version="6.1.4" />
		<PackageReference Include="MediatR" Version="8.0.1" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.2" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.8" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\FlexCharge.Common\FlexCharge.Common.csproj" />
		<ProjectReference Include="..\FlexCharge.Contracts\FlexCharge.Contracts.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="Configuration\DefaultCheckoutUrlData.json" />
	  <EmbeddedResource Include="Configuration\DefaultScrapingData.json" />
	  <EmbeddedResource Include="Configuration\DefaultSubmitData.json" />
	</ItemGroup>

    <ItemGroup>
        <Folder Include="CloudStorage\"/>
    </ItemGroup>

    <!--	<Choose>-->
<!--		<When Condition=" '$(Configuration)'=='Staging' ">-->
<!--			<ItemGroup>-->
<!--				<Content Remove="appsettings.Development.json" />-->

<!--				&lt;!&ndash; Other files you want to update in the scope of Debug &ndash;&gt;-->
<!--				<None Update="other_files">-->
<!--					<CopyToOutputDirectory>Never</CopyToOutputDirectory>-->
<!--				</None>-->
<!--			</ItemGroup>-->
<!--		</When>-->
<!--		<When Condition=" '$(Configuration)'=='Development' ">-->
<!--			<ItemGroup>-->
<!--				<Content Remove="appsettings.Staging.json" />-->

<!--				&lt;!&ndash; Other files you want to update in the scope of Debug &ndash;&gt;-->
<!--				<None Update="other_files">-->
<!--					<CopyToOutputDirectory>Never</CopyToOutputDirectory>-->
<!--				</None>-->
<!--			</ItemGroup>-->
<!--		</When>-->
<!--	</Choose>-->

</Project>
