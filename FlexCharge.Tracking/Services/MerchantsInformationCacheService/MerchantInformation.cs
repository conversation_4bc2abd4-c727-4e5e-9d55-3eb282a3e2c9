using System;

namespace FlexCharge.Tracking.Services.MerchantsInformationCacheService;

public class MerchantInformation
{
    public Guid Mid { get; set; }

    public Guid? Pid { get; set; }
    public Guid? Aid { get; set; }
    public Guid? IntegrationPartnerId { get; set; }

    public bool IsActive { get; set; }
    public bool IsLocked { get; set; }

    public bool IgnoreSiteIdFromClient { get; set; }

    public bool IsCrawlingEnabled { get; set; }
    public bool IsIframeMessagesCollectEnabled { get; set; }
}