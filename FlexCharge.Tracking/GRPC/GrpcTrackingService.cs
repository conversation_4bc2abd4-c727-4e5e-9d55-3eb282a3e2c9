using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using FlexCharge.Common.Cache.BigPayload;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Tracking;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Contracts.Common;
using FlexCharge.Tracking.Consumers;
using Grpc.Core;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using SerializedRequest = FlexCharge.Grpc.Tracking.SerializedRequest;
using SerializedResponse = FlexCharge.Grpc.Tracking.SerializedResponse;

namespace FlexCharge.Tracking.GRPC;

public class GrpcTrackingService : FlexCharge.Grpc.Tracking.GrpcTrackingService.GrpcTrackingServiceBase
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IBigPayloadService _bigPayloadService;

    public GrpcTrackingService(PostgreSQLDbContext dbContext, IBigPayloadService bigPayloadService)
    {
        _dbContext = dbContext;
        _bigPayloadService = bigPayloadService;
    }

    public override async Task<SerializedResponse> GetTrackingInformation(SerializedRequest request,
        ServerCallContext context)
    {
        using var workspan = Workspan.Start<GrpcTrackingService>();

        try
        {
            var message = JsonConvert.DeserializeObject<GetTrackingInformationCommand>(request.Request);

            workspan
                .Request(request.Request)
                .LogEnterAndExit();

            if (message.MerchantId == null && message.SenseKey == null)
                throw new Exception("SenseKey and MerchantId cannot be null at the same time.");

            var query = _dbContext.Tracking.AsQueryable()
                .AsNoTracking();

            if (!string.IsNullOrWhiteSpace(message.ExternalOrderId))
            {
                query = query.Where(x => x.ExternalOrderId == message.ExternalOrderId);
            }

            if (!string.IsNullOrWhiteSpace(message.OrderSessionKey))
            {
                query = query.Where(x => x.OrderSessionKey == message.OrderSessionKey);
            }

            if (message.SenseKey != null)
            {
                query = query.Where(x => x.SenseKey == message.SenseKey);
            }

            if (message.MerchantId != null)
            {
                var mid = message.MerchantId.Value;
                query = query.Where(x =>
                        //TODO D: Mid must be replaced with obfuscated merchant identifier
                        x.MerchantId ==
                        mid //&& x.IsUsed != true //do not test for IsUsed -> we cannot take risks in this case (duplicate or mismatched order, but the first one ha benn processed already )
                );
            }

            if (message.MatchTimeThreshold != null)
            {
                query = query.Where(x =>
                    x.CreatedOn >= message.MatchTimeThreshold && x.ModifiedOn >= message.MatchTimeThreshold);
            }

            if (message.OrderByTimeDescending)
            {
                query = query.OrderByDescending(x => x.ModifiedOn);
            }

            if (message.ReturnOnlyLatest)
            {
                query = query.Take(1);
            }

            var results = await query.ToListAsync();

            var trackingInformation = results.Select(x =>
                new TrackingEntry()
                {
                    SenseKey = x.SenseKey,
                    ExternalOrderId = x.ExternalOrderId,
                    OrderSessionKey = x.OrderSessionKey,
                    MerchantId = x.MerchantId,
                    Timestamp = x.ModifiedOn,
                    BrowserInfo = x.BrowserInfo,
                    ConfigurationId = x.ConfigurationId,
                    CheckoutData = x.CheckoutData != null
                        ? JsonConvert.DeserializeObject<Dictionary<string, string>>(x.CheckoutData)
                        : new Dictionary<string, string>(),
                    Location = ParseLocation(x.Location, workspan),
                    Providers = JsonConvert.DeserializeObject<List<ProviderDTO>>(x.Providers),
                }).ToList();

            var trackingInformationBigPayload = await _bigPayloadService.CreateBigPayloadAsync(trackingInformation);

            GetTrackingInformationCommandResponse response = new()
            {
                TrackingHistoryInDescendingOrder =
                    new BigPayloadKey<List<TrackingEntry>>(trackingInformationBigPayload.Key)
            };

            return new SerializedResponse
            {
                Response = JsonConvert.SerializeObject(response)
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }


    private JsonDocument ParseLocation(string location, Workspan workspan)
    {
        try
        {
            return JsonDocument.Parse(location);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            return null;
        }
    }
}