{"containerDefinitions": [{"name": "fc-core-tracking-server", "image": "556663010871.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-tracking:8f31fb9993e42e8ef513fd02312a27dc8cd8a701", "cpu": 0, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "tracking_service_staging"}, {"name": "DB_DATABASE", "value": "fc_tracking"}, {"name": "DB_HOST", "value": "flexcharge-staging-2023-08-08.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Staging"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}, {"name": "AWS_S3_STATIC_FILES_BUCKET", "value": "flex-staging-static-assets"}, {"name": "AWS_S3_IAM_REGION", "value": "us-east-1"}, {"name": "CLARITY_ID", "value": "fvm2pjvnal"}, {"name": "KOUNT_MERCHANT_ID", "value": "100488"}, {"name": "KOUNT_PATH", "value": "https://tst-kaptcha.flex-charge.com"}, {"name": "STRIPE_PUBLIC_KEY", "value": "pk_test_51LtV3wDlaqLb1O5YBhMonHMZ057376ZYRK9cAQu1RX0YH1gVvqTsS6Bb6APmkizUN8IOjB6Ob32gTprQRaXVybji00x8WhkM9r"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_DB_TRACKING_PASSWORD-KmjHJG"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_SNS_IAM_ACCESS_KEY-lZ8I9V"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_SNS_IAM_SECRET_KEY-YwuJZJ"}, {"name": "AWS_S3_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_S3_IAM_ACCESS_KEY-5IPcsd"}, {"name": "AWS_S3_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_S3_IAM_SECRET_KEY-UZwGMP"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-tracking-server-staging", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-tracking-server-staging", "taskRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Staging-Role", "executionRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Staging-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512"}