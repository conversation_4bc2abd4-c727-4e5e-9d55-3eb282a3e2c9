{"app": {"name": "tracking-service", "version": "0.0.1"}, "jwt": {"Provider": "Cognito", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "6jl6laoa1jtk2hgqdu79bu3m7d", "Region": "us-east-1", "UserPoolId": "us-east-1_7Snu9L5Rt", "AppClientId": "6jl6laoa1jtk2hgqdu79bu3m7d"}, "cache": {"connectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379"}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/tracking-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "swagger": {"enabled": true, "reDocEnabled": false, "name": "v1", "title": "tracking-service", "version": "v1", "routePrefix": "", "includeSecurity": true}, "io": {"logoImageValidation": {"minFileSize": 1024, "maxFileSize": 1048576, "minWidth": 32, "maxWidth": 2048, "minHeight": 32, "maxHeight": 2048, "minAspectRatio": 0.01, "maxAspectRatio": 100}, "backgroundImageValidation": {"minFileSize": 1024, "maxFileSize": 1048576, "minWidth": 32, "maxWidth": 2048, "minHeight": 32, "maxHeight": 2048, "minAspectRatio": 0.01, "maxAspectRatio": 100}, "faviconImageValidation": {"minFileSize": 1024, "maxFileSize": 1048576, "minWidth": 16, "maxWidth": 512, "minHeight": 16, "maxHeight": 512, "minAspectRatio": 1, "maxAspectRatio": 1}}, "jaeger": {"agentHost": "**************", "agentPort": 6831}, "amazonS3Storage": {"publicFilesCDN": "https://d1ykmzvmilywr0.cloudfront.net"}, "tracking": {"pageDataStorageBucket": "tracking-pagedata-sandbox"}, "Kestrel": {"Endpoints": {"HTTP": {"Url": "http://*:80", "Protocols": "Http1AndHttp2"}, "gRPC": {"Url": "http://*:5000", "Protocols": "Http2"}}}, "AllowedHosts": "*"}