// <auto-generated />
using System;
using System.Text.Json;
using FlexCharge.Tracking;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Tracking.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20230411103329_make-siteid-nullable")]
    partial class makesiteidnullable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Tracking.Entities.Configuration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ButtonsCornersRadius")
                        .HasColumnType("text");

                    b.Property<string>("CancelUrl")
                        .HasColumnType("text");

                    b.Property<JsonDocument>("CheckoutUrl")
                        .HasColumnType("jsonb");

                    b.Property<int>("ConsentFlowConfiguration")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FontFamily")
                        .HasColumnType("text");

                    b.Property<bool>("IsCloseButtonVisible")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsLogoVisible")
                        .HasColumnType("boolean");

                    b.Property<string>("Logo")
                        .HasColumnType("text");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModalCornersRadius")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PrimaryColor")
                        .HasColumnType("text");

                    b.Property<string>("Recorders")
                        .HasColumnType("jsonb");

                    b.Property<string>("RejectUrl")
                        .HasColumnType("text");

                    b.Property<JsonDocument>("ScrapingData")
                        .HasColumnType("jsonb");

                    b.Property<string>("SecondaryColor")
                        .HasColumnType("text");

                    b.Property<Guid?>("SiteId")
                        .HasColumnType("uuid");

                    b.Property<JsonDocument>("SubmitData")
                        .HasColumnType("jsonb");

                    b.Property<string>("SuccessUrl")
                        .HasColumnType("text");

                    b.Property<string>("TokenizationPublicKey")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Configurations");
                });

            modelBuilder.Entity("FlexCharge.Tracking.Entities.Tracking", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BrowserInfo")
                        .HasColumnType("text");

                    b.Property<string>("CheckoutData")
                        .HasColumnType("text");

                    b.Property<Guid>("ConfigurationId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeviceInfo")
                        .HasColumnType("text");

                    b.Property<string>("ExternalOrderId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Providers")
                        .HasColumnType("text");

                    b.Property<Guid?>("SenseKey")
                        .HasColumnType("uuid");

                    b.Property<string>("TrackingData")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Tracking");
                });
#pragma warning restore 612, 618
        }
    }
}
