using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Tracking.Migrations
{
    /// <inheritdoc />
    public partial class addOrderSessionKeytotrackingtable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "OrderSessionKey",
                table: "TrackingArchive",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OrderSessionKey",
                table: "Tracking",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OrderSessionK<PERSON>",
                table: "TrackingArchive");

            migrationBuilder.DropColumn(
                name: "OrderSessionKey",
                table: "Tracking");
        }
    }
}
