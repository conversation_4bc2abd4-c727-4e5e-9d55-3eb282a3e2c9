using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Tracking.Migrations
{
    /// <inheritdoc />
    public partial class addcheckoutpagestyles : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CheckoutPageBackgroundImage",
                table: "Configurations",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CheckoutPageFontColor",
                table: "Configurations",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CheckoutPagePrimaryColor",
                table: "Configurations",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CheckoutPageSecondaryColor",
                table: "Configurations",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Favicon",
                table: "Configurations",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsCheckoutOneColumn",
                table: "Configurations",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CheckoutPageBackgroundImage",
                table: "Configurations");

            migrationBuilder.DropColumn(
                name: "CheckoutPageFontColor",
                table: "Configurations");

            migrationBuilder.DropColumn(
                name: "CheckoutPagePrimaryColor",
                table: "Configurations");

            migrationBuilder.DropColumn(
                name: "CheckoutPageSecondaryColor",
                table: "Configurations");

            migrationBuilder.DropColumn(
                name: "Favicon",
                table: "Configurations");

            migrationBuilder.DropColumn(
                name: "IsCheckoutOneColumn",
                table: "Configurations");
        }
    }
}
