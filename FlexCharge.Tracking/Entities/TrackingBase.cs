using System;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Tracking.Entities;

[Index(nameof(MerchantId))]
[Index(nameof(SenseKey))]
[Index(nameof(CreatedOn))]
[Index(nameof(ModifiedOn))]
[Index(nameof(ExternalOrderId))]
public class TrackingBase : AuditableEntity
{
    public Guid MerchantId { get; set; }
    public Guid? SenseKey { get; set; }
    public Guid ConfigurationId { get; set; }

    public string? CheckoutData { get; set; }

    public string TrackingData { get; set; }
    public string? FormData { get; set; }

    public string? BrowserInfo { get; set; }

    public string? ExternalOrderId { get; set; }
    public string? OrderSessionKey { get; set; }

    public string Location { get; set; }
    public string DeviceInfo { get; set; }
    public string Providers { get; set; }
    public WebCrawling? WebCrawling { get; set; }
    public BrowserFingerprint? BrowserFingerprint { get; set; }
    public Guid? SiteId { get; set; }
}