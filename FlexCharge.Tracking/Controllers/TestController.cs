#if DEBUG
using System;
using System.Linq;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cache.DistributedMemoryDatabase;
using FlexCharge.Common.Shared.Merchants.Sites;
using FlexCharge.Common.Shared.Tracking;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace FlexCharge.Tracking.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly IExternalRequestsDistributedCache _externalRequestsDistributedCache;
        private readonly IExternalRequestsDistributedMemoryDatabase _externalRequestsDistributedMemoryDatabase;
        private readonly IMerchantSitesService _merchantSitesService;
        private readonly IMapper _mapper;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly AppOptions _globalData;


        public TestController(IOptions<AppOptions> globalData, IMapper mapper,
            PostgreSQLDbContext dbContext,
            IExternalRequestsDistributedCache externalRequestsDistributedCache,
            IExternalRequestsDistributedMemoryDatabase externalRequestsDistributedMemoryDatabase,
            IMerchantSitesService merchantSitesService)
        {
            _externalRequestsDistributedCache = externalRequestsDistributedCache;
            _externalRequestsDistributedMemoryDatabase = externalRequestsDistributedMemoryDatabase;
            _merchantSitesService = merchantSitesService;
            _mapper = mapper;
            _dbContext = dbContext;
            _globalData = globalData.Value;
        }


        [HttpGet]
        [ProducesResponseType(200)]
        public async Task<ActionResult> Get([FromQuery] string externalOrderId, [FromQuery] Guid merchantId)
        {
            using var workspan = Workspan.StartEndpoint<TestController>(this, null, _globalData);

            //return await Test_CreateTrackingRecord(externalOrderId, merchantId);

            return await Test_DistributedMemoryDatabase();
        }

        private async Task<ActionResult> Test_DistributedMemoryDatabase()
        {
            await _externalRequestsDistributedMemoryDatabase.InsertToListAtBeginningAsync(
                "List1", new string[] {"Value1", "Value2"},
                TimeSpan.FromMinutes(1));

            await _externalRequestsDistributedMemoryDatabase.InsertToListAtBeginningAsync(
                "List1", "Value",
                TimeSpan.FromMinutes(1));

            var data = await _externalRequestsDistributedMemoryDatabase.GetAllStringsFromListAsync("List1");
            return Ok();
        }

        private async Task<ActionResult> Test_CreateTrackingRecord(string externalOrderId, Guid merchantId)
        {
            var trackingKey =
                TrackingSharedCacheKeyFactory.CreateTrackingKey_ByExternalOrderId(merchantId, externalOrderId);
            var response = await _externalRequestsDistributedCache.GetValueAsync<TrackingEntry>(trackingKey.Key);

            var mid = Guid.NewGuid();

            _dbContext.Tracking.Add(new Entities.Tracking
            {
                MerchantId = mid,
                // SenseKey = Guid.NewGuid(),
            });
            await _dbContext.SaveChangesAsync();

            var merchant = await _dbContext.Tracking.Where(x => x.MerchantId == mid).AsNoTracking()
                .SingleOrDefaultAsync();

            if (merchant != null)
            {
                merchant.SenseKey = Guid.NewGuid();
            }

            _dbContext.Tracking.Update(merchant);
            await _dbContext.SaveChangesAsync();

            if (response != null)
            {
                return Ok(response);
            }
            else
            {
                return Ok("Not found");
            }
        }

        [HttpGet("test-wrong-tracking-data")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestWrongTrackingData()
        {
            using var workspan = Workspan.StartEndpoint<TestController>(this, null, _globalData);

            var aConfiguration = await _dbContext.Configurations.OrderBy(x => x.CreatedOn).LastAsync();
            aConfiguration.Id = Guid.NewGuid();

            var badData = new TestPayload() {Test = "AAA\\\\\u0000\\\\u0000\\\\u0000"};


            string jsonString = JsonConvert.SerializeObject(badData);
            JsonDocument document = JsonDocument.Parse(jsonString);

            aConfiguration.ScrapingData = document;

            _dbContext.Configurations.Add(aConfiguration);

            try
            {
                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                workspan.Log.Fatal(ex, "ERROR");
            }

            return Ok();
        }

        public class TestPayload
        {
            public string Test { get; set; }
        }

        [HttpGet("test-get-site-id-by-host")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestGetSiteIdByHost(Guid mid, string url)
        {
            var siteId = await _merchantSitesService.GetSiteIdByHostOrDefault(mid, new Uri(url));

            if (siteId == null)
            {
                return NotFound();
            }

            return Ok(siteId);
        }
    }
}
#endif