// using System;
// using System.Collections.Generic;
// using System.IO;
// using System.Linq;
// using System.Net.Http;
// using System.Security.Claims;
// using System.Text;
// using System.Threading.Tasks;
// using FlexCharge.Common.Authentication;
// using FlexCharge.Eligibility.DTO;
// using FlexCharge.Utils.Tests;
// using Microsoft.AspNetCore.Http;
// using Newtonsoft.Json;
// using NUnit.Framework;
//
//
// namespace FlexCharge.Eligibility.Tests;
//
// public class OutcomeApiTest
// {
//     private FlexWebApplicationFactory<Program> webAppFactory;
//
//     [SetUp]
//     public void Setup()
//     {
//         LaunchSettingsHelper.SetEnvVariables<Program>();
//         webAppFactory = new();
//     }
//
//     [Test]
//     public async Task Null_MIT_throws_exception_test()
//     {
//         // var client = webAppFactory.CreateClient();
//
//         // var r=await client.PostAsJsonAsync("/outcome",
//         //     new OutcomeRequest() { OrderSessionKey = Guid.NewGuid() });
//
//
//         var response = await webAppFactory.Server.SendAsync(context =>
//         {
//             var claims = new List<Claim>()
//             {
//                 new Claim(ClaimTypes.Name, "username"),
//                 new Claim(ClaimTypes.NameIdentifier, "userId"),
//                 new Claim(MyClaimTypes.MERCHANT_ID, Guid.NewGuid().ToString()),
//                 // new Claim(MyClaimTypes.COGNITO_GROUP, MyGroups.SUPER_ADMIN)
//             };
//
//             var identity = new ClaimsIdentity(claims, "TestAuthType");
//             var claimsPrincipal = new ClaimsPrincipal(identity);
//             context.User = claimsPrincipal;
//             context.Request.Method = HttpMethods.Post;
//             context.Request.Path = "/outcome";
//             context.Request.ContentType = "application/json; charset=utf-8";
//             var outReq = new OutcomeRequest() {OrderSessionKey = Guid.NewGuid()};
//             var json = JsonConvert.SerializeObject(outReq);
//             var requestContent = new StringContent(json, Encoding.UTF8); //, "application/json");
//
//             context.Request.Body = requestContent.ReadAsStream();
//             context.Request.ContentLength = context.Request.Body.Length;
//         });
//
//         using (var reader = new StreamReader(response.Response.Body))
//         {
//             // response.Response.Body.Seek(0, SeekOrigin.Begin);
//             var stringResult = reader.ReadToEnd();
//             Assert.AreNotEqual(response.Response.StatusCode, 404);
//         }
//     }
// }