using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace FlexCharge.Utils
{
    public static class CryptoHashingHelper
    {
        public static string ComputeContentHash(string content, string hashAlgorithmName = "SHA512")
        {
            using (var hashAlgorithm = HashAlgorithm.Create(hashAlgorithmName))
            {
                byte[] hashedBytes = hashAlgorithm.ComputeHash(Encoding.UTF8.GetBytes(content));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        public static string ComputeContentSaltedHash(string content, string salt, string hashAlgorithmName = "SHA512")
        {
            using (var hashAlgorithm = HashAlgorithm.Create(hashAlgorithmName))
            {
                byte[] hashedBytes = hashAlgorithm.ComputeHash(Encoding.UTF8.GetBytes(content + salt));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        /// <summary>
        /// Created hash from multiple values in the following form: hash1_hash2_..._hashN
        /// </summary>
        /// <param name="valuesToHash"></param>
        /// <param name="salt"></param>
        /// <param name="hashAlgorithmName"></param>
        /// <param name="separator"></param>
        /// <returns></returns>
        public static string CreateConcatenatedHash(IEnumerable<object> valuesToHash, string salt = null,
            string hashAlgorithmName = "SHA512",
            char separator = '_')
        {
            var hashes = salt != null
                ? valuesToHash.Select(x => ComputeContentHash((x ?? "").ToString(), hashAlgorithmName))
                : valuesToHash.Select(x => ComputeContentSaltedHash((x ?? "").ToString(), salt, hashAlgorithmName));

            return ConcatenateHashes(hashes, separator);
        }


        public static string ConcatenateHashes(IEnumerable<string> inputHashes, char separator = '_')
        {
            StringBuilder concatenatedHash = new StringBuilder();

            bool firstValue = true;
            foreach (var hashToConcatenate in inputHashes)
            {
                if (firstValue)
                {
                    firstValue = false;
                }
                else
                {
                    concatenatedHash.Append(separator);
                }

                concatenatedHash.Append(hashToConcatenate);
            }

            return concatenatedHash.ToString();
        }
    }
}