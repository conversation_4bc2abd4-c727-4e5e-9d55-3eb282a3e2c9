using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace FlexCharge.Utils;

public static class HttpExtensions
{
    public static async Task<HttpResponseMessage> PostAsync(this HttpClient httpClient, string requestUri,
        string userName, string password, HttpContent content, CancellationToken cancellationToken = default,
        String? challengeCharSet = null)
    {
        return await SendAsync(httpClient, HttpMethod.Post, requestUri, content, userName, password, challengeCharSet,
            cancellationToken);
    }

    public static async Task<HttpResponseMessage> PostAsync(this HttpClient httpClient, string requestUri,
        (string name, string value)[] reqHeaders,
        HttpContent content, CancellationToken cancellationToken = default)
    {
        return await SendAsync(httpClient, HttpMethod.Post, requestUri, content, reqHeaders,
            cancellationToken);
    }

    public static async Task<HttpResponseMessage> PutAsync(this HttpClient httpClient, string requestUri,
        string userName, string password, HttpContent content, CancellationToken cancellationToken = default,
        string? challengeCharSet = null)
    {
        return await SendAsync(httpClient, HttpMethod.Put, requestUri, content, userName, password, challengeCharSet,
            cancellationToken);
    }

    public static async Task<HttpResponseMessage> GetAsync(this HttpClient httpClient, string requestUri,
        (string name, string value)[] reqHeaders, CancellationToken cancellationToken = default)
    {
        return await SendAsync(httpClient, HttpMethod.Get, requestUri, null, reqHeaders,
            cancellationToken);
    }


    public static async Task<HttpResponseMessage> GetAsync(this HttpClient httpClient, string requestUri,
        (string name, string value)[] reqHeaders, HttpContent content, CancellationToken cancellationToken = default)
    {
        return await SendAsync(httpClient, HttpMethod.Get, requestUri, content, reqHeaders,
            cancellationToken);
    }

    public static async Task<HttpResponseMessage> DeleteAsync(this HttpClient httpClient, string requestUri,
        (string name, string value)[] reqHeaders, HttpContent content, CancellationToken cancellationToken = default)
    {
        return await SendAsync(httpClient, HttpMethod.Delete, requestUri, content, reqHeaders,
            cancellationToken);
    }


    public static async Task<HttpResponseMessage> PutAsync(this HttpClient httpClient, string requestUri,
        (string name, string value)[] reqHeaders, HttpContent content, CancellationToken cancellationToken = default)
    {
        return await SendAsync(httpClient, HttpMethod.Put, requestUri, content, reqHeaders,
            cancellationToken);
    }

    public static async Task<HttpResponseMessage> GetAsync(this HttpClient httpClient, string requestUri,
        string userName, string password, CancellationToken cancellationToken = default,
        string? challengeCharSet = null)
    {
        return await SendAsync(httpClient, HttpMethod.Get, requestUri, null, userName, password, challengeCharSet,
            cancellationToken);
    }

    public static async Task<HttpResponseMessage> DeleteAsync(this HttpClient httpClient, string requestUri,
        string userName, string password, CancellationToken cancellationToken = default,
        string? challengeCharSet = null)
    {
        return await SendAsync(httpClient, HttpMethod.Delete, requestUri, null, userName, password, challengeCharSet,
            cancellationToken);
    }

    // public  static async Task<TRes> GetFromJsonAsync( this HttpClient httpClient, string requestUri, 
    //     string userName, string password,  CancellationToken cancellationToken = default, string? challengeCharSet = null )
    // {
    //     var response= await SendAsync(httpClient, HttpMethod.Put, requestUri, null, userName, password, challengeCharSet,
    //         cancellationToken);
    //     
    // }
    private static Task<HttpResponseMessage> SendAsync(this HttpClient httpClient, HttpMethod httpMethod,
        string requestUri, HttpContent content,
        (string name, string value)[] reqHeaders, CancellationToken cancellationToken = default)
    {
        HttpRequestMessage httpRequestMessage = new HttpRequestMessage(httpMethod, requestUri);
        httpRequestMessage.Content = content;
        foreach (var header in reqHeaders)
            httpRequestMessage.Headers.Add(header.name, header.value);


        return httpClient.SendAsync(httpRequestMessage, cancellationToken);
    }


    private static Task<HttpResponseMessage> SendAsync(this HttpClient httpClient, HttpMethod httpMethod,
        string requestUri, HttpContent content,
        string userName,
        string password, string? challengeCharSet = null, CancellationToken cancellationToken = default)
    {
        if (userName.IndexOf(':') > -1)
            throw new ArgumentException(message: "RFC 7617 states that usernames cannot contain colons.",
                paramName: nameof(userName));

        HttpRequestMessage httpRequestMessage = new HttpRequestMessage(httpMethod, requestUri);
        httpRequestMessage.Content = content;

        Encoding encoding = Encoding.ASCII;
        if (challengeCharSet != null)
        {
            try
            {
                encoding = Encoding.GetEncoding(challengeCharSet);
            }
            catch
            {
                encoding = Encoding.ASCII;
            }
        }

        httpRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            scheme: "Basic",
            parameter: Convert.ToBase64String(encoding.GetBytes(userName + ":" + password))
        );

        return httpClient.SendAsync(httpRequestMessage, cancellationToken);
    }
}