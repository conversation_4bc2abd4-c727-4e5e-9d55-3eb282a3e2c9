using System;
using System.Linq;

namespace FlexCharge.Utils;

public static class PaymentInstrumentsValidators
{
    public static bool ValidLength(string param,int length)
    {
        return !string.IsNullOrEmpty(param) && param.Length == length;
    }

    public static bool ValidateRoutingNumber(string routingNum)
    {
        var checkSum = 0;
        var sum = 0;
        var mod = 0;

        //var firstTwoDigits = int.Parse(routingNum.Substring(0, 2));
        // if (Enumerable.Range(01, 12).Contains(firstTwoDigits) || Enumerable.Range(21, 32).Contains(firstTwoDigits))
        //     return false;

        if (!ValidLength(routingNum,9))
            return false;

        var result = routingNum.Substring(routingNum.Length - 1);
        checkSum = int.Parse(result);

        sum = (7 * (int.Parse("" + routingNum[0]) + int.Parse("" + routingNum[3]) +
                    int.Parse("" + routingNum[6]))) +
              (3 * (int.Parse("" + routingNum[1]) + int.Parse("" + routingNum[4]) +
                    int.Parse("" + routingNum[7]))) +
              (9 * (int.Parse("" + routingNum[2]) + int.Parse("" + routingNum[5])));


        mod = sum % 10;

        if (mod == checkSum)
            return true;
        else
            return false;
    }
    
    public static bool IsCreditCardExpired(string expirationMonth, string expirationYear)
    {
        // Parse expiration month and year strings to integers
        var expMonth = int.Parse(expirationMonth);
        var expYear = int.Parse(expirationYear);

        return IsCreditCardExpired(expMonth, expYear);
    }
    
    public static bool IsCreditCardExpired(int expirationMonth, int expirationYear)
    {
        // Check if the card is expired based on the expiration month and year
        var currentYear = DateTime.Now.Year % 100;
        var currentMonth = DateTime.Now.Month;

        if (expirationYear < currentYear || (expirationYear == currentYear && expirationMonth < currentMonth))
        {
            return true; // Card is expired
        }
        else
        {
            return false; // Card is not expired
        }
    }
}