using System;

namespace FlexCharge.Utils;

public static class <PERSON>riHelper
{
    /// <summary>
    /// Creates U<PERSON> with default scheme if missing
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    /// <exception cref="Exception">Throws exception if Uri cannot be created</exception>
    public static Uri CreateHttpsUri(string url)
    {
        //see: https://stackoverflow.com/questions/5289739/add-scheme-to-url-if-needed

        Uri uri = CreateUriWithDefaultSchemeIfMissing(url, "https://");
        if (uri != null &&
            (uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps))
        {
            return uri;
        }
        else throw new Exception($"Cannot create Uri - invalid url: {url}");
    }

    private static Uri? CreateUriWithDefaultSchemeIfMissing(string url, string defaultScheme)
    {
        //see: https://stackoverflow.com/questions/5289739/add-scheme-to-url-if-needed

        Uri uri = null;
        if (!Uri.TryCreate(url, UriKind.Absolute, out uri))
        {
            Uri.TryCreate(defaultScheme + url, UriKind.Absolute, out uri);
        }

        return uri;
    }
}