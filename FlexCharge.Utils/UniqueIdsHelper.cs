using System;

namespace FlexCharge.Utils;

public static class UniqueIdsHelper
{
    /// <summary>
    /// Returns Hexadecimal pseudo-unique codes
    /// </summary>
    /// <param name="salt">Pass OrderId for example</param>
    /// <returns></returns>
    public static string GeneratePseudoUniqueId(string salt)
    {
        //see: https://yetanotherchris.dev/csharp/friendly-unique-id-generation-part-2/
        //It was modified and now produces only 100 collisions per 1M transactions on average
        return string.Format("{0:X}",
            $"{salt}_{DateTime.UtcNow.Ticks}_{Random.Shared.Next(0, 1000000)}".GetHashCode()).ToUpper();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="salt">Pass OrderId for example</param>
    /// <returns></returns>
    public static string GeneratePseudoUniqueId(Guid salt)
    {
        return GeneratePseudoUniqueId(salt.ToString());
    }
}