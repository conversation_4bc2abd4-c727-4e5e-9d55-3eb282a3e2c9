using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace FlexCharge.Utils
{
    public static class StringHelpers
    {
        public static string RemoveWhitespace(this string input)
        {
            return new string(input.ToCharArray()
                .Where(c => !Char.IsWhiteSpace(c))
                .ToArray());
        }

        public static string ToStringWithoutDashes(this Guid guid)
        {
            return guid.ToString("N");
        }

        public static string Mask(this string source, int start, int maskLength)
        {
            return source.Mask(start, maskLength, 'X');
        }

        public static string Mask(this string source, int start, int maskLength, char maskCharacter)
        {
            if (maskLength < 0)
            {
                throw new ArgumentException("Mask length cannot have a negative value");
            }

            if (start > source.Length - 1)
            {
                throw new ArgumentException("Start position is greater than string length");
            }

            if (maskLength > source.Length)
            {
                throw new ArgumentException("Mask length is greater than string length");
            }

            if (start + maskLength > source.Length)
            {
                throw new ArgumentException("Start position and mask length imply more characters than are present");
            }

            string mask = new string(maskCharacter, maskLength);
            string unMaskStart = source.Substring(0, start);
            string unMaskEnd = source.Substring(start + maskLength);

            return unMaskStart + mask + unMaskEnd;
        }

        public static string MaskAll(this string source, char maskCharacter)
        {
            if (string.IsNullOrEmpty(source))
                return source;

            return new string(maskCharacter, source.Length);
        }

        public static string GetLast(this string source, int tailLength)
        {
            if (tailLength >= source.Length)
                return source;
            return source.Substring(source.Length - tailLength);
        }

        public static string GetFirst(this string source, int numChars)
        {
            if (numChars >= source.Length)
                return source;
            return source.Substring(0, numChars);
        }

        public static string GetNumbers(this string input)
        {
            return new string(input.Where(c => char.IsDigit(c)).ToArray());
        }

        public static string ToKebabCase(this string input)
        {
            // Remove leading and trailing whitespace
            input = input.Trim();

            // Replace spaces, underscores, or camel case with dashes
            string kebabCase = Regex.Replace(input, @"\s+|_+|(?<=[a-z])([A-Z])", "-$1").ToLower();

            return kebabCase;
        }

        public static bool IsNullOrEmpty(this string input)
        {
            return string.IsNullOrEmpty(input);
        }

        public static string ToUnderscoreCase(this string input)
        {
            // Remove leading and trailing whitespace
            input = input.Trim();

            // Replace spaces, dashes, or camel case with underscores
            string underscoreCase = Regex.Replace(input, @"\s+|-+|(?<=[a-z])([A-Z])", "_$1").ToLower();

            return underscoreCase;
        }

        public static string TruncateAndRemoveAlphaNumeric(this string value, int maxLength,
            bool removeNotAlphaNumeric = true)
        {
            var result = value;
            if (string.IsNullOrEmpty(result))
                return result;

            if (removeNotAlphaNumeric)
            {
                result = Regex.Replace(result, @"[^a-zA-Z0-9\s]", "");
            }

            result = result.Substring(0, Math.Min(result.Length, maxLength));
            return result;
        }

        public static string Truncate(this string value, int maxLength)
        {
            if (string.IsNullOrEmpty(value))
                return value;

            var result = value.Substring(0, Math.Min(value.Length, maxLength));

            return result;
        }

        public static string RemoveNotDigits(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return value;
            var result = Regex.Replace(value, "[^0-9]", "");
            return result;
        }


        public static string FirstCharToUpper(this string input) => input switch
        {
            null => throw new ArgumentNullException(nameof(input)),
            "" => throw new ArgumentException($"{nameof(input)} cannot be empty", nameof(input)),
            _ => string.Concat(input[0].ToString().ToUpper(), input.AsSpan(1))
        };

        public static string FirstCharToLower(this string input) => input switch
        {
            null => throw new ArgumentNullException(nameof(input)),
            "" => throw new ArgumentException($"{nameof(input)} cannot be empty", nameof(input)),
            _ => string.Concat(input[0].ToString().ToLower(), input.AsSpan(1))
        };

        public static string FirstCharToUpperOthersToLower(this string input) => input switch
        {
            null => throw new ArgumentNullException(nameof(input)),
            _ => input.ToLower().FirstCharToUpper()
        };

        public static string ExtractPart(this string input, char separator, int indexToExtract)
        {
            if (input == null)
                throw new ArgumentNullException(nameof(input), "Input string cannot be null.");

            try
            {
                // Split the input string using the specified separator
                string[] parts = input.Split(separator);

                // Check if the specified index is within the bounds
                if (indexToExtract < 0 || indexToExtract >= parts.Length)
                    return null;

                // Extract the part at the specified index
                string extractedPart = parts[indexToExtract];

                return extractedPart;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error occurred while extracting part: {ex.Message}");
                throw; // Rethrow the exception to propagate it further if needed
            }
        }

        public static string? GetFirstNonNullOrWhiteSpaceStringOrDefault(params string[] strings)
        {
            return strings.FirstOrDefault(str => !string.IsNullOrWhiteSpace(str));
        }
    }
}