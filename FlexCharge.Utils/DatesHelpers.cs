using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Linq;

namespace FlexCharge.Utils;

public static class DatesHelper
{
    public static IEnumerable<DateTime> GetDateRange(DateTime startDate, DateTime endDate)
    {
        var date = startDate.Date;

        while (date <= endDate.Date)
        {
            yield return date;
            date = date.AddDays(1);
        }
    }

    public static IEnumerable<DateTime> FindMissingDates(IEnumerable<DateTime> input)
    {
        // get the range of dates to check
        var from = input.Min();
        var to = input.Max();

        // Count of days in array
        var numberOfDays = to.Subtract(from).Days;

        // create an IEnumerable<DateTime> for all dates in the range
        var allDates = Enumerable.Range(0, numberOfDays)
            .Select(n => from.AddDays(n));

        // return all dates, except those found in the input
        return allDates.Except(input);
    }

    /// <summary>
    /// Tries to parse a "YYYY-MM" formatted string into year and month.
    /// </summary>
    /// <param name="yearMonth">The year and month string.</param>
    /// <param name="year">Output year.</param>
    /// <param name="month">Output month.</param>
    /// <returns>True if parsing is successful; otherwise, false.</returns>
    public static bool TryParseYearMonth(string yearMonth, out int year, out int month)
    {
        year = 0;
        month = 0;

        if (string.IsNullOrWhiteSpace(yearMonth))
            return false;

        if (DateTime.TryParseExact(
                yearMonth,
                "yyyy-MM",
                CultureInfo.InvariantCulture,
                DateTimeStyles.None,
                out DateTime parsedDate))
        {
            year = parsedDate.Year;
            month = parsedDate.Month;
            return true;
        }

        return false;
    }

    /// <summary>
    /// Gets the start of the month for the given "YYYY-MM" formatted string.
    /// </summary>
    /// <param name="yearMonth">The year and month string.</param>
    /// <returns>The start date of the month.</returns>
    /// <exception cref="ArgumentException">Thrown when the input is invalid.</exception>
    public static DateTime GetStartOfMonth(string yearMonth)
    {
        if (!TryParseYearMonth(yearMonth, out int year, out int month))
            throw new ArgumentException("Invalid date format. Please use 'YYYY-MM'.");

        return new DateTime(year, month, 1);
    }

    /// <summary>
    /// Gets the start of the month for the given "YYYY-MM" formatted string or null.
    /// </summary>
    /// <param name="yearMonth">The year and month string.</param>
    /// <returns>The start date of the month or null in case failing.</returns>
    public static DateTime? GetStartOfMonthOrDefault(string yearMonth)
    {
        if (!TryParseYearMonth(yearMonth, out int year, out int month))
            return null;

        return new DateTime(year, month, 1);
    }

    /// <summary>
    /// Gets the end of the month for the given "YYYY-MM" formatted string.
    /// </summary>
    /// <param name="yearMonth">The year and month string.</param>
    /// <returns>The end date of the month.</returns>
    /// <exception cref="ArgumentException">Thrown when the input is invalid.</exception>
    public static DateTime GetEndOfMonth(string yearMonth)
    {
        DateTime startOfMonth = GetStartOfMonth(yearMonth);
        return startOfMonth.AddMonths(1).AddTicks(-1);
    }

    /// <summary>
    /// Gets the end of the month for the given "YYYY-MM" formatted string or null.
    /// </summary>
    /// <param name="yearMonth">The year and month string.</param>
    /// <returns>The end date of the month or null in case failing.</returns>
    public static DateTime? GetEndOfMonthOrDefault(string yearMonth)
    {
        DateTime? startOfMonth = GetStartOfMonthOrDefault(yearMonth);

        if (!startOfMonth.HasValue)
            return null;

        return startOfMonth.Value.AddMonths(1).AddTicks(-1);
    }

    /// <summary>
    /// Gets the year and month from a "YYYY-MM" formatted string.
    /// </summary>
    /// <param name="yearMonth">The year and month string.</param>
    /// <returns>A tuple containing the year and month.</returns>
    /// <exception cref="ArgumentException">Thrown when the input is invalid.</exception>
    public static (int Year, int Month) GetYearAndMonth(string yearMonth)
    {
        if (!TryParseYearMonth(yearMonth, out int year, out int month))
        {
            throw new ArgumentException("Invalid date format. Please use 'YYYY-MM'.");
        }

        return (year, month);
    }
}

public static class DateTimeExtensions
{
    /// <summary>
    /// Creates UTC date from local date
    /// </summary>
    /// <param name="localDate"></param>
    /// <returns></returns>
    public static DateTime ToUtcDate(this DateTime localDate)
    {
        return new DateTime(localDate.Year, localDate.Month, localDate.Day, 0, 0, 0, DateTimeKind.Utc);
    }

    public static DateTime ToUtcDateTime(this DateTime localDate)
    {
        return new DateTime(localDate.Year, localDate.Month, localDate.Day, localDate.Hour, localDate.Minute,
            localDate.Second, DateTimeKind.Utc);
    }
}