using System;
using System.Threading;

namespace FlexCharge.Utils.Concurrency;

public class ConcurrentLong
{
    long _Value;

    public ConcurrentLong(long value)
    {
        Write(value);
    }

    public long IncrementAndRead()
    {
        return Interlocked.Increment(ref _Value);
    }

    public long DecrementAndRead()
    {
        return Interlocked.Decrement(ref _Value);
    }

    public long Read()
    {
        return Interlocked.Read(ref _Value);
    }

    public long Write(long value)
    {
        return Interlocked.Exchange(ref _Value, value);
    }

    public long AddAndRead(long value)
    {
        return Interlocked.Add(ref _Value, value);
    }

    public void Add(long value)
    {
        Interlocked.Add(ref _Value, value);
    }

    public long Value
    {
        get => Read();
        //set => Write(value);
    }
}

public class ConcurrentDateTime
{
    long _Value;

    public ConcurrentDateTime(DateTime value)
    {
        Write(value);
    }

    public DateTime Read()
    {
        return new DateTime(Interlocked.Read(ref _Value), DateTimeKind.Utc);
    }

    public void Write(DateTime value)
    {
        Interlocked.Exchange(ref _Value, value.ToUniversalTime().Ticks);
    }
    
    public DateTime Value
    {
        get => Read();
        //set => Write(value);
    }
}

public class ConcurrentBoolean
{
    long _Value;

    public ConcurrentBoolean(bool value)
    {
        Write(value);
    }

    public bool Read()
    {
        return Interlocked.Read(ref _Value) == 1;
    }

    public void Write(bool value)
    {
        Interlocked.Exchange(ref _Value, value ? 1 : 0);
    }
    
    public bool ReadAndSet(bool newValue)
    {
        bool currentValue = Interlocked.CompareExchange(ref _Value, newValue? 1 : 0, newValue? 0 : 1) == 1; 
        return currentValue;
    }

    public bool ReadAndSetToTrue() => ReadAndSet(true);

    public bool ReadAndSetToFalse() => ReadAndSet(false);
    
    public bool Value
    {
        get => Read();
        //set => WriteAndEnsure(value);
    }
}

public class Concurrent<T>
    where T : class
{
    T _Value;

    public Concurrent(T value)
    {
        Write(value);
    }

    public T Read()
    {
        return Interlocked.CompareExchange(ref _Value, null, null);
    }

    // public T ReadOrCreate(Func<T> create)
    // {
    //     T value = Interlocked.CompareExchange<T>(ref _Value, create(), null);
    // }

    public T Write(T value)
    {
        return Interlocked.Exchange(ref _Value, value);
    }
    
    /// <summary>
    /// method utilizes a compare-and-swap loop to atomically update the value.
    /// This ensures that if another thread updates the value between reading it and trying to set a new value,
    /// the method will keep trying until the value is successfully set.
    /// </summary>
    /// <param name="newValue"></param>
    public void WriteAndEnsure(T newValue)
    {
        T original;
        do
        {
            original = _Value;
        } while (original != Interlocked.CompareExchange(ref _Value, newValue, original));
    }
    
    public T Value
    {
        get => Read();
        //set => WriteAndEnsure(value);
    }
}
