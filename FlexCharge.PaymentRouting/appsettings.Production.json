{"app": {"name": "payment-routing-service", "version": "0.0.1"}, "jwt": {"Provider": "Cognito", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "3v4ll30aqfb435trbds7447524", "Region": "us-east-1", "UserPoolId": "us-east-1_xHv2n5DhZ", "AppClientId": "3v4ll30aqfb435trbds7447524"}, "cache": {"connectionString": "cache-redis-production.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "cache-redis-production.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "idempotency.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "tracking.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379"}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/payment-routing-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "swagger": {"enabled": true, "reDocEnabled": false, "name": "v1", "title": "payment-routing-service", "version": "v1", "routePrefix": "", "includeSecurity": true}, "jaeger": {"agentHost": "**************", "agentPort": 6831}, "AllowedHosts": "*"}