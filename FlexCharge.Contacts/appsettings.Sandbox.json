{"app": {"name": "contacts-service", "version": "0.0.1"}, "jwt": {"Provider": "Cognito", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "6jl6laoa1jtk2hgqdu79bu3m7d", "Region": "us-east-1", "UserPoolId": "us-east-1_7Snu9L5Rt", "AppClientId": "6jl6laoa1jtk2hgqdu79bu3m7d"}, "basicOauth": {"expiryMinutes": 60, "issuer": "Api-Client-Service", "SecretKey": "TAKEN FROM SECRETS", "Provider": "Api-Client-Service", "ValidateLifetime": true, "ValidateAudience": true, "ValidateIssuer": true, "ValidAudience": "contacts"}, "cache": {"connectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379"}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/contacts-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "swagger": {"enabled": true, "reDocEnabled": false, "name": "v1", "title": "contacts-service", "version": "v1", "routePrefix": "", "includeSecurity": true}, "jaeger": {"agentHost": "**************", "agentPort": 6831}, "AllowedHosts": "*"}