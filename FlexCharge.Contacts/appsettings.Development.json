{
    "app": {
        "name": "contacts-service",
        "version": "0.0.1"
    },
    "jwt": {
        "Provider": "Cognito",
        "validateLifetime": true,
        "ValidateAudience": true,
        "ValidAudience": "4n0i8a4i9o1vk3g3tf64o6blg7",
        "Region": "us-east-1",
        "UserPoolId": "us-east-1_rCUpTgXY4",
        "AppClientId": "4n0i8a4i9o1vk3g3tf64o6blg7"
    },
    "basicOauth": {
        "expiryMinutes": 60,
        "issuer": "Api-Client-Service",
        "SecretKey": "TAKEN FROM SECRETS",
        "Provider": "Api-Client-Service",
        "ValidateLifetime": true,
        "ValidateAudience": true,
        "ValidateIssuer": true,
        "ValidAudience": "contacts"
    },
    "cache": {
        "connectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379",
        "instance": "",
        "schemaName": "dbo",
        "tableName": "Cache",
        "BigPayloadCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379",
        "IdempotencyCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379",
        "ExternalRequestsCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379"
    },
    "sendgrid": {
        "key": "",
        "SenderEmail": "",
        "SenderName": "Fithood"
    },
    "CancellationEmailOptions": {
        "productionEmail": "",
        "testingEmail": ""
    },
    "sms": {
        "SID": "",
        "Token": "",
        "TwilioPhone": "",
        "WhatsappPhone": "",
        "TwilioCompanyName": "",
        "ServiceSid": "", //Programmable Messaging/Messaging Services/Fithood Identity service
        "TwilioAuthyAPIKey": "",
        "TwilioVoiceSmsStartUrl": "https://api.authy.com/protected/json/phones/verification/start",
        "TwilioVoiceSmsChecktUrl": ""
    },
    
    "serilog": {
        "consoleEnabled": true,
        "level": "Information",
        "path": "../logs/contacts-{0}.txt"
    },
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Information"
        }
    },
    "swagger": {
        "enabled": true,
        "reDocEnabled": false,
        "name": "v1",
        "title": "contacts-service",
        "version": "v1",
        "routePrefix": "",
        "includeSecurity": true
    },
    "jaeger": {
        "agentHost" : "localhost",
        "agentPort" : 6831
    },
    "AllowedHosts": "*"
}
