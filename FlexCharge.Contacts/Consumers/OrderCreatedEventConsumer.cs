using System;
using System.Threading.Tasks;
using AutoMapper;
using EntityFramework.Exceptions.Common;
using FlexCharge.Common.Logging.LogSuppression;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contacts.DTO;
using FlexCharge.Contacts.Entities;
using FlexCharge.Contacts.Messages;
using FlexCharge.Contracts;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using OpenTelemetry;

namespace FlexCharge.Contacts.Consumers;

public class OrderCreatedEventConsumer : IConsumer<OrderCreatedEvent>
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<GetContactConsumer> _logger;

    public OrderCreatedEventConsumer(PostgreSQLDbContext dbContext, IMapper mapper, ILogger<GetContactConsumer> logger)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OrderCreatedEvent> context)
    {
        using var workspan = Workspan.Start<OrderCreatedEventConsumer>()
            .Baggage("OrderId", context.Message.OrderId)
            .Baggage("Email", context.Message.Email)
            .Baggage("ProfileUrl", context.Message.ProfileUrl);

        Contact contact = null;
        try
        {
            using var _ = DatabaseLogSuppressor
                .SuppressUniqueConstraintError<PostgreSQLDbContext>("Contacts",
                    "PK_Contacts");

            contact = new Contact
            {
                Id = context.Message.DesiredContactId,
                FirstName = context.Message.FirstName,
                LastName = context.Message.LastName,
                Email = context.Message.Email,
                SecondaryEmail = context.Message.SecondaryEmail,
                Phone = context.Message.Phone,
                ProfileUrl = context.Message.ProfileUrl,
                Meta = JsonConvert.SerializeObject(new
                {
                    OrderId = context.Message.OrderId,
                }),
                IsExternal = true,
                Currency = context.Message.Currency,
            };

            await _dbContext.Contacts.AddAsync(contact);

            await _dbContext.SaveChangesAsync();

            await context.Publish(_mapper.Map<ContactCreatedEvent>(contact));
        }
        catch (UniqueConstraintException e)
        {
            _dbContext.Entry(contact!).State = EntityState.Detached;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
        }
    }
}