{"containerDefinitions": [{"name": "core-contacts", "image": "556663010871.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-contacts:7da9b252999a69d0cc1ab1ed52f769f29f960e1d", "cpu": 0, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "contacts_service_prod"}, {"name": "DB_DATABASE", "value": "fc_contacts"}, {"name": "DB_HOST", "value": "flexcharge-prod.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Production"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}, {"name": "NEW_RELIC_APP_NAME", "value": "Contacts-production"}, {"name": "OTEL_SERVICE_NAME", "value": "Contacts-production"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_DB_CONTACTS_PASSWORD-QeZ9G9"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_SNS_IAM_ACCESS_KEY-lbCS4b"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_SNS_IAM_SECRET_KEY-a0ce7k"}, {"name": "API_CLIENT_JWT_SIGNING_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PRODUCTION-API-CLIENT-JWT-SIGNING-KEY-iGzRih"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-contacts-server-prod", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-contacts-server-prod", "taskRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Production-Role", "executionRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Production-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048"}