using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.BinChecker.Migrations
{
    /// <inheritdoc />
    public partial class bin : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Bins",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    BinID = table.Column<int>(type: "integer", nullable: false),
                    CardIssuerBin = table.Column<string>(type: "text", nullable: true),
                    CardIssueType = table.Column<string>(type: "text", nullable: true),
                    CardBrand = table.Column<string>(type: "text", nullable: true),
                    CardClass = table.Column<string>(type: "text", nullable: true),
                    CardSubType = table.Column<string>(type: "text", nullable: true),
                    CardIssuingCountry = table.Column<string>(type: "text", nullable: true),
                    CardIssuerRegulated = table.Column<string>(type: "text", nullable: true),
                    CardIssuer = table.Column<string>(type: "text", nullable: true),
                    CardIssueDetail = table.Column<string>(type: "text", nullable: true),
                    CardReloadable = table.Column<string>(type: "text", nullable: true),
                    LowAccount = table.Column<string>(type: "text", nullable: true),
                    HighAccount = table.Column<string>(type: "text", nullable: true),
                    Active = table.Column<int>(type: "integer", nullable: false),
                    BinLength = table.Column<int>(type: "integer", nullable: false),
                    PanLength = table.Column<int>(type: "integer", nullable: false),
                    Year = table.Column<int>(type: "integer", nullable: false),
                    Month = table.Column<int>(type: "integer", nullable: false),
                    Day = table.Column<int>(type: "integer", nullable: false),
                    FSAIndicator = table.Column<string>(type: "text", nullable: true),
                    PrepaidIndicator = table.Column<string>(type: "text", nullable: true),
                    CardProductSubType = table.Column<string>(type: "text", nullable: true),
                    LargeTicketIndicator = table.Column<string>(type: "text", nullable: true),
                    CardProcessingIndicator = table.Column<string>(type: "text", nullable: true),
                    CardFundSource = table.Column<string>(type: "text", nullable: true),
                    PanMinimumLength = table.Column<int>(type: "integer", nullable: false),
                    PanMaximumLength = table.Column<int>(type: "integer", nullable: false),
                    TokenBinIndicator = table.Column<string>(type: "text", nullable: true),
                    CardB2BProgram = table.Column<string>(type: "text", nullable: true),
                    CardDebitNetworkParticipant = table.Column<string>(type: "text", nullable: true),
                    CardBillingCurrency = table.Column<string>(type: "text", nullable: true),
                    CardMoneySendIndicator = table.Column<string>(type: "text", nullable: true),
                    CardMoneyTransferIndicator = table.Column<string>(type: "text", nullable: true),
                    CardOnlineGamblingIndicator = table.Column<string>(type: "text", nullable: true),
                    CardFastFunds = table.Column<string>(type: "text", nullable: true),
                    CardOriginalCreditIndicator = table.Column<string>(type: "text", nullable: true),
                    CardIssuerPhone = table.Column<string>(type: "text", nullable: true),
                    CardIssuerWebsite = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifyOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Bins", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Bins");
        }
    }
}
