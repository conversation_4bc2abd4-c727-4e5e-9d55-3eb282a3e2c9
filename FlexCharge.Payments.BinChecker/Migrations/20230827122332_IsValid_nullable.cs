using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.BinChecker.Migrations
{
    /// <inheritdoc />
    public partial class IsValidnullable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.AlterColumn<bool>(
            //     name: "<PERSON><PERSON>ali<PERSON>",
            //     table: "BinRanges",
            //     type: "boolean",
            //     nullable: true,
            //     oldClrType: typeof(bool),
            //     oldType: "boolean");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.AlterColumn<bool>(
            //     name: "<PERSON><PERSON>ali<PERSON>",
            //     table: "BinRanges",
            //     type: "boolean",
            //     nullable: false,
            //     defaultValue: false,
            //     oldClrType: typeof(bool),
            //     oldType: "boolean",
            //     oldNullable: true);
        }
    }
}
