using System.ComponentModel;
using FlexCharge.Payments.BinChecker.Entities;
using Microsoft.EntityFrameworkCore;
using NpgsqlTypes;

namespace FlexCharge.Payments.BinChecker.Services.BinNumberValidationServices.BINDB;

[Index(nameof(CardNumberRange))]
//[Index(nameof(BinNumberRange))]
//[Index(nameof(BinID), IsUnique = true)]// to avoid collisions 
public class BinRange : AuditableEntity
{
    public int BinID { get; set; }

    public NpgsqlRange<long> CardNumberRange { get; set; }

    //public NpgsqlRange<long> BinNumberRange { get; set; }
    public string CardIssueType { get; set; }
    public string CardBrand { get; set; }
    public string CardClass { get; set; }
    public string CardSubType { get; set; }
    public string CardIssuingCountry { get; set; }
    public string CardCountryCode { get; set; }
    public string CardIssuerRegulated { get; set; }
    public string CardIssuer { get; set; }
    public string CardIssueDetail { get; set; }

    public string CardReloadable { get; set; }

    // public string LowAccount { get; set; }
    // public string HighAccount { get; set; }
    public int Active { get; set; }
    public int BinLength { get; set; }
    public int PanLength { get; set; }
    public int Year { get; set; }
    public int Month { get; set; }
    public int Day { get; set; }
    public string FSAIndicator { get; set; }
    public string PrepaidIndicator { get; set; }
    public string CardProductSubType { get; set; }
    public string LargeTicketIndicator { get; set; }
    public string CardProcessingIndicator { get; set; }
    public string CardFundSource { get; set; }
    public int PanMinimumLength { get; set; }
    public int PanMaximumLength { get; set; }
    public string TokenBinIndicator { get; set; }
    public string CardB2BProgram { get; set; }
    public string CardDebitNetworkParticipant { get; set; }
    public string CardBillingCurrency { get; set; }
    public string CardMoneySendIndicator { get; set; }
    public string CardMoneyTransferIndicator { get; set; }
    public string CardOnlineGamblingIndicator { get; set; }
    public string CardFastFunds { get; set; }
    public string CardOriginalCreditIndicator { get; set; }
    public string CardIssuerPhone { get; set; }
    public string CardIssuerWebsite { get; set; }
    public string DataProvider { get; set; }
    public bool? IsValid { get; set; }
}