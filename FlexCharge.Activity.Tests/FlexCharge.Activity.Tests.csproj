<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DefineConstants>TRACE</DefineConstants>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DefineConstants>TRACE</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="6.0.16" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.1.0" />
        <PackageReference Include="Moq" Version="4.18.4" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="NUnit" Version="3.13.3" />
        <PackageReference Include="NUnit3TestAdapter" Version="4.2.1" />
        <PackageReference Include="NUnit.Analyzers" Version="3.3.0" />
        <PackageReference Include="coverlet.collector" Version="3.1.2" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\FlexCharge.Activity\FlexCharge.Activity.csproj" />
    </ItemGroup>

</Project>
