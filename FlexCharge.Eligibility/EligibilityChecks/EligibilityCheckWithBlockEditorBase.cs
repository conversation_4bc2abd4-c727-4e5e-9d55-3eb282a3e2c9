using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Shared.UIBuilder;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.WorkflowEngine.Workflows;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.EligibilityChecks;

public abstract class EligibilityCheckWithBlockEditorBase : EligibilityCheckBase
{
    private Dictionary<string, string> blockParameters { get; set; } = null;

    public sealed override Dictionary<string, string> GetBlockParameters()
    {
        EnsureBlockParametersExist();

        return blockParameters;
    }

    private void EnsureBlockParametersExist()
    {
        if (blockParameters == null)
            CreateDefaultBlockParameters();
    }

    protected virtual void CreateDefaultBlockParameters()
    {
        blockParameters = new();
    }

    protected void SetBlockParameter(string name, string value)
    {
        blockParameters[name] = value;
    }

    protected string? GetBlockParameter(string name)
    {
        return blockParameters.GetValueOrDefault(name);
    }

    public override async Task<BlockEditor> GetBlockEditorAsync(Dictionary<string, string> blockParameters,
        IServiceScopeFactory serviceScopeFactory)
    {
        Initialize(new EligibilityCheckContext(null, null, null, null, DataChangeFlags.None, false, false,
            null,
            UserExperienceFlowType.Frictionless,
            null, null), serviceScopeFactory, null);

        try
        {
            await InitializeScopeAsync();

            this.blockParameters = blockParameters;

            var editorUI = await CreateBlockEditorAsync(blockParameters);

            return new BlockEditor()
            {
                UIComponents = editorUI
            };
        }
        finally
        {
            await DisposeScopeAsync();
        }
    }

    public async Task<List<UIComponent>> CreateBlockEditorAsync(Dictionary<string, string> commandParameters)
    {
        //AddParameter("C_ID", CureId);

        IUIBuilder UI = new UIBuilder();

        using (UI.StartUIBuilder())
        {
            //using (StartChallengePopup(ShowCloseButton, ShowLogo))
            {
                using (UI.StartContent())
                using (UI.StartMainContainer())
                {
                    await CreateBlockEditorContentsAsync(UI);
                }
            }
        }

        return UI.UIComponents;
    }

    protected abstract Task CreateBlockEditorContentsAsync(IUIBuilder ui);


    #region Updating Editable Parameters

    public sealed override async Task<List<AnswerValidationError>> TryUpdateBlockEditableParametersAsync(
        IDictionary<Guid, string> answers,
        IServiceScopeFactory serviceScopeFactory)
    {
        Initialize(new EligibilityCheckContext(null, null, null, null, DataChangeFlags.None, false, false,
            null,
            UserExperienceFlowType.Frictionless,
            null, null), serviceScopeFactory, null);

        try
        {
            await InitializeScopeAsync();

            var validationErrors = await ValidateUserAnswersAsync(answers);

            if (validationErrors?.Any() != true)
            {
                EnsureBlockParametersExist();
                await UpdateEditableParametersAsync(answers);
            }

            return validationErrors;
        }
        finally
        {
            await DisposeScopeAsync();
        }
    }

    protected virtual async Task UpdateEditableParametersAsync(IDictionary<Guid, string> parameters)
    {
        await Task.CompletedTask;
    }

    public List<AnswerValidationError> AnswerValidationErrors { get; private set; }

    public async Task<List<AnswerValidationError>> ValidateUserAnswersAsync(IDictionary<Guid, string> parameters)
    {
        using var workspan = Workspan.Start<EligibilityCheckWithBlockEditorBase>();

        List<AnswerValidationError> validationErrors = new();

        try
        {
            AnswerValidationErrors = new List<AnswerValidationError>();

            await ValidateEditableParametersAsync(parameters);

            validationErrors.AddRange(AnswerValidationErrors);
        }
        catch (Exception ex)
        {
            workspan.RecordException(ex);
            validationErrors.Add(new AnswerValidationError(Guid.Empty, "General validation error"));
        }
        finally
        {
            AnswerValidationErrors = null;
        }

        return validationErrors; //General validation error
    }

    public virtual async Task ValidateEditableParametersAsync(IDictionary<Guid, string> parameters)
    {
    }


    protected void AddAnswerError(Guid inputId, string errorMessage)
    {
        AnswerValidationErrors.Add(new AnswerValidationError(inputId, errorMessage));
    }

    protected bool TryGetAnswer(IDictionary<Guid, string> parameters, Guid questionId,
        out string answer, bool canBeEmpty = false, bool canBeWhitespace = false)
    {
        bool answerIsProvided = false;

        if (parameters.TryGetValue(questionId, out answer))
        {
            if (canBeEmpty) answerIsProvided = true;
            else if (canBeWhitespace) answerIsProvided = answer != null;
            else answerIsProvided = !string.IsNullOrWhiteSpace(answer);
        }

        if (answerIsProvided)
        {
            Log($"Question: {questionId}. Answer: {answer}.");
        }
        else
        {
            Log($"Question: {questionId} is not answered.");
        }

        return answerIsProvided;
    }

    protected bool TryGetAnswerAsString(IDictionary<Guid, string> parameters,
        Guid questionId,
        out string? answer, bool normalizeString = true, bool canBeEmpty = false, bool canBeWhitespace = false)
    {
        answer = null;

        if (TryGetAnswer(parameters, questionId, out var answerString, canBeWhitespace))
        {
            answer = answerString.Trim().ToUpper();
            return true;
        }
        else return false;
    }

    protected bool TryGetAnswerAsInteger(IDictionary<Guid, string> parameters, Guid questionId,
        out int? answer)
    {
        answer = null;

        if (TryGetAnswer(parameters, questionId, out var answerString))
        {
            int answerValue;
            if (int.TryParse(answerString, out answerValue))
            {
                answer = answerValue;
                return true;
            }
            else
            {
                Log($"Question: {questionId}. Answer cannot be converted to Integer.");
                return false;
            }
        }
        else return false;
    }

    protected bool TryGetAnswerAsGuid(IDictionary<Guid, string> parameters, Guid questionId,
        out Guid answer)
    {
        answer = Guid.Empty;

        if (TryGetAnswer(parameters, questionId, out var answerString))
        {
            if (Guid.TryParse(answerString, out answer))
            {
                return true;
            }
            else
            {
                Log($"Question: {questionId}. Answer cannot be converted to Guid.");
                return false;
            }
        }
        else return false;
    }

    protected bool TryGetAnswerAsBoolean(IDictionary<Guid, string> parameters, Guid questionId,
        out bool? answer)
    {
        answer = null;

        if (TryGetAnswer(parameters, questionId, out var answerString))
        {
            if (bool.TryParse(answerString, out var answerValue))
            {
                answer = answerValue;
                return true;
            }
            else
            {
                Log($"Question: {questionId}. Answer cannot be converted to Boolean.");
                return false;
            }
        }
        else return false;
    }

    protected bool IsButtonPressed(IDictionary<Guid, string> parameters, Guid buttonId)
    {
        if (TryGetAnswerAsBoolean(parameters, buttonId, out var isButtonPressed))
        {
            if (isButtonPressed == true) return true;
        }

        return false;
    }

    #endregion

    #region Saved Input Values Support

    protected void SaveInputValues(Dictionary<Guid, string> inputValues)
    {
        SetBlockParameter("InputValues", JsonConvert.SerializeObject(inputValues));
    }

    protected InputValues LoadInputValues()
    {
        Dictionary<Guid, string> inputValues;

        var inputValuesJson = GetBlockParameter("InputValues");
        if (string.IsNullOrWhiteSpace(inputValuesJson))
        {
            inputValues = new Dictionary<Guid, string>();
        }
        else
        {
            inputValues = JsonConvert.DeserializeObject<Dictionary<Guid, string>>(inputValuesJson);
        }

        return new InputValues(inputValues);
    }

    protected class InputValues
    {
        private IDictionary<Guid, string> _inputValues;

        public InputValues(IDictionary<Guid, string> inputValues)
        {
            _inputValues = inputValues;
        }

        public bool TryGetInputValue(Guid questionId,
            out string answer, bool canBeEmpty = false, bool canBeWhitespace = false)
        {
            bool valueExists = false;

            if (_inputValues.TryGetValue(questionId, out answer))
            {
                if (canBeEmpty) valueExists = true;
                else if (canBeWhitespace) valueExists = answer != null;
                else valueExists = !string.IsNullOrWhiteSpace(answer);
            }

            return valueExists;
        }

        public string? StringOrDefault(
            Guid inputId, bool canBeEmpty = true, bool canBeWhitespace = true)
        {
            if (TryGetInputValue(inputId, out var valueString, canBeEmpty, canBeWhitespace))
                return valueString;

            return null;
        }

        public Guid? GuidOrDefault(Guid inputId)
        {
            var stringValue = StringOrDefault(inputId);

            if (stringValue != null && Guid.TryParse(stringValue, out var guidValue))
                return guidValue;

            return null;
        }

        public bool? BooleanOrDefault(Guid inputId)
        {
            var stringValue = StringOrDefault(inputId);
            if (stringValue != null && bool.TryParse(stringValue, out var booleanValue))
                return booleanValue;

            return null;
        }
    }

    #endregion
}