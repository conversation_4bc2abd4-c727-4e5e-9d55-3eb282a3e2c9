using System;
using FlexCharge.Eligibility.Entities;

namespace FlexCharge.Eligibility.EligibilityChecks;

public static class PartnerSpecificOverrides
{
    public static Guid DECLINE_DEFENSE_PARTNER = new Guid("a33ff484-a9ba-4212-81d1-311b185b7ae2"); // Decline Defense

    public static bool Disable_MIT_CascadingPayments(Merchant merchant)
    {
        // if (merchant.Pid == DECLINE_DEFENSE_PARTNER)
        //     return true;

        return false;
    }
}