using System.Threading.Tasks;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RuleEngine;
using FlexCharge.Eligibility.Services.Orders;
using FlexCharge.Eligibility.Services.Orders.OrderStates;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.PRODUCTION_TEST_BLOCKS;

public class E9997_PRODUCTION_TEST_ORDER_ExecuteCure_ApproveAfter : SetOfferStateCheckBase
{
    //!!! DO NOT REMOVE THIS LINE FOR PRODUCTION TEST BLOCKS!!!
    protected override bool IsProductionTestOrderBlock => true;
    public override bool IsProductionBlock => true;

    protected override async Task ExecuteBlockAsync()
    {
        if (Order.ExecutedCures.Count > 0)
        {
            // Approve after any cure execution

            Context.Stage3Result = RuleEngineResponse.Approve();

            await SetOrderStateAsync(StateOfOrder.EligibilityStage3Passed);
            await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_Success);
        }
        else
        {
            Context.Stage3Result = RuleEngineResponse.CureSet("C2002a");

            await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_Success);
            await SetOrderStateAsync(StateOfOrder.ConditionalInternal);
        }
    }
}