// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Text;
// using System.Threading.Tasks;
// using FlexCharge.Common.MassTransit;
// using FlexCharge.Common.Shared.UIBuilder;
// using FlexCharge.Contracts.Commands;
//
//
// namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.WorkflowTesting;
//
// public class E9805_TestPaymentActionBlock : EligibilityCheckWithBlockEditorBase
// {
//     public override bool IsProductionBlock => false;
//
//     private List<GetProvidersCommandResponse.Provider> _availableProviders;
//
//
//     protected override async Task ExecuteBlockAsync()
//     {
//     }
//
//
//     protected override void CreateDefaultBlockParameters()
//     {
//         base.CreateDefaultBlockParameters();
//
//         //SetBlockParameter("Description", "IF");
//         //SetBlockParameter("Description", "IF");
//     }
//
//     private Guid _paymentProviderInputId = new Guid("96b3051c-805c-4b3a-af74-e36e0e734976");
//     private Guid _paymentActionInputId = new Guid("b85a4b46-dc03-43ad-b9fc-b662982cf4c2");
//
//     protected override async Task CreateBlockEditorContentsAsync(IUIBuilder UI)
//     {
//         var current = LoadInputValues();
//
//         //AddTitle(Order, evaluateRequest, "The CVV you have provided seems incorrect.");
//
//         UI.HorizontalSeparator();
//
//         UI.SubTitle()
//             .Text(
//                 "Select payment provider and action to perform below");
//
//         UI.Text()
//             .Text("Payment provider:")
//             ;
//
//         await PopulateAvailablePaymentProvidersAsync();
//
//         UI.Select(_paymentProviderInputId)
//             //.PotentialAnswers(x => x.AddAnswers(PaymentProviderPossibleAnswers))
//             .PotentialAnswers(x =>
//                 x.AddAnswers(_availableProviders.Select(p =>
//                     new ValueAnswer<GetProvidersCommandResponse.Provider>(p.Id, p.Name, p))))
//             .Validations(v => v.Required("Provider is required"))
//             .DefaultValue(current.GuidOrDefault(_paymentProviderInputId))
//             ;
//
//
//         UI.Text()
//             .Text("Perform action(s):")
//             ;
//
//         // UI.Checkbox(new Guid())
//         //     .Text("AAA");
//
//         UI.Select(_paymentActionInputId)
//             //.Text("Property").Placeholder("Property")
//             .PotentialAnswers(x => x.AddAnswers(PaymentActionPossibleAnswers))
//             .Validations(v => v.Required("Action is required"))
//             .DefaultValue(current.GuidOrDefault(_paymentActionInputId))
//             ;
//     }
//
//     private async Task PopulateAvailablePaymentProvidersAsync()
//     {
//         if (_availableProviders != null)
//             return;
//
//         var getProvidersRequestClient =
//             CreateMassTransitRequestClient<GetProvidersCommand>();
//
//         var providers =
//             await getProvidersRequestClient.RunCommandAsync<GetProvidersCommand, GetProvidersCommandResponse>(
//                 new GetProvidersCommand());
//
//         _availableProviders = providers.Message.Providers;
//     }
//
//
//     public override async Task ValidateEditableParametersAsync(IDictionary<Guid, string> parameters)
//     {
//         await PopulateAvailablePaymentProvidersAsync();
//
//         TryGetAnswerAsGuid(parameters, _paymentProviderInputId, out var paymentProviderAnswer);
//         PaymentProviderAnswerHelper.TryToGetAnswer(paymentProviderAnswer, out var paymentProvider);
//
//         if (!_availableProviders.Any(p => p.Id == paymentProvider.Id))
//         {
//             AddAnswerError(_paymentProviderInputId, "Unknown payment provider");
//         }
//     }
//
//     protected override async Task UpdateEditableParametersAsync(IDictionary<Guid, string> parameters)
//     {
//         await PopulateAvailablePaymentProvidersAsync();
//
//         Dictionary<Guid, string> inputValues = new();
//
//         TryGetAnswerAsGuid(parameters, _paymentProviderInputId, out var paymentProviderAnswer);
//         PaymentProviderAnswerHelper.TryToGetAnswer(paymentProviderAnswer, out var paymentProvider);
//         inputValues[_paymentProviderInputId] = paymentProviderAnswer.ToString();
//
//         TryGetAnswerAsGuid(parameters, _paymentActionInputId, out var paymentActionAnswer);
//         PaymentActionAnswerHelper.TryToGetAnswer(paymentActionAnswer, out var paymentAction);
//         inputValues[_paymentActionInputId] = paymentActionAnswer.ToString();
//
//         var providerInfo = _availableProviders.Single(p => p.Id == paymentProviderAnswer);
//
//
//         SetBlockParameter("Provider", providerInfo.Name);
//         SetBlockParameter("Description", providerInfo.Description);
//         SetBlockParameter("Icon", providerInfo.ProviderLogo);
//
//         SaveInputValues(inputValues);
//     }
//
//     #region Payment Action Possible Answers
//
//     enum PaymentAction
//     {
//         Authorize,
//         Capture,
//         Void,
//         Debit,
//         Credit
//     }
//
//     static readonly ValueAnswer<PaymentAction>[] PaymentActionPossibleAnswers = new[]
//     {
//         new ValueAnswer<PaymentAction>(new Guid("7ae3ff66-224c-4537-adbc-395f8e7588da"), "Authorize",
//             PaymentAction.Authorize),
//         new ValueAnswer<PaymentAction>(new Guid("acdb0359-67d9-4e47-903c-c396a8727f71"), "Capture",
//             PaymentAction.Capture),
//         new ValueAnswer<PaymentAction>(new Guid("a8cf4d8a-9f86-4cff-a4bc-23c7244236b3"), "Void",
//             PaymentAction.Void),
//         new ValueAnswer<PaymentAction>(new Guid("3b43011c-e7b5-421e-94d0-5251247a352c"), "Debit",
//             PaymentAction.Debit),
//         new ValueAnswer<PaymentAction>(new Guid("7c3b4be5-24a8-428c-82fc-cc17a4597201"), "Credit",
//             PaymentAction.Credit),
//     };
//
//     private static SelectorAnswersHelper<ValueAnswer<PaymentAction>> PaymentActionAnswerHelper =
//         new(PaymentActionPossibleAnswers);
//
//     #endregion
//
//     #region Payment Provider Possible Answers
//
//     enum PaymentProviders
//     {
//         Stripe,
//         NMI,
//     }
//
//     static readonly ValueAnswer<PaymentProviders>[] PaymentProviderPossibleAnswers = new[]
//     {
//         new ValueAnswer<PaymentProviders>(new Guid("6418f350-2804-4c0d-842a-0486ca63b82d"), "Stripe",
//             PaymentProviders.Stripe),
//         new ValueAnswer<PaymentProviders>(new Guid("f23a94b8-e41b-407c-a4dd-0b36caa9f386"), "NMI",
//             PaymentProviders.NMI),
//     };
//
//     private static SelectorAnswersHelper<ValueAnswer<PaymentProviders>> PaymentProviderAnswerHelper =
//         new(PaymentProviderPossibleAnswers);
//
//     #endregion
// }

