using System;
using System.Threading.Tasks;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Services;
using FlexCharge.Eligibility.Services.FraudDeviceFingerprintingService;
using FlexCharge.Eligibility.Services.ThreeDSService;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations;

public class E3003_Try3DSFrictionlessAuthentication : EligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    protected override async Task<bool> CanBeExecutedAsync()
    {
        bool hasActualResult = HasStoredResult;

        if (hasActualResult)
            return false;

        if (Order.IsCIT && // Only for CIT for now
            Order.Is3dsEnabled(Merchant))
        {
            if (Order.Is3dsInformationOnlyEnabled(Merchant))
            {
                if (string.Compare(OrderPayload.PaymentMethod?.<PERSON><PERSON>rand, "Mastercard",
                        StringComparison.InvariantCultureIgnoreCase) != 0)
                {
                    // Only for Mastercard for now (TokenEx limitation)
                    return false;
                }
            }

            return await base.CanBeExecutedAsync();
        }

        return false;
    }

    protected override void ProcessDataChanges()
    {
        base.ProcessDataChanges();

        if (ChangedData.IsCavvVerificationDataChanged())
        {
            ResetStoredResult();
            Order.IsSCAAttempted = false;
        }
    }

    protected override async Task ExecuteBlockAsync()
    {
        Order.SCAAuthenticationToken = null;
        Order.SCAChallengeToken = null;
        Order.IsSCAAttempted = true;

        var informationalOnly3ds = Order.Is3dsInformationOnlyEnabled(Merchant);

        try
        {
            Log("InformationalOnly3ds: " + informationalOnly3ds);

            var authenticateResponse = await StartAuthenticateTransactionAsync(informationalOnly3ds);
            var scaAuthenticationToken = authenticateResponse.AuthenticationTransactionToken;

            #region Processing 3DS Authentication Result

            if (authenticateResponse.IsPending)
            {
                #region Observability

                Log(
                    $"3DS SCA: Authentication challenge pending. Token: {scaAuthenticationToken}. Informational only: {informationalOnly3ds}");
                await AddActivityAsync(EligibilityActivities.Frictionless3DS_ChallengePending,
                    data: authenticateResponse, meta: meta => meta
                        .SetValue("InformationalOnly", informationalOnly3ds));

                #endregion

                // Saving SCA token for Execute customer 3DS challenge
                Order.SCAChallengeToken = scaAuthenticationToken;
            }
            else if (authenticateResponse.Authenticated &&
                     TEMPORARY_TEST_HELPER.Is3DSDisabled(Context.OrderPayload) == false)
            {
                #region Observability

                Log(
                    $"3DS SCA: Authentication succeeded. Token: {scaAuthenticationToken}. Informational only: {informationalOnly3ds}");
                await AddActivityAsync(EligibilityActivities.Frictionless3DS_Succeeded,
                    data: authenticateResponse, meta: meta => meta
                        .SetValue("InformationalOnly", informationalOnly3ds));

                #endregion

                // Saving SCA token for Successful 3DS authentication
                Order.SCAAuthenticationToken = scaAuthenticationToken;

                // Storing all CAVV responses history (this can be a good indicator of low-risk customer)
                Order.AddSCAAuthenticationResponse(new SCAAuthenticationResponse(authenticateResponse.CAVV,
                    informationalOnlyAuthentication: false));
            }
            else if (authenticateResponse.InformationOnlyAuthenticated)
            {
                #region Observability

                Log(
                    $"3DS SCA: Informational-only authentication succeeded. Token: {scaAuthenticationToken}. Informational only: {informationalOnly3ds}");
                await AddActivityAsync(EligibilityActivities.Frictionless3DS_InformationalOnly_Succeeded,
                    data: authenticateResponse, meta: meta => meta
                        .SetValue("InformationalOnly", informationalOnly3ds));

                #endregion

                // Saving SCA token for Successful 3DS authentication
                Order.SCAAuthenticationToken = scaAuthenticationToken;

                // Storing all CAVV responses history (this can be a good indicator of low-risk customer)
                Order.AddSCAAuthenticationResponse(new SCAAuthenticationResponse(authenticateResponse.CAVV,
                    informationalOnlyAuthentication: true));
            }
            else
            {
                Log(
                    $"3DS SCA: Not authenticated. Token: {scaAuthenticationToken}. Informational only: {informationalOnly3ds}");
                await AddActivityAsync(!informationalOnly3ds
                        ? EligibilityActivities.Frictionless3DS_NotAuthenticated
                        : EligibilityActivities.Frictionless3DS_InformationalOnly_NotAuthenticated,
                    data: authenticateResponse, meta: meta => meta
                        .SetValue("InformationalOnly", informationalOnly3ds));

                Order.AddSCAAuthenticationResponse(new SCAAuthenticationResponse(null, informationalOnly3ds));
            }

            #endregion

            StoreResult(true); //To flag that 3DS already attempted with this payment instrument
        }
        catch (Exception ex)
        {
            Log($"3DS SCA: Error. {ex.Message}. Informational only: {informationalOnly3ds}");
            await AddActivityAsync(EligibilityErrorActivities.Frictionless3DS_Error, data: ex.Message,
                meta: meta => meta.SetValue("InformationalOnly", informationalOnly3ds));
        }
    }

    private async Task<Authenticate3DSCommandResponse> StartAuthenticateTransactionAsync(bool informationalOnly3ds)
    {
        using var serviceScope = ServiceScopeFactory.CreateScope();

        (string BrowserInfo, string IpAddress) deviceFingerprintingInfo;

        #region Getting Browser Info for 3DS Authentication

        var fraudDeviceFingerprintingService =
            serviceScope.ServiceProvider.GetRequiredService<IFraudDeviceFingerprintingService>();

        deviceFingerprintingInfo =
            await fraudDeviceFingerprintingService.GetDeviceFingerprintInfoAsync(Order.Mid, Order.SenseKey,
                Order.ExternalOrderReference);

        #endregion


        var authenticate3DsTestScenario = Authenticate3DSTestScenario.None;

        #region Tesing Support

        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
        {
            var securityCheckService = serviceScope.ServiceProvider.GetRequiredService<ISecurityCheckService>();
            if (securityCheckService.Is3DSFrictionlessTestCard(OrderPayload.PaymentMethod?.CardBinNumber,
                    OrderPayload.PaymentMethod?.CardLast4Digits))
            {
                authenticate3DsTestScenario = Authenticate3DSTestScenario.Authenticated;
            }
            else if (securityCheckService.Is3DSNotAuthenticatedTestCard(OrderPayload.PaymentMethod?.CardBinNumber,
                         OrderPayload.PaymentMethod?.CardLast4Digits))
            {
                authenticate3DsTestScenario = Authenticate3DSTestScenario.NotAuthenticated;
            }
            else if (securityCheckService.Is3DSChallengeTestCard(OrderPayload.PaymentMethod?.CardBinNumber,
                         OrderPayload.PaymentMethod?.CardLast4Digits))
            {
                authenticate3DsTestScenario = Authenticate3DSTestScenario.Challenge;
            }
            else if (OrderPayload.Payer?.Phone != null)
            {
                int switchStartIndex = OrderPayload.Payer.Phone.IndexOf(':');

                if (switchStartIndex > 0)
                {
                    string phoneNumberSwitch = OrderPayload.Payer.Phone.Substring(switchStartIndex + 1);
                    if (phoneNumberSwitch.Length == 2)
                    {
                        switch (phoneNumberSwitch)
                        {
                            case "AA":
                            case "AC":
                            case "AN":
                                authenticate3DsTestScenario = Authenticate3DSTestScenario.Authenticated;
                                break;
                            case "CA":
                            case "CC":
                            case "CN":
                                authenticate3DsTestScenario = Authenticate3DSTestScenario.Challenge;
                                break;
                            case "NA":
                            case "NC":
                            case "NN":
                                authenticate3DsTestScenario = Authenticate3DSTestScenario.NotAuthenticated;
                                break;
                        }
                    }
                }
            }

            //We need to specify a test scenario for Spreedly test 3DS
            if (authenticate3DsTestScenario == Authenticate3DSTestScenario.None)
            {
                authenticate3DsTestScenario = Authenticate3DSTestScenario.Authenticated;
            }

            Log($"3DS Test scenario: {authenticate3DsTestScenario}");
            await AddActivityAsync(EligibilityActivities.Frictionless3DS_TestScenarioStarted,
                data: authenticate3DsTestScenario.ToString());
        }

        #endregion

        var threeDSService = GetRequiredService<I3DSService>();

        var threeDSTransactionSenseKeyGuid = !string.IsNullOrWhiteSpace(Order.SenseKey)
            ? Guid.Parse(Order.SenseKey)
            : Guid.NewGuid(); // In case of optional SenseJS we do not have real sense key -> using random one

        var authenticate3DsCommandResponse = await threeDSService.AuthenticateAsync(Context.Merchant, Order.Id,
            threeDSTransactionSenseKeyGuid,
            informationalOnly: informationalOnly3ds,
            Order.PaymentInstrumentToken, Order.Amount, OrderPayload.Transaction.Currency,
            OrderPayload.Transaction.TimestampUtc,
            OrderPayload.IsRecurring == true, OrderPayload.ExpiryDateUtc, OrderPayload.Subscription?.Interval,
            OrderPayload.Payer?.Email, deviceFingerprintingInfo.IpAddress, OrderPayload.OrderId,
            deviceFingerprintingInfo.BrowserInfo, OrderPayload.IsMIT == false,
            Order.SenseKeyIsMatched,
            authenticate3DsTestScenario);


        return authenticate3DsCommandResponse;
    }
}