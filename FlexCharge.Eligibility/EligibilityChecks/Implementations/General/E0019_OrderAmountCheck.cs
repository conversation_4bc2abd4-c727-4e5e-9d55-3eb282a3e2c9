using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.EligibilityService;
using Microsoft.EntityFrameworkCore;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.General;

public class E0019_OrderAmountCheck : EligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    protected override bool CanProcessNotEligibleOffers => true;

    protected override async Task<bool> CanBeExecutedAsync()
    {
        return await base.CanBeExecutedAsync();
    }

    const int DEFAULT_ORDER_MIN_AMOUNT = 900;
    const int DEFAULT_ORDER_MAX_AMOUNT = 30000;

    protected override async Task ExecuteBlockAsync()
    {
        int orderMinAmount = Merchant.MinOrderAmount ?? DEFAULT_ORDER_MIN_AMOUNT;
        int orderMaxAmount = Merchant.MaxOrderAmount ?? DEFAULT_ORDER_MAX_AMOUNT;

        if (Order.Amount < orderMinAmount)
        {
            await AddActivityAsync(EligibilityActivities.OrderAmountIsTooLow_Decline);

            await StopOrderAsync();
        }
        else if (Order.Amount > orderMaxAmount)
        {
            await AddActivityAsync(EligibilityActivities.OrderAmountIsTooHigh_Decline);

            await StopOrderAsync();
        }
    }

    private async Task StopOrderAsync()
    {
        if (Order.IsMIT()) throw new NotEligibleCancelledException();
        throw new NotEligibleException();
    }
}