using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl.Controls;
using Microsoft.EntityFrameworkCore;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.General;

public class E0017_InitialTransactionChecks : EligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    protected override bool CanProcessNotEligibleOffers => true;

    OffSwitch _disable_CheckEmailOrPhoneIsPresent_CIT_OffSwitch;
    OffSwitch _disable_CheckEmailOrPhoneIsPresent_MIT_OffSwitch;

    public E0017_InitialTransactionChecks()
    {
        _disable_CheckEmailOrPhoneIsPresent_CIT_OffSwitch = AddExternalControl(
            new OffSwitch(new Guid("40524752-3ce5-4787-909e-12e09d429b44"),
                "Email/Phone Presence Check (CIT)",
                "Validation of whether an email or phone number is present during ingestion on CIT.",
                ExternalControls.PreAuthGroup.Name,
                ExternalControls.PreAuthGroup.TransactionChecks_CIT.Name,
                ExternalControlOwnerType.Block,
                nameof(E0017_InitialTransactionChecks)));

        _disable_CheckEmailOrPhoneIsPresent_MIT_OffSwitch = AddExternalControl(
            new OffSwitch(new Guid("515986db-a7cb-4d17-bb1b-5428847e2310"),
                "Email/Phone Presence Check (MIT)",
                "Validation of whether an email or phone number is present during ingestion on MIT.",
                ExternalControls.PreAuthGroup.Name,
                ExternalControls.PreAuthGroup.TransactionChecks_MIT.Name,
                ExternalControlOwnerType.Block,
                nameof(E0017_InitialTransactionChecks)));
    }

    protected override async Task<bool> CanBeExecutedAsync()
    {
        return await base.CanBeExecutedAsync();
    }

    protected override async Task ExecuteBlockAsync()
    {
        if (!await CheckIfTransactionIsDeclinedAsync(OrderPayload, Order.Id))
            await StopOrderAsync();

        if (!await CheckIfMerchantHasAccessToRequestAsync(Merchant, OrderPayload, Order.Id))
            await StopOrderAsync();

        bool skipEmailOrPhoneCheck = false;
        if (Order.IsCIT)
        {
            skipEmailOrPhoneCheck = await IsOffSwitchEngagedAsync(_disable_CheckEmailOrPhoneIsPresent_CIT_OffSwitch);
        }
        else // MIT
        {
            skipEmailOrPhoneCheck = await IsOffSwitchEngagedAsync(_disable_CheckEmailOrPhoneIsPresent_MIT_OffSwitch);
        }

        if (!skipEmailOrPhoneCheck)
        {
            if (!await CheckIfEmailOrPhoneIsPresentAsync(OrderPayload, Order.Id))
                await StopOrderAsync();
        }

        Log("Order is declined by processor");
    }

    private async Task StopOrderAsync()
    {
        if (Order.IsMIT())
        {
            throw new NotEligibleCancelledException();
        }

        throw new NotEligibleException();
    }

    private async Task<bool> CheckIfTransactionIsDeclinedAsync(EvaluateRequest request,
        Guid offerId)
    {
        if (request.IsDeclined != true)
        {
            LogError("Order is approved by processor -> can't be processed");

            await AddActivityAsync(EligibilityErrorActivities.InitialTransactionIsNotDeclined,
                data: request);

            return false;
        }
        else return true;
    }

    private async Task<bool> CheckIfMerchantHasAccessToRequestAsync(Merchant merchant, EvaluateRequest request,
        Guid offerId)
    {
        //Check if Merchant has Merchant-initiated transactions enabled
        if (request.IsMIT == true && merchant.IsMitEnabled == false)
        {
            Log("Merchant has no access to MIT requests");
            await AddActivityAsync(SecurityActivities.MerchantHasNoAccessToMITRequests);
            return false;
        }

        return true;
    }

    private async Task<bool> CheckIfEmailOrPhoneIsPresentAsync(EvaluateRequest request,
        Guid offerId)
    {
        if (string.IsNullOrWhiteSpace(request.Payer.Email) && string.IsNullOrWhiteSpace(request.Payer.Phone))
        {
            LogError("Payer Email or Phone must be provided -> can't be processed");

            await AddActivityAsync(EligibilityErrorActivities.NoPayerEmailOrPhoneProvided,
                data: request);

            return false;
        }
        else return true;
    }
}