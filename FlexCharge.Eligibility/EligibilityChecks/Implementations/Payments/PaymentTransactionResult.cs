using System;
using FlexCharge.Contracts.Commands;
using MassTransit;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations;

public class PaymentTransactionResult
{
    public PaymentTransactionResult()
    {
    }

    public PaymentTransactionResult(DebitPaymentCommandResponse response)
    {
        Success = response.Success;

        ProviderResponseCode = response.ProviderResponseCode;
        ResponseMessage = response.ResponseMessage;

        InternalResponseCode = response.InternalResponseCode;
        InternalResponseMessage = response.InternalResponseMessage;
        InternalResponseGroup = response.InternalResponseGroup;

        Provider = response.Provider;
        //BinNumber = response.BinNumber;
        TransactionId = response.TransactionId;
        CvvCode = response.CvvCode;
        AvsCode = response.AvsCode;
    }

    public PaymentTransactionResult(FullyAuthorizePaymentCommandResponse response)
    {
        Success = response.Success;

        ProviderResponseCode = response.ProviderResponseCode;
        ResponseMessage = response.ResponseMessage;

        InternalResponseCode = response.InternalResponseCode;
        InternalResponseMessage = response.InternalResponseMessage;
        InternalResponseGroup = response.InternalResponseGroup;

        Provider = response.Provider;
        BinNumber = response.BinNumber;
        TransactionId = response.TransactionId;
        CvvCode = response.CvvCode;
        AvsCode = response.AvsCode;
    }

    public PaymentTransactionResult(VerifyAmountPaymentCommandResponse response)
    {
        Success = response.Success;

        ProviderResponseCode = response.ProviderResponseCode;
        ResponseMessage = response.ResponseMessage;

        InternalResponseCode = response.InternalResponseCode;
        InternalResponseMessage = response.InternalResponseMessage;
        InternalResponseGroup = response.InternalResponseGroup;

        Provider = response.Provider;
        BinNumber = response.BinNumber;
        TransactionId = response.TransactionId;
        CvvCode = response.CvvCode;
        AvsCode = response.AvsCode;
    }

    public PaymentTransactionResult(ChargePaymentCommandResponse response)
    {
        Success = response.Success;

        ProviderResponseCode = response.ProviderResponseCode;
        ResponseMessage = response.ResponseMessage;

        InternalResponseCode = response.InternalResponseCode;
        InternalResponseMessage = response.InternalResponseMessage;
        InternalResponseGroup = response.InternalResponseGroup;

        Provider = response.Provider;
        //BinNumber = response.BinNumber;
        TransactionId = response.TransactionId;
        CvvCode = response.CvvCode;
        AvsCode = response.AvsCode;
    }

    public bool Success { get; set; }
    public string ProviderResponseCode { get; set; }
    public string ResponseMessage { get; set; }

    public string InternalResponseCode { get; set; }
    public string InternalResponseMessage { get; set; }
    public string InternalResponseGroup { get; set; }

    public string Provider { get; set; }
    public string BinNumber { get; set; }
    public Guid TransactionId { get; set; }

    public string AvsCode { get; set; }

    public string CvvCode { get; set; }
}