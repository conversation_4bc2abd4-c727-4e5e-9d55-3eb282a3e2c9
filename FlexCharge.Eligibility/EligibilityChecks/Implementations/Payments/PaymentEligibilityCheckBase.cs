using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Services.PaymentsService;
using FlexCharge.Eligibility.Services.RiskManagement;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations;

public abstract class PaymentEligibilityCheckBase : EligibilityCheckBase
{
    protected override async Task<bool> CanBeExecutedAsync()
    {
        using var workspan = Workspan.Start<PaymentEligibilityCheckBase>();

        if (Order.IsPaymentAuthorizedOrCaptured())
        {
            workspan.Log.Information("Order has an active authorized or captured payment");
            return false;
        }

        return await base.CanBeExecutedAsync();
    }

    protected void CreateAuthorizationAttemptedFingerprintActivity()
    {
        var fingerprintService = Context.FingerprintService;

#pragma warning disable CS4014
        Task.Run(async () => await
            fingerprintService.ProcessAuthorizationAttemptedAsync(Order)
                .ContinueWith(t =>
                {
                    using var localWorkspan = Workspan.Start<PaymentEligibilityCheckBase>();
                    if (t.Exception != null)
                    {
                        t.Exception?.Handle(ex =>
                        {
                            localWorkspan.RecordFatalException(ex);
                            return true;
                        });
                    }
                    else
                    {
                        localWorkspan.Log.Information("Authorization Attempted fingerprint processed successfully");
                    }
                }));
#pragma warning restore CS4014
    }

    protected async Task VoidAuthorizationIfAnyAsync(Order order, IPaymentsService paymentsService)
    {
        if (order.PaymentTransactionResult != null)
        {
            await paymentsService.VoidPaymentAuthorizationAndCancelACHDebitPaymentAsync(order);
        }
    }
}