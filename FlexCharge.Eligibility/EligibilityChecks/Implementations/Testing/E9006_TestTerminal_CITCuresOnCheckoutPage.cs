using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Eligibility.Enums;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.Testing;

public class E9006_TestTerminal_CITCuresOnCheckoutPage : EligibilityCheckBase
{
    public override bool IsProductionBlock => false;

    protected override async Task<bool> CanBeExecutedAsync()
    {
        if (Order.IsCIT && Context.Stage3Result.OrderState.IsConditional())
            return true;

        return await base.CanBeExecutedAsync();
    }

    protected override async Task ExecuteBlockAsync()
    {
        //Context.Stage3Result.CureSet = new List<string>() {"C2002a"};
        //Context.Stage3Result.CureSet = new List<string>() {"C1001"};
    }
}