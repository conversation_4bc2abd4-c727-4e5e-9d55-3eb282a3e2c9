using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Eligibility.Services.PCSMServices;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.Testing;

public class E9001_EnsureExperianCreditBuroCalled : EligibilityCheckBase
{
    public override bool IsProductionBlock => false;

    protected override async Task ExecuteBlockAsync()
    {
        Stage2Response stage2Result = Context.Stage2Result;

        if (!stage2Result.BureauProviders.Any(x => x.ToUpper() == "EXPERIAN"))
        {
            stage2Result.BureauProviders.Add("EXPERIAN");
        }

        await Task.CompletedTask;
    }
}