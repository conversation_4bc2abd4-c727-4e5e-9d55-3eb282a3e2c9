using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.RiskManagement.Fingerprinting;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.RiskManagement;

public class E2015_BlockNSFCodeForPrepaidCards : EligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    protected override async Task<bool> CanBeExecutedAsync()
    {
        return await base.CanBeExecutedAsync();
    }

    protected override async Task ExecuteBlockAsync()
    {
        if (OrderPayload.PaymentMethod?.CardType == "PREPAID")
        {
            var lastTransactionResult = Order.GetLastTransactionResultOrDefault();

            if (lastTransactionResult.NormalizedResponseCodeGroup == nameof(MappedResponseGroup.InsufficientFunds))
            {
                await AddDeclineActivityAsync(new DeclineDescription(
                    DeclineStage.PostAuth,
                    DeclineCategory.PaymentMethod,
                    new[] {DeclineRuleType.ResponseCode, DeclineRuleType.CardType},
                    DeclineRule.NSF_For_Prepaid_Card,
                    "Prepaid card with Insufficient funds. Declining order"));

                if (Order.IsMIT()) await StopOrderRetriesAsync();
                throw new NotEligibleException();
            }
        }
    }
}