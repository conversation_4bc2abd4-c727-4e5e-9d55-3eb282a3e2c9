namespace FlexCharge.Eligibility.EligibilityChecks.Implementations;

public static class ExternalControls
{
    #region Base Classes

    public abstract class ControlGroupBase
    {
        public abstract string Name { get; }
    }

    public record ControlSubGroup(string Name);

    #endregion

    public class PreAuthGroupDefinition : ControlGroupBase
    {
        public override string Name => "Pre Auth";

        public ControlSubGroup TransactionChecks_CIT = new("Transaction Checks (CIT)");
        public ControlSubGroup TransactionChecks_MIT = new("Transaction Checks (MIT)");

        //public ControlSubGroup BinCheck = new("Bin Check");
        public ControlSubGroup VelocityChecks_CIT = new("Velocity Checks (CIT)");
        public ControlSubGroup VelocityChecks_MIT = new("Velocity Checks (MIT)");
        public ControlSubGroup BlockLists_CIT = new("Block Lists (CIT)");
        public ControlSubGroup BlockLists_MIT = new("Block Lists (MIT)");
        public ControlSubGroup DisputeOptimization_CIT = new("Dispute Optimization CIT");
        public ControlSubGroup DisputeOptimization_MIT = new("Dispute Optimization MIT");
    }

    public static PreAuthGroupDefinition PreAuthGroup = new();


    public class RecyclingStrategyGroupDefinition : ControlGroupBase
    {
        public override string Name => "Recycling Strategy";

        public ControlSubGroup MaxRecycles = new("Max Recycles");
    }

    public static RecyclingStrategyGroupDefinition RecyclingStrategyGroup = new();

    public class CascadeStrategyGroupDefinition : ControlGroupBase
    {
        public override string Name => "Cascade Strategy";

        public ControlSubGroup CIT = new("CIT");
        public ControlSubGroup PaymentProcessorLoadBalancing = new("Payment Processor Load Balancing");
    }

    public static CascadeStrategyGroupDefinition CascadeStrategyGroup = new();
}