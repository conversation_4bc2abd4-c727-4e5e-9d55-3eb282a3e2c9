using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Services.PCSMServices;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.GhostMode;

public class E9905_EnsureExperianCreditBuroCalledInGhostMode : EligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    protected override async Task<bool> CanBeExecutedAsync()
    {
        if (!Context.IsGhostMode)
        {
            Log("This cure can't be executed in ghost mode");
            await AddActivityAsync(EligibilityActivities.RuleEngine_Stage3_OrderIsInGhostMode);

            return false; //do not execute in non-Ghost mode
        }
        else return await base.CanBeExecutedAsync();
    }

    protected override async Task ExecuteBlockAsync()
    {
        Stage2Response stage2Result = Context.Stage2Result;

        if (!stage2Result.BureauProviders.Any(x => x.ToUpper() == "EXPERIAN"))
        {
            stage2Result.BureauProviders.Add("EXPERIAN");
        }

        await Task.CompletedTask;
    }
}