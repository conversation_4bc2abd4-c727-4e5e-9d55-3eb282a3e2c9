using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.BinNumberValidationServices;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.TEMPORARY;

public class E9901_TemporarilyDisableNonVisaCards : EligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    protected override async Task ExecuteBlockAsync()
    {
        if (EnvironmentHelper.IsInProduction)
        {
            if (OrderPayload.PaymentMethod?.CardIssuer?.ToUpper() != "VISA")
            {
                throw new NotEligibleException();
            }
        }
    }
}