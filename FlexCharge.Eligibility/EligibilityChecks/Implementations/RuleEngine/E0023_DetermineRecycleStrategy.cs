using System;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Entities;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.RuleEngine;

public class E0023_DetermineRecycleStrategy : EligibilityCheckBase
{
    public override bool IsProductionBlock => true;

    protected override async Task<bool> CanBeExecutedAsync()
    {
        if (Order.IsMIT() &&
            Order.MaxAuthorizationAttempts ==
            null) // for now, max authorization attempts is defined by the response code of the very first authorization
        {
            return await base.CanBeExecutedAsync();
        }

        return false;
    }

    protected override async Task ExecuteBlockAsync()
    {
        // if (stage3Response.MaxRetryAttempts < 0)
        // {
        //     workspan.Log.Fatal("MaxRetryAttempts is less than or equal to 0.");
        //     throw new NotEligibleException();
        // }

        var lastPaymentOperationResponse = Order.GetLastTransactionResultOrDefault();

        // If we have authorization results - we can set the max attempts
        if (lastPaymentOperationResponse != null)
        {
            var recycleStrategyByErrorCodeMapper = GetRecycleStrategyMapper(Merchant.Pid);

            var recycleStrategy =
                await recycleStrategyByErrorCodeMapper.GetRecycleStrategyAsync(lastPaymentOperationResponse
                    .NormalizedResponseCode);

            #region Observability

            if (!recycleStrategy.IsValidStrategy)
            {
                LogFatalError(
                    $"No recycle strategy found for internal response code {lastPaymentOperationResponse.NormalizedResponseCode}. Mapper: {recycleStrategyByErrorCodeMapper.Name}");

                await AddActivityAsync(EligibilityErrorActivities.RecycleStrategy_Determination_Error, data:
                    recycleStrategy, meta: meta => meta
                        .SetValue("ResponseCode", lastPaymentOperationResponse.NormalizedResponseCode)
                        .SetValue("ResponseMessage", recycleStrategyByErrorCodeMapper.Name)
                        .SetValue("Mapper", recycleStrategyByErrorCodeMapper.Name)
                );
            }
            else
            {
                Log(
                    $"Recycle strategy found {JsonConvert.SerializeObject(recycleStrategy)} for code {lastPaymentOperationResponse.NormalizedResponseCode}. Mapper: {recycleStrategyByErrorCodeMapper.Name}");

                await AddActivityAsync(EligibilityActivities.RecycleStrategy_Determined_Sucessfully, data:
                    recycleStrategy, meta: meta => meta
                        .SetValue("MaxRetries", recycleStrategy.MaxRetires?.ToString())
                        .SetValue("ResponseCode", lastPaymentOperationResponse.NormalizedResponseCode)
                        .SetValue("ResponseMessage", recycleStrategyByErrorCodeMapper.Name)
                        .SetValue("Mapper", recycleStrategyByErrorCodeMapper.Name)
                );
            }

            #endregion

            Order.MaxAuthorizationAttempts = recycleStrategy.MaxRetires;
        }
    }

    private static ErrorCodeToRecycleStrategyMapperBase GetRecycleStrategyMapper(Guid? pid)
    {
        if (pid == PartnerSpecificOverrides.DECLINE_DEFENSE_PARTNER) // Partner is Decline Defence?
        {
            return new DeclineDefenceErrorCodeToRecycleStrategyMapper();
        }
        else return new FlexFactorErrorCodeToRecycleStrategyMapper();
    }
}