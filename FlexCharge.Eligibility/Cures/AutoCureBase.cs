using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.PaymentsService;
using FlexCharge.Eligibility.Services.ThreeDSService;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Polly;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Cures;

public abstract class AutoCureBase : CureBase, IAutoCure
{
    //private IRequestClient<VoidPaymentCommand> _voidPaymentRequest;
    public override bool IsAutoCure => true;

    public async Task<AutoCureResult> ExecuteAsync(EligibilityCheckContext context)
    {
        using var workspan = Workspan.Start<AutoCureBase>();

        try
        {
            Log($"CURE: {CureId} > Started"); //> {JsonConvert.SerializeObject(Payload)}");

            var order = context.Order;

            order.CurrentCureId = null; // we don't want to execute the same cure again

            var cureResult = await ExecuteCureAsync(context);
            order.Payload = JsonConvert.SerializeObject(context.OrderPayload); // payload can be updated

            Log($"CURE: {CureId} > Exited with result: {cureResult}"); //> {JsonConvert.SerializeObject(Payload)}");

            return cureResult;
        }
        catch (Exception e)
        {
            LogError(e, $"EXCEPTION: Cure {CureId} > Execution failed"); //> {JsonConvert.SerializeObject(Payload)}");
            throw;
        }
    }

    protected abstract Task<AutoCureResult> ExecuteCureAsync(EligibilityCheckContext context);

    #region Payment Support

    protected async Task<FullyAuthorizePaymentCommandResponse?> PerformFullAuthorization(
        EligibilityCheckContext context, int? gatewayOrder = null)
    {
        using var serviceScope = ServiceScopeFactory.CreateScope();

        var paymentsService = serviceScope.ServiceProvider.GetRequiredService<IPaymentsService>();

        //await PerformVoidAuthorizationIfAnyAsync(context.Order, paymentsService);
        if (Order.IsPaymentAuthorizedOrCaptured())
        {
            LogFatalError("A payment is already has already been processed. Cannot perform full authorization.");
            return null;
        }

        var threeDSResult = await GetCurrentThreeDsResult(serviceScope);

        var authorizationResponse =
            await paymentsService.AuthorizeAsync(
                context.Order.PaymentInstrumentToken,
                context.Order, context.Merchant, context.Site,
                context.PaymentInstrumentInformation, threeDSResult,
                gatewayOrder);
        return authorizationResponse;
    }

    // private async Task PerformVoidAuthorizationIfAnyAsync(Order order, IPaymentsService paymentsService)
    // {
    //     if (order.PaymentTransactionResult != null)
    //     {
    //         await paymentsService.VoidPaymentAuthorizationAndCancelACHDebitPaymentAsync(order);
    //     }
    // }

    private async Task<ThreeDSResult?> GetCurrentThreeDsResult(IServiceScope serviceScope)
    {
        ThreeDSResult threeDSResult = null;
        // Is there any successful 3DS result?
        if (Order.SCAAuthenticationToken != null)
        {
            using var dbContext = serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>();

            var threeDSecureTransaction = await dbContext.ThreeDSecureTransactions
                .Where(x => x.OrderId == Order.Id)
                .OrderByDescending(x => x.CreatedOn).FirstOrDefaultAsync();

            if (threeDSecureTransaction?.ThreeDSResults != null)
            {
                threeDSResult = JsonConvert.DeserializeObject<ThreeDSResult>(threeDSecureTransaction?.ThreeDSResults);
            }
        }

        return threeDSResult;
    }

    #endregion
}