using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.Entities;
using Polly;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C0001_ReprocessWithDifferentMIDSameMCC : AutoCureBase
{
    public override bool IsImplemented => false;

    protected override async Task<AutoCureResult> ExecuteCureAsync(EligibilityCheckContext context)
    {
#if DEBUG
        // var IsInProduction = RuntimeEnvironment.RuntimeEnvironment.IsInProduction;
        //
        // if (!IsInProduction &&
        //     evaluateRequest.PaymentMethod.ExpirationYear == 2033 &&
        //     evaluateRequest.PaymentMethod.ExpirationMonth == 10
        //    )
        // {
        //     return AutoCureResult.CURED;
        // }
#endif

        try
        {
            //Processing with default gateway (ours)
            var fullAuthorizationResponse = await PerformFullAuthorization(context, gatewayOrder: null);

            if (fullAuthorizationResponse?.Success == true) return AutoCureResult.CURED;
        }
        catch (Exception e)
        {
            LogError(e, "Full authorization error");
        }


        return AutoCureResult.NOT_CURED;
    }
}