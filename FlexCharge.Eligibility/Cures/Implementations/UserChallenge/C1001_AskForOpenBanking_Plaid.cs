using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Cures.Implementations.UserChallenge.Plaid;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Services.PaymentsService;
using FlexCharge.Common.Shared.UIBuilder;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Merchant = FlexCharge.Contracts.Merchant;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C1001_AskForOpenBanking_Plaid : OpenBankingOrNewPaymentInstrumentCureBase
{
    // Ask for a new credit card - verify check digit on the fly
    // Returns new payment instrument token

    private Guid _plaidQuestionId = new Guid("C1154BAA-91CD-4E23-A9D1-D0BE1BFA25FB");


    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        AddTitle(Order, evaluateRequest,
            "Unfortunately we could not solve the issue with your card. How about paying via your bank account?");

        UI.HorizontalSeparator();

        using (UI.StartFullRowList())
        {
            UI.ListItem()
                .Text(
                    "All you need to do is to link your bank account via the Plaid service.");

            UI.ListItem()
                .Text(
                    "Please follow the instructions and choose your main bank account, where you receive your salary / income.");

            UI.ListItem()
                .Text("You will be charged just the amount due.<br><b>No additional charges, no fees. Ever.</b>");
        }

        UI.FullRowInput(InputType.OpenBanking_Plaid, _plaidQuestionId)
            .Text("PublicToken");

        AddParameter("OpenBankingField", _plaidQuestionId.ToString());
        AddParameter("PlaidLinkedToken", PlaidLinkedToken);
        // UI.SmallText()
        //     .Create("We’ll only ask you for this information once and you can easily update in the Flex app later.");
    }


    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        //see: https://plaid.com/docs/link/web/#onsuccess
        if (challengeReEvaluateRequest.Answers.TryGetValue(_plaidQuestionId, out var serializedPlaidResponse) &&
            !string.IsNullOrWhiteSpace(serializedPlaidResponse))
        {
            Plaid.PlaidResponse plaidResponse;

            Log($"Plaid response received: {serializedPlaidResponse}");

            try
            {
                plaidResponse = JsonConvert.DeserializeObject<Plaid.PlaidResponse>(serializedPlaidResponse);

                //Displayed once a user has successfully linked their Item.
                var publicToken = plaidResponse.PublicToken;
                var accountId = plaidResponse.AccountId;
                var accountType = plaidResponse.Accounts.SingleOrDefault(x => x.Id == plaidResponse.AccountId)?.Subtype;
                var accountName = plaidResponse.Accounts.SingleOrDefault(x => x.Id == plaidResponse.AccountId)?.Name;
                var institutionName = plaidResponse.Institution?.Name;


                return await ProcessNewOpenBankingPublicTokenAsync(order, publicToken, accountId,
                    accountName,
                    accountType, institutionName);
            }
            catch (Exception ex)
            {
                Log($"Incorrect Plaid response received: {serializedPlaidResponse}");
                return UserChallengeResult.NOT_CURED;
            }
        }

        Log($"Plaid response not returned from widget");

        return UserChallengeResult.NOT_CURED;
    }
}