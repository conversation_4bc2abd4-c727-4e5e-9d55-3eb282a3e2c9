// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Threading.Tasks;
// using FlexCharge.Eligibility.Cures.Implementations;
// using FlexCharge.Eligibility.DTO;
// using FlexCharge.Eligibility.Entities;
// using FlexCharge.Eligibility.Services;
// using FlexCharge.Common.Shared.UIBuilder;
// using Microsoft.Extensions.DependencyInjection;
// using Newtonsoft.Json;
//
// namespace FlexCharge.Eligibility.Cures.Implementations;
//
// public class P0003_CongratulationsAfterCuresPopup : PopupUserChallengeBase
// {
//     public override bool IsImplemented => true;
//
//      public override bool ShowPopupCloseCounter => this.IsAutoConsentFlowConfiguration;
//
//
//     protected override void Create(Order order, EvaluateRequest evaluateRequest)
//     {
//         string merchantName = evaluateRequest.Merchant.Name;
//         string last4Digits = evaluateRequest.PaymentMethod.CardLast4Digits;
//
//         UI.Title()
//             .Text($"Your payment of ${Utils.Formatters.IntToDecimal(order.Amount)} has failed but no worries, your purchase was completed anyway!");
//
//         UI.HorizontalSeparator();
//
//
//         using (UI.StartFullRowList())
//         {
//             UI.ListItem()
//                 .Text(
//                     $"You will be charged ${Utils.Formatters.IntToDecimal(order.Amount)} when the issue with your payment gets resolved.");
//
//             UI.ListItem()
//                 .Text(
//                     $"You will be charged ${Utils.Formatters.IntToDecimal(order.Amount)} only.<br>" +
//                     $"<b>No interest, no fees. Ever.</b>");
//         }
//         
//         AddParameter("Redirect", "Approve");
//     }
//
//     protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
//         EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
//     {
//         return await CloseThisPopupAsync();
//     }
// }