// using System;
// using System.Threading.Tasks;
// using FlexCharge.Common.RuntimeEnvironment;
// using FlexCharge.Contracts.Commands;
// using FlexCharge.Eligibility.DTO;
// using FlexCharge.Eligibility.Entities;
// using FlexCharge.Eligibility.Entities.Extensions;
// using FlexCharge.Eligibility.Services.FraudDeviceFingerprintingService;
// using FlexCharge.Eligibility.Services.ThreeDSService;
// using FlexCharge.Common.Shared.UIBuilder;
// using Microsoft.Extensions.DependencyInjection;
//
// namespace FlexCharge.Eligibility.Cures.Implementations;
//
// public class C0002_3DS : UserChallengeCureBase
// {
//     public override bool IsImplemented => true;
//     protected override bool SupportsPreExecution => true;
//
//     private Guid _spreedly3dsQuestionId = new Guid("260907C2-F9F2-4A1C-99FD-4375F06170EA");
//     
//     protected override async Task<UserChallengeCurePreExecutionResult> TryPreExecuteCureAsync(Order order,
//         EvaluateRequest evaluateRequest)
//     {
//         if (order.SCAChallengeToken != null) //already have a pending challenge?
//         {
//             Log($"3DS SCA: Authentication challenge already pending. Token: {order.SCAChallengeToken}");
//             return UserChallengeCurePreExecutionResult.EXECUTE_USER_CHALLENGE;
//         }
//
//         var authenticate3DsCommandResponse = await StartAuthenticateTransactionAsync(order, evaluateRequest);
//         var scaAuthenticationToken = authenticate3DsCommandResponse.AuthenticationTransactionToken;
//         if (authenticate3DsCommandResponse.Success)
//         {
//             
//             Log($"3DS SCA: Authentication succeeded. Token: {scaAuthenticationToken}");
//
//             order.SCAAuthenticationToken = scaAuthenticationToken;
//             order.SCAChallengeToken = null;
//             
//             return UserChallengeCurePreExecutionResult.RE_EVALUATE;
//         }
//         else if (authenticate3DsCommandResponse.IsPending) // Execute customer 3DS challenge
//         {
//             Log($"3DS SCA: Authentication challenge pending. Token: {scaAuthenticationToken}");
//             order.SCAAuthenticationToken = null;
//             order.SCAChallengeToken = scaAuthenticationToken;
//             
//             return UserChallengeCurePreExecutionResult.EXECUTE_USER_CHALLENGE;
//         }
//         else
//         {
//             order.SCAAuthenticationToken = null;
//             order.SCAChallengeToken = null;
//                 
//             Log($"3DS SCA: Not authenticated. Token: {scaAuthenticationToken}");
//             return UserChallengeCurePreExecutionResult.NOT_APPLICABLE;
//         }
//     }
//
//    // If 3DS is successful the low fraud requirement is not longer required, i.e. the risk of fraud is automatically low even if fraud service providers say the contrary
//
//     protected override void Create(EvaluateRequest evaluateRequest)
//     {
//         AddTitle(Order, evaluateRequest, "We need an additional validation to process your payment. You will be now redirected to your bank.");
//
//         UI.HorizontalSeparator();
//
//         using (UI.StartFullRowList())
//         {
//             UI.ListItem()
//                 .Text(
//                     "You will be asked for a unique code that will be sent to you by your bank via SMS or via your banking app.");
//
//             UI.ListItem()
//                 .Text(
//                     "Please confirm the code you will receive and we will be able to complete the transaction.");
//         }
//         
//         UI.FullRowInput(InputType.Spreedly_3DS, _spreedly3dsQuestionId)
//             .Text("PublicToken");
//         
//         AddParameter("Token", Order.SCAChallengeToken);
//     }
//     
//     private async Task<Authenticate3DSCommandResponse> StartAuthenticateTransactionAsync(Order order,
//         EvaluateRequest evaluateRequest)
//     {
//         using var serviceScope = ServiceScopeFactory.CreateScope();
//
//         string browserInfo = "";
//
//         #region Getting Browser Info for Spreedly 3DS Authentication
//
//         var fraudDeviceFingerprintingService =
//             serviceScope.ServiceProvider.GetRequiredService<IFraudDeviceFingerprintingService>();
//
//         browserInfo = await fraudDeviceFingerprintingService.GetSpreedlyBrowserInfoAsync(order.Mid, order.SenseKey);
//        
//         #endregion
//         
//         
//         var authenticate3DsTestScenario = Authenticate3DSTestScenario.None;
//
//         #region Tesing Support
//
//         if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment && evaluateRequest.Payer?.Phone != null)
//         {
//             
//             int switchStartIndex = evaluateRequest.Payer.Phone.IndexOf(':');
//
//             if (switchStartIndex > 0)
//             {
//                 string phoneNumberSwitch = evaluateRequest.Payer.Phone.Substring(switchStartIndex + 1);
//                 if (phoneNumberSwitch.Length == 1 || phoneNumberSwitch.Length == 2)
//                 {
//                     switch (phoneNumberSwitch)
//                     {
//                         case "A":
//                         case "AA":
//                         case "CA":
//                         case "NA":
//                             authenticate3DsTestScenario = Authenticate3DSTestScenario.Authenticated;
//                             break;
//                         case "C":
//                         case "AC":
//                         case "CC":
//                         case "NC":
//                             authenticate3DsTestScenario = Authenticate3DSTestScenario.Challenge;
//                             break;
//                         case "N":
//                         case "AN":
//                         case "CN":
//                         case "NN":
//                             authenticate3DsTestScenario = Authenticate3DSTestScenario.NotAuthenticated;
//                             break;
//                     }
//                     
//                     
//                 }
//
//             }
//         }
//
//         #endregion
//         
//         var threeDSService = serviceScope.ServiceProvider.GetRequiredService<I3DSService>();
//         var authenticate3DsCommandResponse = await threeDSService.AuthenticateAsync(order.Mid, order.Id,
//             order.PaymentInstrumentToken, order.Amount,
//             evaluateRequest.Transaction.Currency,
//             evaluateRequest.Payer?.Email, null, evaluateRequest.OrderId,
//             browserInfo,
//             authenticate3DsTestScenario);
//
//
//         return authenticate3DsCommandResponse;
//     }
//
//     protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
//         EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
//     {
//         using var serviceScope = ServiceScopeFactory.CreateScope();
//
//         var threeDSService = serviceScope.ServiceProvider.GetRequiredService<I3DSService>();
//         var authenticate3DsCommandResponse = await threeDSService.GetAuthenticationStatusAsync(order.Mid, order.Id, order.SCAChallengeToken);
//         
//         //Storing all CAVV responses history (this can be a good indicator of low-risk customer)
//         order.AddSCAAuthenticationResponse(new SCAAuthenticationResponse(authenticate3DsCommandResponse.CAVV));
//         
//         if (authenticate3DsCommandResponse.Success)
//         {
//             order.SCAAuthenticationToken = new string(order.SCAChallengeToken);
//             order.SCAChallengeToken = null;
//             Log("3DS SCA Authentication Successful");
//             return UserChallengeResult.RE_EVALUATE;
//         }
//         else
//         {
//             order.SCAChallengeToken = null;
//             order.SCAAuthenticationToken = null;
//             Log("3DS SCA Authentication Unsuccessful");
//         }
//
//         return UserChallengeResult.NOT_CURED;
//     }
// }