using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Common.Shared.UIBuilder;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C9998_TestAskCardExpiryDateAgain : C0008_AskCardExpiryDateAgain
{
    // Ask for the Expiry Date again (the one provided is wrong)
    // Returns new payment instrument token

    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        //base.Create();

        AddTitle(Order, evaluateRequest, "Card expiry");

        UI.HorizontalSeparator();

        UI.SubTitle()
            .Text("Your card expiry date was incorrect - please, enter again:");

        UI.FullRowInput(InputType.Text, Guid.NewGuid())
            .Text("Expiry")
            .Placeholder("MM/YY")
            .Validations(v => v
                    .Required("Expiry date is required")
                    .Regex(@"^(0[1-9]|1[0-2])\/?([0-9]{4}|[0-9]{2})$", "Expiry date is incorrect")
                //.Regex(@"^(0[1-9]|1[0-2])\/([0-9]{4}|[0-9]{2})$", "Expiry date is incorrect") //forces /
            );
    }

    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        return UserChallengeResult.RE_EVALUATE;
    }
}