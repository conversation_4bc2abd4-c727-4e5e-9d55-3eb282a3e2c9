using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Common.Shared.UIBuilder;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C9991_TestSelectComponent : UserChallengeCureBase
{
    private Guid _SexAnswerId = new Guid("875807A3-6CC9-484C-9E6E-7215DD14A587");
    private Guid _MaleAnswerId = new Guid("E7FB3990-D365-4BC4-BE40-DF7E90648F37");
    private Guid _FemaleAnswerId = new Guid("72786C90-6A41-4D8C-A7BC-0FD7393654E5");

    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        using (UI.StartRow())
        {
            UI.Select(_SexAnswerId)
                .Text("Your Sex:")
                .PotentialAnswers(x => x
                    .AddAnswer(_MaleAnswerId, "Male", "I'm a male", "Male")
                    .AddAnswer(_FemaleAnswerId, "Female", "I'm a female", "Female"))
                .DefaultValue(_MaleAnswerId);
        }
    }

    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        if (challengeReEvaluateRequest.Answers.TryGetValue(_SexAnswerId, out var customerSexString) &&
            Guid.TryParse(customerSexString, out var customerSexAnswerId))
        {
            if (customerSexAnswerId == _MaleAnswerId) return UserChallengeResult.NOT_CURED;

            if (customerSexAnswerId == _FemaleAnswerId) return UserChallengeResult.RE_EVALUATE;
        }


        return UserChallengeResult.RE_EVALUATE;
    }
}