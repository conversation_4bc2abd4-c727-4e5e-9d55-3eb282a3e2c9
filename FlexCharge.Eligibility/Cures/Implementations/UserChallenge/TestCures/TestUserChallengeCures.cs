using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Common.Shared.UIBuilder;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C0099_TestUserChallengeCure : UserChallengeCureBase
{
    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        AddTitle(Order, evaluateRequest, "Title");

        UI.HorizontalSeparator();

        UI.SubTitle()
            .Text("Subtitle");


        UI.SmallText()
            .Text("Small text");


        UI.FullRowInput(InputType.Text, Guid.NewGuid())
            .Text("Text:").Placeholder("Write something...")
            .Description("Text description")
            .Tooltip("Text tooltip");

        UI.FullRowInput(InputType.Number, Guid.NewGuid())
            .Text("Number:")
            .Placeholder("CVV")
            .Attribute("maxLength", "4")
            .Description("Number description")
            .Tooltip("Enter your CVV")
            .DefaultValue(null);

        UI.FullRowInput(InputType.Text, Guid.NewGuid())
            .Text("SSN:").Placeholder("### ### ###")
            .Mask("000 000 000");

        UI.FullRowInput(InputType.Text, Guid.NewGuid())
            .Text("Date:").Placeholder("MM/YY")
            .Validations(v => v
                .Required("Date is required")
                .Regex("^(0[1-9]|1[0-2])\\/?([0-9]{4}|[0-9]{2})$", "Date is incorrect")
            );

        UI.FullRowInput(InputType.Text, Guid.NewGuid())
            .Text("Your Sex:")
            //potentialAnswersType: ChallengeAnswerType.Select,
            .PotentialAnswers(x => x
                .AddAnswer(Guid.NewGuid(), "Male", "I'm a male", "Male")
                .AddAnswer(Guid.NewGuid(), "Female", "I'm a female", "Female"));

        // FullRowInput(InputType.Zip, Guid.NewGuid())
        //     .Create("Zip:", description: "ZIP", placeholder: "Enter your zip", tooltip: "Postal code");

        //FullRowInput(InputType.DateOfBirth, Guid.NewGuid())
        //    .Create("Date Of Birth:", description: "Your date of birth", tooltip: "When you were born?");

        UI.Text()
            .Text("Some text");

        //.AddTranslation();
    }


    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        return UserChallengeResult.RE_EVALUATE;
    }
}

public class C0033_TestUserChallengeCure : UserChallengeCureBase
{
    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        UI.Input(InputType.Cvv, new Guid("4081AFC4-A4C8-43C8-AAC6-641D2B73B707"))
            .Text("Enter CVV:").Placeholder("CVV")
            .Attribute("maxLength", "4")
            .Description("Your CVV was incorrect - please, enter again")
            .Tooltip("Enter your CVV");
        /*,
            defaultValue: null,
            potentialAnswersType: ChallengeAnswerType.Text, potentialAnswers: x => x
                .AddAnswer(Guid.NewGuid(), "Option 1", "Description 1", "Tooltip 1")
                .AddAnswer(Guid.NewGuid(), "Option 2", "Description 2", "Tooltip 2")
                .AddAnswer(Guid.NewGuid(), "Option 3", "Description 3", "Tooltip 3"));*/
    }


    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        return UserChallengeResult.RE_EVALUATE;
    }
}