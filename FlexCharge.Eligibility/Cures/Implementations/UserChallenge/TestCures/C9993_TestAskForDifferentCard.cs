using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Common.Shared.UIBuilder;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C9993_TestAskForDifferentCard : C0003_AskForDifferentCard
{
    // Ask for a new credit card - verify check digit on the fly
    // Returns new payment instrument token

    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        //base.Create();

        AddTitle(Order, evaluateRequest, "Complete your account");

        UI.HorizontalSeparator();

        UI.SubTitle()
            .Text("Please share your billing details.");

        UI.SmallText()
            .Text("We’ll only ask you for this information once and you can easily update in the Flex app later.");

        using (UI.StartRow())
        {
            UI.SmallInput(InputType.Text, Guid.NewGuid())
                .Text("First Name")
                .Validations(v =>
                    v.Required("First Name is required"));

            UI.SmallInput(InputType.Text, Guid.NewGuid())
                .Text("Last Name")
                .Validations(v =>
                    v.Required("Last Name is required"));
        }

        UI.FullRowInput(InputType.Text, Guid.NewGuid())
            .Text("Card Number")
            .Validations(v => v
                .Required("Card number is required")
                .Regex(
                    "^(?:(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11}))$",
                    "Card number is incorrect",
                    stripWhitespaceBeforeValidation: true // to support masks with whitespace between number groups 
                ));


        using (UI.StartRow())
        {
            UI.SmallInput(InputType.Text, Guid.NewGuid())
                .Text("CVV")
                .Placeholder("CVV")
                .Attribute("maxLength", "4")
                .Validations(v => v
                    .Required("CVV is required")
                    .Regex("^[0-9]{3,4}$", "CVV is incorrect"));

            UI.SmallInput(InputType.Text, Guid.NewGuid())
                .Text("Expiry")
                .Placeholder("MM/YY")
                .Validations(v => v
                    .Required("Expiry date is required")
                    .Regex("^(0[1-9]|1[0-2])\\/?([0-9]{4}|[0-9]{2})$", "Expiry date is incorrect")
                );
        }
    }

    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        return UserChallengeResult.RE_EVALUATE;
    }
}