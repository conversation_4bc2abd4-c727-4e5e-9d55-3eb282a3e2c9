#if DEBUG
//#define TEST_FAKE_REQUEST
#endif

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Controllers;
using FlexCharge.Eligibility.DTO;
using MassTransit;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Consumers;

public class ExecuteInternalEvaluateRequestCommandConsumer :
    IdempotentCommandConsumer<ExecuteInternalEvaluateRequestCommand, ExecuteInternalEvaluateRequestCommandResponse>
{
    private readonly ITransmitAndEvaluateService _transmitAndEvaluateService;
    private readonly PostgreSQLDbContext _dbContext;

    public ExecuteInternalEvaluateRequestCommandConsumer(
        IServiceScopeFactory serviceScopeFactory,
        ITransmitAndEvaluateService transmitAndEvaluateService,
        PostgreSQLDbContext dbContext) : base(serviceScopeFactory)
    {
        _transmitAndEvaluateService = transmitAndEvaluateService;
        _dbContext = dbContext;
    }

    protected override async Task<ExecuteInternalEvaluateRequestCommandResponse> ConsumeCommand(
        ExecuteInternalEvaluateRequestCommand command, CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", command.MerchantId)
            .Baggage("Pid", command.PartnerId)
            .Baggage("RequestId", command.RequestId);

#if TEST_FAKE_REQUEST
        Task.Run(() =>
        {
            Task.Delay(5000);
            _publisher.Publish(new InternalEvaluateCompletedEvent()
            {
                InternalRequestId = message.InternalRequestId,
                Result = null
            });
        });
        
        return new InternalEvaluateRequestCommandResponse
        {
            Succeeded = true
        };
#else
        var evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(command.Request);
        var subscriptionDefinition = command.SubscriptionDefinition;
        try
        {
            var merchant = _dbContext.Merchants.FirstOrDefault(m => m.Mid == command.MerchantId);

            if (merchant == null)
            {
                Workspan.Log.Fatal("Merchant not found");

                return new ExecuteInternalEvaluateRequestCommandResponse
                {
                    RequestSucceeded = false
                };
            }

            var pid = command.PartnerId;
            if (command.PartnerIdUnknown)
            {
                pid = merchant.Pid;

                Workspan
                    .Baggage("Pid", command.PartnerId);
            }

            if (subscriptionDefinition != null)
            {
                // add grace period to the expiry date
                var orderExpiryDate = subscriptionDefinition.CurrentPeriodEnd;
                // default grace period is 5 days
                orderExpiryDate += TimeSpan.FromDays(merchant.DefaultSubscriptionGracePeriodDays ?? 5);
                evaluateRequest.ExpiryDateUtc = orderExpiryDate;
            }

            var orderMeta = CreateOrderMetadata(command, evaluateRequest);

            var result = await _transmitAndEvaluateService.EvaluateAsync(
                evaluateRequest, command.MerchantId,
                pid, command.RequestId,
                EvaluateProcessingType.Asynchronous, true,
                orderMeta,
                cancellationToken
            );

            BadRequestObjectResult badRequestObjectResult = result as BadRequestObjectResult;
            if (badRequestObjectResult != null)
            {
                //format errors from badRequestObjectResult
                var errorsDictionary = badRequestObjectResult.Value as Dictionary<string, object>;
                if (errorsDictionary != null)
                {
                    var errors = new Dictionary<string, List<string>>();
                    foreach (var error in errorsDictionary)
                    {
                        var errorList = error.Value as string[];
                        errors[error.Key] = errorList?.ToList();
                    }

                    return new ExecuteInternalEvaluateRequestCommandResponse
                    {
                        RequestSucceeded = true,
                        RequestStatusCode = 400,
                        ValidationErrors = errors
                    };
                }
            }

            return new ExecuteInternalEvaluateRequestCommandResponse
            {
                RequestSucceeded = true,
                RequestStatusCode = 200,
            };
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);

            return new ExecuteInternalEvaluateRequestCommandResponse
            {
                RequestSucceeded = false
            };
        }

#endif
    }

    private static Dictionary<string, string> CreateOrderMetadata(ExecuteInternalEvaluateRequestCommand command,
        EvaluateRequest evaluateRequest)
    {
        var orderMeta = new Dictionary<string, string>();

        #region Setting order meta data parameters, required for processing orders from external systems (e.g. Stripe App integration)

        // If order is processing on another system, it's account id in this system.
        // For example, when recycling Stripe MIT orders
        if (command.ExternalAccountId != null)
        {
            orderMeta.Add("ExternalAccountId", command.ExternalAccountId);
        }

        // If subscription is processing on another system, name of this system.
        if (command.ExternalSubscriptionService != null)
        {
            orderMeta.Add("ExternalSubscriptionService", command.ExternalSubscriptionService);
        }

        if (command.ForwardedPaymentInstrumentToCreateId != null)
        {
            orderMeta.Add("ForwardedPaymentInstrumentId",
                command.ForwardedPaymentInstrumentToCreateId.Value.ToString());
        }

        #endregion

        var externalSubscriptionId = evaluateRequest.Subscription?.SubscriptionId;

        if (externalSubscriptionId != null)
        {
            orderMeta.Add("ExternalSubscriptionId", externalSubscriptionId);
        }

        return orderMeta;
    }
}