using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Services.Orders;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Consumers;

public class PaymentDisputeCreatedEventConsumer : IdempotentEventConsumer<PaymentDisputeCreatedEvent>
{
    private readonly IOrderService _orderService;

    public PaymentDisputeCreatedEventConsumer(IServiceScopeFactory serviceScopeFactory,
        IOrderService orderService)
        : base(serviceScopeFactory)
    {
        _orderService = orderService;
    }

    protected override async Task ConsumeEvent(PaymentDisputeCreatedEvent message, CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId);

        try
        {
            await _orderService.BlockConsumerByOrderFingerprintsAsync(message.OrderId, message.Description);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e, "Unable to block by dispute");
        }
    }
}