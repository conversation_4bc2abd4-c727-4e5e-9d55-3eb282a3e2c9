using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Consumers;

public class MerchantCreatedEventConsumer : ConsumerBase<MerchantCreatedEvent>
{
    private readonly PostgreSQLDbContext _dbContext;


    public MerchantCreatedEventConsumer(PostgreSQLDbContext dbContext,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
    }


    protected override async Task ConsumeMessage(MerchantCreatedEvent message, CancellationToken cancellationToken)
    {
        try
        {
            var existingMerchant =
                await _dbContext.Merchants.SingleOrDefaultAsync(x => x.Mid == message.MerchantId);


            if (existingMerchant is null)
            {
                //_logger.LogInformation($"Correlation: {context.CorrelationId} > MerchantCreatedEvent > Merchant {message.MerchantId} NOT FOUND CREATING NEW");
                //workspan.Log.Information("Creating new Merchant: {MerchantId}", message.MerchantId);
                var merchant = new Merchant();

                MerchantUpdateHelper.UpdateMerchant(merchant, message);

                await _dbContext.Merchants.AddAsync(merchant);

                await _dbContext.SaveChangesAsync();

                Workspan.Log.Information("Merchant created: {Mid}", message.MerchantId);
            }
            else
            {
                Workspan.Log.Error(
                    "Can't create new Merchant - Merchant already exists. Merchant: {Mid}",
                    message.MerchantId);
            }
        }
        catch (Exception e)
        {
            Workspan.RecordException(e,
                "EXCEPTION: Correlation: {CorrelationId} > Failed MerchantCreatedEventConsumer > {MessageMerchantId}",
                Context.CorrelationId, message.MerchantId);
        }
    }
}