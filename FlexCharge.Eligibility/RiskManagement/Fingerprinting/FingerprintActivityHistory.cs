using System;
using System.Collections.Generic;
using System.Linq;
using FlexCharge.Eligibility.DistributedCache;

namespace FlexCharge.Eligibility.RiskManagement.Fingerprinting;

public record FingerprintActivityHistory
{
    /// <summary>
    /// To be able to migrate to the new history in the future
    /// </summary>
    public int Version => 1;
    public List<HistoricalFingerprintActivity> History { get; set; }
}

public static class FingerprintActivityHistoryExtensions
{
    public static long GetTotalAmount(this List<HistoricalFingerprintActivity> history, FingerprintActivityType activityType)
    {
        var activityTypeString = activityType.ToString();
        
        return history
            .Where(x=> x.ActivityType == activityTypeString)
            .Sum(x => (long)x.Amount);
    }

    public static long GetTotalAmount(this List<HistoricalFingerprintActivity> history,
        FingerprintActivityType activityType,
        DateTime lookupWindowStart)
    {
        var activityTypeString = activityType.ToString();
        
        long totalAmount = 0;
        for (var index = history.Count - 1; index >= 0; index--)
        {
            var historicalActivity = history[index];

            if (historicalActivity.Time < lookupWindowStart) break;

            if (historicalActivity.ActivityType == activityTypeString)
            {
                totalAmount += historicalActivity.Amount;
            }
        }

        return totalAmount;
    }
}