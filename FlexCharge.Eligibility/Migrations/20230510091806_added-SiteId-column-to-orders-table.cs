using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    /// <inheritdoc />
    public partial class addedSiteIdcolumntoorderstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Merchants_Mid",
                table: "Merchants");

            migrationBuilder.AddColumn<Guid>(
                name: "SiteId",
                table: "Orders",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Merchants_Mid",
                table: "Merchants",
                column: "Mid",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Merchants_Mid",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "SiteId",
                table: "Orders");

            migrationBuilder.CreateIndex(
                name: "IX_Merchants_Mid",
                table: "Merchants",
                column: "Mid");
        }
    }
}
