// <auto-generated />
using System;
using FlexCharge.Eligibility;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20240407110301_add-OrderSource-column-to-Orders-table")]
    partial class addOrderSourcecolumntoOrderstable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.ExecutedCure", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsUserChallenge")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("Payload")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("ExecutedCures");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.FingerprintActivity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActivityType")
                        .HasColumnType("text");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<Guid?>("ContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Fingerprint")
                        .HasColumnType("text");

                    b.Property<string>("FingerprintType")
                        .HasColumnType("text");

                    b.Property<bool?>("IsCIT")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Meta")
                        .HasColumnType("text");

                    b.Property<Guid?>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("PaymentInstrumentFingerprint")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ActivityType");

                    b.HasIndex("Fingerprint");

                    b.ToTable("FingerprintActivities");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.FingerprintStatistics", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("EvaluateMatches")
                        .HasColumnType("integer");

                    b.Property<string>("Fingerprint")
                        .HasColumnType("text");

                    b.Property<string>("FingerprintType")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Meta")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("TotalFullyPaidInOrders")
                        .HasColumnType("integer");

                    b.Property<int>("TotalMatches")
                        .HasColumnType("integer");

                    b.Property<int>("TotalOpenOffers")
                        .HasColumnType("integer");

                    b.Property<long>("TotalOpenOffersAmount")
                        .HasColumnType("bigint");

                    b.Property<int>("TotalOpenOrders")
                        .HasColumnType("integer");

                    b.Property<long>("TotalOpenOrdersAmount")
                        .HasColumnType("bigint");

                    b.Property<long>("TotalPaidInAmount")
                        .HasColumnType("bigint");

                    b.Property<int>("TotalWrittenOffOrders")
                        .HasColumnType("integer");

                    b.Property<long>("TotalWrittenOffOrdersAmount")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("Fingerprint");

                    b.ToTable("FingerprintStatistics");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.Merchant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AccountUpdaterEnabled")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("Aid")
                        .HasColumnType("uuid");

                    b.Property<bool>("CITConsumerNotificationsEnabled")
                        .HasColumnType("boolean");

                    b.Property<int?>("CITOrderExpirationInterval")
                        .HasColumnType("integer");

                    b.Property<bool>("CaptureRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerSupportEmail")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportLink")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportName")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportPhone")
                        .HasColumnType("text");

                    b.Property<string>("Dba")
                        .HasColumnType("text");

                    b.Property<decimal?>("DynamicAuthorizationDiscountThrottlePercentage")
                        .HasColumnType("numeric");

                    b.Property<bool>("EvaluateAsync")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("GhostModeThrottlePercentage")
                        .HasColumnType("numeric");

                    b.Property<bool>("Global3DSEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("IntegrationType")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsBureauActiveForProduction")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsLocked")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMitEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("MITConsumerCuresEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("MITConsumerNotificationsEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("MITGetSiteByDynamicDescriptorEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("MITOneTimeSubscriptionsEnabled")
                        .HasColumnType("boolean");

                    b.Property<int?>("MaxOrderAmount")
                        .HasColumnType("integer");

                    b.Property<string>("Mcc")
                        .HasColumnType("text");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<int?>("MinOrderAmount")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("OfferRequestsMaxPerDay")
                        .HasColumnType("integer");

                    b.Property<int?>("OfferRequestsRateLimitCount")
                        .HasColumnType("integer");

                    b.Property<int?>("OfferRequestsRateLimitIntervalMS")
                        .HasColumnType("integer");

                    b.Property<decimal?>("OfferRequestsThrottlePercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Offer_NSF_RequestsThrottle_Percentage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Orders_MaxMonthlyAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("Pid")
                        .HasColumnType("uuid");

                    b.Property<bool?>("SenseJSOptional")
                        .HasColumnType("boolean");

                    b.Property<bool>("SkipFraudCheck")
                        .HasColumnType("boolean");

                    b.Property<bool?>("UIWidgetOptional")
                        .HasColumnType("boolean");

                    b.Property<bool>("UseDefaultSiteForUnknownMerchantUrlsEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("VirtualTerminalEnabled")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("Mid")
                        .IsUnique();

                    b.ToTable("Merchants");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.MerchantDomain", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<string>("DescriptorCity")
                        .HasColumnType("text");

                    b.Property<string>("Domain")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedOn");

                    b.HasIndex("Domain");

                    b.HasIndex("MerchantId");

                    b.ToTable("MerchantDomains");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ACHToken")
                        .HasColumnType("text");

                    b.Property<string>("ACHTransactionId")
                        .HasColumnType("text");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("AuthorizationModifiers")
                        .HasColumnType("text");

                    b.Property<string>("BureauResults")
                        .HasColumnType("jsonb");

                    b.Property<DateTime?>("CompletedTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ConfirmationId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ConsentTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CumulativeFingerprintStatistics")
                        .HasColumnType("text");

                    b.Property<int>("CureSequenceCounter")
                        .HasColumnType("integer");

                    b.Property<string>("CurrentCureId")
                        .HasColumnType("text");

                    b.Property<string>("DesiredPaymentTransactionType")
                        .HasColumnType("text");

                    b.Property<int>("EvaluationCount")
                        .HasColumnType("integer");

                    b.Property<string>("EvaluationRequestType")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExternalOrderReference")
                        .HasColumnType("text");

                    b.Property<Guid?>("ExternalSubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<string>("FraudResults")
                        .HasColumnType("jsonb");

                    b.Property<string>("FullBureauResults")
                        .HasColumnType("jsonb");

                    b.Property<string>("FullFraudResults")
                        .HasColumnType("jsonb");

                    b.Property<bool>("IsBureauChecked")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCIT")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsExplicitConsent")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFraudChecked")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFullAuthorizationChecked")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFullyPaidIn")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGhostMode")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsInternalEvaluation")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsPaymentCaptured")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsProcessingByExternalRecyclingEngine")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsSCAAttempted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsZeroVerificationChecked")
                        .HasColumnType("boolean");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("text");

                    b.Property<int?>("MaxAuthorizationAttempts")
                        .HasColumnType("integer");

                    b.Property<string>("MerchantDescriptor")
                        .HasColumnType("text");

                    b.Property<string>("Meta")
                        .HasColumnType("text");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<string>("OrderSource")
                        .HasColumnType("text");

                    b.Property<string>("Origin")
                        .HasColumnType("text");

                    b.Property<string>("Payload")
                        .HasColumnType("text");

                    b.Property<string>("PaymentInstrumentFingerprint")
                        .HasColumnType("text");

                    b.Property<string>("PaymentInstrumentToken")
                        .HasColumnType("text");

                    b.Property<string>("PaymentProviderBlackList")
                        .HasColumnType("jsonb");

                    b.Property<string>("PaymentTransactionResult")
                        .HasColumnType("jsonb");

                    b.Property<string>("PaymentTransactionsResponses")
                        .HasColumnType("text");

                    b.Property<string>("PopupSequence")
                        .HasColumnType("text");

                    b.Property<string>("RecyclingEngine")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RetryScheduledTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SCAAuthenticationResponses")
                        .HasColumnType("text");

                    b.Property<string>("SCAAuthenticationToken")
                        .HasColumnType("text");

                    b.Property<string>("SCAChallengeToken")
                        .HasColumnType("text");

                    b.Property<string>("SenseKey")
                        .HasColumnType("text");

                    b.Property<bool>("SenseKeyIsMatched")
                        .HasColumnType("boolean");

                    b.Property<string>("SentNotifications")
                        .HasColumnType("text");

                    b.Property<Guid?>("SiteId")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<bool?>("StopRetries")
                        .HasColumnType("boolean");

                    b.Property<string>("StoredEligibilityCheckResults")
                        .HasColumnType("jsonb");

                    b.Property<string>("TransmitActivityHistory")
                        .HasColumnType("text");

                    b.Property<bool>("UseIFrames")
                        .HasColumnType("boolean");

                    b.Property<string>("ZeroVerificationResult")
                        .HasColumnType("jsonb");

                    b.HasKey("Id");

                    b.HasIndex("ConfirmationId")
                        .IsUnique();

                    b.HasIndex("CreatedOn");

                    b.HasIndex("EvaluationCount");

                    b.HasIndex("ExpiryDate");

                    b.HasIndex("IsCIT");

                    b.HasIndex("IsGhostMode");

                    b.HasIndex("ModifiedOn");

                    b.HasIndex("PaymentInstrumentFingerprint");

                    b.HasIndex("RetryScheduledTime");

                    b.HasIndex("SenseKey");

                    b.HasIndex("State");

                    b.HasIndex("StopRetries");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.ResponseCodes", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Messsage")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ResponseCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ResponseCodes");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.RiskManagementListItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Fingerprint")
                        .HasColumnType("text");

                    b.Property<string>("FingerprintType")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Meta")
                        .HasColumnType("text");

                    b.Property<Guid?>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Reason")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ValidUntil")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Fingerprint");

                    b.HasIndex("FingerprintType");

                    b.HasIndex("Meta");

                    b.HasIndex("Mid");

                    b.ToTable("RiskManagementList");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.Site", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerSupportEmail")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportLink")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportName")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportPhone")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<string>("DescriptorCity")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Mid");

                    b.ToTable("Sites");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.SiteTag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uuid");

                    b.Property<string>("Tag")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Tag");

                    b.ToTable("SiteTags");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.ThreeDSecureTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AcsURL")
                        .HasColumnType("text");

                    b.Property<string>("Authenticate_ThreeDSServerTransID")
                        .HasColumnType("text");

                    b.Property<string>("AuthenticationTransactionToken")
                        .HasColumnType("text");

                    b.Property<string>("AuthenticationType")
                        .HasColumnType("text");

                    b.Property<string>("ChallengeCompletionIndicator")
                        .HasColumnType("text");

                    b.Property<string>("Challenge_AcsTransID")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EncodedCReq")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("PreAuthenticate_ThreeDSServerTransID")
                        .HasColumnType("text");

                    b.Property<Guid>("SenseKey")
                        .HasColumnType("uuid");

                    b.Property<bool?>("ThreeDSMethodCompleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ThreeDSMethodNotificationURL")
                        .HasColumnType("text");

                    b.Property<string>("ThreeDSMethodURL")
                        .HasColumnType("text");

                    b.Property<string>("ThreeDSResults")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Authenticate_ThreeDSServerTransID")
                        .IsUnique();

                    b.HasIndex("AuthenticationTransactionToken")
                        .IsUnique();

                    b.HasIndex("PreAuthenticate_ThreeDSServerTransID")
                        .IsUnique();

                    b.HasIndex("SenseKey");

                    b.ToTable("ThreeDSecureTransactions");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.ExecutedCure", b =>
                {
                    b.HasOne("FlexCharge.Eligibility.Entities.Order", null)
                        .WithMany("ExecutedCures")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.Order", b =>
                {
                    b.Navigation("ExecutedCures");
                });
#pragma warning restore 612, 618
        }
    }
}
