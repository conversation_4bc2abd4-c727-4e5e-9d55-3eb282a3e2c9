using System;
using System.ComponentModel;
using FlexCharge.Eligibility.Enums;
using MassTransit.Futures.Contracts;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Eligibility.Entities;

[Index(nameof(Mid), IsUnique = true)]
public class Merchant : AuditableEntity
{
    public string Dba { get; set; }
    public System.Guid Mid { get; set; }

    public System.Guid? Pid { get; set; }
    public System.Guid? IntegrationPartnerId { get; set; }
    public System.Guid? Aid { get; set; }
    public bool IsActive { get; set; }
    public bool IsLocked { get; set; }
    public bool? Pcidss { get; set; }
    public string? IntegrationType { get; set; }

    public bool IsBureauActiveForProduction { get; set; }
    public bool IsMitEnabled { get; set; }
    public bool MITEvaluateAsync { get; set; }
    public bool CITEvaluateAsync { get; set; }

    public string? Mcc { get; set; }

    /// <summary>
    /// This risk level is used for all non-visa cards (Mastercard only for now)
    /// </summary>
    public int? RiskLevel { get; set; }

    public int? RiskLevel_Visa { get; set; }

    public int? RiskTier { get; set; }

    public decimal? GhostModeThrottlePercentage { get; set; }
    public decimal? OfferRequestsThrottlePercentage { get; set; }
    public int? OfferRequestsMaxPerDay { get; set; }
    public int? OfferRequestsRateLimitCount { get; set; }
    public int? OfferRequestsRateLimitIntervalMS { get; set; }

    public decimal? Offer_NSF_RequestsThrottle_Percentage { get; set; }
    public decimal? Orders_MaxMonthlyAmount { get; set; }

    public string CustomerSupportName { get; set; }
    public string CustomerSupportEmail { get; set; }
    public string CustomerSupportPhone { get; set; }
    public string CustomerSupportLink { get; set; }

    public bool SkipFraudCheck { get; set; } = false;
    public bool VirtualTerminalEnabled { get; set; } = false;
    public bool MITGetSiteByDynamicDescriptorEnabled { get; set; } = false;
    public bool MITConsumerNotificationsEnabled { get; set; } = false;
    public bool MITConsumerCuresEnabled { get; set; } = false;

    public bool RedactIpEnabled { get; set; }


    public bool? UIWidgetOptional { get; set; }
    public bool? SenseJSOptional { get; set; }

    public bool BillingInformationOptional { get; set; } = false;


    public bool CITConsumerNotificationsEnabled { get; set; }
    public bool UseDefaultSiteForUnknownMerchantUrlsEnabled { get; set; } = false;
    public bool MITOneTimeSubscriptionsEnabled { get; set; } = false;

    public bool AccountUpdaterEnabled { get; set; } = false;

    public int? MinOrderAmount { get; set; }
    public int? MaxOrderAmount { get; set; }

    public bool Global3DSEnabled { get; set; } = false;
    public bool InformationalOnly3DS { get; set; } = false;

    public bool SchemeTransactionIdEnabled { get; set; } = false;

    public bool PayerEnabled { get; set; } = false;

    public decimal? DynamicAuthorizationDiscountThrottlePercentage { get; set; }

    public bool CaptureRequired { get; set; } = false;


    /// <summary>
    /// CIT Order Expiration Interval in minutes. If null, the default value will be used.
    /// </summary>
    public int? CITOrderExpirationInterval { get; set; }

    public bool MITImmediateRetryEnabled { get; set; } = false;

    public bool IsAvsRequired { get; set; } = false;
    public bool IsCvvRequired { get; set; } = false;

    public bool EnableGlobalNetworkTokenization { get; set; } = false;

    public string SupportedCountries { get; set; }

    public string? WorkflowsExternalParameters { get; set; }

    public Guid? EligibilityStrategyWorkflowId { get; set; }
    public Guid? NotEligibleOrderProcessingWorkflowId { get; set; }
    public Guid? NotEligibleEverOrderProcessingWorkflowId { get; set; }
    public Guid? RecyclingStrategyWorkflowId { get; set; }

    /// <summary>
    /// Used for Stripe App
    /// </summary>
    public int? DefaultSubscriptionGracePeriodDays { get; set; }
}