using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Eligibility.Entities;

[Index(nameof(Mid))]
public class Site : AuditableEntity
{
    public Guid Mid { get; set; }

    public string Name { get; set; }
    public string Descriptor { get; set; }
    public string DescriptorCity { get; set; }
    
    public string CustomerSupportName { get; set; }
    public string CustomerSupportEmail { get; set; }
    public string CustomerSupportPhone { get; set; }
    public string CustomerSupportLink { get; set; }
    public string State { get; set; }
}