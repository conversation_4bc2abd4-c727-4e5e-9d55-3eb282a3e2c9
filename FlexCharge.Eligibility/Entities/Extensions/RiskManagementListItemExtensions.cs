using System;
using System.Collections.Generic;
using System.Reflection;
using FlexCharge.Eligibility.Entities.Enums;
using FlexCharge.Eligibility.RiskManagement.Fingerprinting;
using FlexCharge.Utils;

namespace FlexCharge.Eligibility.Entities;

public static class RiskManagementListItemExtensions
{
    private static Dictionary<string, string> _friendlyFingerprintTypeMap;

    static RiskManagementListItemExtensions()
    {
        _friendlyFingerprintTypeMap = new();
        foreach (var field in typeof(FingerprintType).GetFields(BindingFlags.Public | BindingFlags.Static))
        {
            var attribute = field.GetCustomAttribute<BlockableFingerprintAttribute>();
            if (attribute != null)
            {
                FingerprintType fingerprintType = (FingerprintType) Enum.Parse(typeof(FingerprintType), field.Name);
                _friendlyFingerprintTypeMap[fingerprintType.ToString()] = attribute.FriendlyName;
            }
        }
    }

    public static RiskManagementListItemType ToListType(this RiskManagementListItem item)
    {
        return EnumHelpers.ParseEnum<RiskManagementListItemType>(item.Type);
    }

    public static string GetHumanReadableFingerprintType(this RiskManagementListItem item)
    {
        if (_friendlyFingerprintTypeMap.TryGetValue(item.FingerprintType, out var friendlyName))
            return friendlyName;

        return item.FingerprintType;
    }

// public static RiskManagementListItemCreatedByRole ToCreatedByRole(this RiskManagementListItem item)
// {
//     return EnumHelpers.ParseEnum<RiskManagementListItemCreatedByRole>(item.CreatedByRole);
// }
}