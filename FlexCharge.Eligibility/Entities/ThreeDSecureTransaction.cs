using System;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Eligibility.Entities;

[Index(nameof(SenseKey))]
[Index(nameof(PreAuthenticate_ThreeDSServerTransID), IsUnique = true)]
[Index(nameof(Authenticate_ThreeDSServerTransID), IsUnique = true)]
[Index(nameof(AuthenticationTransactionToken), IsUnique = true)]
public class ThreeDSecureTransaction : AuditableEntity
{
    public Guid SenseKey { get; set; }
    public Guid OrderId { get; set; }
    public Guid Mid { get; set; }


    public string? PreAuthenticate_ThreeDSServerTransID { get; set; }
    public string? ThreeDSMethodURL { get; set; }
    public string? ThreeDSMethodNotificationURL { get; set; }

    public bool? ThreeDSMethodCompleted { get; set; }
    
    public string? Authenticate_ThreeDSServerTransID { get; set; }
    
    public string? Challenge_AcsTransID { get; set; }


    public string ThreeDSResults { get; set; }
    
    public string? AcsURL { get; set; }
    public string? AuthenticationType { get; set; }
    public string? EncodedCReq { get; set; }
    
    public string? ChallengeCompletionIndicator { get; set; }
    public string? AuthenticationTransactionToken { get; set; }
}
