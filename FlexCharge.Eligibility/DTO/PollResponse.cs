using System;
using FlexCharge.Common.Response;

namespace FlexCharge.Eligibility.DTO
{
    public class PollResponse : BaseResponse, IOptionalOrderSessionKey, IOptionalExternalOrderReference
    {
        public Guid? OrderSessionKey { get; set; }
        public string? ExternalOrderReference { get; set; }
        public bool UseIframes { get; set; }

        public string? MerchantName { get; set; }
        public int? Amount { get; set; }
        public string? CurrencySymbol { get; set; }

        public bool AcsServerAvailable { get; set; }
    }
}