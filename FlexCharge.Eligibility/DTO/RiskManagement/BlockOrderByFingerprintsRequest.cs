using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc.Validators;

namespace FlexCharge.Eligibility.DTO.RiskManagement;

public class BlockOrderByFingerprintsRequest
{
    [Required]
    public List<string> FingerprintTypesToBlock { get; set; }
    
    public DateTime? BlockUntil { get; set; }
    public string? BlockReason { get; set; }

    public bool? NetworkLevel { get; set; } = false;
}