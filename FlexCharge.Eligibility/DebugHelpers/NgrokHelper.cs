using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.DebugHelpers;

#if DEBUG
public static class NgrokHelper
{
    #region NGROK Models

    static class NGROK
    {
        public class Config
        {
            [JsonProperty("addr")] public string Addr { get; set; }

            [JsonProperty("inspect")] public bool Inspect { get; set; }
        }

        public class Conns
        {
            [JsonProperty("count")] public int Count { get; set; }

            [JsonProperty("gauge")] public int Gauge { get; set; }

            [JsonProperty("rate1")] public double Rate1 { get; set; }

            [JsonProperty("rate5")] public double Rate5 { get; set; }

            [JsonProperty("rate15")] public double Rate15 { get; set; }

            [JsonProperty("p50")] public long P50 { get; set; }

            [JsonProperty("p90")] public long P90 { get; set; }

            [JsonProperty("p95")] public long P95 { get; set; }

            [JsonProperty("p99")] public long P99 { get; set; }
        }

        public class Http
        {
            [JsonProperty("count")] public int Count { get; set; }

            [JsonProperty("rate1")] public double Rate1 { get; set; }

            [JsonProperty("rate5")] public double Rate5 { get; set; }

            [JsonProperty("rate15")] public double Rate15 { get; set; }

            [JsonProperty("p50")] public long P50 { get; set; }

            [JsonProperty("p90")] public double P90 { get; set; }

            [JsonProperty("p95")] public long P95 { get; set; }

            [JsonProperty("p99")] public long P99 { get; set; }
        }

        public class Metrics
        {
            [JsonProperty("conns")] public Conns Conns { get; set; }

            [JsonProperty("http")] public Http Http { get; set; }
        }

        public class Root
        {
            [JsonProperty("tunnels")] public List<Tunnel> Tunnels { get; set; }

            [JsonProperty("uri")] public string Uri { get; set; }
        }

        public class Tunnel
        {
            [JsonProperty("name")] public string Name { get; set; }

            [JsonProperty("ID")] public string ID { get; set; }

            [JsonProperty("uri")] public string Uri { get; set; }

            [JsonProperty("public_url")] public string PublicUrl { get; set; }

            [JsonProperty("proto")] public string Proto { get; set; }

            [JsonProperty("config")] public Config Config { get; set; }

            [JsonProperty("metrics")] public Metrics Metrics { get; set; }
        }
    }

    #endregion

    public static async Task<string> GetNgrokWebhooksDomainAsync()
    {
        int webhooks_local_port = 5040;
        string tunnelPublicUrl = await GetNgrokTunnelPublicUrlAsync(webhooks_local_port);
        if (tunnelPublicUrl == null)
        {
            try
            {
                string ngrokPath;
                if (System.IO.File.Exists("/usr/local/bin/ngrok"))
                {
                    ngrokPath = "/usr/local/bin/ngrok";
                }
                else if (System.IO.File.Exists("/opt/homebrew/bin/ngrok"))
                {
                    ngrokPath = "/opt/homebrew/bin/ngrok";
                }
                else throw new Exception("Ngrok not installed");

                System.Diagnostics.Process process = new();
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardInput = true;
                process.StartInfo.RedirectStandardError = true;
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.CreateNoWindow = true;
                process.StartInfo.FileName = ngrokPath;
                process.StartInfo.Arguments = $"http {webhooks_local_port}";
                process.Start();

                Thread.Sleep(1000);
            }
            catch (Exception e)
            {
            }
        }

        tunnelPublicUrl = await GetNgrokTunnelPublicUrlAsync(webhooks_local_port);

        if (tunnelPublicUrl == null)
            throw new Exception(
                $"Please, install and run ngrok: ngrok http {webhooks_local_port}. To install ngrok, run: brew install ngrok/ngrok/ngrok and then ngrok config add-authtoken <your_ngrok_token>");

        return tunnelPublicUrl;
        //
        //
        // return "https://db33-84-95-19-10.ngrok-free.app";
    }

    private static async Task<string> GetNgrokTunnelPublicUrlAsync(int webhooksLocalPort)
    {
        string publicUrl = null;

        try
        {
            using var httpClient = new HttpClient();
            var ngrokResponse = await httpClient.GetAsync("http://localhost:4040/api/tunnels");
            var tunnels = await ngrokResponse.Content.ReadAsStringAsync();

            var ngrokData = JsonConvert.DeserializeObject<NGROK.Root>(tunnels);
            foreach (var tunnel in ngrokData.Tunnels)
            {
                if (tunnel.Config.Addr.Contains(":" + webhooksLocalPort.ToString()))
                {
                    {
                        publicUrl = tunnel.PublicUrl;
                    }
                }
            }
        }
        catch (Exception e)
        {
        }

        return publicUrl;
    }
}
#endif