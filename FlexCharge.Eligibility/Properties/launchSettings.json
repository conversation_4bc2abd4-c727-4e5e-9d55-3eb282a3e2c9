{
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:53750",
      "sslPort": 44330
    }
  },
  "$schema": "http://json.schemastore.org/launchsettings.json",
  "profiles": {
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": false,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "DB_HOST": "localhost",
        "DB_PORT": "5432",
        "DB_DATABASE": "fc.eligibility",
        "DB_USERNAME": "eligibility-service-staging",
        "DB_PASSWORD": "11111",
        "WORKFLOW_ENGINE_DB_HOST": "localhost",
        "WORKFLOW_ENGINE_DB_PORT": "5432",
        "WORKFLOW_ENGINE_DB_DATABASE": "fc.workflow",
        "WORKFLOW_ENGINE_DB_USERNAME": "workflow-engine-service-staging",
        "WORKFLOW_ENGINE_DB_PASSWORD": "11111",

        "POSTGRES_DB_SENSEJS_HOST": "flexcharge-staging.ctwfhnhdjewu.us-east-1.rds.amazonaws.com",
        "POSTGRES_DB_SENSEJS_PORT": "5432",
        "POSTGRES_DB_SENSEJS": "fc_sensejs",
        "POSTGRES_USER_SENSEJS": "sensejs_service_staging",
        "POSTGRES_PASSWORD_SENSEJS": "g77EX+drNMEuJO7arsU=",
        
        "SNS_IAM_REGION": "us-east-1",
        "SNS_IAM_ACCESS_KEY": "********************",
        "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW",
        "FRAUD_SEON_BASE_URL": "https://api.seon.io/SeonRestService/fraud-api/v2",
        "BIN_SEON_BASE_URL": "https://api.seon.io/SeonRestService/bin-api/v1",
        "FRAUD_SEON_API_KEY": "cb04f241-92b5-41df-b0f2-6e2be07affec",

        "FRAUD_KOUNT_BASE_URL": "https://risk.test.kount.net",
        "FRAUD_KOUNT_MERCHANT_ID": "100488",
        "FRAUD_KOUNT_API_KEY": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIxMDA0ODgiLCJhdWQiOiJLb3VudC4xIiwiaWF0IjoxNjQ2MDM2ODMxLCJzY3AiOnsia2EiOm51bGwsImtjIjpudWxsLCJhcGkiOnRydWUsInJpcyI6dHJ1ZSwidGRzIjpudWxsfX0.zjHs4AZjaC_QZ44LxEJ7HIPiPrD0WAdXy9Du1yUH5FY",

        "EXPERIAN_SUBSCRIBER_CODE": "2937860",
        "EXPERIAN_USER_NAME": "<EMAIL>",
        "EXPERIAN_PASSWORD" : "Q1w2e3r4$@",
        "EXPERIAN_CLIENT_ID" : "kfW6MRYfcfrN9MADAqHth5MoyHEhGt5N",
        "EXPERIAN_CLIENT_SECRET": "vGrcO4LvDAljmLKc",
        "EXPERIAN_PERMISSIBLE_PURPOSE": "6H",

        "API_CLIENT_JWT_SIGNING_KEY": "$x*ztCv+B2cBme!@ASWIyFwFeLL2ust+@a"
      }
    },
    "FlexCharge.Eligibility": {
      "commandName": "Project",
      "launchBrowser": false,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "DB_HOST": "localhost",
        "DB_PORT": "5432",
        "DB_DATABASE": "fc.eligibility",
        "DB_USERNAME": "eligibility-service-staging",
        "DB_PASSWORD": "11111",
        "WORKFLOW_ENGINE_DB_HOST": "localhost",
        "WORKFLOW_ENGINE_DB_PORT": "5432",
        "WORKFLOW_ENGINE_DB_DATABASE": "fc.workflow",
        "WORKFLOW_ENGINE_DB_USERNAME": "workflow-engine-service-staging",
        "WORKFLOW_ENGINE_DB_PASSWORD": "11111",

        "POSTGRES_DB_SENSEJS_HOST": "flexcharge-staging.ctwfhnhdjewu.us-east-1.rds.amazonaws.com",
        "POSTGRES_DB_SENSEJS_PORT": "5432",
        "POSTGRES_DB_SENSEJS": "fc_sensejs",
        "POSTGRES_USER_SENSEJS": "sensejs_service_staging",
        "POSTGRES_PASSWORD_SENSEJS": "g77EX+drNMEuJO7arsU=",

        "SNS_IAM_REGION": "us-east-1",
        "SNS_IAM_ACCESS_KEY": "********************",
        "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW",
        "FRAUD_SEON_BASE_URL": "https://api.seon.io/SeonRestService/fraud-api/v2",
        "BIN_SEON_BASE_URL": "https://api.seon.io/SeonRestService/bin-api/v1",
        "FRAUD_SEON_API_KEY": "cb04f241-92b5-41df-b0f2-6e2be07affec",

        "FRAUD_KOUNT_BASE_URL": "https://risk.test.kount.net",
        "FRAUD_KOUNT_MERCHANT_ID": "100488",
        "FRAUD_KOUNT_API_KEY": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIxMDA0ODgiLCJhdWQiOiJLb3VudC4xIiwiaWF0IjoxNjQ2MDM2ODMxLCJzY3AiOnsia2EiOm51bGwsImtjIjpudWxsLCJhcGkiOnRydWUsInJpcyI6dHJ1ZSwidGRzIjpudWxsfX0.zjHs4AZjaC_QZ44LxEJ7HIPiPrD0WAdXy9Du1yUH5FY",

        "EXPERIAN_SUBSCRIBER_CODE": "2937860",
        "EXPERIAN_USER_NAME": "<EMAIL>",
        "EXPERIAN_PASSWORD" : "Q1w2e3r4$@",
        "EXPERIAN_CLIENT_ID" : "kfW6MRYfcfrN9MADAqHth5MoyHEhGt5N",
        "EXPERIAN_CLIENT_SECRET": "vGrcO4LvDAljmLKc",
        "EXPERIAN_PERMISSIBLE_PURPOSE": "6H",

        "API_CLIENT_JWT_SIGNING_KEY": "$x*ztCv+B2cBme!@ASWIyFwFeLL2ust+@a"
      },
      "applicationUrl": "https://localhost:5012;http://localhost:5013"
    },
    "Docker": {
      "commandName": "Docker",
      "launchBrowser": false,
      "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger",
      "publishAllPorts": true,
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "DB_HOST": "host.docker.internal",
        "DB_PORT": "5432",
        "DB_DATABASE": "fc.eligibility",
        "DB_USERNAME": "eligibility-service-staging",
        "DB_PASSWORD": "11111",
        "WORKFLOW_ENGINE_DB_HOST": "localhost",
        "WORKFLOW_ENGINE_DB_PORT": "5432",
        "WORKFLOW_ENGINE_DB_DATABASE": "fc.workflow",
        "WORKFLOW_ENGINE_DB_USERNAME": "workflow-engine-service-staging",
        "WORKFLOW_ENGINE_DB_PASSWORD": "11111",

        "POSTGRES_DB_SENSEJS_HOST": "flexcharge-staging.ctwfhnhdjewu.us-east-1.rds.amazonaws.com",
        "POSTGRES_DB_SENSEJS_PORT": "5432",
        "POSTGRES_DB_SENSEJS": "fc_sensejs",
        "POSTGRES_USER_SENSEJS": "sensejs_service_staging",
        "POSTGRES_PASSWORD_SENSEJS": "g77EX+drNMEuJO7arsU=",

        "SNS_IAM_REGION": "us-east-1",
        "SNS_IAM_ACCESS_KEY": "********************",
        "SNS_IAM_SECRET_KEY": "uPO48OgTDXReduu6gz7QzqtuQalSgLnkB6ZHDRtW",
        "FRAUD_SEON_BASE_URL": "https://api.seon.io/SeonRestService/fraud-api/v2",
        "BIN_SEON_BASE_URL": "https://api.seon.io/SeonRestService/bin-api/v1",
        "FRAUD_SEON_API_KEY": "cb04f241-92b5-41df-b0f2-6e2be07affec",

        "FRAUD_KOUNT_BASE_URL": "https://risk.test.kount.net",
        "FRAUD_KOUNT_MERCHANT_ID": "100488",
        "FRAUD_KOUNT_API_KEY": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIxMDA0ODgiLCJhdWQiOiJLb3VudC4xIiwiaWF0IjoxNjQ2MDM2ODMxLCJzY3AiOnsia2EiOm51bGwsImtjIjpudWxsLCJhcGkiOnRydWUsInJpcyI6dHJ1ZSwidGRzIjpudWxsfX0.zjHs4AZjaC_QZ44LxEJ7HIPiPrD0WAdXy9Du1yUH5FY",

        "EXPERIAN_SUBSCRIBER_CODE": "2937860",
        "EXPERIAN_USER_NAME": "<EMAIL>",
        "EXPERIAN_PASSWORD" : "Q1w2e3r4$@",
        "EXPERIAN_CLIENT_ID" : "kfW6MRYfcfrN9MADAqHth5MoyHEhGt5N",
        "EXPERIAN_CLIENT_SECRET": "vGrcO4LvDAljmLKc",
        "EXPERIAN_PERMISSIBLE_PURPOSE": "6H"
      }
      //"useSSL": true
    }
  }
}