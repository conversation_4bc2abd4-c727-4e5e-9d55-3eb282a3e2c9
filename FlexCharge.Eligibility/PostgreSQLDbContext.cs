using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using EntityFramework.Exceptions.PostgreSQL;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;

namespace FlexCharge.Eligibility
{
    public class PostgreSQLDbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public PostgreSQLDbContext(DbContextOptions<PostgreSQLDbContext> options,
            IHttpContextAccessor httpContextAccessor)
            : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        protected PostgreSQLDbContext(DbContextOptions options)
            : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseExceptionProcessor();
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Automatically apply all configurations (IEntityTypeConfiguration<> derived classes) from the current assembly
            modelBuilder.ApplyConfigurationsFromAssembly(this.GetType().Assembly);

            modelBuilder.Entity<Merchant>().Property<bool>("IsDeleted");
            modelBuilder.Entity<Merchant>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            // modelBuilder.Entity<Activity>().Property<bool>("IsDeleted");
            // modelBuilder.Entity<Activity>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<Order>().Property<bool>("IsDeleted");
            modelBuilder.Entity<Order>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<ResponseCodes>().Property<bool>("IsDeleted");
            modelBuilder.Entity<ResponseCodes>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<ExecutedCure>().Property<bool>("IsDeleted");
            modelBuilder.Entity<ExecutedCure>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<MerchantDomain>().Property<bool>("IsDeleted");
            modelBuilder.Entity<MerchantDomain>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);


            modelBuilder.Entity<FingerprintActivity>().Property<bool>("IsDeleted");
            modelBuilder.Entity<FingerprintActivity>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<FingerprintStatistics>().Property<bool>("IsDeleted");
            modelBuilder.Entity<FingerprintStatistics>()
                .HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<RiskManagementListItem>().Property<bool>("IsDeleted");
            modelBuilder.Entity<RiskManagementListItem>()
                .HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);


            // modelBuilder.Entity<CacheItem>().Property<bool>("IsDeleted");
            // modelBuilder.Entity<CacheItem>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<Site>().Property<bool>("IsDeleted");
            modelBuilder.Entity<Site>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<SiteTag>().Property<bool>("IsDeleted");
            modelBuilder.Entity<SiteTag>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<ThreeDSecureTransaction>().Property<bool>("IsDeleted");
            modelBuilder.Entity<ThreeDSecureTransaction>()
                .HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<Payer>().Property<bool>("IsDeleted");
            modelBuilder.Entity<Payer>()
                .HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            // modelBuilder.Entity<Merchant>()
            //     .Property(b => b.ThreeDSecureEnabled)
            //     .HasDefaultValue(true);
        }


        public DbSet<Order> Orders { get; set; }
        public DbSet<ResponseCodes> ResponseCodes { get; set; }
        public DbSet<Merchant> Merchants { get; set; }
        public DbSet<ExecutedCure> ExecutedCures { get; set; }

        public DbSet<Site> Sites { get; set; }
        public DbSet<SiteTag> SiteTags { get; set; }
        public DbSet<MerchantDomain> MerchantDomains { get; set; }

        public DbSet<FingerprintActivity> FingerprintActivities { get; set; }
        public DbSet<FingerprintStatistics> FingerprintStatistics { get; set; }
        public DbSet<RiskManagementListItem> RiskManagementList { get; set; }

        public DbSet<ThreeDSecureTransaction> ThreeDSecureTransactions { get; set; }

        public DbSet<Payer> Payers { get; set; }

        public override int SaveChanges()
        {
            UpdateSoftDeleteStatuses();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UpdateSoftDeleteStatuses();
            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        private void UpdateSoftDeleteStatuses()
        {
            var user = _httpContextAccessor?.HttpContext?.User?.Claims.SingleOrDefault(x =>
                x.Type == ClaimTypes.NameIdentifier);
            foreach (var entry in ChangeTracker.Entries())
            {
                switch (entry.State)
                {
                    case EntityState.Modified:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                entry.Property("ModifiedBy").CurrentValue = user.Value;
                        }


                        if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                            entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                        entry.CurrentValues["IsDeleted"] = false;
                        break;
                    }
                    case EntityState.Added:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "CreatedBy"))
                                entry.Property("CreatedBy").CurrentValue = user.Value;
                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                entry.Property("ModifiedBy").CurrentValue = user.Value;
                        }

                        if (entry.Properties.Any(o => o.Metadata.Name == "CreatedOn"))
                            entry.Property("CreatedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                        if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                            entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();

                        entry.CurrentValues["IsDeleted"] = false;
                        break;
                    }

                    case EntityState.Deleted:
                        entry.State = EntityState.Modified;
                        entry.CurrentValues["IsDeleted"] = true;
                        break;
                }
            }
        }
    }
}