using FlexCharge.Eligibility.DTO;

namespace FlexCharge.Eligibility;

public static class TEMPORARY_TEST_HELPER
{
    public static bool Is3DSDisabled(EvaluateRequest request)
    {
        return IsTestOrder(request);
    }

    public static bool IsFingerprintCheckDisabled(EvaluateRequest request)
    {
        return IsTestOrder(request);
    }

    public static bool IsDisputeOptimizationCheckDisabled(EvaluateRequest request)
    {
        return IsTestOrder(request);
    }

    public static bool IsTestOrder(EvaluateRequest request)
    {
        return request?.Payer?.Email?.ToLower() == "<EMAIL>";
    }

    public static bool IsMITNotificationsTestOrder(EvaluateRequest request)
    {
        var emailLowercase = request?.Payer?.Email?.ToLower();
        return emailLowercase != null &&
               (emailLowercase.EndsWith("@flex-charge.com") ||
                emailLowercase.EndsWith("@flexfactor.io") ||
                emailLowercase.EndsWith("@grsee.com"));
    }
}