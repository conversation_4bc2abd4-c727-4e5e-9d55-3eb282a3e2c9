using System;
using System.Collections.Generic;

namespace FlexCharge.Eligibility.Services.PCSMServices;

public class Stage3Request
{
    public IDictionary<string, string> FraudProviderResponses { get; set; }
    public IDictionary<string, int> BureauProviderResponses { get; set; }

    // public string ResponseCodeGroup { get; set; }
    // public string ResponseCodeNormalized { get; set; }

    public string ResponseCode { get; set; }
    public string ResponseCodeSource { get; set; }
    public string ZeroVerificationResponseCode { get; set; }
    public bool IsFullyAuthorized { get; set; }
    public bool IsCustomerInitiatedTransaction { get; set; }
    public bool IsRecurringPayments { get; set; }

    public string CvvResultCode { get; set; }
    public string CavvResultCode { get; set; }
    public string AvsResultCode { get; set; }

    public int TransmitApprovedTransactionCount { get; set; }

    public DateTime? OrderExpiryDateTime { get; set; }

    public string LastTransactionType { get; set; }
    public string NormalizedResponseCode { get; set; }
    public string NormalizedResponseMessage { get; set; }
    public string NormalizedResponseCodeGroup { get; set; }
}