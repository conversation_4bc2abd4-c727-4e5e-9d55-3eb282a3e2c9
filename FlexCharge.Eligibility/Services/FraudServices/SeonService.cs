using System;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Services.FraudServices.SeonModels;
using FlexCharge.Common.GeoServices;
using FlexCharge.Utils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace FlexCharge.Eligibility.Services.FraudServices;

public class SeonService : IFraudService
{
    private readonly HttpClient _httpClient;
    private readonly IGeoServices _geoServices;
    private readonly ILogger<SeonService> _logger;

    public SeonService(ILogger<SeonService> logger, HttpClient httpClient, IGeoServices geoServices)
    {
        _logger = logger;
        _httpClient = httpClient;
        _geoServices = geoServices;
    }

    public async Task<FraudResponse> ValidateAsync(FraudRequest fraudRequest)
    {
        using var workspan = Workspan.Start<SeonService>();

        var fraudResponse = new FraudResponse();
        try
        {
            var payerPhoneNumber = fraudRequest.Payer.Phone;
            if (string.IsNullOrWhiteSpace(payerPhoneNumber))
            {
                payerPhoneNumber = fraudRequest.BillingInformation.Phone;
                if (string.IsNullOrWhiteSpace(payerPhoneNumber))
                {
                    payerPhoneNumber = fraudRequest.ShippingInformation?.Phone;
                }
            }

            var seonRequest = new SeonFraudRequestModel()
            {
                config = new Config()
                {
                    device_fingerprinting =
                        true, //True to leverage the device fingerprint module

                    ip_api = true,
                    ip = new Ip() //Parameters to configure the IP API request
                    {
                        include = "flags,history,id",
                        version = "v1"
                    },

                    email_api = true,
                    email = new Email() //Parameters to configure the Email API request
                    {
                        include = "flags,history,id",
                        version = "v2"
                    },

                    phone_api = true,
                    phone = new Phone() //Parameters to configure the Phone API request
                    {
                        include = "flags,history,id",
                        version = "v1"
                    },

                    //response_fields // Lists all fields you want to receive in the API response. We recommend excluding the field to receive all the default fields and objects.
                },

                session = fraudRequest
                    .Session, //This field should contain the base64 encoded session data returned by the SDKs. This is only compatible with Fraud API v2.0.

                //device_id // This field should only be used if you have a device fingerprinting solution already, whose IDs you wish to link to SEON transactions or you want to build rules on those IDs. Thus, this field is a third_party_device_id.
                //action_type = "verification",

                #region Merchant

                merchant_id = fraudRequest.Mid.ToString(),
                //merchant_category //Category of the merchant. Example: digital_item_seller
                //merchant_created_at // Date the merchant was created on your site, using the UNIX time format and UTC time zone. Example: 1446370717 (Sun, 01 Nov 2015 09:38:37 +0000)
                //merchant_country // Country code for the merchant’s address. Uses the two-character ISO 3166-1 format. Examples: US, DE

                #endregion

                #region Transaction

                transaction_amount = (float) Formatters.IntToDecimal(fraudRequest.Transaction.Amount),
                transaction_currency = fraudRequest.Transaction.Currency,
                //transaction_type //Transaction type, according to your business. Examples: purchase, return
                //transaction_id

                #endregion

                email = fraudRequest.Payer.Email,
                email_domain = fraudRequest.Payer.Email?.Split('@').LastOrDefault() ?? null,
                ip = fraudRequest.Ip,


                #region Payment

                payment_mode = "card", // card, paypal, wire transfer, bitcoin, etc.
                payment_provider = fraudRequest.ResponseCodeSource,

                #region Card

                card_bin = fraudRequest.PaymentMethod.CardBinNumber,
                card_fullname =
                    fraudRequest.PaymentMethod
                        .HolderName, //User’s full name found on the card. Can be hashed in ASCII encoding as well (e.g. MD5, SHA-2 family).
                //card_hash = fraudRequest.PaymentMethod.Fingerprint, //Hash of the credit card used by the user in ASCII encoding. We recommend using HMAC-SHA256 or RSA-SHA256 and strictly advise against using a MD5 hash.
                card_expire = fraudRequest.PaymentMethod.ExpirationYear.ToString("D4") + '-' +
                              fraudRequest.PaymentMethod.ExpirationMonth
                                  .ToString("D2"), //Card’s expiration date. Example: 2022-01
                card_last = fraudRequest.PaymentMethod.CardLast4Digits, //The last 4 digits of the card number.
                avs_result =
                    fraudRequest
                        .AvsCode, //avs_result //Standard AVS codes returned to you by the credit card processor. Examples: N, A
                cvv_result =
                    fraudRequest.CvvCode ==
                    "M", //Result of the CVV check. Accepted values: true, false
                //status_3d = fraudRequest.CavvCode, //Status of 3D Secure Results. Example: Y, A, N

                #endregion

                #endregion

                #region Order

                items = fraudRequest.CartItems?.Select(x => new SeonCartItem
                {
                    //item_id, //Unique product identifier in your system.
                    item_quantity = x.ProductQuantity.ToString(),
                    item_name = x.ProductItem,
                    item_price = Formatters.LongToDecimal(x.ProductPrice).ToString(CultureInfo.InvariantCulture),
                    item_category = x.ProductType,
                    item_custom_fields = new ItemCustomFields()
                }).ToList(),

                #endregion

                #region User

                user_fullname = fraudRequest.PaymentMethod.HolderName,
                user_dob = fraudRequest.Payer.Birthdate?.ToString("yyyy-MM-dd"), //fraudRequest.Payer.Birthdate,
                phone_number = payerPhoneNumber != null ? CorrectPhoneForSeon(payerPhoneNumber, fraudRequest) : null,
                //user_id
                //user_created
                //user_country
                //user_city
                //user_region
                //user_zip
                //user_street
                //user_street2

                #endregion
            };

            if (fraudRequest.ShippingInformation != null)
            {
                #region Shipping Information

                seonRequest.shipping_country = fraudRequest.ShippingInformation != null
                    ? fraudRequest.ShippingInformation
                        .CountryCode
                    : null; //Country code for the user’s billing address. Uses the two-character ISO 3166-1 format. Examples: US, DE
                seonRequest.shipping_city = fraudRequest.ShippingInformation.City;
                seonRequest.shipping_region =
                    fraudRequest.ShippingInformation
                        .State;
                seonRequest.shipping_zip = fraudRequest.ShippingInformation.Zipcode;
                seonRequest.shipping_street =
                    fraudRequest.ShippingInformation.AddressLine1; //User’s shipping street address line 1
                seonRequest.shipping_street2 = fraudRequest.ShippingInformation.AddressLine2;
                seonRequest.shipping_phone = CorrectPhoneForSeon(fraudRequest.ShippingInformation.Phone, fraudRequest);
                seonRequest.shipping_fullname = fraudRequest.ShippingInformation.FirstName + " " +
                                                fraudRequest.ShippingInformation.LastName;
                //shipping_method //The type of shipping method used by the customer. Examples: standard, UPS, FedEx

                #endregion
            }

            if (fraudRequest.BillingInformation != null)
            {
                #region Billing Information

                seonRequest.billing_country =
                    fraudRequest.BillingInformation
                        .CountryCode; //Country code for the user’s billing address. Uses the two-character ISO 3166-1 format. Examples: US, DE
                seonRequest.billing_city = fraudRequest.BillingInformation.City;
                seonRequest.billing_region =
                    fraudRequest.BillingInformation
                        .State;
                seonRequest.billing_zip =
                    fraudRequest.BillingInformation
                        .Zipcode; //Zip/postal code of a user’s billing address. Examples: 10005, PH1 1EU
                seonRequest.billing_street =
                    fraudRequest.BillingInformation.AddressLine1; //User’s billing street address line 1
                seonRequest.billing_street2 = fraudRequest.BillingInformation.AddressLine2;
                seonRequest.billing_phone = CorrectPhoneForSeon(fraudRequest.BillingInformation.Phone, fraudRequest);

                #endregion
            }

            seonRequest.custom_fields = new CustomFields()
            {
                order_id = fraudRequest.OrderId.ToString(),
                external_order_id = fraudRequest.ExternalOrderReference
            };

            var message = new HttpRequestMessage
            {
                Content =
                    new StringContent(JsonConvert.SerializeObject(seonRequest), Encoding.UTF8, "application/json"),
                Method = HttpMethod.Post,
                RequestUri = new Uri(Environment.GetEnvironmentVariable("FRAUD_SEON_BASE_URL")),
            };

            message.Headers.Add("X-API-KEY", Environment.GetEnvironmentVariable("FRAUD_SEON_API_KEY"));

            var response = await _httpClient.SendAsync(message);
            //_logger.LogInformation($"Seon response: {JsonConvert.SerializeObject(response)}");

            if (!response.IsSuccessStatusCode)
            {
                String errorResponseString = "";
                try
                {
                    errorResponseString = await response.Content.ReadAsStringAsync();
                }
                catch
                {
                }

                workspan.Log.Error("SeonService > Validate > {response}/r/nContents: {contents}", response.ToString(),
                    errorResponseString);
                fraudResponse.AddError(response.ReasonPhrase, response.StatusCode.ToString(), friendly: false);
                return fraudResponse;
            }

            var responseString = await response.Content.ReadAsStringAsync();
            workspan.Log.Information("SeonService > Validate > Response => {response}", responseString);

            //var serializedResponse = await response.Content.ReadFromJsonAsync<SeonModels.SeonResponseModel>();
            var serializedResponse = JsonSerializer.Deserialize<SeonModels.SeonResponseModel>(responseString);

            fraudResponse.Response = serializedResponse;

            //map response state no need to
            if (serializedResponse?.data.state == "APPROVE")
                fraudResponse.Result = FraudResponseState.APPROVE;
            else if (serializedResponse?.data.state == "DECLINE")
                fraudResponse.Result = FraudResponseState.DECLINE;
            else
                fraudResponse.Result = FraudResponseState.REVIEW;

            fraudResponse.Scores.Add("FraudScore", serializedResponse.data.fraud_score);

            return fraudResponse;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, $"EXCEPTION: SeonService > Validate > {JsonConvert.SerializeObject(fraudRequest)}");
            throw;
        }
    }

    private string? CorrectPhoneForSeon(string phoneNumber, FraudRequest fraudRequest)
    {
        //Registered phone number of a user, including country code. Cannot include spaces or hyphens, the + sign is optional. Maximum length is 19 characters. Example: 36704316088

        if (phoneNumber == null) return null;

        var phone = new string(phoneNumber.Where(ch => char.IsDigit(ch)).ToArray());

        if (phone.Length <= 19)
        {
            //Adding 1 (+1) for US phones, if missing
            if (phone.Length == 10 && phone[0] != '1' && fraudRequest.BillingInformation.CountryCode == "US")
            {
                phone = "1" + phone;
            }

            return phone;
        }

        return null;
    }
}