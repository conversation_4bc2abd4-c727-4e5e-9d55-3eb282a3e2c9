using System.Collections.Generic;

namespace FlexCharge.Eligibility.Services.FraudServices.SeonModels;

public class SeonResponseModel
{
    public bool success { get; set; }
    public object error { get; set; }
    public SeonData data { get; set; }
}

public class SeonData
{
    public string id { get; set; }
    public string state { get; set; }
    public double fraud_score { get; set; }
    public BinDetails bin_details { get; set; }
    public string version { get; set; }
    public List<AppliedRule> applied_rules { get; set; }
    public DeviceDetails device_details { get; set; }
    public int calculation_time { get; set; }
    public int seon_id { get; set; }
    public IpDetails ip_details { get; set; }
    public EmailDetails email_details { get; set; }
    public PhoneDetails phone_details { get; set; }
}

public class DeviceDetails
    {
        public string session_id { get; set; }
        public string type { get; set; }
        public string dns_ip { get; set; }
        public string dns_ip_country { get; set; }
        public string dns_ip_isp { get; set; }
        public string source { get; set; }
        public string cookie_hash { get; set; }
        public string region_timezone { get; set; }
        public bool? cookie_enabled { get; set; }
        public string os { get; set; }
        public bool? flash_enabled { get; set; }
        public bool? java_enabled { get; set; }
        public string device_type { get; set; }
        public bool? @private { get; set; }
        public bool? webrtc_activated { get; set; }
        public int? webrtc_count { get; set; }
        public List<object> webrtc_ips { get; set; }
        public string user_agent { get; set; }
        public string window_size { get; set; }
        public string screen_resolution { get; set; }
        public string screen_available_resolution { get; set; }
        public int? screen_color_depth { get; set; }
        public int? screen_pixel_ratio { get; set; }
        public int? plugin_count { get; set; }
        public List<object> plugin_list { get; set; }
        public object plugin_hash { get; set; }
        public string browser_hash { get; set; }
        public string browser { get; set; }
        public string browser_version { get; set; }
        public int? font_count { get; set; }
        public List<object> font_list { get; set; }
        public string font_hash { get; set; }
        public string device_hash { get; set; }
        public bool touch_support { get; set; }
        public object device_memory { get; set; }
        public int? hardware_concurrency { get; set; }
        public string platform { get; set; }
        public string region_language { get; set; }
        public string webgl_hash { get; set; }
        public string webgl_vendor { get; set; }
        public string audio_hash { get; set; }
        public object do_not_track { get; set; }
        public bool? adblock { get; set; }
        public object battery_level { get; set; }
        public object battery_charging { get; set; }
        public string canvas_hash { get; set; }
        public object device_ip_address { get; set; }
        public object device_ip_country { get; set; }
        public object device_ip_isp { get; set; }
        public List<object> social_logins { get; set; }
        public List<object> accept_language { get; set; }
    }

public class BinDetails
{
    public string card_bin { get; set; }
    public string bin_bank { get; set; }
    public string bin_card { get; set; }
    public string bin_type { get; set; }
    public string bin_level { get; set; }
    public string bin_country { get; set; }
    public string bin_country_code { get; set; }
    public string bin_website { get; set; }
    public string bin_phone { get; set; }
    public bool? bin_valid { get; set; }
    public string card_issuer { get; set; }
    public string id { get; set; }
}

public class AppliedRule
{
    public string id { get; set; }
    public string name { get; set; }
    public string operation { get; set; }
    public double? score { get; set; }
}

public class History
{
    public int? hits { get; set; }
    public int? customer_hits { get; set; }
    public int? first_seen { get; set; }
    public int? last_seen { get; set; }
}

public class Flag
{
    public string note { get; set; }
    public int? date { get; set; }
    public object industry { get; set; }
}

public class IpDetails
{
    public string ip { get; set; }
    public double? score { get; set; }
    public string country { get; set; }
    public string state_prov { get; set; }
    public string city { get; set; }
    public string timezone_offset { get; set; }
    public string isp_name { get; set; }
    public double? latitude { get; set; }
    public double? longitude { get; set; }
    public string type { get; set; }
    public List<int> open_ports { get; set; }
    public bool? tor { get; set; }
    public bool? vpn { get; set; }
    public bool? web_proxy { get; set; }
    public bool? public_proxy { get; set; }
    public int? spam_number { get; set; }
    public List<object> spam_urls { get; set; }
    public string id { get; set; }
    public History history { get; set; }
    public List<Flag> flags { get; set; }
}

public class DomainDetails
{
    public string domain { get; set; }
    public string tld { get; set; }
    public bool? registered { get; set; }
    public string created { get; set; }
    public string updated { get; set; }
    public string expires { get; set; }
    public string registrar_name { get; set; }
    public object registered_to { get; set; }
    public bool? disposable { get; set; }
    public bool? free { get; set; }
    public bool? custom { get; set; }
    public bool? dmarc_enforced { get; set; }
    public bool? spf_strict { get; set; }
    public bool? valid_mx { get; set; }
    public bool? accept_all { get; set; }
    public bool? suspicious_tld { get; set; }
    public bool? website_exists { get; set; }
}

public class Apple
{
    public bool? registered { get; set; }
}

public class Ebay
{
    public bool? registered { get; set; }
}

public class Facebook
{
    public bool? registered { get; set; }
    public object url { get; set; }
    public object name { get; set; }
    public object photo { get; set; }
}

public class Flickr
{
    public bool? registered { get; set; }
}

public class Foursquare
{
    public bool? registered { get; set; }
}

public class Github
{
    public bool? registered { get; set; }
}

public class Google
{
    public object registered { get; set; }
    public object photo { get; set; }
}

public class Gravatar
{
    public bool? registered { get; set; }
}

public class Instagram
{
    public bool? registered { get; set; }
}

public class Lastfm
{
    public bool? registered { get; set; }
}

public class Linkedin
{
    public object registered { get; set; }
    public object url { get; set; }
    public object name { get; set; }
    public object company { get; set; }
    public object title { get; set; }
    public object location { get; set; }
    public object website { get; set; }
    public object twitter { get; set; }
    public object photo { get; set; }
}

public class Microsoft
{
    public bool? registered { get; set; }
}

public class Myspace
{
    public bool? registered { get; set; }
}

public class Pinterest
{
    public bool? registered { get; set; }
}

public class Skype
{
    public bool? registered { get; set; }
    public object country { get; set; }
    public object city { get; set; }
    public object gender { get; set; }
    public object name { get; set; }
    public object id { get; set; }
    public object handle { get; set; }
    public object bio { get; set; }
    public object age { get; set; }
    public object language { get; set; }
    public object state { get; set; }
    public object photo { get; set; }
}

public class Spotify
{
    public bool? registered { get; set; }
}

public class Tumblr
{
    public bool? registered { get; set; }
}

public class Twitter
{
    public bool? registered { get; set; }
}

public class Vimeo
{
    public bool? registered { get; set; }
}

public class Weibo
{
    public bool? registered { get; set; }
}

public class Yahoo
{
    public bool? registered { get; set; }
}

public class AccountDetails
{
    public Apple apple { get; set; }
    public Ebay ebay { get; set; }
    public Facebook facebook { get; set; }
    public Flickr flickr { get; set; }
    public Foursquare foursquare { get; set; }
    public Github github { get; set; }
    public Google google { get; set; }
    public Gravatar gravatar { get; set; }
    public Instagram instagram { get; set; }
    public Lastfm lastfm { get; set; }
    public Linkedin linkedin { get; set; }
    public Microsoft microsoft { get; set; }
    public Myspace myspace { get; set; }
    public Pinterest pinterest { get; set; }
    public Skype skype { get; set; }
    public Spotify spotify { get; set; }
    public Tumblr tumblr { get; set; }
    public Twitter twitter { get; set; }
    public Vimeo vimeo { get; set; }
    public Weibo weibo { get; set; }
    public Yahoo yahoo { get; set; }
    public Whatsapp whatsapp { get; set; }
    public Telegram telegram { get; set; }
    public Viber viber { get; set; }
}

public class BreachDetails
{
    public bool? haveibeenpwned_listed { get; set; }
    public int? number_of_breaches { get; set; }
    public object first_breach { get; set; }
    public List<object> breaches { get; set; }
}

public class EmailDetails
{
    public string email { get; set; }
    public double? score { get; set; }
    public bool? deliverable { get; set; }
    public DomainDetails domain_details { get; set; }
    public AccountDetails account_details { get; set; }
    public BreachDetails breach_details { get; set; }
    public string id { get; set; }
    public History history { get; set; }
    public List<object> flags { get; set; }
}

public class Whatsapp
{
    public object registered { get; set; }
    public object photo { get; set; }
    public object last_seen { get; set; }
}

public class Telegram
{
    public object registered { get; set; }
    public object photo { get; set; }
    public object last_seen { get; set; }
}

public class Viber
{
    public bool? registered { get; set; }
    public object photo { get; set; }
    public object last_seen { get; set; }
}

public class PhoneDetails
{
    public long? number { get; set; }
    public bool? valid { get; set; }
    public string type { get; set; }
    public string country { get; set; }
    public string carrier { get; set; }
    public double? score { get; set; }
    public AccountDetails account_details { get; set; }
    public string id { get; set; }
    public History history { get; set; }
    public List<object> flags { get; set; }
}