using System;
using System.Collections.Generic;

namespace FlexCharge.Eligibility.Services.FraudServices.SeonModels;

public class SeonFraudRequestModel
{
    public Config config { get; set; }
    public string ip { get; set; }
    public string action_type { get; set; }
    public string transaction_id { get; set; }
    public string affiliate_id { get; set; }
    public string affiliate_name { get; set; }
    public string order_memo { get; set; }
    public string email { get; set; }
    public string email_domain { get; set; }
    public string password_hash { get; set; }
    public string user_fullname { get; set; }
    public string user_name { get; set; }
    public string user_id { get; set; }
    public string user_dob { get; set; }
    public string user_category { get; set; }
    public string user_account_status { get; set; }
    public int user_created { get; set; }
    public string user_country { get; set; }
    public string user_city { get; set; }
    public string user_region { get; set; }
    public string user_zip { get; set; }
    public string user_street { get; set; }
    public string user_street2 { get; set; }
    public string device_id { get; set; }
    public string session { get; set; }
    public string payment_mode { get; set; }
    public string card_fullname { get; set; }
    public string card_bin { get; set; }
    public string card_hash { get; set; }
    public string card_last { get; set; }
    public string card_expire { get; set; }
    public string avs_result { get; set; }
    public bool cvv_result { get; set; }
    public string receiver_fullname { get; set; }
    public string receiver_bank_account { get; set; }
    public string sca_method { get; set; }
    public string user_bank_account { get; set; }
    public string user_bank_name { get; set; }
    public float user_balance { get; set; }
    public string user_verification_level { get; set; }
    public string status_3d { get; set; }
    public string regulation { get; set; }
    public string payment_provider { get; set; }
    public string phone_number { get; set; }
    public string transaction_type { get; set; }
    public float transaction_amount { get; set; }
    public string transaction_currency { get; set; }
    public string brand_id { get; set; }
    public List<SeonCartItem> items { get; set; }
    public string shipping_country { get; set; }
    public string shipping_city { get; set; }
    public string shipping_region { get; set; }
    public string shipping_zip { get; set; }
    public string shipping_street { get; set; }
    public string shipping_street2 { get; set; }
    public string shipping_phone { get; set; }
    public string shipping_fullname { get; set; }
    public string shipping_method { get; set; }
    public string billing_country { get; set; }
    public string billing_city { get; set; }
    public string billing_region { get; set; }
    public string billing_zip { get; set; }
    public string billing_street { get; set; }
    public string billing_street2 { get; set; }
    public string billing_phone { get; set; }
    public string discount_code { get; set; }
    public string bonus_campaign_id { get; set; }
    public string gift { get; set; }
    public string gift_message { get; set; }
    public string merchant_id { get; set; }
    public string merchant_created_at { get; set; }
    public string merchant_country { get; set; }
    public string merchant_category { get; set; }
    public string details_url { get; set; }
    public CustomFields custom_fields { get; set; }
}

public class Ip
{
    public string include { get; set; }
    public int timeout { get; set; }
    public string version { get; set; }
}

public class Email
{
    public string include { get; set; }
    public int timeout { get; set; }
    public string version { get; set; }
}

public class Phone
{
    public string include { get; set; }
    public int timeout { get; set; }
    public string version { get; set; }
}

public class Config
{
    public Ip ip { get; set; }
    public Email email { get; set; }
    public Phone phone { get; set; }
    public bool ip_api { get; set; }
    public bool email_api { get; set; }
    public bool phone_api { get; set; }
    public bool device_fingerprinting { get; set; }
    public bool ignore_velocity_rules { get; set; }
    public string response_fields { get; set; }
}

public class ItemCustomFields
{
}

public class SeonCartItem
{
    public string item_id { get; set; }
    public string item_quantity { get; set; }
    public string item_name { get; set; }
    public string item_price { get; set; }
    public string item_store { get; set; }
    public string item_store_country { get; set; }
    public string item_category { get; set; }
    public string item_url { get; set; }
    public ItemCustomFields item_custom_fields { get; set; }
}

public class CustomFields
{
    // public bool is_intangible_item { get; set; }
    // public bool is_pay_on_delivery { get; set; }
    // public string departure_airport { get; set; }
    // public int days_to_board { get; set; }
    // public string arrival_airport { get; set; }

    public string order_id { get; set; }
    public string external_order_id { get; set; }
}
