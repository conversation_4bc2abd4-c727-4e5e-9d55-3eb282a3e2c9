using System;
using System.Linq;
using System.Reflection;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils;

namespace FlexCharge.Eligibility.Services.Orders.OrderStates;

public sealed class StateMachinesStatesValidator
{
    private StateMachinesStatesValidator()
    {
    }

    public static void ValidateAllStatesDefinedAndThrowAsync()
    {
        using var workspan = Workspan.Start<StateMachinesStatesValidator>();

        // Locate all state machines states
        var allStateMachinesStates = ReflectionHelpers.ScanForType<StateMachinesStatesValidator, IStateMachineState>()
            .Where(x => x.IsClass && !x.IsAbstract);

        foreach (var stateMachineState in allStateMachinesStates)
        {
            try
            {
                var initializeMethod = FindInitializeMethod(stateMachineState);

                if (initializeMethod == null)
                {
                    throw new InvalidOperationException(
                        $"Failed to find Initialize method for StateMachineState {stateMachineState}");
                }

                initializeMethod.Invoke(null, null);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Failed to validate StateMachineState {StateMachineState}",
                    stateMachineState);
                throw;
            }
        }

        MethodInfo FindInitializeMethod(Type stateMachineState)
        {
            var initializeMethod = stateMachineState.GetMethod("Initialize", BindingFlags.Public | BindingFlags.Static);

            if (initializeMethod == null)
            {
                var baseClass = stateMachineState.BaseType;
                if (baseClass != null)
                {
                    return FindInitializeMethod(baseClass);
                }
            }

            return initializeMethod;
        }
    }
}