using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cache.BigPayload;
using FlexCharge.Common.Cache.DistributedMemoryDatabase;
using FlexCharge.Common.Shared.Tracking;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Tracking;
using FlexCharge.Contracts.Common;
using FlexCharge.Eligibility.DistributedCache;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services.SessionMatcherService.SessionMatchers;
using FlexCharge.Grpc.Tracking;
using MassTransit;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Services.SessionMatcherService;

public partial class SessionMatcherService : ISessionMatcherService
{
    readonly TimeSpan TRANSACTION_SEARCH_THRESHOLD = TimeSpan.FromMinutes(5);

    private readonly IActivityService _activityService;
    private readonly IExternalRequestsDistributedCache _externalRequestsDistributedCache;
    private readonly IExternalRequestsDistributedMemoryDatabase _externalRequestsDistributedMemoryDatabase;
    private readonly IRequestClient<GetTrackingInformationCommand> _getTrackingInformationRequest;
    private readonly IBigPayloadService _bigPayloadService;
    private readonly ServiceCollectionExtensions.SessionMatcherResolver _sessionMatcherResolver;
    private readonly Grpc.Tracking.GrpcTrackingService.GrpcTrackingServiceClient _grpcTrackingServiceClient;

    public SessionMatcherService(
        IActivityService activityService,
        IExternalRequestsDistributedCache externalRequestsDistributedCache,
        IExternalRequestsDistributedMemoryDatabase externalRequestsDistributedMemoryDatabase,
        IRequestClient<GetTrackingInformationCommand> getTrackingInformationRequest,
        IBigPayloadService bigPayloadService,
        ServiceCollectionExtensions.SessionMatcherResolver sessionMatcherResolver,
        GrpcTrackingService.GrpcTrackingServiceClient grpcTrackingServiceClient)
    {
        _activityService = activityService;
        _externalRequestsDistributedCache = externalRequestsDistributedCache;
        _externalRequestsDistributedMemoryDatabase = externalRequestsDistributedMemoryDatabase;
        _getTrackingInformationRequest = getTrackingInformationRequest;
        _bigPayloadService = bigPayloadService;
        _sessionMatcherResolver = sessionMatcherResolver;
        _grpcTrackingServiceClient = grpcTrackingServiceClient;
    }

    private Dictionary<string, string> _sessionDataToMatch;

    private ISessionMatcher CreateSessionMatcher(SessionMatchersEnum matcherType)
    {
        return _sessionMatcherResolver(matcherType);
    }

    private static Dictionary<string, string> ConvertLocationInformationToDictionary(TrackingEntry match)
    {
        Dictionary<string, string> locationProperties = null;
        if (match?.Location?.RootElement != null)
        {
            locationProperties = new();
            var locationRootElement = match.Location.RootElement;
            locationProperties["host"] = locationRootElement.GetProperty("host").GetString();
            locationProperties["href"] = locationRootElement.GetProperty("href").GetString();
            locationProperties["port"] = locationRootElement.GetProperty("port").GetString();
            locationProperties["origin"] = locationRootElement.GetProperty("origin").GetString();
            locationProperties["hostname"] = locationRootElement.GetProperty("hostname").GetString();
            locationProperties["pathname"] = locationRootElement.GetProperty("pathname").GetString();
            locationProperties["protocol"] = locationRootElement.GetProperty("protocol").GetString();
        }

        return locationProperties;
    }

    public async Task<(Guid? SenseKey, TrackingEntry Tracking)> GetTrackingInformationFromDistributedCacheAsync(
        Guid mid, Order order, DateTime matchTimeThreshold,
        bool matchOnlyBySenseKey)
    {
        using var workspan = Workspan.Start<SessionMatcherService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);


        (Guid? SenseKey, TrackingEntry Tracking) trackingInformation = (null, null);

        TrackingEntry trackingValue = null;

        // Trying to get tracking by sense key, if available
        if (!string.IsNullOrEmpty(order.SenseKey) && Guid.TryParse(order.SenseKey, out var senseKey))
        {
            var trackingKey = TrackingSharedCacheKeyFactory.CreateTrackingKey_BySenseKey(mid, senseKey);

            try
            {
                trackingValue = await _externalRequestsDistributedCache.GetValueAsync<TrackingEntry>(trackingKey.Key);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }

            if (trackingValue != null)
            {
                workspan.Log.Information("Found in cache by sense key {SenseKey}: {TrackingValue}", senseKey,
                    JsonConvert.SerializeObject(trackingValue));
            }
        }

        // Trying to get tracking by external order id
        if (trackingValue == null && matchOnlyBySenseKey == false)
        {
            var trackingKey =
                TrackingSharedCacheKeyFactory.CreateTrackingKey_ByExternalOrderId(mid, order.ExternalOrderReference);

            try
            {
                trackingValue =
                    await _externalRequestsDistributedCache.GetValueAsync<TrackingEntry>(trackingKey.Key);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }

            if (trackingValue != null)
            {
                workspan.Log.Information(
                    "Found in cache by ExternalOrderReference key {ExternalOrderReference}: {TrackingValue}",
                    order.ExternalOrderReference, JsonConvert.SerializeObject(trackingValue));
            }
        }

        if (trackingValue != null)
        {
            if (trackingValue.Timestamp >= matchTimeThreshold) //not too old?
            {
                trackingInformation = (trackingValue.SenseKey, trackingValue);
            }
            else
            {
                workspan.Log.Warning("Tracking information from cache is too old: {Timestamp}",
                    trackingValue.Timestamp);
            }
        }

        return trackingInformation;
    }

    private async Task<(Guid? SenseKey, IDictionary<string, string> LocationInformation, string BrowserInformation)>
        GetSessionLocationInformationFromDistributedCacheAsync(Guid mid, Order order, DateTime matchTimeThreshold,
            bool matchOnlyBySenseKey)
    {
        using var workspan = Workspan.Start<SessionMatcherService>();


        (Guid? SenseKey, IDictionary<string, string> LocationInformation, string BrowserInformation)
            senseKeyInformation = (null, null, null);

        var trackingInformation =
            await GetTrackingInformationFromDistributedCacheAsync(mid, order, matchTimeThreshold, matchOnlyBySenseKey);

        if (trackingInformation != (null, null))
        {
            senseKeyInformation = (trackingInformation.SenseKey,
                ConvertLocationInformationToDictionary(trackingInformation.Tracking),
                trackingInformation.Tracking.BrowserInfo);
        }

        return senseKeyInformation;
    }

    public async Task<(Guid? SenseKey, IDictionary<string, string> LocationInformation, string BrowserInformation)?>
        GetSessionLocationInformationBySenseKeyAsync(
            Guid senseKey,
            Guid mid, Order order,
            bool checkSenseKeyExpiration = true)
    {
        using var workspan = Workspan.Start<SessionMatcherService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid)
            .Baggage("SenseKey", senseKey);


        var matchTimeThreshold = DateTime.UtcNow - TRANSACTION_SEARCH_THRESHOLD;

        var senseKeyInformation =
            await GetSessionLocationInformationFromDistributedCacheAsync(mid, order, matchTimeThreshold, true);

        //Not found in cache? -> try to find in database
        if (senseKeyInformation.SenseKey != null && senseKeyInformation.LocationInformation != null)
        {
            workspan.Log.Information(
                "SenseKey {SenseKey} provided in request is found in tracking cache", senseKey);

            return (senseKeyInformation.SenseKey, senseKeyInformation.LocationInformation,
                senseKeyInformation.BrowserInformation);
        }
        else
        {
            var getTrackingInformationCommand = new GetTrackingInformationCommand()
            {
                SenseKey = senseKey,
                MerchantId = mid,
                MatchTimeThreshold = checkSenseKeyExpiration ? matchTimeThreshold : null,
                OrderByTimeDescending = true,
                ReturnOnlyLatest = true
            };


            GetTrackingInformationCommandResponse trackingResponse;
            try
            {
                var response = await _grpcTrackingServiceClient.GetTrackingInformationAsync(
                    new Grpc.Tracking.SerializedRequest
                    {
                        Request = JsonConvert.SerializeObject(getTrackingInformationCommand)
                    });

                trackingResponse =
                    JsonConvert.DeserializeObject<GetTrackingInformationCommandResponse>(response.Response);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "GRPC FAILED - FALLBACK TO MASSTRANSIT");

                trackingResponse =
                    (await _getTrackingInformationRequest.GetResponse<GetTrackingInformationCommandResponse>(
                        getTrackingInformationCommand)).Message;
            }

            var trackingInformationBigPayload =
                new BigPayload<List<TrackingEntry>>(trackingResponse.TrackingHistoryInDescendingOrder.Key);

            var trackingHistoryInDescendingOrder =
                await trackingInformationBigPayload.GetValueAndRemoveAsync(_bigPayloadService);


            var trackingSessionInformation = trackingHistoryInDescendingOrder?.FirstOrDefault();

            // var trackingSessionInformation = await _senseJsDbContext.Tracking
            //     .Where(x => x.MerchantId == mid && x.SenseKey == senseKey &&
            //                 (checkSenseKeyExpiration
            //                     ? x.CreatedAt >= matchTimeThreshold && x.UpdatedAt >= matchTimeThreshold
            //                     : true)
            //     ).OrderByDescending(x => x.CreatedAt).FirstOrDefaultAsync();

            if (trackingSessionInformation?.SenseKey != null && trackingSessionInformation?.SenseKey != Guid.Empty)
            {
                workspan.Log.Information(
                    "SenseKey {SenseKey} provided in request is found in tracking database", senseKey);

                return (trackingSessionInformation.SenseKey,
                    ConvertLocationInformationToDictionary(trackingSessionInformation),
                    trackingSessionInformation.BrowserInfo);
            }
            else
            {
                workspan.Log.Information("SenseKey {SenseKey} provided in request is not found in tracking database",
                    senseKey);
                return null;
            }
        }
    }
}