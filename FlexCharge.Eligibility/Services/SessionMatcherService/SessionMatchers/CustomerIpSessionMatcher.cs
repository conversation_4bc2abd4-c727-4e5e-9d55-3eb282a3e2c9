using FlexCharge.Eligibility.DTO;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Services.SessionMatcherService.SessionMatchers;

public class CustomerIpSessionMatcher : SessionMatcherBase
{
    public CustomerIpSessionMatcher(IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
    }


    protected override bool Match(EvaluateRequest request)
    {
        if (ExistAndEqual("CustomerIp", request.CustomerIp) &&
            ExistAndEqual("SiteId", request.SiteId.ToString()))
        {
            TraceLog("Session found by CustomerIp");
            return true;
        }


        return false;
    }
}