using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cache.BigPayload;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Tracking;
using FlexCharge.Contracts.Common;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Services.SessionMatcherService;

public partial class SessionMatcherService
{
    private async Task<(Guid? SenseKey, IDictionary<string, string>? LocationInformation, string BrowserInformation)>
        TryToMatchOrderSessionKeyToSenseKey(
            EvaluateRequest request, Order order,
            IList<TrackingEntry> trackedSortedDescendingByUpdateTime,
            SessionMatchersEnum sessionMatcherType)
    {
        using var workspan = Workspan.Start<SessionMatcherService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        var sessionMatcher = CreateSessionMatcher(sessionMatcherType);

        TrackingEntry match = null;
        bool duplicateMatch = false;
        try
        {
            foreach (var tracked in trackedSortedDescendingByUpdateTime)
            {
                var valuesToMatch = tracked.CheckoutData != null
                    ? tracked.CheckoutData
                    : new Dictionary<string, string>();

                if (tracked.ExternalOrderId != null)
                {
                    valuesToMatch.Add("ExplicitExternalOrderId", tracked.ExternalOrderId);
                }

                bool possibleMatch = sessionMatcher.Match(valuesToMatch, request, order);

                if (possibleMatch)
                {
                    if (match == null) match = tracked;
                    else if (tracked.SenseKey == match.SenseKey)
                    {
                        // Same SenseKey? => skipping - it's previous data update () 

                        /* Sometimes there can be duplicate SenseKey records in SenseJS database (e.g. consumer clicks submit on checkout page,
                         but some validation error occurs → he clicks again after fixing entered information → and pressed submit again -> we get
                         duplicate SenseKey records)
                         So we need to take the very last record when duplicate SenseKey records found.
                         */
                    }
                    else // duplicate match with another SenseKey -> we can't be sure which one is the right one
                    {
                        duplicateMatch = true;

                        workspan.Log.Information(
                            "Duplicate match found found");

                        await _activityService
                            .CreateActivityAsync(
                                Activities.SessionMatcherErrorActivities.SessionMatching_MultiplePossibleMatchesFound,
                                set => set.CorrelationId(order.Id).TenantId(order.Mid)
                                    .Meta(meta => meta
                                        .SetValue("Matcher", sessionMatcherType.ToString())
                                    ));

                        throw new NotEligibleException();

                        match = null;
                        break;
                    }
                }
            }
        }
        catch (NotEligibleException nee)
        {
            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e,
                "Unable to match SenseKey {SenseKey} to OrderSessionKey", request.SenseKey);

            await _activityService
                .CreateActivityAsync(Activities.SessionMatcherErrorActivities.SessionMatching_Error,
                    data: e, set => set.CorrelationId(order.Id).TenantId(order.Mid)
                        .Meta(meta => meta
                            .SetValue("Matcher", sessionMatcherType.ToString())
                        ));

            throw;
        }

        if (match == null)
        {
            if (duplicateMatch)
            {
                workspan.Log.Information(
                    "Duplicate match found found for SenseKey: {SenseKey}", request.SenseKey);
            }
            else
            {
                workspan.Log.Information(
                    "No match found found for SenseKey: {SenseKey}", request.SenseKey);
            }
        }
        else
        {
            //match.IsUsed = true;
        }

        return (match?.SenseKey, ConvertLocationInformationToDictionary(match), match?.BrowserInfo);
    }

    public async Task<(Guid? SenseKey, IDictionary<string, string>? LocationInformation, string BrowserInformation)>
        TryToMatchOrderToSenseKey(EvaluateRequest request, Guid mid, Order order,
            int retryNumber,
            SessionMatchersEnum sessionMatcherType,
            bool checkSenseKeyExpiration)
    {
        using var workspan = Workspan.Start<SessionMatcherService>()
            .Baggage("OrderId", order.Id)
            .Baggage("Mid", order.Mid);

        await _activityService.CreateActivityAsync(SessionMatcherActivities.SessionMatching_Starting,
            set => set.CorrelationId(order.Id).TenantId(mid)
                .Meta(meta => meta
                    .SetValue("Matcher", sessionMatcherType.ToString())
                    .SetValue("Attempt#", retryNumber + 1)
                ));


        var matchTimeThreshold = DateTime.UtcNow - TRANSACTION_SEARCH_THRESHOLD;

        workspan.Log.Information("Trying to get session location information from distributed cache");

        var senseKeyInformation =
            await GetSessionLocationInformationFromDistributedCacheAsync(mid, order, matchTimeThreshold, false);

        //Not found in cache? -> try to find in database
        if (senseKeyInformation.SenseKey == null)
        {
            workspan.Log.Information("Trying to get session location information from Tracking microservice");

            var getTrackingInformationCommand = new GetTrackingInformationCommand()
            {
                ExternalOrderId = order.ExternalOrderReference,
                MerchantId = mid,
                MatchTimeThreshold = checkSenseKeyExpiration ? matchTimeThreshold : null,
                OrderByTimeDescending = true
            };

            GetTrackingInformationCommandResponse trackingInformationResponse;
            try
            {
                var response = await _grpcTrackingServiceClient.GetTrackingInformationAsync(
                    new Grpc.Tracking.SerializedRequest
                    {
                        Request = JsonConvert.SerializeObject(getTrackingInformationCommand)
                    });

                trackingInformationResponse =
                    JsonConvert.DeserializeObject<GetTrackingInformationCommandResponse>(response.Response);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "GRPC FAILED - FALLBACK TO MASSTRANSIT");

                trackingInformationResponse =
                    (await _getTrackingInformationRequest.GetResponse<GetTrackingInformationCommandResponse>(
                        getTrackingInformationCommand)).Message;
            }

            var trackingInformationBigPayload =
                new BigPayload<List<TrackingEntry>>(trackingInformationResponse.TrackingHistoryInDescendingOrder
                    .Key);

            var trackingHistory = await trackingInformationBigPayload.GetValueAndRemoveAsync(_bigPayloadService);


            var possibleMatches = trackingHistory;

            workspan.Log.Information(
                "Possible matches for Merchant: {Matches}", possibleMatches.Count);

            senseKeyInformation =
                await TryToMatchOrderSessionKeyToSenseKey(request, order, possibleMatches, sessionMatcherType);

            if (senseKeyInformation.SenseKey != null && senseKeyInformation.SenseKey != Guid.Empty)
            {
                workspan.Log.Information(
                    "SenseKey {SenseKey} found for Order", senseKeyInformation);
            }
            else
            {
                workspan.Log.Information(
                    "SenseKey not found for Order");
            }
        }

        return senseKeyInformation;
    }
}