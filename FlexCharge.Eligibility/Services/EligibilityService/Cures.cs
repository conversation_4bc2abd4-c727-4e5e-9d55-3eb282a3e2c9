using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Response;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.UIBuilder.DTO;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Commands.Tracking;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.DistributedCache;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.PCSMServices;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;


namespace FlexCharge.Eligibility.Services.EligibilityService;

public partial class EligibilityService
{
    #region Challenges

    public async Task<ChallengeResponse> Challenge(ChallengeRequest payload, System.Guid mid)
    {
        using var workspan = Workspan.Start<EligibilityService>()
            .Baggage("Mid", mid)
            .Baggage("OrderId", payload.OrderSessionKey);

        var response = new ChallengeResponse();
        try
        {
            workspan.Log.Information("Looking for challenge OrderSessionKey: {OrderSessionKey} Mid:{mid}",
                payload.OrderSessionKey, mid);


            var order = await _dbContext.Orders.Where(x => x.Id == payload.OrderSessionKey && x.Mid == mid)
                .SingleOrDefaultAsync();

            var merchant = await _dbContext.Merchants.Where(x => x.Mid == mid).SingleOrDefaultAsync();

            if (order != null)
            {
                var userChallengeResponse =
                    await CreateUserChallengeResponseAsync(merchant, order, payload, mid, response);

                return userChallengeResponse;
            }
            else
            {
                workspan.Log.Information("Order not found: {OrderSessionKey} Mid:{Mid}",
                    payload.OrderSessionKey, mid);

                response.AddError("Cannot get challenge");

                await _activityService.CreateActivityAsync(EligibilityErrorActivities.UserChallenge_OrderNotFound,
                    data: payload,
                    set => set
                        .TenantId(mid)
                        .CorrelationId(payload.OrderSessionKey));

                return await NotEligible(response, order, Consts.OrderNotEligible);
            }

            #region Commented

            // else
            // {
            //     #region Cure Testing Support
            //
            //     string testing_order_guid_start = "4CE65C9C";
            //     string testing_order_guid_end = "40A1-8357-70CB5F4505B4";
            //     string order_session_key_string = payload.OrderSessionKey.ToString().ToUpper();
            //     var isCureTestingCall = order_session_key_string.StartsWith(testing_order_guid_start) &&
            //                             order_session_key_string.EndsWith(testing_order_guid_end);
            //
            //     if (isCureTestingCall)
            //     {
            //         var orderCurrentCureId = "C" + order_session_key_string.Substring(9, 4);
            //
            //         Order fakeOrder = new Order() {CurrentCureId = orderCurrentCureId};
            //
            //         IUserChallengeCure userChallengeCure = await CreateUserChallengeCure(fakeOrder, testCall: true);
            //         if (userChallengeCure == null) return await NotEligible(response, order, Consts.OrderNotEligible);
            //
            //         var userChallengeResponse =
            //             await CreateUserChallengeResponseAsync(userChallengeCure, fakeOrder, payload, mid, response);
            //
            //         await AddActivityAsync(EligibilityActivities.UserChallenge_Created, order,
            //             data: userChallengeResponse,
            //             subcategory: userChallengeCure.CureId,
            //             meta: meta => meta.SetValue("CureId", order.CurrentCureId));
            //
            //         return userChallengeResponse;
            //     }
            //
            //     #endregion
            // }

            #endregion
        }
        catch (Exception e)
        {
            workspan.Log.Error(e,
                $"EXCEPTION: EligibilityService > Challenge > Failed request {payload.OrderSessionKey}");

            await _activityService.CreateActivityAsync(EligibilityErrorActivities.UserChallenge_ChallengeRequestError,
                set => set.TenantId(mid).CorrelationId(payload.OrderSessionKey));

            throw;
        }
    }

    async Task<ChallengeResponse> CreateUserChallengeResponseAsync(Entities.Merchant merchant, Order order,
        ChallengeRequest challengeRequest, Guid mid, ChallengeResponse response)
    {
        using var workspan = Workspan.Start<EligibilityService>()
            .Baggage("Mid", mid)
            .Baggage("OrderId", order.Id)
            .Baggage("OrderSessionKey", challengeRequest.OrderSessionKey);

        var evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

        response.OrderSessionKey = challengeRequest.OrderSessionKey;
        response.SenseKey = challengeRequest.SenseKey;

        bool showPopupOrCure = !string.IsNullOrWhiteSpace(order.PopupSequence) ||
                               (order.State.ToOrderState().IsConditionalConsumerInteraction() &&
                                !string.IsNullOrWhiteSpace(order.CurrentCureId));


        if (showPopupOrCure)
        {
            IUserChallengeCure challenge = await CreateUserChallengeCure(merchant, order);
            if (challenge == null)
            {
                workspan.Log.Information("Cannot get challenge for OrderSessionKey: {OrderSessionKey}",
                    challengeRequest.OrderSessionKey);

                response.AddError("Cannot get challenge");

                await AddActivityAsync(EligibilityErrorActivities.UserChallenge_NoChallengeFoundForThisOffer, order,
                    data: challengeRequest);

                return await NotEligible(response, order, Consts.OrderNotEligible);
            }

            bool isAutoConsentUIFlowConfiguration;

            var merchantConfiguration =
                await _merchantsService.GetMerchantSiteConfigurationAsync(mid, order.SiteId.Value);

            #region Retrieveing UI Flow Type from SenseJS Database

            isAutoConsentUIFlowConfiguration =
                GetUserExperienceFlowType(merchantConfiguration) is UserExperienceFlowType.AutoConsent;

            #endregion

            #region Initializing Challenge

            challenge.IsAutoConsentFlowConfiguration = isAutoConsentUIFlowConfiguration;
            challenge.AlreadyExecutedCuresCount = order.ExecutedCures.Count - 1; // not counting this cure
            challenge.IsImplicitConsentFlow = isAutoConsentUIFlowConfiguration || !order.IsExplicitConsent;
            challenge.InIFrame = order.UseIFrames;

            #region Getting Partner sending settings

            // Validating only partner settings required to send Email on behalf of a partner
            HashSet<string> requiredFields = new()
            {
                nameof(PartnerSettings.Name),
                nameof(PartnerSettings.LogoUrl),
                nameof(PartnerSettings.SiteUrl),
                nameof(PartnerSettings.TermsAndConditionsUrl),
                nameof(PartnerSettings.PrivacyPolicyUrl),
            };

            var partnerSettings = await _partnerService.GetPartnerSettingsAsync(merchant, order, requiredFields);

            #endregion

            if (partnerSettings != null)
            {
                challenge.SetPartnerName(partnerSettings.Name);

                // challenge.SetLogoUrl(!string.IsNullOrWhiteSpace(order.LogoUrl)
                //     ? order.LogoUrl
                //     : partnerSettings.LogoUrl);
                challenge.SetLogoUrl(order.LogoUrl);

                challenge.SetPartnerSiteUrl(partnerSettings.SiteUrl);
                //challenge.SetOuterLogoUrl(merchantConfiguration.Logo);

                challenge.SetTermsAndConditionsUrl(partnerSettings.TermsAndConditionsUrl);
                challenge.SetPrivacyPolicyUrl(partnerSettings.PrivacyPolicyUrl);
            }

            #endregion

            await challenge.InitializeChallengeAsync(evaluateRequest);

            var challenges = await challenge.CreateChallengeAsync(evaluateRequest);

            workspan.Log.Information("Found challenge for OrderSessionKey: {OrderSessionKey}",
                challengeRequest.OrderSessionKey);


            response.Challenge = new List<ChallengeItemDTO>();
            foreach (var challengeItem in challenges)
            {
                response.Challenge.Add(_mapper.Map<ChallengeItemDTO>(challengeItem));
            }

            // Is this cure (not popup) and is it the first user challenge?
            if (order.CurrentCureId != null && order.NumberOfUserChallengesExecuted() == 1 &&
                order.IsInTerminalOrKioskMode())
            {
                await _publisher.Publish(new ConsumerChallengePresentedEvent
                {
                    Mid = order.Mid,
                    Pid = merchant.Pid,
                    OrderId = order.Id,
                    MerchantOrderId = evaluateRequest.OrderId,
                    Timestamp = DateTime.UtcNow
                });
            }

            response.Parameters = challenge.ChallengeParameters;
            response.IsUserCancellable = challenge.IsUserCancellable;
            await AddActivityAsync(EligibilityActivities.UserChallenge_Created, order,
                data: response,
                subcategory: challenge.CureId,
                meta: meta => meta.SetValue("CureId", challenge.CureId));
        }
        else if (order.IsApproved())
        {
            response.Status = nameof(EligibilityStatusCodes.APPROVED);
            response.Result = nameof(EligibilityResultCodes.Success);
        }
        else if (order.CannotBeEligibleNow())
        {
            response.Status = nameof(EligibilityStatusCodes.DECLINED);
            response.Result = nameof(EligibilityResultCodes.Success);
        }

        return response;
    }

    #endregion

    #region Re-Evaluate After Consumer Challenge

    public async Task<ChallengeReEvaluateResponse> ReEvaluateWithUserChallengeResults(
        ChallengeReEvaluateRequest payload, Guid mid, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<EligibilityService>()
            .Baggage("Mid", mid)
            .Baggage("OrderId", payload.OrderSessionKey);

        Order order = null;
        Entities.Merchant merchant = null;
        var response = new ChallengeReEvaluateResponse();

        try
        {
            var orderAndMerchant = await _dbContext.Orders.Where(x => x.Id == payload.OrderSessionKey && x.Mid == mid)
                .Join(_dbContext.Merchants, x => x.Mid, x => x.Mid,
                    (ord, merchant) => new
                    {
                        Order = ord,
                        Merchant = merchant
                    })
                .SingleOrDefaultAsync();

            order = orderAndMerchant?.Order;

            if (order is null)
            {
                #region Handling Order not found error

                response.Result = EligibilityStatusCodes.DECLINED.ToString();

                workspan.Log.Error(
                    "IN: Eligibility service > ReEvaluate > Order not found for key: {OrderSessionKey}, mid: {mid}",
                    payload.OrderSessionKey, mid);

                await _activityService.CreateActivityAsync(EligibilityErrorActivities.OrderNotFound, data: payload,
                    set => set.TenantId(mid));

                response.AddError(Consts.GeneralError, code: nameof(Consts.GeneralError), friendly: false);

                return response;

                #endregion
            }

            merchant = orderAndMerchant.Merchant;

            response = new ChallengeReEvaluateResponse()
            {
                //Result = order.State,
                OrderSessionKey = order.Id,
                SenseKey = order.SenseKey,
                ExternalOrderReference = order.ExternalOrderReference,
                Amount = order.Amount,
                CurrencySymbol = GetOrderCurrencySymbol(order)
            };

            ThrowIfOrderCannotBeEligible(order);

            // need to define what to do on each cure journey 
            // what are the actions that need to be performed based on cure ID

            IUserChallengeCure userChallengeCure = await CreateUserChallengeCure(merchant, order);
            if (userChallengeCure == null)
            {
                throw new NotEligibleException();
            }
            else
            {
                // Is this cure (not popup) and is it the first user challenge?
                if (order.CurrentCureId != null && order.NumberOfUserChallengesExecuted() == 1 &&
                    order.IsInTerminalOrKioskMode())
                {
                    await _publisher.Publish(new ConsumerChallengeAttemptedEvent
                    {
                        Mid = order.Mid,
                        Pid = orderAndMerchant.Merchant.Pid,
                        OrderId = order.Id,
                        MerchantOrderId = order.ExternalOrderReference,
                        Timestamp = DateTime.UtcNow
                    });
                }

                #region Remove popup already shown

                if (order.HasCurrentPopup())
                {
                    // we don't want to see same popup again
                    order.RemoveCurrentPopupFromSequence();
                    //order.CurrentPopupId = null;
                    await _dbContext.SaveChangesAsync();
                }

                #endregion

                #region Activity

                await AddActivityAsync(EligibilityActivities.UserChallenge_UserAnsweredQuestions, order,
                    data: payload.Answers,
                    subcategory: userChallengeCure.CureId,
                    meta: meta => meta.SetValue("CureId", userChallengeCure.CureId));

                #endregion

                if (userChallengeCure.TryValidateUserAnswers(order, payload, out var answersValidationErrors))
                {
                    if (answersValidationErrors?.Any() == true)
                    {
                        #region Processing validation errors

                        foreach (var validationError in answersValidationErrors)
                        {
                            response.AddError(error: validationError.ErrorMessage,
                                key: validationError.AnswerId.ToString(), friendly: true);

                            await AddActivityAsync(EligibilityErrorActivities.UserChallenge_AnswerValidationError,
                                order, data: validationError,
                                subcategory: userChallengeCure.CureId,
                                meta: meta => meta.SetValue("CureId", userChallengeCure.CureId));
                        }

                        #endregion

                        throw new NotEligibleException();
                    }
                }
                else
                {
                    #region Activity

                    await AddActivityAsync(EligibilityErrorActivities.UserChallenge_GeneralAnswerValidationError,
                        order,
                        subcategory: userChallengeCure.CureId,
                        meta: meta => meta.SetValue("CureId", userChallengeCure.CureId));

                    #endregion

                    throw new NotEligibleException();
                }
            }

            #region Activity

            await AddActivityAsync(EligibilityActivities.UserChallenge_CureExecutionStarted, order, data: payload,
                subcategory: userChallengeCure.CureId,
                meta: meta => meta.SetValue("CureId", userChallengeCure.CureId));

            #endregion

            UserChallengeResult cureResult;
            try
            {
                cureResult = await userChallengeCure.ExecuteAsync(payload);
            }
            finally
            {
                await _dbContext.SaveChangesAsync(); // cure can modify order entity
            }

            #region Activity

            await AddActivityAsync(EligibilityActivities.UserChallenge_CureResultReceived, order,
                data: cureResult.ToString(),
                subcategory: userChallengeCure.CureId,
                meta: meta => meta.SetValue("CureId", userChallengeCure.CureId));

            #endregion

            bool reEvaluate;

            if (cureResult is UserChallengeResult.RE_EVALUATE)
            {
                workspan.LogEligibility.Information($"Starting re-evaluation");

                #region Activity

                await AddActivityAsync(EligibilityActivities.Cures_ReEvaluationRequested, order,
                    subcategory: userChallengeCure.CureId,
                    meta: meta => meta.SetValue("CureId", userChallengeCure.CureId));

                #endregion

                reEvaluate = true;
            }
            else if (cureResult is UserChallengeResult.SHOW_ANOTHER_POPUP)
            {
                order.AddPopupToPopupSequence(userChallengeCure.AnotherUserChallenge, true);
                await _dbContext.SaveChangesAsync();
                return await ShowPopupToCustomerAsync(response);
            }
            else if (cureResult is UserChallengeResult.CLOSE_THIS_POPUP)
            {
                ThrowIfOrderCannotBeEligible(order);

                response.Result = order.State;
                return response;
            }
            else if (cureResult is UserChallengeResult.START_ANOTHER_CURE)
            {
                order.CurrentCureId = CureBase.GetCureId(userChallengeCure.AnotherUserChallenge);
                await _dbContext.SaveChangesAsync();

                return await ChallengeCustomerAsync(response, order);
            }
            else if (cureResult is UserChallengeResult.NOT_CURED)
            {
                //Run re-evaluation to get a new cure
                reEvaluate = true;
            }
            else
            {
                reEvaluate = false;
            }

            ThrowIfOrderCannotBeEligible(order);

            if (reEvaluate)
            {
                return await ReEvaluateAfterUserChallengeAsync<ChallengeReEvaluateRequest, ChallengeReEvaluateResponse>(
                    payload,
                    mid, order, changedData: userChallengeCure.ChangedData,
                    cancellationToken);
            }
            else //NOT_ELIGIBLE - It can be when cure is closed by user without an answer or in test cures
            {
                throw new NotEligibleException();
            }


            //await SetOrderEligibilityState(eligibilityResponse, order, stage3Result.EligibilityState);
        }
        catch (NotEligibleException)
        {
            // if (order?.HasCurrentPopup() == false)
            // {
            return await NotEligible(response, order, Consts.OrderNotEligible);
            // }
            // else
            // {
            //    return await ChallengeCustomer(response, order, true);
            //}
        }
        catch (Exception e)
        {
            workspan.Log.Error(e,
                $"EXCEPTION: EligibilityService > ReEvaluate > Failed eligibility request {payload.OrderSessionKey}");
            await _activityService.CreateActivityAsync(EligibilityErrorActivities.UserChallenge_CureExecutionError,
                data: e,
                set => set.TenantId(mid).CorrelationId(payload.OrderSessionKey));

            throw;
        }
        finally
        {
        }
    }

    async Task<IUserChallengeCure> CreateUserChallengeCure(Entities.Merchant merchant, Order order,
        bool testCall = false)
    {
        using var workspan = Workspan.Start<EligibilityService>();

        var currentPopupId = order.GetCurrentPopupId();
        //if (currentPopupId != null) order.CurrentPopupId = currentPopupId;

        string cureOrPopupId = !string.IsNullOrWhiteSpace(currentPopupId)
            ? currentPopupId
            : order.State.ToOrderState().IsConditionalConsumerInteraction()
                ? order.CurrentCureId
                : null;

        if (string.IsNullOrWhiteSpace(cureOrPopupId)) return null;

        var cure = await _cureService.CreateCureOrDefaultAsync(_activityService, merchant, order, cureOrPopupId,
            testCall);

        if (cure is null)
        {
            workspan.LogEligibility.Error($"Unknown cure: {order.CurrentCureId}");
            await AddActivityAsync(EligibilityErrorActivities.UserChallenge_CureNotFound, order,
                data: order.CurrentCureId,
                subcategory: order.CurrentCureId,
                meta: meta => meta.SetValue("CureId", order.CurrentCureId));

            return null;
        }

        if (!cure.IsUserChallengeCure)
        {
            workspan.LogEligibility.Error(
                $"Wrong cure type. User challenge cure expected. CureId: {order.CurrentCureId}");
            await AddActivityAsync(EligibilityErrorActivities.UserChallenge_NotUserChallengeCure, order,
                data: order.CurrentCureId,
                subcategory: order.CurrentCureId,
                meta: meta => meta.SetValue("CureId", order.CurrentCureId));

            return null;
        }

        IUserChallengeCure userChallengeCure = cure as IUserChallengeCure;

        if (userChallengeCure.RequiresIFrame && order.UseIFrames == false)
        {
            await AddActivityAsync(
                EligibilityErrorActivities.UserChallenge_CureRequiresIFrameError,
                order,
                subcategory: userChallengeCure.CureId,
                meta: meta => meta.SetValue("CureId", userChallengeCure.CureId));

            return null;
        }

        return cure as IUserChallengeCure;
    }

    #endregion

    private async Task<TResponse> TestCures<TResponse>(TResponse response, Entities.Merchant merchant, Order order)
        where TResponse : BaseResponse, IOptionalEligibilitySessionIdPair, IOptionalExternalOrderReference, new()
    {
        using var workspan = Workspan.Start<EligibilityService>();

        await AddActivityAsync(Activities.EligibilityActivities.Testing_EnteredTestMode, order,
            subcategory: "CureTestingMode");

        await _dbContext.Entry(order).Collection(x => x.ExecutedCures).LoadAsync();

        var alreadyExecutedCures = order.ExecutedCures;
        var orderPayload = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

        var splittedPhone = orderPayload.Payer.Phone?.Split('.');
        if (splittedPhone.Length < 2 || splittedPhone[0] != "+99")
            throw new NotEligibleException();

        Stage3Response fakeStage3Result = new Stage3Response();

        fakeStage3Result.CureSet = splittedPhone.Skip(1).Select(x => "C" + GetTestCureName(x)).ToList();

        var cureNameToExecute =
            fakeStage3Result.CureSet.FirstOrDefault(
                x => !alreadyExecutedCures.Any(c =>
                    c.Name == x) // Guy's wisdom: Do not try the same and expect different results
            );

        if (string.IsNullOrWhiteSpace(cureNameToExecute))
        {
            workspan.LogEligibility.Information("No cure executed: all cures already tried");
            throw new NotEligibleException();
        }

        order.CureSequenceCounter++;
        order.CurrentCureId = cureNameToExecute;
        await _dbContext.SaveChangesAsync();

        var cure = await _cureService.CreateCureOrDefaultAsync(_activityService, merchant, order, cureNameToExecute);
        IUserChallengeCure userChallengeCure = cure as IUserChallengeCure;
        if (userChallengeCure != null)
        {
            try
            {
                var site = _dbContext.Sites.FirstOrDefault(x => x.Id == order.SiteId);

                var cureTestEligibilityContext = new EligibilityCheckContext(merchant, site, order,
                    orderPayload, DataChangeFlags.All, false, false,
                    _fingerprintService, UserExperienceFlowType.Frictionless, null,
                    null);

                if (await cure.CanBeExecutedAsync(cureTestEligibilityContext) == false)
                {
                    workspan.Log.Information("Cure {CureId} cannot be executed", cure.CureId);
                }
                else
                {
                    workspan.Log.Information("Cure {CureId} can be executed", cure.CureId);
                }
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
            }

            var userChallengeCurePreExecutionResult = await userChallengeCure.TryPreExecuteAsync(order);
            await _dbContext.SaveChangesAsync(); // cure can modify order entity

            // if (userChallengeCurePreExecutionResult == UserChallengeCurePreExecutionResult.RE_EVALUATE)
            // {
            //     //await Evaluate(request, merchant, order, isReEvaluation: true, userChallengeCure.ChangedData);
            //     return null;
            // }
            // else if (userChallengeCurePreExecutionResult == UserChallengeCurePreExecutionResult.NOT_APPLICABLE)
            // {
            //     throw new NotEligibleException();
            // }
        }

        return await ChallengeCustomerAsync(response, order);
    }

    private string GetTestCureName(string cureName)
    {
        int cureOptionsSwitchIndex = cureName.IndexOf(":");
        if (cureOptionsSwitchIndex >= 0)
        {
            cureName = cureName.Substring(0, cureOptionsSwitchIndex);
        }

        return cureName;
    }

    private async Task<bool> IsInCureTestingMode(bool skipTestingMode, Order order, EvaluateRequest orderPayload)
    {
        bool inCureTestingMode;
        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
        {
            inCureTestingMode = EnvironmentHelper.IsInSandboxOrStagingOrDevelopment
                                && (orderPayload.BillingInformation?.FirstName == "CURE" ||
                                    orderPayload.BillingInformation?.FirstName == "IFRAME" ||
                                    orderPayload.BillingInformation?.FirstName == "SUBSCRIPTION")
                                && orderPayload.BillingInformation?.LastName == "TESTER"
                                && orderPayload.Payer?.Phone?.StartsWith("+99.") == true
                                && !skipTestingMode;

            if (inCureTestingMode && orderPayload.Payer.Phone != null)
            {
                await AddActivityAsync(EligibilityActivities.Testing_EnteredTestMode, order,
                    subcategory: "CureTestingMode",
                    meta: meta => meta
                        .SetValue("TestMode", "CureTestingMode"));
                return true;
            }
        }

        return false;
    }
}