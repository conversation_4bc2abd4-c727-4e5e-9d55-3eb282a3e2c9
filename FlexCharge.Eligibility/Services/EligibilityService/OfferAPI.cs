#define ENABLE_IFRAME_TESTING
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Response;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DistributedCache;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services.Orders.OrderStates;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using Merchant = FlexCharge.Eligibility.DTO.Merchant;

namespace FlexCharge.Eligibility.Services.EligibilityService;

public partial class EligibilityService
{
    #region Get Offer Session Key

    /// <summary>
    /// Called from our UI widget to know if there's an active order session for this SenseKey
    /// </summary>
    /// <param name="request"></param>
    /// <param name="cancellationToken"></param>
    /// <param name="getMid"></param>
    /// <returns></returns>
    public async Task<PollResponse> GetOrderSessionAsync(PollRequest request, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<EligibilityService>()
            .Baggage("Mid", request.Mid)
            .Baggage("OrderId", request.OrderSessionKey)
            .Baggage("SenseKey", request.SenseKey);

        PollResponse pollResponse = new PollResponse();

        try
        {
            workspan.LogEligibility.Information($"Starting GetOrderSessionKey");

            pollResponse = await GetOrderSessionInternal(request, cancellationToken /*lastRetry: retriesLeft <= 0*/);

            return pollResponse;
        }
        catch (NotEligibleException)
        {
            return await NotEligible(pollResponse);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);

            await _activityService.CreateActivityAsync(EligibilityErrorActivities.GetOrderSession_Error, data: e,
                set => set
                    .TenantId(request.Mid)
                    .Meta(meta => meta
                        .SetValue("Request", JsonConvert.SerializeObject(request)))
            );

            throw;
        }

        //return await NotEligible(pollResponse);
    }

    public async Task<PollResponse> GetOrderSessionInternal(PollRequest request, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<EligibilityService>()
            .Baggage("Mid", request.Mid)
            .Baggage("OrderId", request.OrderSessionKey)
            .Baggage("SenseKey", request.SenseKey);

        var response = new PollResponse();
        response.OrderSessionKey = null;


        Order order = null;

        try
        {
            await _activityService.CreateActivityAsync(EligibilityActivities.GetOrderSession_Starting, data: request,
                set => set.TenantId(request.Mid).CorrelationId(request.OrderSessionKey));

            #region Trying to get Ghost Mode Flag from Distributed Cache

            string isGhostModeFlagFromCacheString_BySenseKey = null;

            if (!string.IsNullOrWhiteSpace(request.SenseKey))
            {
                var isGhostRequestFlagCacheKey_BySenseKey =
                    CacheKeyFactory.CreateIsGhostModeRequestFlagKey_BySenseKey(request.SenseKey);
                isGhostModeFlagFromCacheString_BySenseKey =
                    await _distributedCache.GetStringValueAsync(isGhostRequestFlagCacheKey_BySenseKey.Key);
            }

            string isGhostModeFlagFromCacheString_ByOrderId = null;
            if (request.OrderSessionKey != null)
            {
                var isGhostRequestFlagCacheKey_ByOrderId =
                    CacheKeyFactory.CreateIsGhostModeRequestFlagKey_ByOrderId(request.OrderSessionKey.Value);
                isGhostModeFlagFromCacheString_ByOrderId =
                    await _distributedCache.GetStringValueAsync(isGhostRequestFlagCacheKey_ByOrderId.Key);
            }

            #endregion


            bool isGhostMode = false;
            if (isGhostModeFlagFromCacheString_BySenseKey != null ||
                isGhostModeFlagFromCacheString_ByOrderId != null)
            {
                isGhostMode = isGhostModeFlagFromCacheString_BySenseKey == "true" ||
                              isGhostModeFlagFromCacheString_ByOrderId == "true";
            }
            else
            {
                #region Ghost mode flag is not in cache yet - trying to check if this merchant is in full ghost mode

                var merchant = await _dbContext.Merchants.SingleOrDefaultAsync(x => x.Mid == request.Mid);

                if (merchant == null)
                {
                    await _activityService.CreateActivityAsync(
                        SecurityCriticalActivities.MerchantDoesntExist, data: request,
                        set => set
                            .TenantId(request.Mid)
                            .CorrelationId(request.OrderSessionKey));

                    workspan.Log.Error($"Merchant not found");

                    throw new NotEligibleException();
                }


                if (merchant.GhostModeThrottlePercentage == 1)
                {
                    isGhostMode = true;
                }

                #region Saving Ghost Mode flag in distributed cache

                if (!string.IsNullOrWhiteSpace(request.SenseKey))
                {
                    var isGhostRequestFlagCacheKey_BySenseKey =
                        CacheKeyFactory.CreateIsGhostModeRequestFlagKey_BySenseKey(request.SenseKey);

                    await _distributedCache.SetStringAsync(isGhostRequestFlagCacheKey_BySenseKey.Key,
                        isGhostMode.ToString(),
                        isGhostRequestFlagCacheKey_BySenseKey.CacheOptions);
                }

                if (request.OrderSessionKey != null)
                {
                    var isGhostRequestFlagCacheKey_ByOrderId =
                        CacheKeyFactory.CreateIsGhostModeRequestFlagKey_ByOrderId(request.OrderSessionKey.Value);

                    await _distributedCache.SetStringAsync(isGhostRequestFlagCacheKey_ByOrderId.Key,
                        isGhostMode.ToString(),
                        isGhostRequestFlagCacheKey_ByOrderId.CacheOptions);
                }

                #endregion

                #endregion
            }


            if (isGhostMode)
            {
                #region Activity

                workspan.Log.Information($"Merchant is in ghost mode");


                await _activityService.CreateActivityAsync(
                    EligibilityActivities.GetOrderSession_OrderIsInGhostMode, data: request,
                    set => set
                        .TenantId(request.Mid)
                        .CorrelationId(request.OrderSessionKey));

                #endregion

                throw new NotEligibleException();
            }

            var orderStateCacheKey = request.OrderSessionKey != null
                ? CacheKeyFactory.CreateOrderStateKey(request.Mid, request.OrderSessionKey.Value)
                : CacheKeyFactory.CreateOrderStateKey(request.Mid, request.SenseKey);

            var getOrderSessionResult =
                await _distributedCache.GetEnumValueAsync<GetOrderSessionResult>(orderStateCacheKey.Key);

            try
            {
                await PopulateThreeDSServerTransactionDataFromSenseKeyAsync(request.SenseKey, response);
            }
            catch (Exception e)
            {
                workspan.RecordException(e, "Failed to get 3DS Server Transaction data");
            }

            if (getOrderSessionResult == null)
            {
                workspan.Log.Information($"Order session result not found in cache");

                // Offer not found in cache by SenseKey - maybe still in processing (timeouts must be implemented on the client side)
                response.Result = nameof(GetOrderSessionResult.PROCESSING);
                return response;
            }
            else
            {
                workspan.Log.Information("Order session result {OrderSessionResult} found in cache",
                    getOrderSessionResult.ToString());

                response.Result = getOrderSessionResult.ToString();

                if (getOrderSessionResult
                    is GetOrderSessionResult.WAITING_FOR_APPROVAL
                    or GetOrderSessionResult.CONDITIONAL_CONSUMER_INTERACTION
                    or GetOrderSessionResult.APPROVED)
                {
                    // //Removing terminal state to be sure it's processed only once
                    // await _distributedCache.RemoveAsync(orderStateCacheKey.Key);

                    //We need to ensure that there are no duplicate matching orders

                    var matchingOrders = await GetMatchingActiveOrdersAsync(request);


                    if (matchingOrders.Count == 0)
                    {
                        #region Log and Activity

                        workspan.LogEligibility.Error(
                            $"No matching order found for SenseKey {request.SenseKey}, OrderSessionKey: {request.OrderSessionKey}, Mid {request.Mid}");

                        await _activityService
                            .CreateActivityAsync(EligibilityErrorActivities.GetOrderSession_NoMatchingOrderFound,
                                data: request,
                                set => set
                                    .TenantId(request.Mid)
                                    .CorrelationId(request.OrderSessionKey)
                                    .Meta(meta => meta
                                        .SetValue("Request", JsonConvert.SerializeObject(request)))
                            );

                        #endregion

                        throw new NotEligibleException();
                    }
                    else if (matchingOrders.Count > 1)
                    {
                        #region Log and Activity

                        workspan.LogEligibility.Error(
                            $"More than one matching order for SenseKey {request.SenseKey}, OrderSessionKey: {request.OrderSessionKey}, Mid {request.Mid}");

                        await _activityService.CreateActivityAsync(
                            EligibilityErrorActivities.GetOrderSession_MoreThanOneMatchingOrderFound,
                            data: matchingOrders,
                            set => set
                                .TenantId(request.Mid)
                                .CorrelationId(request.OrderSessionKey)
                                .Meta(meta => meta
                                    .SetValue("Request", JsonConvert.SerializeObject(request)))
                        );

                        #endregion

                        throw new NotEligibleException();
                    }
                    else
                    {
                        #region Processing matching order

                        workspan.LogEligibility.Information(
                            $"Found matching order for SenseKey {request.SenseKey}, OrderSessionKey: {request.OrderSessionKey}, Mid {request.Mid}");

                        order = matchingOrders.Single();
                        var orderState = order.State.ToOrderState();

                        await PopulateFromOrderAsync(order, response);


                        if (orderState.CannotBeEligible() ||
                            order.IsGhostMode) // always must return NOT_ELIGIBLE to not to interfere with merchant's purchase flow
                        {
                            #region Activity

                            await AddActivityAsync(
                                EligibilityErrorActivities.GetOrderSession_FoundOrderIsNotEligible, order, data: order);

                            #endregion

                            throw new NotEligibleException();
                        }
                        else
                        {
                            #region Processing APPROVED, WAITING_FOR_APPROVAL or CONDITIONAL_CONSUMER_INTERACTION order

                            //session is set only for eligible orders - so no further processing can be done on not eligible orders
                            response.OrderSessionKey = order.Id;

                            #region IFrame Testing Support

#if ENABLE_IFRAME_TESTING

                            await CheckIfInIframeTestingMode(order);

#endif
                            //IFrames are always enabled for now
                            order.UseIFrames = true;

                            response.UseIframes = order.UseIFrames;

                            #endregion

                            #region Activity

                            await AddActivityAsync(EligibilityActivities.GetOrderSession_OrderSessionFound, order);

                            #endregion

                            #region Commented

                            //TODO D: Add popup for customer consent , if user experience flow requires explicit consent
                            // if (userExperienceFlowType
                            //         is UserExperienceFlowType.AutoConsent
                            //         or UserExperienceFlowType.Frictionless)
                            // {

                            //workspan.Log.Information($"Popup sequence 1: {order.PopupSequence}");

                            #endregion

                            if (getOrderSessionResult is GetOrderSessionResult.APPROVED)
                            {
                                response.Result = nameof(OrderState.ORDER_APPROVED);
                            }
                            else
                            {
                                await using var orderLock = new OrderEvaluationDistributedLock();
                                var approveResult = await ConsentApprove<ConsentApproveRequest, ConsentApproveResponse>(
                                    orderLock,
                                    new ConsentApproveRequest()
                                    {
                                        Mid = order.Mid,
                                        OrderSessionKey = order.Id,
                                        SenseKey = order.SenseKey,
                                        IsExplicit = false
                                    }, order.Mid, false, true, cancellationToken);
                                //}

                                response.Result = approveResult.Result;
                            }

                            #region Commented

                            //order.CurrentPopupId = order.GetCurrentPopupId();
                            //await _dbContext.SaveChangesAsync();

                            //workspan.Log.Information($"Popup sequence 2: {order.PopupSequence}");

                            #endregion

                            var currentPopupId = order.GetCurrentPopupId();

                            if (currentPopupId != null)
                            {
                                response.Result = nameof(OrderState.CONDITIONAL_CONSUMER_INTERACTION);
                            }

                            return response;

                            #endregion
                        }

                        #endregion
                    }
                }
                else
                {
                    return response;
                }
            }
        }
        catch (Exception e)
        {
            #region Log and Activity

            workspan.RecordException(e,
                "EXCEPTION: EligibilityService > GetOrderSessionKey > Failed request {Request}",
                JsonConvert.SerializeObject(request));

            await _activityService.CreateActivityAsync(EligibilityErrorActivities.GetOrderSession_Error, data: e,
                set => set
                    .TenantId(request.Mid)
                    .CorrelationId(order?.Id)
                    .Meta(meta => meta
                        .SetValue("Request", JsonConvert.SerializeObject(request)))
            );

            #endregion

            throw new NotEligibleException();
        }
    }

    private async Task PopulateFromOrderAsync(Order order, PollResponse response)
    {
        var currencySymbol = GetOrderCurrencySymbol(order);

        response.ExternalOrderReference = order.ExternalOrderReference;

        response.MerchantName = (await GetMerchantInformationByOrderAsync(order)).MerchantPublicName;
        response.Amount = order.Amount;
        response.CurrencySymbol = currencySymbol ?? "";
    }

    private async Task PopulateThreeDSServerTransactionDataFromSenseKeyAsync(string? senseKey, PollResponse response)
    {
        if (senseKey == null) return;

        var threeDSecureTransaction = await _threeDsService.GetThreeDSServerTransactionAsync(Guid.Parse(senseKey));
        response.AcsServerAvailable = threeDSecureTransaction != null;
    }

    public async Task<(string PaymentDescriptor, string MerchantPublicName, Guid SiteId)>
        GetMerchantInformationByOrderAsync(Order order)
    {
        Site merchantSite = null;
        Guid? siteId = order.SiteId;
        MerchantDomain merchantDomain = null;

        // if (siteId == null)
        // {
        //     var evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);
        //
        //     merchantDomain = await GetMerchantDomainByUrlAsync(order.Mid, evaluateRequest.SiteUrl);
        //
        //     siteId = merchantDomain?.SiteId;
        //
        //     if (siteId != null)
        //     {
        //         order.SiteId = siteId;
        //         await _dbContext.SaveChangesAsync();
        //     }
        // }

        if (siteId != null)
        {
            merchantSite = await _dbContext.Sites.SingleOrDefaultAsync(x => x.Id == siteId);
        }

        string merchantPublicName;
        string paymentDescriptor;
        Guid merchantSiteId;
        if (merchantSite != null)
        {
            merchantPublicName = merchantSite.Name;
            merchantSiteId = merchantSite.Id;
            paymentDescriptor = merchantSite.Descriptor;
        }
        else
        {
            var merchant = await _dbContext.Merchants.SingleAsync(x => x.Mid == order.Mid);
            merchantPublicName = merchant.Dba;
            merchantSiteId = merchantDomain.SiteId;
            paymentDescriptor = merchantDomain.Descriptor;
        }

        return (paymentDescriptor, merchantPublicName, merchantSiteId);
    }

    private static string GetOrderCurrencySymbol(Order order)
    {
        var orderPayload = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

        l18n.CurrencyTools.TryGetCurrencySymbol(orderPayload.Transaction.Currency,
            out var currencySymbol);
        return currencySymbol;
    }

    private async Task<List<Order>> GetMatchingActiveOrdersAsync(PollRequest request)
    {
        var merchantCITOrdersExpireAfter = await GetMerchantCITOrderExpirationIntervalAsync(request.Mid);

        DateTime expiredOrdersCutoffTime = DateTime.UtcNow.Subtract(merchantCITOrdersExpireAfter);


        var matchingOrdersQuery =
            request.OrderSessionKey != null
                ? _dbContext.Orders.Where(x =>
                    x.Id == request.OrderSessionKey.Value &&
                    x.Mid == request.Mid)
                : _dbContext.Orders.Where(x =>
                    x.SenseKey == request.SenseKey &&
                    x.Mid == request.Mid &&
                    x.ModifiedOn > expiredOrdersCutoffTime);


        var matchingOrders = await matchingOrdersQuery
            .Where(x =>
                (x.SenseKey == null &&
                 (
                     x.IsCIT == false ||
                     //Ignoring sense key for MIT and Terminal/Kiosk-mode orders - it can be null if it's first hosted cure run
                     x.OrderSource == nameof(OrderSource.terminal) ||
                     x.OrderSource == nameof(OrderSource.kiosk)
                 )
                )
                || x.SenseKey == request.SenseKey)
            .ToListAsync();

        return matchingOrders.Where(x => !x.CannotBeEligibleNow()
                //&& !x.IsApproved()
            )
            .ToList(); // only orders that can be eligible
    }

    private async Task<TimeSpan> GetMerchantCITOrderExpirationIntervalAsync(Guid mid)
    {
        var expiryIntervalConfigurationKey = CacheKeyFactory.CreateMerchantCITOrderExpiryIntervalConfigurationKey(mid);
        var citOrderExpirationIntervalInMinutesString =
            await _distributedCache.GetValueAsync<string?>(expiryIntervalConfigurationKey.Key);

        TimeSpan citOrderExpirationInterval;

        if (citOrderExpirationIntervalInMinutesString == null || !int.TryParse(
                citOrderExpirationIntervalInMinutesString,
                out var citOrderExpirationIntervalInMinutes))
        {
            var merchant = await _dbContext.Merchants.SingleOrDefaultAsync(x => x.Mid == mid);

            if (merchant == null)
                throw new Exception($"Merchant {mid} not found");

            citOrderExpirationInterval = merchant.GetCITOrderExpirationInterval();

            await _distributedCache.SetAsync(expiryIntervalConfigurationKey.Key,
                ((int) citOrderExpirationInterval.TotalMinutes).ToString(),
                expiryIntervalConfigurationKey.CacheOptions);
        }
        else
        {
            citOrderExpirationInterval = TimeSpan.FromMinutes(citOrderExpirationIntervalInMinutes);
        }

        return citOrderExpirationInterval;
    }

    private async Task CheckIfInIframeTestingMode(Order order)
    {
        bool inIFrameTestingMode = false;
        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
        {
            var orderPayload = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

            inIFrameTestingMode = EnvironmentHelper.IsInSandboxOrStagingOrDevelopment
                                  && orderPayload.BillingInformation?.FirstName == "IFRAME"
                                  && orderPayload.BillingInformation?.LastName == "TESTER";

            if (inIFrameTestingMode)
            {
                //response.UseIframes = true;
                order.UseIFrames = true;
                await _dbContext.SaveChangesAsync();

                await AddActivityAsync(Activities.EligibilityActivities.Testing_EnteredTestMode, order,
                    subcategory: "IframeTestingMode");
            }
        }
    }

    #endregion

    #region User Consent

    public async Task<ConsentResponse> ConsentReject(ConsentRequest request, Guid mid)
    {
        using var workspan = Workspan.Start<EligibilityService>();

        await using var orderLock = new OrderEvaluationDistributedLock();
        return await Consent<ConsentRequest, ConsentResponse>(
            orderLock,
            new ConsentRequest() {OrderSessionKey = request.OrderSessionKey},
            mid, false, true, true);
    }

    public async Task<TResponse> ConsentApprove<TRequest, TResponse>(OrderEvaluationDistributedLock orderLock,
        TRequest request,
        Guid mid, bool isExplicitConsent, bool isCalledFromClient, CancellationToken cancellationToken)
        where TRequest : IOrderSessionKey, new()
        where TResponse : BaseResponse, IOptionalEligibilitySessionIdPair, IOptionalExternalOrderReference, new()
    {
        using var workspan = Workspan.Start<EligibilityService>();

        var response = await Consent<TRequest, TResponse>(
            orderLock,
            request, mid, true, isExplicitConsent, isCalledFromClient);

        if (!response.Success || response.Result == OrderState.NOT_ELIGIBLE.ToString())
            return response;

        return await EvaluateStage2<TRequest, TResponse>(
            orderLock,
            request, mid,
            isReEvaluation: false,
            calledFromConsentApprove: true,
            isCalledFromClient,
            cancellationToken);
    }

    private async Task<TResponse> Consent<TRequest, TResponse>(
        OrderEvaluationDistributedLock orderLock,
        TRequest request, Guid mid, bool consentReceived,
        bool isExplicitConsent, bool isCalledFromClient)
        where TRequest : IOrderSessionKey
        where TResponse : BaseResponse, IOptionalExternalOrderReference, new()
    {
        using var workspan = Workspan.Start<EligibilityService>()
            .Baggage(nameof(request.OrderSessionKey), request.OrderSessionKey);

        var response = new TResponse();
        Order order = null;

        try
        {
            workspan.Log.Information(isExplicitConsent
                ? $"Consent approved: {consentReceived}"
                : $"Consent implicitly approved: {consentReceived}");

            order = await _dbContext.Orders.Where(x => x.Id == request.OrderSessionKey && x.Mid == mid)
                .SingleOrDefaultAsync();

            if (order is null)
            {
                workspan.Log.Information("Order not found");

                await _activityService.CreateActivityAsync(EligibilityErrorActivities.OrderNotFound, data: request,
                    set => set
                        .TenantId(mid)
                        .Meta(meta => meta
                            .SetValue("Request", JsonConvert.SerializeObject(request)))
                );

                return await NotEligible(response);
            }

            await orderLock.AcquireOrderLock(order.Id, mid, _distributedLockService, _activityService);

            // if (isCalledFromClient && !order.IsCIT)
            // {
            //     throw new Exception("Cannot call Consent from client for non customer-initiated order");
            // }

            #region Populating response with data used for UI purposes to present popup with order information

            var orderPayload = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

            l18n.CurrencyTools.TryGetCurrencySymbol(orderPayload.Transaction.Currency, out var currencySymbol);

            response.ExternalOrderReference = order.ExternalOrderReference;
            response.Amount = orderPayload.Transaction.Amount;
            response.CurrencySymbol = currencySymbol ?? "";

            #endregion

            #region Commented

            // #region Checking: On consent offer must be in  ELIGIBILITY_STAGE3_PASSED || CONDITIONAL_USER_INTERACTION state
            // if (order.State.ToOrderState() != OrderState.ELIGIBILITY_STAGE3_PASSED && 
            //     order.State.ToOrderState() != OrderState.CONDITIONAL_CONSUMER_INTERACTION )
            // {
            //     workspan.Log.Information(
            //         "Offer state {State} doesnt allow approve request. Key: {OrderSessionKey} mid: {Mid}",
            //         order.State, request.OrderSessionKey, mid);
            //
            //     await AddActivityAsync(EligibilityErrorActivities.IncorrectOfferState, order,
            //         data: order.State.ToOrderState());
            //
            //     return await NotEligible(response, order, Consts.OrderNotEligible);
            // }
            //
            // #endregion

            #endregion

            #region Checking: We can only approve an order with disabled Auto consent UI flow

            // We don't need user consent to process offer?
            bool isAutoConsentUIFlow =
                await GetUserExperienceFlowTypeAsync(mid, order.SiteId.Value) is UserExperienceFlowType.AutoConsent;

            #region Commented

            // if (isAutoConsentUIFlow)
            // {
            //     workspan.Log.Information(
            //         "Cannot approve consent for auto approve UI flow. Key: {OrderSessionKey} mid: {Mid}",
            //         order.State, request.OrderSessionKey, mid);
            //
            //     await AddActivityAsync(EligibilityErrorActivities.Consent_CustomerConsentIsNotSupported, order);
            //     
            //     return await NotEligible(response, order, Consts.GeneralError);
            // }

            #endregion

            #endregion

            //Waiting while order is evaluated
            order = await WaitWhileOfferIsEvaluated(order);
            _dbContext.Update(order);

            if (!isAutoConsentUIFlow)
            {
                order.IsExplicitConsent = isExplicitConsent;
                //order.State = (approve ? OrderState.CONSENT_RECEIVED : OrderState.CONSENT_REJECTED).ToString();
                if (consentReceived)
                {
                    order.ConsentTimestamp = DateTime.UtcNow;
                }


                if (consentReceived)
                {
                    await AddActivityAsync(EligibilityActivities.Offer_ConsentApproved, order,
                        eventName: nameof(EligibilityOrderApprovedEvent), data: order);
                }
                else
                {
                    await AddActivityAsync(EligibilityActivities.Offer_ConsentRejected, order,
                        eventName: nameof(EligibilityOrderRejectedEvent), data: order);
                }

                //await _dbContext.SaveChangesAsync();

                using (var sendMessageWorkspan = Workspan.Start<EligibilityService>("OrderEligibilityXXXEvent"))
                {
                    if (consentReceived)
                    {
                        await _publisher.Publish(new EligibilityOrderApprovedEvent
                        {
                            Mid = mid,
                            PaymentInstrumentToken = order.PaymentInstrumentToken,
                            State = order.State.ToString(),
                            CureId = order.CurrentCureId,
                            CureSequenceCounter = order.CureSequenceCounter,
                            Payload = order.Payload
                        });
                    }
                    else
                    {
                        await _publisher.Publish(new EligibilityOrderRejectedEvent
                        {
                            Mid = mid,
                            PaymentInstrumentToken = order.PaymentInstrumentToken,
                            State = order.State.ToString(),
                            CureId = order.CurrentCureId,
                            CureSequenceCounter = order.CureSequenceCounter,
                            Payload = order.Payload
                        });

                        throw new NotEligibleConsentRejectedException();
                    }
                }
            }
        }
        catch (NotEligibleException nex)
        {
            if (order != null)
            {
                try
                {
                    if (nex is NotEligibleConsentRejectedException)
                    {
                        await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligibleConsentRejected);
                    }
                    else await _orderStateMachine.SetOrderStateAsync(order, StateOfOrder.NotEligible);
                }
                catch (Exception e)
                {
                    workspan.RecordFatalException(e, "Failed to set order state to not eligible");
                }

                await _paymentsService.VoidPaymentAuthorizationAndCancelACHDebitPaymentAsync(order);
            }

            response.Result = nameof(OrderState.NOT_ELIGIBLE);
        }
        catch (Exception e)
        {
            workspan.RecordException(e,
                $"EXCEPTION: EligibilityService > Consent > Failed request {request.OrderSessionKey} Mid: {mid}, Request: {consentReceived}");

            await _activityService.CreateActivityAsync(EligibilityErrorActivities.Consent_Error,
                set => set
                    .TenantId(mid)
                    .CorrelationId(request.OrderSessionKey)
                    .Meta(meta => meta
                        .SetValue("Request", JsonConvert.SerializeObject(request)))
            );

            throw;
        }
        finally
        {
            await _dbContext
                .SaveChangesAsync(); // Saving immediately after order.State changed to avoid processing duplicate calls 
        }

        return response;
    }

    #endregion
}