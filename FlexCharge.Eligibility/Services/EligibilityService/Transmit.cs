using System;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DTO;


namespace FlexCharge.Eligibility.Services.EligibilityService;

public partial class EligibilityService
{
    public async Task ProcessTransmittedOrder(TransmitRequest request, Guid transmitCorrelationId)
    {
        using var workspan = Workspan.Start<EligibilityService>();

        var activity = await _activityService.CreateActivityAsync(TransmitActivities.Transmit, request,
            activityId: transmitCorrelationId,
            set => set
                .TenantId(request.Mid)
                .CorrelationId(transmitCorrelationId)
                .Meta(meta => meta
                    .SetValue("Amount", request.Transaction.Amount)
                    .SetValue("ExternalOrderId", request.OrderId)));

        await _fingerprintService.ProcessTransmitOrEvaluateRequestAsync(request, false, false);
    }
}