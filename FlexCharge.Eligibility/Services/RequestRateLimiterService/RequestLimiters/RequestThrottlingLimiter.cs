using System;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Services.RequestRateLimiterService.RequestLimiters;

class RequestThrottlingLimiter : RequestLimiterBase
{
    protected override bool ShouldStopRequest(DateTime currentTimeUtc, Merchant merchant, Order order,
        MerchantRequestStatistics merchantRequestStatistics, EvaluateRequest evaluateRequest)
    {
        if (!merchant.OfferRequestsThrottlePercentage.HasValue) return false;

        double throttlePercentage = decimal.ToDouble(merchant.OfferRequestsThrottlePercentage.Value);
        if (Random.Shared.NextDouble() <= throttlePercentage)
        {
            return false;
        }

        return true;
    }
}