// using System;
// using System.Linq;
// using System.Threading;
// using System.Threading.Tasks;
// using FlexCharge.Eligibility.Entities;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Caching.Distributed;
//
// namespace FlexCharge.Eligibility.Services.Cache;
//
// public interface IPostgreSQLDistributedCacheService : IDistributedCache
// {
//     Task SaveAsync(CancellationToken token = new());
// }
//
// class PostgreSQLDistributedCacheService : IPostgreSQLDistributedCacheService
// {
//     private readonly PostgreSQLDbContext _dbContext;
//
//
//     public PostgreSQLDistributedCacheService(PostgreSQLDbContext dbContext, IDatabaseExpiredItemsRemoverLoopService databaseExpiredItemsRemoverLoopService)
//     {
//         _dbContext = dbContext;
//         databaseExpiredItemsRemoverLoopService.Start();
//     }
//
//     public byte[] Get(string key)
//     {
//         var value = _dbContext.DistributedCache.SingleOrDefault(x => x.Key == key && x.ExpiresAtTime > DateTime.UtcNow);
//         UpdateExpirationTime(value);
//
//         return value?.Value;
//     }
//
//     private static void UpdateExpirationTime(CacheItem value)
//     {
//         if (value != null)
//         {
//             var utcNow = DateTime.UtcNow;
//             var valueSlidingExpirationInSeconds = value.SlidingExpirationInSeconds;
//             var absoluteExpiration = value.AbsoluteExpiration;
//
//             DateTime? expiresAt = null;
//
//             if (valueSlidingExpirationInSeconds.HasValue)
//             {
//                 expiresAt = utcNow.AddSeconds(valueSlidingExpirationInSeconds.Value);
//             }
//
//             if (absoluteExpiration.HasValue)
//             {
//                 if (expiresAt == null || expiresAt > absoluteExpiration.Value)
//                 {
//                     expiresAt = absoluteExpiration;
//                 }
//             }
//
//             value.ExpiresAtTime = expiresAt;
//         }
//     }
//
//     public async Task<byte[]> GetAsync(string key, CancellationToken token = new CancellationToken())
//     {
//         var value = await _dbContext.DistributedCache
//             .SingleOrDefaultAsync(x => x.Key == key && x.ExpiresAtTime > DateTime.UtcNow, token)
//             .ConfigureAwait(false);
//
//         UpdateExpirationTime(value);
//
//         return value?.Value;
//     }
//
//     public void Refresh(string key)
//     {
//         Get(key);
//     }
//
//     public async Task RefreshAsync(string key, CancellationToken token = new CancellationToken())
//     {
//         await GetAsync(key, token);
//     }
//
//     public void Remove(string key)
//     {
//         var value = _dbContext.DistributedCache.SingleOrDefault(x => x.Key == key);
//         if (value != null)
//         {
//             _dbContext.Remove(value);
//         }
//     }
//
//     public async Task RemoveAsync(string key, CancellationToken token = new CancellationToken())
//     {
//         var value = await _dbContext.DistributedCache.SingleOrDefaultAsync(x => x.Key == key);
//         if (value != null)
//         {
//             _dbContext.Remove(value);
//         }
//     }
//
//     public void Set(string key, byte[] value, DistributedCacheEntryOptions options)
//     {
//         var cacheItem = _dbContext.DistributedCache.SingleOrDefault(x => x.Key == key);
//         if (cacheItem == null)
//         {
//             cacheItem = new() {Key = key};
//             _dbContext.DistributedCache.Add(cacheItem);
//         }
//
//         cacheItem.Value = value;
//
//         cacheItem.AbsoluteExpiration = options.AbsoluteExpiration?.DateTime;
//         cacheItem.SlidingExpirationInSeconds = options.SlidingExpiration?.TotalSeconds;
//
//         UpdateExpirationTime(cacheItem);
//     }
//
//     public async Task SetAsync(string key, byte[] value, DistributedCacheEntryOptions options,
//         CancellationToken token = new CancellationToken())
//     {
//         var cacheItem = await _dbContext.DistributedCache.SingleOrDefaultAsync(x => x.Key == key);
//         if (cacheItem == null)
//         {
//             cacheItem = new() {Key = key};
//             _dbContext.DistributedCache.Add(cacheItem);
//         }
//         
//         cacheItem.Value = value;
//
//         cacheItem.AbsoluteExpiration = options.AbsoluteExpiration?.DateTime;
//
//         if (cacheItem.AbsoluteExpiration == null && options.AbsoluteExpirationRelativeToNow != null)
//         {
//             cacheItem.AbsoluteExpiration = DateTime.UtcNow + options.AbsoluteExpirationRelativeToNow;
//         }
//
//         cacheItem.SlidingExpirationInSeconds = options.SlidingExpiration?.TotalSeconds;
//         
//         UpdateExpirationTime(cacheItem);
//     }
//
//     public async Task SaveAsync(CancellationToken token = new CancellationToken())
//     {
//         await _dbContext.SaveChangesAsync();
//     }
// }