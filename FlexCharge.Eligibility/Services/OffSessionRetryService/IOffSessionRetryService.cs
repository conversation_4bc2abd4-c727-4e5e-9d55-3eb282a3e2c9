using System;
using System.Threading;
using System.Threading.Tasks;

namespace FlexCharge.Eligibility.Services.OffSessionRetryService;

public interface IOffSessionRetryService
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="orderId"></param>
    /// <param name="notScheduledRetry">Attempts retry, regardless of whether it is scheduled or not</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task RetryOffSessionOrderAsync(Guid orderId, bool notScheduledRetry = false,
        CancellationToken cancellationToken = default);
}