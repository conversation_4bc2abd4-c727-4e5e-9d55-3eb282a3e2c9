using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;

namespace FlexCharge.Eligibility.Services.DistributedLock.Implementations.DistributedCache;

class DistributedCacheLockAcquisitionHandle : LockAcquisitionHandle
{
    private IDistributedCache _distributedCache;

    public DistributedCacheLockAcquisitionHandle(IDistributedCache distributedCache,
        string lockName, bool isAcquired, bool shouldReleaseLock) : base(lockName, isAcquired, shouldReleaseLock)
    {
        _distributedCache = distributedCache;
    }

    protected override void ReleaseLock()
    {
        _distributedCache.Remove(LockName);
        _distributedCache = null;
    }

    protected override async Task ReleaseLockAsync()
    {
        await _distributedCache.RemoveAsync(LockName);
        _distributedCache = null;
    }
}