using System.Text.RegularExpressions;

namespace FlexCharge.Eligibility.Services.RiskManagement;

public static class PhoneNumberNormalizer
{
    private static readonly Regex nonDigitRegex = new Regex("[^0-9]", RegexOptions.Compiled);

    public static string NormalizePhone(string phoneNumber)
    {
        // Remove all non-digit characters
        string digitsOnly = nonDigitRegex.Replace(phoneNumber, "");

        return digitsOnly;
    }

    public static bool TryNormalizePhoneNumber(string phoneNumber, out string normalizedPhoneNumber)
    {
        normalizedPhoneNumber = null;

        // Remove all non-digit characters
        string digitsOnly = nonDigitRegex.Replace(phoneNumber, "");

        if (string.IsNullOrWhiteSpace(phoneNumber) || phoneNumber.Length < 10)
            return false;

        // Check if the phone number is already in the international format
        if (phoneNumber.StartsWith("+"))
        {
            normalizedPhoneNumber = $"+{digitsOnly}";
            return true;
        }

        // Not in international format, assuming it's a US phone number

        if (digitsOnly.StartsWith("1") && digitsOnly.Length == 11)
        {
            // Remove the leading '1' if present
            digitsOnly = digitsOnly.Substring(1);
        }

        // Check if the phone number has 10 digits
        if (digitsOnly.Length == 10)
        {
            // Prepend '+1' to make it a US phone number format
            normalizedPhoneNumber = $"+1{digitsOnly}";
        }

        return normalizedPhoneNumber != null;
    }
}