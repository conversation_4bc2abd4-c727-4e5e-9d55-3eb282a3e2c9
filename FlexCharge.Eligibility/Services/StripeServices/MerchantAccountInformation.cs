namespace FlexCharge.Eligibility.Services.StripeServices;

public record MerchantAccountInformation
{
    public record AddressInformation
    {
        public string? City { get; set; }
        public string? CountryCode { get; set; }
        public string? Line1 { get; set; }
        public string? Line2 { get; set; }
        public string? PostalCode { get; set; }
        public string? StateCode { get; set; }
    }

    public string? BusinessType { get; init; }
    public string? CompanyStructure { get; init; }
    public string? Mcc { get; init; }
    public string? BusinessName { get; init; }
    public string? BusinessUrl { get; init; }
    public string? AccountCountry { get; init; }
    public string? AccountDefaultCurrency { get; init; }
    public string? AccountType { get; init; }
    public string? AccountEmail { get; init; }
    public string? SupportUrl { get; init; }
    public string? PaymentsDescriptor { get; init; }
    public string? CardPaymentsDescriptorPrefix { get; init; }

    public string? SupportEmail { get; init; }
    public string? SupportPhone { get; init; }
    public AddressInformation? SupportAddress { get; init; }
}