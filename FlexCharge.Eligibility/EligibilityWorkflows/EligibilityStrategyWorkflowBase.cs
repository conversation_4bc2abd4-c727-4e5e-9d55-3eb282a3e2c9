using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.OrdersRecyclingEngine.Strategies;

namespace FlexCharge.Eligibility.EligibilityWorkflows;

public abstract class EligibilityStrategyWorkflowBase : CodeFirstWorkflowBase<EligibilityCheckContext>
{
    public override string Domain => "Eligibility";

    public override bool IsReadonly => true;

    protected Order Order { get; }
    protected Merchant Merchant { get; }
    protected bool OffSessionRetry { get; }

    public EligibilityStrategyWorkflowBase(Order order, Merchant merchant, bool offSessionRetry)
    {
        Order = order;
        Merchant = merchant;
        OffSessionRetry = offSessionRetry;
    }
}