using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.General;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.PRODUCTION_TEST_BLOCKS;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.Tokenization;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;

namespace FlexCharge.Eligibility.EligibilityWorkflows.Implementations.ProductionTest;

public class ProductionTest_Approve_EligibilityStrategy : EligibilityStrategyWorkflowBase
{
    public override int Version => 1;

    public override Guid WorkflowId => new Guid("499a81f8-187f-4287-a514-08cd48452107");

    public override string Name => "Production Test - Approve Eligibility Strategy";

    public override string Description => "";


    public ProductionTest_Approve_EligibilityStrategy(Order order, Merchant merchant, bool offSessionRetry) :
        base(order, merchant, offSessionRetry)
    {
    }

    #region Workflow

    public override async Task<WorkflowBuilder<EligibilityCheckContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<EligibilityCheckContext>();

        workflow
            .INLINE_SUBFLOW(CreateEligibilityWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateEligibilityWorkflow(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        workflow.Add(nameof(E9998_PRODUCTION_TEST_ORDER_SetIsTestOrderFlag));

        workflow.Add(nameof(E0018_EnsureMerchantIsActiveAndNonLocked));
        workflow.Add(nameof(E0017_InitialTransactionChecks));

        workflow.Add(nameof(E0021_UpdatePaymentInstrumentInformation));


        workflow.Add(nameof(E9999_PRODUCTION_TEST_ORDER_SetOfferState_Stage3Passed));

        workflow.Add(nameof(E5001_ProcessEligibilityResult));

        //eligibilitySequence.Add(nameof(E5002_UpdateFingerprints));
    }

    #endregion
}