using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Common;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.General;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RequestModification;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RiskManagement;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RuleEngine;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RuleEngine.Everi;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.Testing;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.Tokenization;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.DisputeOptimization;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Entities.Enums;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl.Controls;

namespace FlexCharge.Eligibility.EligibilityWorkflows.Implementations.EveriTerminal;

public class EveriTerminal_EligibilityStrategy : EligibilityStrategyWorkflowBase
{
    public override int Version => 1;

    public override Guid WorkflowId => new("b5b932e1-a941-4d19-b621-be3a252090b8");

    public override string Name => "Everi - Eligibility Strategy";

    public override string Description => "";


    public EveriTerminal_EligibilityStrategy(Order order, Merchant merchant, bool offSessionRetry) : base(order,
        merchant,
        offSessionRetry)
    {
    }

    static IssuersToRemoveDeviceList _issuersToRemoveDevice = new();

    #region Strategy-Specific Parameters

    private IntegerValue _PspLoadBalancing_RoundRobinPercentage;
    private IntegerValue _PspLoadBalancing_WeightedPercentage;

    #endregion


    #region Workflow

    private void CreateWorkflowExternalControls(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        _PspLoadBalancing_RoundRobinPercentage = workflow.AddExternalControl(
            new IntegerValue(new Guid("bb91df3f-6b58-4479-9e4d-ec2c684f6198"),
                "Round Robin Strategy %",
                "Sets percentage of orders that will be processed using Round Robin strategy. The rest will be routing to other strategies.",
                ExternalControls.CascadeStrategyGroup.Name,
                ExternalControls.CascadeStrategyGroup.PaymentProcessorLoadBalancing.Name,
                ExternalControlOwnerType.Workflow,
                nameof(EveriTerminal_EligibilityStrategy),
                defaultValue: 100,
                minValue: 0, maxValue: 100));

        _PspLoadBalancing_WeightedPercentage = workflow.AddExternalControl(
            new IntegerValue(new Guid("dc0a5a2e-ae72-4aa7-b119-127cfd65be7e"),
                "Weighted Load Balancing Strategy %",
                "Sets percentage of orders that will be processed using Weighted Load Balancing strategy. The rest will be routed to random load balancer",
                ExternalControls.CascadeStrategyGroup.Name,
                ExternalControls.CascadeStrategyGroup.PaymentProcessorLoadBalancing.Name,
                ExternalControlOwnerType.Workflow,
                nameof(EveriTerminal_EligibilityStrategy),
                defaultValue: 0,
                minValue: 0, maxValue: 100));
    }


    public override async Task<WorkflowBuilder<EligibilityCheckContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<EligibilityCheckContext>();

        CreateWorkflowExternalControls(workflow);

        await _issuersToRemoveDevice.LoadAsync(skipLoading: true);


        workflow
            .INLINE_SUBFLOW(CreateEligibilityWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateEligibilityWorkflow(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        bool useSalePayment = Order.GetDesiredPaymentTransactionType() == DesiredPaymentTransactionType.Sale;

        //!!!!!!DO NOT INSERT CURES ABOVE THIS!!!!!!
        if (Order.IsMIT())
        {
            Workspan.Current!
                .Log.Fatal("MIT orders are not supported for Everi integration");

            throw new NotEligibleException("MIT is not supported for Everi integration");
        }

        if (useSalePayment)
        {
            Workspan.Current!
                .Log.Fatal("Sale payment is not supported for Everi integration");

            throw new NotEligibleException("Sale payment is not supported for Everi integration");
        }

        workflow.Add(nameof(E0015_SetOfferState_EvaluationStarted));

        workflow.Add(nameof(E0018_EnsureMerchantIsActiveAndNonLocked));

        workflow.Add(nameof(E2012_EvaluateRequestRewriter));

        workflow.Add(nameof(E0017_InitialTransactionChecks));

        workflow.Add(nameof(E0021_UpdatePaymentInstrumentInformation));
        workflow.Add(nameof(E1002_LuhnCheck));


        workflow.Add(nameof(E0019_OrderAmountCheck));

        workflow.Add(nameof(E0016_EnsureNoOtherEligibleOrderWithTheSameSenseKeyExists));

        workflow.Add(nameof(E2010_LoadFingerprints));
        workflow.Add(nameof(E2002_LoadFingerprintStatistics));
        workflow.Add(nameof(E2008_FingerprintsCheck));
        workflow.Add(nameof(EligibilityChecks.Implementations.TEMPORARY
            .E9902_TemporarilyDisableNonVisaOrMastercardCards));
        workflow.Add(nameof(E2004_BlockByFingerprintLists));

        workflow.Add(nameof(E1001_BinCheck));

        workflow.Add(nameof(E2013_BlockByDisputeOptimization));

        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
        {
            workflow.Add(nameof(E9005_ReplacePaymentInstrumentCountryWithUS));
        }

        workflow.Add(nameof(E2007_BlockByCardCountry));

        workflow.Add(nameof(E2006_SkipPaymentProvidersByBinResults));

        workflow.Add(nameof(E2011_FingerprintsOpenOffersCheck));
        workflow.Add(nameof(E2014_FingerprintsWrittenOffOrdersCheck));

        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
        {
            workflow.Add(nameof(E9004_ReplacePaymentInstrumentTypeWithCREDIT));
        }

        var parallelBlockBuilder1 = workflow.AddParallelBlock();

        parallelBlockBuilder1.Add(nameof(E3003_Try3DSFrictionlessAuthentication));

        // CREATE DETERMINE PAYMENT ROUTING STRATEGY SUB FLOW
        var determinePaymentRoutingStrategySubFlow =
            CreateDeterminePaymentRoutingStrategySubFlow(workflow, Merchant);

        workflow.Add(nameof(E3002_TryFullAuthorization));

        // Connect multiple outs block's terminal blocks to the next block in the sequence as we now we have next block as current
        determinePaymentRoutingStrategySubFlow.ContinueWith(workflow.CurrentBlock);


        workflow.Add(nameof(E3005_TryACHDebitPayment));

        workflow.Add(nameof(E2016_BlockStolenCards));
        //workflow.Add(nameof(E2015_BlockNSFCodeForPrepaidCards));

        workflow.Add(nameof(E0003_SetOfferState_Stage1Passed));
        workflow.Add(nameof(E0002_RuleEngineStage2));
        workflow.Add(nameof(E0007_OfferNSFRequestsThrottlingLimiting));


        var parallelBlockBuilder2 = workflow.AddParallelBlock();
        parallelBlockBuilder2.Add(nameof(E2001_FraudCheck));
        parallelBlockBuilder2.Add(nameof(E4001_CreditBureauCheck));


        workflow.Add(nameof(E0004_SetOfferState_Stage2Passed));
        workflow.Add(nameof(E0016_EnsureNoOtherEligibleOrderWithTheSameSenseKeyExists));
        workflow.Add(nameof(E0028_RuleEngineStage3_Everi));
        workflow.Add(nameof(E0023_DetermineRecycleStrategy));

        if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
        {
            workflow.Add(nameof(E9006_TestTerminal_CITCuresOnCheckoutPage));
        }

        workflow.Add(nameof(E5001_ProcessEligibilityResult));

        workflow.Add(nameof(E5004_SendCheckoutLinkToConsumer));
    }

    private IfBlockMultipleExitsBuilder<EligibilityCheckContext>
        CreateDeterminePaymentRoutingStrategySubFlow(
            WorkflowBuilder<EligibilityCheckContext> workflow, Entities.Merchant merchant)
    {
        var selectRoundRobinStrategyOrSomeOtherABTestBlockBuilder =
            workflow.AddABTestBlock("RoundRobinLoadBalancingOrOther",
                context => context.GetExternalParameterValue(_PspLoadBalancing_RoundRobinPercentage));

        #region Blocks to select strategies

        var useRoundRobinStrategyTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();

        useRoundRobinStrategyTransformBlock.SetTransformation(
            $"SetPaymentRoutingStrategy_{PaymentRoutingStrategyTypes.RoundRobinLoadBalancing}",
            context =>
            {
                context.Order.PaymentRoutingStrategy = nameof(PaymentRoutingStrategyTypes.RoundRobinLoadBalancing);
            });

        var useWeightedLoadBalancingStrategyTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();

        useWeightedLoadBalancingStrategyTransformBlock.SetTransformation(
            $"SetPaymentRoutingStrategy_{PaymentRoutingStrategyTypes.WeightedLoadBalancing}",
            context =>
            {
                context.Order.PaymentRoutingStrategy = nameof(PaymentRoutingStrategyTypes.WeightedLoadBalancing);
            });

        var useRandomLoadBalancingStrategyTransformBlock =
            new EligibilityChecks.Workflow.Steps.TransformBlock<EligibilityCheckContext>();

        useRandomLoadBalancingStrategyTransformBlock.SetTransformation(
            $"SetPaymentRoutingStrategy_{PaymentRoutingStrategyTypes.RandomLoadBalancing}",
            context =>
            {
                context.Order.PaymentRoutingStrategy = nameof(PaymentRoutingStrategyTypes.RandomLoadBalancing);
            });

        #endregion

        selectRoundRobinStrategyOrSomeOtherABTestBlockBuilder.AddCaseA(useRoundRobinStrategyTransformBlock);

        var selectWeightedOrRandomStrategyABTestBlockBuilder =
            workflow.AddABTestBlock("WeightedOrRandomLoadBalancing",
                context => context.GetExternalParameterValue(_PspLoadBalancing_WeightedPercentage));

        selectRoundRobinStrategyOrSomeOtherABTestBlockBuilder.AddCaseB(selectWeightedOrRandomStrategyABTestBlockBuilder
            .Block);

        selectWeightedOrRandomStrategyABTestBlockBuilder.AddCaseA(useWeightedLoadBalancingStrategyTransformBlock);
        selectWeightedOrRandomStrategyABTestBlockBuilder.AddCaseB(useRandomLoadBalancingStrategyTransformBlock);


        var ifPaymentRoutingStrategyNotDetermined =
            workflow
                .IF("PaymentRoutingStrategy not determined?",
                    context => string.IsNullOrWhiteSpace(context.Order.PaymentRoutingStrategy))
                .TRUE(t => t
                    .BLOCK(selectRoundRobinStrategyOrSomeOtherABTestBlockBuilder.Block));

        return ifPaymentRoutingStrategyNotDetermined;
    }

    #endregion
}