using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.EligibilityChecks;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.ConsumerNotifications.Stripe;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.Entities;

namespace FlexCharge.Eligibility.EligibilityWorkflows.Implementations.ExternalTokenDunning;

public class ExternalTokenDunning_NotEligible_OrderProcessingStrategy : NotEligible_OrderProcessingWorkflowBase
{
    public override int Version => 1;

    public override Guid WorkflowId => _stableId;

    public override string Name => "External Token Dunning - Not Eligible - Order Processing Strategy";

    public override string Description => "";

    // This is a temporary solution until Merchant-related workflow management is implemented
    private static Guid _stableId = new("17e9354b-f722-49de-ad42-14ee181a8ef2");
    public static Guid StableId => _stableId;

    #region Workflow

    public override async Task<WorkflowBuilder<EligibilityCheckContext>> CreateWorkflowAsync()
    {
        var workflow = new WorkflowBuilder<EligibilityCheckContext>();

        workflow
            .INLINE_SUBFLOW(CreateProcessingWorkflow)
            .FINAL_END();
        ;

        return await Task.FromResult(workflow);
    }

    public void CreateProcessingWorkflow(WorkflowBuilder<EligibilityCheckContext> workflow)
    {
        workflow.Add(nameof(E5005_SendEmail_Stripe_UpdateYourBillingInformation));
        //workflow.Add(nameof(E3007_AccountUpdater));
    }

    #endregion
}