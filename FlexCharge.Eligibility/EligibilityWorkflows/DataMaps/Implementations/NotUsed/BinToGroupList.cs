// using System.Collections.Generic;
// using FlexCharge.Common.Telemetry;
//
// namespace FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.NotUsed;
//
// class BinToGroupList : ValidatableDataMapBase<BinGroupRecord, int, BinGroupRecord>
// {
//     protected override string SourceFilePath => $"./Resources/Lists/BinLists/BinToGroupList.csv";
//
//     protected override void ImportRecord(Dictionary<int, BinGroupRecord> map, BinGroupRecord record)
//     {
//         if (string.IsNullOrWhiteSpace(record.Bin_6Digits) || !int.TryParse(record.Bin_6Digits, out var bin))
//         {
//             Workspan.Current?.Log.Fatal("Invalid BIN: {Bin}", record.Bin_6Digits);
//
//             return;
//         }
//
//         AddMapItem(map, bin, record);
//     }
// }
//
// record BinGroupRecord
// {
//     public string Bin_6Digits { get; set; }
//     public string BinCheck_BankNameAgg { get; set; }
//     public string BinCheck_CardNetwork { get; set; }
//     public string BinCheck_CardType_Cleaned { get; set; }
//     public string BinCheck_CardLevelAgg { get; set; }
//     public string BinGroup_CIT { get; set; }
//     public string BinGroup_MIT { get; set; }
// }

