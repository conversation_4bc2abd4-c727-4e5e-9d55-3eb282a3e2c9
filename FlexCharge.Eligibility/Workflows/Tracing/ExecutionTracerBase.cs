using System.Text;
using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.Eligibility.Workflows.Tracing;

public abstract class ExecutionTracerBase : IExecutionTracer
{
    protected abstract void WriteLine(StringBuilder message);

    protected void WriteLine(string message, int ident)
    {
        StringBuilder traceLine = new();

        traceLine.Append(' ', ident);
        traceLine.Append(message);

        WriteLine(traceLine);
    }

    public void WriteInnerAction(string message)
    {
        WriteLine(message, 4);
    }

    public void WriteBlockAction(IBlock block, string message = null)
    {
        StringBuilder traceLine = new StringBuilder();

        traceLine.Append(block.Name);

        if (block.InstanceName != null)
        {
            traceLine.Append(" (");
            traceLine.Append(block.InstanceName);
            traceLine.Append(")");
        }

        if (message != null)
        {
            traceLine.Append(": ");
            traceLine.Append(message);
        }

        WriteLine(traceLine);
    }

    public abstract string GetTraceAsString();
}