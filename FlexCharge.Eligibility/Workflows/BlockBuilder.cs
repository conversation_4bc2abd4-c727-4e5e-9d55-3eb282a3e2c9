using System;
using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow;

public class BlockBuilder<TContext>
    where TContext : IExecutionContext
{
    protected BlockBuilder()
    {
    }

    public BlockBuilder(IBlock<TContext> block)
    {
        Block = block;
    }

    public virtual IBlock<TContext> Block { get; }

    public IBlock<TContext> ContinueWith(IBlock<TContext> nextBlock)
    {
        return ContinueWith(Block, nextBlock);
    }

    public static IBlock<TContext> ContinueWith(IBlock<TContext> block, IBlock<TContext> nextBlock)
    {
        var terminalLinks = block.GetTerminalLinks();

        foreach (var terminalLink in terminalLinks)
        {
            ((IBlock<TContext>) terminalLink.Source).AttachNext(nextBlock, terminalLink.ConnectorIndex);
        }

        return nextBlock;
    }

    public static implicit operator BlockBuilder<TContext>(BlockBase<TContext> block)
    {
        return new BlockBuilder<TContext>(block);
    }

    public WorkflowBuilder<TContext> CONTINUE( /*Action<WorkflowBuilder<TContext>> builder*/)
    {
        var continuationBranch = new WorkflowBuilder<TContext>();

        //builder(continuationBranch);

        continuationBranch.BLOCK(new ContinuationDummyBlock<TContext>());

        ContinueWith(continuationBranch.StartBlock);

        return continuationBranch;
    }
}