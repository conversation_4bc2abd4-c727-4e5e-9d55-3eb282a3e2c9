using System;
using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.Eligibility.Workflows;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow;

public class IfBlockMultipleExitsBuilder<TContext> : BlockMultipleExitsBuilder<TContext>
    where TContext : IExecutionContext
{
    public override IBlock<TContext> Block => _block;

    private readonly IfBlock<TContext> _block;

    public IfBlockMultipleExitsBuilder(IfBlock<TContext> block)
    {
        _block = block;
    }


    public IfBlockMultipleExitsBuilder<TContext> AddTrueCase(BlockBuilder<TContext> block)
    {
        _block.AttachTrue(block.Block);
        return this;
    }

    public IfBlockMultipleExitsBuilder<TContext> AddFalseCase(BlockBuilder<TContext> block)
    {
        _block.AttachFalse(block.Block);
        return this;
    }

    public IfBlockMultipleExitsBuilder<TContext> TRUE(Action<WorkflowBuilder<TContext>> builder)
    {
        WorkflowBuilder<TContext> blockBuilder = new();

        builder(blockBuilder);

        _block.AttachTrue(blockBuilder.StartBlock);

        return this;
    }

    public IfBlockMultipleExitsBuilder<TContext> FALSE(Action<WorkflowBuilder<TContext>> builder)
    {
        WorkflowBuilder<TContext> blockBuilder = new();

        builder(blockBuilder);

        _block.AttachFalse(blockBuilder.StartBlock);

        return this;
    }
}