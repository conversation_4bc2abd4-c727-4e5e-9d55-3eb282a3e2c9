using System.Threading.Tasks;
using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.Eligibility.Workflows.Blocks;

public class GotoEndBlock<TContext> : TerminalBlockBase<TContext>
    where TContext : IExecutionContext
{
    public override BlockType Type => BlockType.GotoEnd;

    public override string Name => "GOTO END";
    public override string FullName => "Goto End Block";

    protected override Task RunInternal()
    {
        return Task.CompletedTask;
    }
}