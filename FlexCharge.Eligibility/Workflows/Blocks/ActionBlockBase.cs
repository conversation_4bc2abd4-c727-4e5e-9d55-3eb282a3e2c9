using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.WorkflowEngine.Workflows;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;

public abstract class ActionBlockBase<TContext> : BlockBase<TContext>, IActionBlock<TContext>
    where TContext : IExecutionContext
{
    public IBlock<TContext> NextLink { get; set; }

    public void AttachNext(IBlock<TContext> nextBlock)
    {
        nextBlock.AttachToPrevious(this);

        NextLink = nextBlock;
    }

    public override IEnumerable<IBlockLink> GetNextLinks()
    {
        return new List<BlockLink>() {new BlockLink(this) {Target = NextLink, Name = "Next", ConnectorIndex = 0}};
    }


    public override void AttachNext(IBlock<TContext> nextBlock, int connectorIndex)
    {
        if (connectorIndex != 0)
            throw new ArgumentException("Action blocks do not support multiple connections", nameof(connectorIndex));

        AttachNext(nextBlock);
    }

    protected sealed override async Task RunInternal()
    {
        await Execute();

        if (NextLink != null)
            await NextLink.Run();
    }

    public abstract Task Execute();
}