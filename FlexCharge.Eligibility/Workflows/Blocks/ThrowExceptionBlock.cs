using System;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Eligibility.Workflows.Exceptions;
using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.Eligibility.Workflows.Blocks;

public class ThrowExceptionBlock<TContext> : TerminalBlockBase<TContext>
    where TContext : IExecutionContext
{
    public override BlockType Type => BlockType.Default;

    public override string Name => "EXCEPTION";
    public override string FullName => "Exception Block";

    public Type ExceptionType { get; set; } = typeof(FlexChargeException);
    public string Message { get; set; }

    protected override Task RunInternal()
    {
        if (ExceptionType is null)
            throw new WorkflowInvalidParameterException(nameof(ExceptionType), "Cannot be null");

        if (!ExceptionType.IsSubclassOf(typeof(Exception)))
            throw new WorkflowInvalidParameterException(nameof(ExceptionType), "Should be a subclass of Exception");

        var exception = Activator.CreateInstance(ExceptionType, Message) as Exception;

        throw exception!;
    }
}