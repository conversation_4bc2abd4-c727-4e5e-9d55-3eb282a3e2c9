using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.UIBuilder;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl.Controls;
using FlexCharge.WorkflowEngine.Workflows;
using Microsoft.Extensions.DependencyInjection;
using MassTransit;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;

public abstract class BlockImplementationBase<TContext> : IBlockImplementation<TContext>
    where TContext : IExecutionContext
{
    private string _name;
    private ValueStorage _valueStorage;
    protected IActivityService ActivityService { get; private set; }

    public string BlockId
    {
        get
        {
            if (_name is null)
            {
                string name = FullName;

                int underscoreIndex = name.IndexOf('_');
                if (underscoreIndex > 0)
                {
                    name = name.Substring(0, underscoreIndex);
                }

                _name = name;
            }

            return _name;
        }
    }

    private string _stepFullName = null;
    public string FullName => _stepFullName ?? (_stepFullName = GetType().Name);

    public TContext Context { get; set; }

    public virtual bool IsImplemented => true;
    public abstract bool IsProductionBlock { get; }
    public bool IsTestBlock => !IsProductionBlock;

    protected IServiceScopeFactory ServiceScopeFactory { get; private set; }

    public virtual void Initialize(TContext context, IServiceScopeFactory serviceScopeFactory,
        ValueStorage valueStorage)
    {
        Context = context;
        ServiceScopeFactory = serviceScopeFactory;
        _valueStorage = valueStorage;
    }

    public virtual Task<BlockEditor> GetBlockEditorAsync(Dictionary<string, string> blockParameters,
        IServiceScopeFactory serviceScopeFactory) => null;

    public virtual async Task<List<AnswerValidationError>> TryUpdateBlockEditableParametersAsync(
        IDictionary<Guid, string> answers,
        IServiceScopeFactory serviceScopeFactory)
    {
        return await Task.FromResult(new List<AnswerValidationError>());
    }

    public virtual Dictionary<string, string> GetBlockParameters() => new();

    protected void Log(string message)
    {
        Workspan.Current?.Log.Information("CHECK #{CheckId}: {Message}", BlockId, message);
    }

    protected void LogError(Exception e, string errorMessage)
    {
        Workspan.Current?.RecordException(e, "CHECK #{CheckId} > {ErrorMessage}", BlockId,
            errorMessage); //> {JsonConvert.SerializeObject(Payload)}");
    }


    protected void LogError(string errorMessage)
    {
        Workspan.Current?.Log.Error("CHECK #{CheckId} > {ErrorMessage} ", BlockId, errorMessage);
    }

    protected void LogFatalError(Exception e, string errorMessage)
    {
        Workspan.Current?.RecordFatalException(e, "CHECK #{CheckId} > {ErrorMessage}", BlockId,
            errorMessage); //> {JsonConvert.SerializeObject(Payload)}");
    }

    protected void LogFatalError(string errorMessage)
    {
        Workspan.Current?.Log.Fatal("CHECK #{CheckId} > {ErrorMessage} ", BlockId, errorMessage);
    }

    protected void LogWarning(string warningMessage)
    {
        Workspan.Current?.Log.Warning("CHECK #{CheckId} > {ErrorMessage} ", BlockId, warningMessage);
    }

    #region Commented

    // protected T GetStoredValue<T>(string name)
    // {
    //     return _valueStorage.GetResult<T>(name);
    // }
    //
    // protected void StoreValue<T>(string name, T value)
    // {
    //     _valueStorage.StoreValue(name, value);
    // }

    #endregion

    protected abstract Task ExecuteBlockAsync();

    protected virtual async Task ExecuteInternalAsync()
    {
        await ExecuteBlockAsync();
    }

    private IServiceScope _serviceScope;

    protected virtual async Task InitializeScopeAsync()
    {
        _serviceScope = ServiceScopeFactory.CreateScope();
        ServiceProvider = _serviceScope.ServiceProvider;

        ActivityService = ServiceProvider.GetRequiredService<IActivityService>();

        await Task.CompletedTask;
    }

    protected virtual async Task DisposeScopeAsync()
    {
        _serviceScope.Dispose();

        ServiceProvider = null;
        ActivityService = null;
    }

    public async Task ExecuteAsync()
    {
        using var workspan = Workspan.Start<BlockImplementationBase<TContext>>(name: FullName);

        if (EnvironmentHelper.IsInProduction)
        {
            if (IsTestBlock)
            {
                throw new Exception($"Can't run test block {FullName} in Production environment");
            }
        }

        try
        {
            await InitializeScopeAsync();

            InitializeBeforeExecution();

            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start(); // execution time includes CanBeExecutedAsync time

            if (await CanBeExecutedAsync())
            {
                #region Executing the eligibility check

                try
                {
                    #region Creating Activity

                    await ActivityService.CreateActivityAsync(EligibilityActivities.EligibilityChecks_ExecutionStarted,
                        set => set
                            .TenantId(Context.TenantId)
                            .Subcategory(FullName)
                            .CorrelationId(Context.CorrelationId)
                            .Meta(meta => meta
                                .SetValue("Check", FullName)));

                    #endregion

                    await ExecuteInternalAsync();
                }
                catch (Exception e) when (e is not NotEligibleException)
                {
                    #region Creating Activity

                    await ActivityService.CreateActivityAsync(
                        EligibilityErrorActivities.EligibilityChecks_ExecutionError,
                        set => set
                            .TenantId(Context.TenantId)
                            .Subcategory(FullName)
                            .CorrelationId(Context.CorrelationId)
                            .Meta(meta => meta
                                .SetValue("Check", FullName)));

                    #endregion

                    workspan.RecordException(e, "Error executing step {FullName}", FullName);
                    throw;
                }
                finally
                {
                    stopwatch.Stop();

                    #region Creating Activity

                    await ActivityService.CreateActivityAsync(EligibilityActivities.EligibilityChecks_ExecutionEnded,
                        set => set
                            .TenantId(Context.TenantId)
                            .Subcategory(FullName)
                            .CorrelationId(Context.CorrelationId)
                            .Meta(meta => meta
                                .SetValue("Check", FullName)
                                .ProcessingTime(stopwatch.ElapsedMilliseconds)
                            ));

                    #endregion
                }

                #endregion
            }
            else
            {
                await ActivityService.CreateActivityAsync(EligibilityActivities.EligibilityChecks_CannotBeExecuted,
                    set => set
                        .TenantId(Context.TenantId)
                        .Subcategory(FullName)
                        .CorrelationId(Context.CorrelationId)
                        .Meta(meta => meta
                            .SetValue("Check", FullName)));
            }
        }
        finally
        {
            await DisposeScopeAsync();
        }
    }


    protected virtual void InitializeBeforeExecution()
    {
    }

    protected virtual async Task<bool> CanBeExecutedAsync() => await Task.FromResult(true);

    public IServiceProvider ServiceProvider { get; private set; }

    protected T GetRequiredService<T>()
    {
        return ServiceProvider.GetRequiredService<T>();
    }

    protected IRequestClient<T> CreateMassTransitRequestClient<T>()
        where T : class
    {
        return ServiceProvider.CreateRequestClient<T>();
    }

    protected async Task AddActivityAsync<TActivityNameEnum>(TActivityNameEnum activityNameEnum,
        string eventName = null,
        object data = null,
        string subcategory = null,
        Action<IPayloadMetadataSetter> meta = null
    )
        where TActivityNameEnum : Enum
    {
        await ActivityService.CreateActivityAsync(activityNameEnum,
            set => set
                .TenantId(Context.TenantId)
                .CorrelationId(Context.CorrelationId)
                .Subcategory(subcategory)
                .EventRaised(eventName)
                .Data(data)
                .Meta(meta));
    }

    #region External Control

    List<IExternalControl> _externalControls = new();

    public IEnumerable<IExternalControl> GetExternalControls()
    {
        return _externalControls;
    }

    protected TExternalControl AddExternalControl<TExternalControl>(TExternalControl externalControl)
        where TExternalControl : IExternalControl
    {
        _externalControls.Add(externalControl);

        return externalControl;
    }


    protected async Task<bool> IsOffSwitchEngagedAsync(OffSwitch offSwitch)
    {
        if (offSwitch.IsSwitchedOff())
        {
            Log($"Off switch engaged: {offSwitch.Name}");
            await AddActivityAsync(WorkflowActivities.OffSwitch_Engaged,
                meta: meta => meta
                    .SetValue("Name", offSwitch.Name)
                    .SetValue("Group", offSwitch.Group)
                    .SetValue("SubGroup", offSwitch.SubGroup)
                    .SetValue("Id", offSwitch.Id)
            );

            return true;
        }

        return false;
    }

    protected int GetExternalControlValue(IntegerValue integerValue)
    {
        int value = integerValue.DefaultValue;

        if (integerValue.Value.HasValue)
        {
            if (integerValue.IsValidValue())
            {
                value = integerValue.Value.Value;
            }
            else
            {
                // Never should be here if UI validation is correct
                LogFatalError(
                    $"External control {integerValue.Name} value {value} is not valid - using default value {integerValue.DefaultValue}");
            }
        }

        Log($"External control {integerValue.Name} = {value}");


        return value;
    }

    #endregion
}