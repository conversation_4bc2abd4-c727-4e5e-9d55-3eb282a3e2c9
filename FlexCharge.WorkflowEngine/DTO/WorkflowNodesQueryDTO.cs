using System.Collections.Generic;

namespace FlexCharge.WorkflowEngine.DTO;

public class WorkflowNodesQueryDTO
{
    public List<WorkflowNodeCategoryDTO> Categories { get; set; } = new();
}

public class WorkflowNodeCategoryDTO
{
    public string CategoryName { get; set; }
    public string CategoryDescription { get; set; }
    public List<WorkflowNodeDTO> AvailableNodes { get; set; }
}

public class WorkflowNodeDTO
{
    public string FullName { get; set; }
    public string Name { get; set; }
    public string Type { get; set; }
    public string Description { get; set; }
    public string Icon { get; set; }


    public object InitialData { get; set; }
}