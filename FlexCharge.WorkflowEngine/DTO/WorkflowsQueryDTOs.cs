// using System;
// using FlexCharge.Common.AutoMapper;
// using FlexCharge.Common.Response;
//
// namespace FlexCharge.WorkflowEngine.DTO;
//
// public class DomainsQueryDTO
// {
//     public Guid Id { get; set; }
//     public string Name { get; set; }
// }
//
// public class DomainsQueryResponseDTO : BaseResponse
// {
//     public PagedDTO<DomainsQueryItemDTO> Items { get; set; }
//     public bool Success { get; set; }
//
//     public class DomainsQueryItemDTO
//     {
//         public Guid Id { get; set; }
//         public string Name { get; set; }
//     }
// }

