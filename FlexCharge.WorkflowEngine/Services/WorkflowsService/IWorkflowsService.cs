using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.WorkflowEngine.DTO;
using FlexCharge.WorkflowEngine.Entities;
using FlexCharge.WorkflowEngine.Workflows;
using ParameterValidationError = FlexCharge.Contracts.Commands.Workflows.ParameterValidationError;
using WorkflowDefinition = FlexCharge.WorkflowEngine.Workflows.WorkflowDefinition;

namespace FlexCharge.WorkflowEngine.Services.WorkflowsService;

public interface IWorkflowsService
{
    Task<WorkflowDefinition?> GetWorkflowDefinitionOrDefaultAsync(Guid id);
    Task<Entities.Workflow> GetActualWorkflowIdOrDefaultAsync(Guid workflowId);

    BlockDefinition? GetBlockDefinitionOrDefault(WorkflowDefinition workflowDefinition, string nodeName);

    Task<BlockEditor> GetBlockEditorByNodeOrDefaultAsync(WorkflowDefinition workflowDefinition, string nodeName,
        Dictionary<string, string> blockParameters);

    Task<(IDictionary<string, string> Parameters, IList<ParameterValidationError> ValidationErrors)>
        UpdateBlockParametersAsync(WorkflowDefinition workflowDefinition, string blockFullName,
            IDictionary<Guid, string> answers);

    Task<BlockEditor> GetBlockEditorOrDefaultAsync(WorkflowDefinition workflowDefinition,
        string blockFullName, Dictionary<string, string> blockParameters);

    Task<WorkflowsQueryResponse> GetWorkflows(string? query, List<WorkflowStatuses>? status, List<string>? domains,
        DateTime? from, DateTime? to, DateTime? modifiedFrom, DateTime? modifiedTo, DateTime? runFrom,
        DateTime? runTo, int pageSize, int pageNumber);

    Task<Workflow> GetWorkflowByIdAsync(Guid id);

    Task CreateWorkflowAsync(WorkflowCreateDTO payload);
    Task UpdateWorkflowAsync(WorkflowUpdateDTO payload);
    Task DeleteWorkflowAsync(Guid id);
    Task PublishWorkflowAsync(Guid id);
    Task<Guid> SaveWorkflowAsync(WorkflowSaveDTO payload);

    Task<List<ExternalControlDefinition>> GetExternalControlsOrDefaultAsync(Guid id);
}