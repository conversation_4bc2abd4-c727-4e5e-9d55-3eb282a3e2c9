using System.ServiceModel;
using FlexCharge.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace FlexCharge.WorkflowEngine.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class HealthController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly AppOptions _globalData;

        public HealthController(ILogger<HealthController> logger, IOptions<AppOptions> globalData)
        {
            _logger = logger;
            _globalData = globalData.Value;
        }


        [HttpGet]
        [ProducesResponseType(200)]
        public ActionResult Get()
        {
            // _logger.LogInformation($"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");
            return Ok();
        }
    }
}