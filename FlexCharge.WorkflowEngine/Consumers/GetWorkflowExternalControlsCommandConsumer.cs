using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands.Workflows;
using FlexCharge.WorkflowEngine.Common.Services.WorkflowService;
using FlexCharge.WorkflowEngine.Services.WorkflowsService;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.WorkflowEngine.Consumers;

public class GetWorkflowExternalControlsCommandConsumer : CommandConsumer<GetWorkflowExternalControlsCommand,
    GetWorkflowExternalControlsCommandResponse>
{
    private readonly IWorkflowsService _workflowsService;

    public GetWorkflowExternalControlsCommandConsumer(IServiceScopeFactory serviceScopeFactory,
        IWorkflowsService workflowsService) : base(
        serviceScopeFactory)
    {
        _workflowsService = workflowsService;
    }

    protected override async Task<GetWorkflowExternalControlsCommandResponse> ConsumeCommand(
        GetWorkflowExternalControlsCommand command, CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("WorkflowId", command.WorkflowId);

        var workflow = await _workflowsService.GetActualWorkflowIdOrDefaultAsync(command.WorkflowId);

        if (workflow == null)
            throw new FlexChargeException($"Workflow not found {command.WorkflowId}");

        var externalControls = await _workflowsService.GetExternalControlsOrDefaultAsync(workflow.Id);

        return new GetWorkflowExternalControlsCommandResponse
        {
            ExternalControls = externalControls.Select(ec =>
                new GetWorkflowExternalControlsCommandResponse.ExternalControlDefinition
                {
                    Type = ec.Type,
                    Id = ec.Id,
                    Name = ec.Name,
                    Description = ec.Description,
                    Group = ec.Group,
                    SubGroup = ec.SubGroup,
                    OwnerType = ec.OwnerType,
                    OwnerName = ec.OwnerName,
                    DefaultValue = ec.DefaultValue,
                    MinValue = ec.MinValue,
                    MaxValue = ec.MaxValue
                }).ToList()
        };
    }
}