using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using MassTransit;

namespace FlexCharge.Identity.Consumers;

// TODO this event will be deprecated
public class AccountManagerSendEmailConsumer : IConsumer<AccountManagerSendEmailEvent>
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IEmailSender _emailSender;
    private readonly IConfiguration _configuration;


    public AccountManagerSendEmailConsumer(PostgreSQLDbContext dbContext, IEmailSender emailSender,
        IConfiguration configuration)
    {
        _dbContext = dbContext;
        _emailSender = emailSender;
        _configuration = configuration;
    }

    public async Task Consume(ConsumeContext<AccountManagerSendEmailEvent> context)
    {
        using var workspan = Workspan.Start<AccountManagerSendEmailConsumer>()
            .Context(context);

        try
        {
            var accountManager = await _dbContext.Users.FindAsync(context.Message.AccountManagerId);

            if (accountManager == null)
            {
                throw new Exception("Account Manager not found");
            }

            // https://flex-charge.atlassian.net/wiki/spaces/PF/pages/*********/Self+boarding+-+Application+Review+Notifications+flow#Account-Manager---Partner---Application-notification
            var templateId = _configuration.GetValue<string>("email:accountManagerNotificationEmailTemplateId");

            await _emailSender.SendEmailAsync(accountManager.Email,
                $"New message about your application",
                context.Message.Comment,
                new
                {
                    partner_color = "e5575b",
                    user_first_name = accountManager.FirstName,
                    applicant_dba = context.Message.Dba,
                    partner_name = context.Message.LegalEntityName,
                    base_url = _configuration.GetValue<string>("email:baseUrl"),
                    path = $"/accounts/applications/",
                    notification = context.Message.Comment,
                    partner_onboarding_email = "<EMAIL>",
                    email = context.Message.Email,
                    email_from = "<EMAIL>",
                    slug = "",

                    partner_address_line_1 = context.Message.PartnerAddressLine1,
                    partner_address_line_2 = context.Message.PartnerAddressLine2,
                    partner_city = context.Message.PartnerCity,
                    partner_state_province_region = context.Message.PartnerState,
                    partner_postal_code = context.Message.PartnerZipCode,
                    partner_country = context.Message.PartnerCountry,
                    partner_site_url = context.Message.PartnerSiteUrl,
                    partner_privacy_policy_url = context.Message.PartnerPrivacyPolicyUrl,
                    partner_terms_url = context.Message.PartnerTermsAndConditionsUrl,

                    distribution = "internal",
                    template_id = templateId
                },
                templateId);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }
}