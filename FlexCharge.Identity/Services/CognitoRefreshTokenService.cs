using System;
using System.Threading.Tasks;
using Amazon;
using Amazon.CognitoIdentityProvider;
using Amazon.CognitoIdentityProvider.Model;
using Amazon.Runtime;
using FlexCharge.Common.Authentication;
using FlexCharge.Services.Identity.Services;
using Microsoft.Extensions.Logging;

namespace FlexCharge.Identity.Services;

public class CognitoRefreshTokenService : IRefreshTokenService
{
    public ILogger<CognitoRefreshTokenService> _Logger { get; set; }
    public CognitoRefreshTokenService(ILogger<CognitoRefreshTokenService> logger)
    {
        _Logger = logger;
    }
    public async Task AddAsync(Guid userId)
    {
        throw new NotImplementedException();
    }

    public async Task<JsonWebToken> CreateAccessTokenAsync(string refreshToken)
    {
        JsonWebToken response = new JsonWebToken();

        try
        {
            _Logger.LogInformation($"ENTERED: CreateAccessTokenAsync ");
            
            AmazonCognitoIdentityProviderClient _client =
                new AmazonCognitoIdentityProviderClient(
                    new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                        Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                    RegionEndpoint.USEast1);

            var authRequest = new InitiateAuthRequest()
            {
                AuthFlow = AuthFlowType.REFRESH_TOKEN_AUTH, 
                ClientId = Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_CLIENT_ID"),
                //UserPoolId = Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID"),
            };

            authRequest.AuthParameters.Add("REFRESH_TOKEN", refreshToken);

            var authResponse = await _client.InitiateAuthAsync(authRequest);

            response.AccessToken = authResponse.AuthenticationResult.AccessToken;
            response.Id = authResponse.AuthenticationResult.IdToken;
            response.Expires = authResponse.AuthenticationResult.ExpiresIn;
            response.RefreshToken = authResponse.AuthenticationResult.RefreshToken;

            return response;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task RevokeAsync(string refreshToken, Guid userId)
    {
        throw new NotImplementedException();
    }

    public async Task RevokeAllAsync(Guid userId)
    {
        throw new NotImplementedException();
    }
}