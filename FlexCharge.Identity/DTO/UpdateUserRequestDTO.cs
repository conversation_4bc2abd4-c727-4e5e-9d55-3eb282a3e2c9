using System;
using System.ComponentModel.DataAnnotations;

namespace FlexCharge.Identity.DTO;

public class UpdateUserRequestDTO
{
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    [Required]
    public string Email { get; set; }
    public string? Phone { get; set; }
    public string? Group { get; set; }
    public int? Timezone { get; set; }
    public string? Language { get; set; }
    public bool? AuthenticatorAppEnabled { get; set; }
    // public bool? SMSAuthentificationEnabled { get; set; }
    // public string? MFAPhoneNumber { get; set; }
    // public Guid? BackupCode { get; set; }
    // public string? AccessToken { get; set; }
    public string? VerificationCode { get; set; }
    public DateTime? TwoFactorEnabled { get; set; }
    public DateTime? LastMFADate { get; set; }
    public DateTime? LastLoginDate { get; set; }
    public DateTime? LastLogin { get; set; }
    public string? LastDeviceMatch { get; set; }
    public Guid? MerchantId { get; set; }
    public bool? IsPiiMasked { get; set; }
    public bool IsEnforceMFAEnabled { get; set; }
}