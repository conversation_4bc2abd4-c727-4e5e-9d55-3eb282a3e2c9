namespace FlexCharge.Identity
{
    public static class Codes
    {
        public static string EmailInUse => "email_in_use";
        public static string InvalidCredentials => "invalid_credentials";
        public static string InvalidCurrentPassword => "invalid_current_password";
        public static string InvalidEmail => "invalid_email";
        public static string InvalidPhone => "invalid_phone";
        public static string InvalidPassword => "invalid_password";
        public static string InvalidRole => "invalid_role";
        public static string RefreshTokenNotFound => "refresh_token_not_found";
        public static string RefreshTokenAlreadyRevoked => "refresh_token_already_revoked";
        public static string UserNotFound => "user_not_found";
        public static string InvalidDate => "invalid_date";

        public static string General_Error => "general_error";
    }

    public static class Consts
    {
        public const string OTP = "Your FlexCharge login code is: {0}";
        public const string AndroidOTP = "<#> Your FlexCharge login code is: {0} {1}";
        public const string OTP_ERROR = "OTP error";
        public const string ApiKeyError = "please check your keys";
        public const string ApikeyRevoked = "apikey_already_revoked";
        public const string GeneralError = "General error, try again later";
        public const string ConfirmationRequired = "Confirmation is required";
        public const string PasswordChangeRequired = "Change password is required";
        public const string PasswordResetRequired = "Reset password is required";
        public const string PasswordResetExpired = "Reset code is invalid or expired, please request a new code.";
        public const string PasswordResetExpired_code = "P00009";
        public const string PasswordChangeRequired_code = "P00011"; 
        public const string PasswordResetRequired_code = "P00010";
        public const string PasswordNotValid = "PasswordNotValid";
        public const string TryLogin = "Try to sign in";
        public const string PasswordMismatch = "Password mismatch";
        public const string PasswordMismatch_code = "P00015";
        

        public const string LoginInformationError = "User or password is incorrect";

        public const string PhoneMandatory = "Insert phone number";
        public const string PhoneValidationMessage = "Phone not valid";
        public const string PhoneValidationError = "Phone not valid";

        public const string EmailValidationError = "Email not valid";
        
        public const string VerificationCodeError = "Incorrect verification code";
        
        public const string MFANotEnabled = "e_100";
        public const string TooManyLoginAttempts = "e_101";

    }
}