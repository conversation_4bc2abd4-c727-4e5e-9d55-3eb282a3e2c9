{"app": {"name": "identity-service", "version": "0.0.1"}, "AWS": {"Region": "us-east-1", "UserPoolClientId": "4n0i8a4i9o1vk3g3tf64o6blg7", "UserPoolClientSecret": "<your user pool client secret goes here>", "UserPoolId": "us-east-1_rCUpTgXY4"}, "cors": {"Origins": [""]}, "jwt": {"IsExternal": true, "Provider": "Cognito", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "4n0i8a4i9o1vk3g3tf64o6blg7", "Region": "us-east-1", "UserPoolId": "us-east-1_rCUpTgXY4", "AppClientId": "4n0i8a4i9o1vk3g3tf64o6blg7"}, "cache": {"connectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379"}, "email": {"provider": "sendgrid", "key": "*********************************************************************", "supportEmail": "<EMAIL>", "supportEmailTemplateId": "d-f2d69b4bb58348b099ecad5f50424688", "operationsRiskStatusUpdatedId": "d-42d6d861e0f841aa97d05456f8fca0eb", "accountManagerNotificationEmailTemplateId": "d-166e708cee8441eebe0b9c061e3fa3a2", "applicantReviewLink": "https://portal-staging.flex-charge.com/auth/confirm-application", "senderEmail": "<EMAIL>", "senderName": "FlexFactor", "baseURL": "http://localhost:3011"}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/identity-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "swagger": {"enabled": true, "reDocEnabled": false, "name": "v1", "title": "identity-service", "version": "v1", "routePrefix": "", "includeSecurity": true}, "jaeger": {"agentHost": "localhost", "agentPort": 6831}, "AllowedHosts": "*"}