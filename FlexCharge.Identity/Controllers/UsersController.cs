using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Identity.DTO;
using FlexCharge.Identity.Messages.Commands;
using FlexCharge.Identity.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Identity.Migrations;
using FlexCharge.Services.Identity.Services;
using Microsoft.AspNetCore.Authorization;
using Newtonsoft.Json;

namespace FlexCharge.Identity.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class UsersController : BaseController
    {
        private readonly IUsersService _usersService;
        private readonly AppOptions globalOptions;
        private readonly AppOptions _globalData;
        private readonly IIdentityService _identityService;
        // private readonly PostgreSQLDbContext _postgreSqlDbContext;

        public UsersController(IOptions<AppOptions> globalData, IUsersService usersService,
            IOptions<AppOptions> globalOptions, IIdentityService identityService)
        {
            this._usersService = usersService;
            this.globalOptions = globalOptions.Value;
            _globalData = globalData.Value;
            _identityService = identityService;
            // _postgreSqlDbContext = postgreSqlDbContext;
        }

        [HttpPost]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(UsersQueryDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(UsersQueryDTO), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Get(GetUsersRequestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<UsersController>(this, payload, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                UsersQueryDTO response;
                if (UserInRole(SuperAdminGroups.SUPER_ADMIN))
                    response = await this._usersService.AdminGetUsers(payload);
                else if (UserInRole(SuperAdminGroups.PARTNER_ADMIN) ||
                         UserInRole(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    response = await this._usersService.GetUsers(payload, null, GetUID(), GetPID());
                }
                else
                {
                    response = await this._usersService.GetUsers(payload, GetMID(), GetUID(), null);
                }

                workspan.Log.Information(
                    $"EXIT: {this.globalOptions.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
                return ReturnResponse(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                throw;
            }
        }

        [HttpPost("revoke")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Revoke(string userName, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<IdentityController>(this, userName, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                workspan.Log.Information(
                    $"ENTERED: {this.globalOptions.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString} ");

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _identityService.AdminDeleteUser(userName);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    await _identityService.DeleteUser(userName, null, GetPID());
                }
                else
                {
                    await _identityService.DeleteUser(userName, GetMID(), null);
                }


                workspan.Log.Information(
                    $"EXIT: {this.globalOptions.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpPost("create")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Create(AdminSignUpRequestDTO request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<UsersController>(this, request, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var userFromDb = await _usersService.GetUserByEmail(request.Email);

                if (userFromDb != null)
                {
                    return BadRequest("User already exists");
                }

                var newGroup = MerchantGroups.MERCHANT_SUPPORT;

                switch (request.Group)
                {
                    case "MERCHANT_ADMIN":
                        newGroup = MerchantGroups.MERCHANT_ADMIN;
                        break;
                    case "MERCHANT_DEVELOPER":
                        newGroup = MerchantGroups.MERCHANT_DEVELOPER;
                        break;
                    case "MERCHANT_SUPPORT":
                        newGroup = MerchantGroups.MERCHANT_SUPPORT;
                        break;
                    case "MERCHANT_SUPPORT_ADMIN":
                        newGroup = MerchantGroups.MERCHANT_SUPPORT_ADMIN;
                        break;
                    case "MERCHANT_FINANCE":
                        newGroup = MerchantGroups.MERCHANT_FINANCE;
                        break;
                }

                request.Group = newGroup;
                if (HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN))
                {
                    request.MerchantId = GetMID();
                }
                else if ((HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                          HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN)) &&
                         request.MerchantId != Guid.Empty)
                {
                    request.PartnerId = GetPID();
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) && request.MerchantId == Guid.Empty ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) &&
                         request.MerchantId == Guid.Empty ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN) &&
                         request.MerchantId == Guid.Empty)
                {
                    return BadRequest("MerchantId is required");
                }

                await _identityService.AdminSignUpAsync(request, null);
                // await _usersService.CreateUserAsync(request.FirstName, request.LastName, request.Email, request.Phone, request.Role);

                workspan.Log.Information("Create user : REQUEST {Request}", JsonConvert.SerializeObject(request));
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpPost("update")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Update(UpdateUserRequestDTO request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<UsersController>(this, request, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                if (request.Group != SuperAdminGroups.SUPER_ADMIN)
                {
                    var newGroup = request.Group;

                    switch (request.Group)
                    {
                        case "MERCHANT_ADMIN":
                            newGroup = MerchantGroups.MERCHANT_ADMIN;
                            break;
                        case "MERCHANT_DEVELOPER":
                            newGroup = MerchantGroups.MERCHANT_DEVELOPER;
                            break;
                        case "MERCHANT_SUPPORT":
                            newGroup = MerchantGroups.MERCHANT_SUPPORT;
                            break;
                        case "MERCHANT_SUPPORT_ADMIN":
                            newGroup = MerchantGroups.MERCHANT_SUPPORT_ADMIN;
                            break;
                        case "MERCHANT_FINANCE":
                            newGroup = MerchantGroups.MERCHANT_FINANCE;
                            break;
                    }

                    request.Group = newGroup;
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _identityService.AdminUpdateUserAsync(request);
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    await _identityService.UpdateUserAsync(request, null, GetPID());
                }
                else
                {
                    await _identityService.UpdateUserAsync(request, GetMID(), null);
                }

                workspan.Log.Information("Update user : REQUEST {Request}", JsonConvert.SerializeObject(request));
                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpGet("groups")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetGroups()
        {
            using var workspan = Workspan.StartEndpoint<UsersController>(this, null, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var groups = await _usersService.GetGroupsAsync();

                return Ok(groups);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpPost("status")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetsStatus(string userName)
        {
            using var workspan = Workspan.StartEndpoint<UsersController>(this, userName, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var status = await _identityService.GetUserStatusAsync(userName);

                if (status == null)
                    return BadRequest("User not found");

                return Ok(status);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpPost("user")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetsUserByEmail(GetUserByEmailDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<UsersController>(this, null, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var user = await _usersService.GetUserByEmail(payload.Email);

                if (user == null)
                    return BadRequest("User not found");

                var response = new UserDTO()
                {
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Group = user.Group,
                    ProfileImageUrl = user.ProfileImageUrl,
                    Status = user.Status,
                    TwoFactorEnabled = user.TwoFactorEnabled,
                    Timezone = user.Timezone,
                    Phone = user.Phone,
                    Language = user.Language,
                    IsPiiMasked = user.IsPiiMasked,
                    IsEnforceMFAEnabled = user.IsEnforceMFAEnabled,
                };

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        [HttpGet("select-list")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetsUsersByGroup([FromQuery] GetUserByGroupDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<UsersController>(this, null, _globalData);

            try
            {
                payload.Groups ??= new List<string>();

                var pid = GetPID();

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.ADMIN_RISK) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.ADMIN_OPERATIONS) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.ADMIN_SALES))
                {
                    payload.Groups.AddRange(new[]
                    {
                        SuperAdminGroups.SUPER_ADMIN,
                        SuperAdminGroups.ADMIN_RISK,
                        SuperAdminGroups.ADMIN_OPERATIONS,
                        SuperAdminGroups.ADMIN_SALES
                    });
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN_RISK) ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN_SALES) ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN_OPERATIONS))
                {
                    if (pid == null)
                        throw new FlexChargeException("PartnerId is required");

                    payload.Groups.AddRange(new[]
                    {
                        SuperAdminGroups.PARTNER_ADMIN,
                        SuperAdminGroups.PARTNER_ADMIN_RISK,
                        SuperAdminGroups.PARTNER_ADMIN_SALES,
                        SuperAdminGroups.PARTNER_ADMIN_OPERATIONS
                    });
                }

                return !ModelState.IsValid
                    ? ValidationProblem()
                    : Ok(await _usersService.GetUsersSelectList(payload.Groups, pid));
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpPost("preferences")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateUserPreferences(UpdateUserPreferencesRequestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<UsersController>(this, null, _globalData);

            try
            {
                var user = new UpdateLocalUserDTO();

                user.Email = payload.Email;
                user.Timezone = payload.Timezone;
                user.Language = payload.Language;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var userFromDb = await _usersService.GetUserByEmail(payload.Email);

                    if (userFromDb == null)
                    {
                        return BadRequest("User not found");
                    }

                    if (userFromDb.PartnerId != GetPID())
                    {
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                    }
                }
                else if (!HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    var userFromDb = await _usersService.GetUserByEmail(payload.Email);

                    if (userFromDb == null)
                    {
                        return BadRequest("User not found");
                    }

                    if (userFromDb.MerchantId != GetMID())
                    {
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                    }
                }

                await _usersService.UpdateLocalUserAsync(user);

                return Ok(user);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpPut("contact-information")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateUContactInformation(UpdateUserContactsRequestDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<UsersController>(this, null, _globalData);

            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var userFromDb = await _usersService.GetUserByEmail(payload.Email);

                    if (userFromDb == null)
                    {
                        return BadRequest("User not found");
                    }

                    if (userFromDb.PartnerId != GetPID())
                    {
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                    }
                }
                else if (!HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    var userFromDb = await _usersService.GetUserByEmail(payload.Email);

                    if (userFromDb == null || userFromDb.MerchantId != GetMID())
                        return BadRequest("User not found");

                    if (userFromDb.MerchantId != GetMID())
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                await _identityService.UpdateUserContactInformationAsync(payload);
                await _usersService.UpdateUserContactInformationAsync(payload);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }
    }
}