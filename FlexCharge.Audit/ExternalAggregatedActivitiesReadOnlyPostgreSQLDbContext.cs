using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Audit
{
    public class ExternalAggregatedActivitiesReadOnlyPostgreSQLDbContext : DbContext
    {
        public ExternalAggregatedActivitiesReadOnlyPostgreSQLDbContext(
            DbContextOptions<ExternalAggregatedActivitiesReadOnlyPostgreSQLDbContext> options) :
            base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.Entity<Entities.Activity>().Property<bool>("IsDeleted");
            builder.Entity<Entities.Activity>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
        }

        public DbSet<Entities.Activity> AggregatedActivities { get; set; }

        public override int SaveChanges()
        {
            // Throw if they try to call this
            throw new InvalidOperationException("This context is read-only.");
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // Throw if they try to call this
            throw new InvalidOperationException("This context is read-only.");
        }
    }
}