using System;
using System.Linq.Expressions;
using FlexCharge.Audit.Entities;
using NRules.Fluent.Dsl;

namespace FlexCharge.Audit.Rules;

/// <summary>
/// see: https://github.com/NRules/NRules/wiki/DSL-Extensions
/// </summary>
public static class DSLExtensions
{
    #region Producing Outcome

    /// <summary>
    /// 
    /// </summary>
    /// <param name="rhs"></param>
    /// <param name="message">Uses lambda to be evaluated at run-time instead on rule creation time</param>
    /// <returns></returns>
    public static IRightHandSideExpression Warning(this IRightHandSideExpression rhs,
        Expression<Func<string>> message)
    {
        return rhs.SetError(()=> Severity.Warning, message);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="rhs"></param>
    /// <param name="message">Uses lambda to be evaluated at run-time instead on rule creation time</param>
    /// <returns></returns>
    public static IRightHandSideExpression Error(this IRightHandSideExpression rhs,
        Expression<Func<string>> message)
    {
        return rhs.SetError(()=> Severity.Error, message);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="rhs"></param>
    /// <param name="message">Uses lambda to be evaluated at run-time instead on rule creation time</param>
    /// <returns></returns>
    public static IRightHandSideExpression CriticalError(this IRightHandSideExpression rhs, 
        Expression<Func<string>> message)
    {
        return rhs.SetError(()=> Severity.Critical, message);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="rhs"></param>
    /// <param name="message">Uses lambda to be evaluated at run-time instead on rule creation time</param>
    /// <returns></returns>
    public static IRightHandSideExpression Security(this IRightHandSideExpression rhs, 
        Expression<Func<string>> message)
    {
        return rhs.SetError(()=> Severity.Security, message);
    }

    #endregion

    // public static ILeftHandSideExpression Match<TFact>(this ILeftHandSideExpression lhs,
    //     params Expression<Func<TFact, bool>>[] conditions)
    // {
    //     lhs.Exists(conditions)
    //         .
    // }

    // public static ILeftHandSideExpression OnlyOneOf<T1, T2>(this ILeftHandSideExpression lhs)
    // {
    //     return lhs
    //             .Or(x => x
    //                 .And(y => y
    //                     .Match<T1>()
    //                     .Not<T2>())
    //                 .And(y => y
    //                     .Match<T2>()
    //                     .Not<T1>())
    //             )
    //         ;
    // }
    //
    // public static ILeftHandSideExpression OnlyOneOf<T1, T2, T3>(this ILeftHandSideExpression lhs)
    // {
    //     return lhs
    //             .Or(x => x
    //                 .And(y => y
    //                     .Match<T1>()
    //                     .Not<T2>()
    //                     .Not<T3>())
    //                 .And(y => y
    //                     .Match<T2>()
    //                     .Not<T1>()
    //                     .Not<T3>())
    //                 .And(y => y
    //                     .Match<T3>()
    //                     .Not<T1>()
    //                     .Not<T2>())
    //             )
    //         ;
    // }
}