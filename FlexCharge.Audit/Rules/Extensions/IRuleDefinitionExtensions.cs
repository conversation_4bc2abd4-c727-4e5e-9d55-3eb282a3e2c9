using System.Linq;
using NRules.RuleModel;

namespace FlexCharge.Audit.Rules.Extensions;

public static class IRuleDefinitionExtensions
{
    public static string? GetRuleId(this IRuleDefinition ruleDefinition)
    {
        string possibleRuleId = ruleDefinition.Name
            .Split('.').LastOrDefault()?
            .Split('_').FirstOrDefault();


        return IsRuleId(possibleRuleId) ? possibleRuleId : null;

        bool IsRuleId(string ruleId)
        {
            return ruleId != null
                   && (ruleId.StartsWith("T") || ruleId.StartsWith("A"))
                   && ruleId.Length == 5;
        }
    }

    public static string GetRuleClassName(this IRuleDefinition ruleDefinition)
    {
        return ruleDefinition.Name.Substring(ruleDefinition.Name.LastIndexOf('.') + 1);
    }
}