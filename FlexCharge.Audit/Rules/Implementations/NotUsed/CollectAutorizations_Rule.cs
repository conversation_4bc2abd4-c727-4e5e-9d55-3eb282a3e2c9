// using System.Collections.Generic;
// using System.Linq;
// using FlexCharge.Audit.Rules.Eligibility;
// using FlexCharge.Audit.Rules.Eligibility.Collections;
// using FlexCharge.Audit.Rules.Orders.PayIn;
// using NRules.Fluent.Dsl;
//
// namespace FlexCharge.Audit.Rules;
//
// public class CollectAutorizations_Rule : Rule
// {
//     IEnumerable<PayIn_Payment_Authorization_Succeeded> _authorizations = null;
//
//     public override void Define()
//     {
//         When()
//             .Exists<PayIn_Payment_Authorization_Succeeded>()
//             .Collect(() => _authorizations)
//             ;
//
//         Then()
//             .AddFact(new AuthorizationsCollection(_authorizations))
//             ;
//
//     }
// }