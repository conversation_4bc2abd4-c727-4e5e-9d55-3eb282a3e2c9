using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Payments.Transactions;
using FlexCharge.Common.Shared.Activities;
using NUnit.Framework;

namespace FlexCharge.Audit.Rules.Implementations.PayInProcessing;

public class A0015_CapturedAmounts_AreNot_Voided_Rule : TestableRuleBase
{
    IEnumerable<Payments_AuthorizePayment_Succeeded_Fact> _authorizations = null;
    IEnumerable<Payments_VoidPayment_Succeeded_Fact> _voids = null;
    IEnumerable<Payments_CapturePayment_Succeeded_Fact> _captures = null;

    private int _authorizedAmount = 0;
    private int _discountAmount = 0;
    private int _capturedAmount = 0;
    private int _voidedAmount = 0;

    public override void Define()
    {
        When()
            .Collect(() => _authorizations)
            .Collect(() => _voids)
            .Collect(() => _captures)
            .Let(() => _authorizedAmount, () =>
                _authorizations.Sum(a => a.Amount))
            .Let(() => _capturedAmount, () =>
                _captures.Sum(a => a.Amount))
            .Let(() => _voidedAmount, () =>
                _voids.Sum(a => a.Amount))
            // Authorized - Captured != Voided -> Some of capture / sale amounts are voided afterwards
            .Having(() =>
                _authorizedAmount - _capturedAmount != _voidedAmount
            )
            ;

        Then()
            .CriticalError(() => "Captured amounts are voided");
    }

    #region Unit Testing

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Positive()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_AuthorizePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 0)
            ));

        await CreateActivityFromFactAsync<Payments_CapturePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 0)
            ));

        await CreateActivityFromFactAsync<Payments_VoidPayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 0)
            ));


        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Negative()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_AuthorizePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 0)
            ));

        await CreateActivityFromFactAsync<Payments_CapturePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 0)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    #endregion
}