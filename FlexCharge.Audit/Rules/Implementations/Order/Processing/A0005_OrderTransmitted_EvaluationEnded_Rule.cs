using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Evaluation;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Processing;
using NUnit.Framework;

namespace FlexCharge.Audit.Rules.Implementations.Order.Processing;

public class A0005_OrderTransmitted_EvaluationEnded_Rule
    : TriggerredRuleBase<OrderTransmittedForEvaluation_TriggerFact>
{
    public override void DefineProblemCondition()
    {
        When()
            .Not<EvaluationEnded_TriggerFact>()
            ;
    }

    public override void DefineThenAction()
    {
        Then()
            .CriticalError(() => "Evaluation request not completed for transmitted order")
            ;
    }

    #region Unit Testing

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Positive()
    {
        //Arrange
        CreateTriggerFact<OrderTransmittedForEvaluation_TriggerFact>();

        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Negative()
    {
        //Arrange
        CreateTriggerFact<OrderTransmittedForEvaluation_TriggerFact>();
        CreateTriggerFact<EvaluationEnded_TriggerFact>();

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    #endregion
}