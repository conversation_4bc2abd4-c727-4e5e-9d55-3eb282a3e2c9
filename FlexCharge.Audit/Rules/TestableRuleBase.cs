using System;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using NRules;
using NRules.Fluent;
using NRules.Fluent.Dsl;
using NUnit.Framework;

namespace FlexCharge.Audit.Rules;

public abstract class TestableRuleBase : Rule
{
    protected ISession Session { get; private set; }
    private int _ruleFireCount;

    private Rule SetUpRule()
    {
        return (Rule) Activator.CreateInstance(this.GetType());
    }

    [SetUp]
    public void SetupTest()
    {
        _ruleFireCount = 0;

        var rule = SetUpRule();
        var definitionFactory = new RuleDefinitionFactory();
        var ruleDefinition = definitionFactory.Create(rule);

        var compiler = new RuleCompiler();
        ISessionFactory factory = compiler.Compile(new[] {ruleDefinition});
        Session = factory.CreateSession();
        Session.Events.RuleFiredEvent += (sender, args) => _ruleFireCount++;
    }

    protected void AssertFiredOnce()
    {
        AssertFiredTimes(1);
    }

    protected void AssertFiredTimes(int expected)
    {
        Assert.AreEqual(expected, _ruleFireCount);
    }

    protected void AssertDidNotFire()
    {
        AssertFiredTimes(0);
    }

    private ActivityBuilder CreateActivity(string name, object data = null,
        Guid? activityId = null,
        string domain = null,
        string source = null)
    {
        var activity = new ActivityDTO
        {
            Name = name,
            ActionTimestamp = DateTime.UtcNow,
            Domain = domain,
            Source = source,
        };


        if (activityId.HasValue) activity.Id = activityId.Value;

        var activityBuilder = new ActivityBuilder();
        activityBuilder.Initialize(activity);

        if (data != null)
        {
            activityBuilder.Data(data);
        }

        return activityBuilder;
    }

    protected async Task CreateActivityFromFactAsync<TActivityFact>(
        object data = null,
        Guid? activityId = null,
        Action<ActivityBuilder> set = null)
        where TActivityFact : ActivityFactBase

    {
        var activityFactAttribute =
            Utils.ReflectionHelpers.GetFirstAttributeOfTypeOrDefault<ActivityFactAttribute>(typeof(TActivityFact));

        var activityBuilder = CreateActivity(activityFactAttribute.ActivityName, data, activityId);

        //PopulateActivityValuesFromAttributes(activity, activityBuilder);

        if (set != null)
        {
            set(activityBuilder);
        }

        var createdActivity = activityBuilder.Activity;
        createdActivity.Category = activityFactAttribute.ActivityCategory;
        var activityFact = Auditor.ActivityToFact(createdActivity.ToFactDTO());


        Session.Insert(activityFact);
    }

    protected void CreateTriggerFact<TTriggerFact>(Action<TTriggerFact> set = null)
        where TTriggerFact : class
    {
        var fact = Activator.CreateInstance<TTriggerFact>();
        set?.Invoke(fact);
        Session.Insert(fact);
    }

    /// <summary>
    /// NOTE: it's recommended to use <see cref="CreateActivityFromFactAsync{TActivityFact}"/> instead of this method 
    /// Use it to create an activity without creating a fact class
    /// </summary>
    /// <param name="category"></param>
    /// <param name="activity"></param>
    /// <param name="data"></param>
    /// <param name="activityId"></param>
    /// <param name="set"></param>
    /// <typeparam name="TActivityCategoryEnum"></typeparam>
    protected async Task CreateActivityAsync<TActivityCategoryEnum>(
        TActivityCategoryEnum category,
        string activity,
        object data = null,
        Guid? activityId = null,
        Action<ActivityBuilder> set = null)
        where TActivityCategoryEnum : Enum

    {
        var activityBuilder = CreateActivity(activity, data, activityId);

        //PopulateActivityValuesFromAttributes(activity, activityBuilder);

        if (set != null)
        {
            set(activityBuilder);
        }

        var createdActivity = activityBuilder.Activity;
        createdActivity.Category = category.ToString();
        var activityFact = Auditor.ActivityToFact(createdActivity.ToFactDTO());


        Session.Insert(activityFact);
    }

    public abstract Task Test_Positive();
    public abstract Task Test_Negative();
}