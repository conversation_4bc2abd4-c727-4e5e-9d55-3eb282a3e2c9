// using NRules.Fluent.Dsl;
//
// namespace FlexCharge.Audit.Rules;
//
// public class ErrorActivityRule:Rule
// {
//     public override void Define()
//     {
//         When()
//             .Exists<ActivityFact>(a => a.le == "EligibilityChecks_ExecutionEnded")
//             .Exists<ActivityFact>(a => a.Name == "Testing_IgnoredCumulativeFingerprintStatistics");
//
//         Then()
//             .Critical();
//     }
// }