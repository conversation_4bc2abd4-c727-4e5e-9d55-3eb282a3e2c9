// using System;
// using System.Collections.Generic;
// using FlexCharge.Audit.DTO;
//
// namespace FlexCharge.Audit.Rules;
//
// public class SessionMatchingStartingFact : ActivityWithMetadataFactBase
// {
//     public int RetryNumber { get; set; }
//
//     public SessionMatchingStartingFact(
//         ActivityToAuditDTO data)
//         : base(data)
//     {
//     }
//
//     public override void SetFactProperties(Dictionary<string, string> meta)
//     {
//         RetryNumber = int.Parse(meta["Retry#"]);
//     }
// }