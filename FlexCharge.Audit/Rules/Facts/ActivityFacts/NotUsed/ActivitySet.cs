// using System;
// using System.Collections.Generic;
// using System.Linq;
// using Microsoft.AspNetCore.JsonPatch.Internal;
//
// namespace FlexCharge.Audit.Rules;
//
// /// <summary>
// /// Allows find activities by name
// /// </summary>
// public class ActivitySet
// {
//     private Dictionary<string, List<ActivityFactBase>> _activitiesMap = new(StringComparer.OrdinalIgnoreCase);
//     public bool Any(string activityName) => _activitiesMap.ContainsKey(activityName);
//
//     public bool TryGetActivities(string activityName, out List<ActivityFactBase> activities) =>
//         _activitiesMap.TryGetValue(activityName, out activities);
//
//     public ActivitySet(IEnumerable<ActivityFactBase> facts)
//     {
//         foreach (var fact in facts)
//         {
//             if (!_activitiesMap.TryGetValue(fact.Name, out var activities))
//             {
//                 activities = new();
//                 _activitiesMap.Add(fact.Name, activities);
//             }
//
//             activities.Add((fact));
//         }
//     }
// }