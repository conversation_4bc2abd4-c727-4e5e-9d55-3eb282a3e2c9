using System.Collections.Generic;
using FlexCharge.Audit.DTO;
using FlexCharge.Common.Shared.Activities;

namespace FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.ExternalProviders;

[ActivityFact(ActivityCategories.ExternalPaymentProvider_Processing, "ExternalProvider_InvoiceProcessing_InvoicePaid")]
public class ExternalProvider_InvoiceProcessing_InvoicePaid_Fact : ActivityWithMetadataFactBase
{
    public int Amount { get; set; }
    public bool PaidExternally { get; set; }

    public ExternalProvider_InvoiceProcessing_InvoicePaid_Fact(ActivityToAuditDTO data) : base(data)
    {
    }

    public override void SetFactProperties(Dictionary<string, string> meta)
    {
        Amount = int.Parse(meta["Amount"]);
        PaidExternally = bool.Parse(meta["PaidExternally"]);
    }
}