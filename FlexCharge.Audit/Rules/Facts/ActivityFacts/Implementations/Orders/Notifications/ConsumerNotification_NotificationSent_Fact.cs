using System.Collections.Generic;
using FlexCharge.Audit.DTO;
using FlexCharge.Common.Shared.Activities;

namespace FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Processing;

[ActivityFact(ActivityCategories.Orders_ConsumerNotification, "ConsumerNotification_NotificationSent")]
public class ConsumerNotification_NotificationSent_Fact : ActivityWithMetadataFactBase
{
    public ConsumerNotification_NotificationSent_Fact(ActivityToAuditDTO data)
        : base(data)
    {
    }

    public override void SetFactProperties(Dictionary<string, string> meta)
    {
    }
}