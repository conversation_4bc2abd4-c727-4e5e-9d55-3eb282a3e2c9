using System.Collections.Generic;
using FlexCharge.Audit.DTO;
using FlexCharge.Common.Shared.Activities;

namespace FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Payments.Transactions;

[ActivityFact(ActivityCategories.Payments_Processing, "Payments_AchDebitPayment_Succeeded")]
public class Payments_AchDebitPayment_Succeeded_Fact : ActivityWithMetadataFactBase
{
    public int Amount { get; set; }

    public Payments_AchDebitPayment_Succeeded_Fact(ActivityToAuditDTO data) : base(data)
    {
    }

    public override void SetFactProperties(Dictionary<string, string> meta)
    {
        Amount = int.Parse(meta["Amount"]);
    }
}