using FlexCharge.Audit.DTO;
using FlexCharge.Common.Shared.Activities;

namespace FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.OrderTerminalStates;

[ActivityFact(ActivityCategories.Eligibility_Evaluation, "Offer_NotEligible")]
public class Offer_NotEligible_Fact : OrderTerminalStateFactBase
{
    public Offer_NotEligible_Fact(ActivityToAuditDTO data) : base(data)
    {
    }
}