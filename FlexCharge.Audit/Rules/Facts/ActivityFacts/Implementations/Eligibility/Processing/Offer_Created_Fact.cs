using System.Collections.Generic;
using FlexCharge.Audit.DTO;
using FlexCharge.Common.Shared.Activities;

namespace FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Processing;

[ActivityFact(ActivityCategories.Eligibility_Evaluation, "Offer_Created")]
public class Offer_Created_Fact : ActivityWithMetadataFactBase
{
    public int Amount { get; set; }
    public string Currency { get; set; }
    public string ResponseCode { get; set; }

    public Offer_Created_Fact(ActivityToAuditDTO data)
        : base(data)
    {
    }

    public override void SetFactProperties(Dictionary<string, string> meta)
    {
        Amount = int.Parse(meta["Amount"]);
        ResponseCode = meta["ResponseCode"];
        Currency = meta["Currency"];
    }
}