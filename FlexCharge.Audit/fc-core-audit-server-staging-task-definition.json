{"containerDefinitions": [{"name": "fc-core-audit-server", "image": "556663010871.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-audit:8f31fb9993e42e8ef513fd02312a27dc8cd8a701", "cpu": 0, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "audit_service_staging"}, {"name": "DB_DATABASE", "value": "fc_audit"}, {"name": "DB_HOST", "value": "flexcharge-staging-2023-08-08.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ACTIVITY_DB_USERNAME", "value": "activity_service_staging"}, {"name": "ACTIVITY_DB_DATABASE", "value": "fc_activity"}, {"name": "ACTIVITY_DB_HOST", "value": "flexcharge-staging-2023-08-08-readonly.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "ACTIVITY_DB_PORT", "value": "5432"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Staging"}, {"name": "AWS_COGNITO_USER_POOL_ID", "value": "us-east-1_rCUpTgXY4"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_DB_AUDIT_PASSWORD-O6SIMf"}, {"name": "ACTIVITY_DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_DB_ACTIVITY_PASSWORD-h6eENF"}, {"name": "AWS_IAM_COGNITO_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_IAM_COGNITO_KEY-SVc81A"}, {"name": "AWS_IAM_COGNITO_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_IAM_COGNITO_SECRET-7KOA9j"}, {"name": "AWS_IAM_COGNITO_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_IAM_COGNITO_CLIENT_ID-xZNZOA"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_SNS_IAM_ACCESS_KEY-lZ8I9V"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_SNS_IAM_SECRET_KEY-YwuJZJ"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-audit-server-staging", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-audit-server-staging", "taskRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Staging-Role", "executionRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Staging-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "1024"}