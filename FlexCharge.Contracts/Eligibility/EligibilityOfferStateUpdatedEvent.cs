using System;
using System.Collections.ObjectModel;
using FlexCharge.Contracts.Commands;

namespace FlexCharge.Contracts;

public record EligibilityOfferStateUpdatedEvent : IdempotentEvent, ICorrelatedMessage
{
    public Guid OrderId { get; set; }
    public Guid Mid { get; set; } //Merchant Id

    public bool IsTestOrder { get; set; }

    public string StatusCategory { get; set; }
    public string StatusSubCategory { get; set; }
    public string StatusDescription { get; set; }
    public string PaymentInstrumentToken { get; set; } //Can be updated by cures
    public string ScaAuthenticationToken { get; set; } //Can be updated by cures
    public bool IsCIT { get; set; }
    public bool IsInitial { get; set; }
    public string LastResponseCodeGateway { get; set; }
    public string LastResponseCode { get; set; }
    public string CVVCheckResult { get; set; }

    public string MerchantDescriptor { get; set; }

    public string? ConfirmationId { get; set; }
    public bool? PaidOutOfBand { get; set; }

    public Guid? SiteId { get; set; }

    public bool IsSenseKeyMatched { get; set; } = false;

    public Dictionary<string, string>? Meta { get; set; }


    Guid? ICorrelatedMessage.MessageCorrelationId => OrderId;
    Guid? ICorrelatedMessage.MessageTenantId => Mid;
}