using System;

namespace FlexCharge.Contracts;

public record ApplicationInviteRequestedEvent
{
    // public string ExternalId { get; set; }
    // public string LegalEntityName { get; set; }
    // public string Dba { get; set; }
    // public string Type { get; set; }
    // public string TaxId { get; set; }
    // public string Descriptor { get; set; }
    // public string BusinessEstablishedDate { get; set; }
    // public string Mcc { get; set; }
    // public string Industry { get; set; }
    // public bool Pcidss { get; set; }
    // public string EcommercePlatform { get; set; }
    // public string Website { get; set; }
    // public int Status { get; set; }
    // public string Description { get; set; }
    //
    // public string Addressline1 { get; set; }
    // public string Addressline2 { get; set; }
    // public string ZipCode { get; set; }
    // public string State { get; set; }
    // public string StateCode { get; set; }
    // public string City { get; set; }
    //
    // public string Country { get; set; }
    //
    public string Name { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }
    public string Group { get; set; }

    public Guid Mid { get; set; }
    public Guid Pid { get; set; }
    public Guid IntegrationPartnerId { get; set; }
    public Guid Aid { get; set; }

    public string MerchantState { get; set; }
    public string? Password { get; set; }
}