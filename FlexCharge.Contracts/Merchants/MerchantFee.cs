namespace FlexCharge.Contracts;

public class MerchantFee
{
    public bool IsActive { get; set; } = true;
    public DateTime CreatedOn { get; set; }

    public string Name { get; set; }
    public FeeType Type { get; set; }
    public ChargeType ChargeType { get; set; }
    public int Amount { get; set; }

    public int? MinimumFeeAmount { get; set; }
}

public enum FeeType
{
    Fixed,
    Percentage
}

public enum ChargeType
{
    PER_TRANSACTION,
    ONE_TIME,
    PER_CHARGEBACK_TRANSACTION
}