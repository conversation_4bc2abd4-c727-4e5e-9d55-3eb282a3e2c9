using FlexCharge.Contracts.Commands;

namespace FlexCharge.Contracts;

//Note: Must be a class to work with Hangfire
public record ScheduleEvent : IdempotentEvent
{
    public enum ScheduleType
    {
        Instant,
        Delayed,
        Recurrent
    }

    public string EventRecipient { get; set; }

    public Guid ScheduledJobId { get; set; }

    public ScheduleType Type { get; set; }
    public TimeSpan Delay { get; set; }
    public string CRON { get; set; }
    //public string Payload { get; set; }
}