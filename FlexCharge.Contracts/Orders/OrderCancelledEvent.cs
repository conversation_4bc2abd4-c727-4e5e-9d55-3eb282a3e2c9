using System;
using FlexCharge.Contracts.Commands;

namespace FlexCharge.Contracts;

public record OrderCancelledEvent : IdempotentEvent, ICorrelatedMessage
{
    public Guid Mid { get; set; } //Merchant Id
    public string? ExternalOrderId { get; set; }
    public Guid OrderId { get; set; }

    public string? CancelledByUserName { get; set; }
    public Guid? CancelledByUserId { get; set; }
    public string? CancelledByUserGroups { get; set; }

    Guid? ICorrelatedMessage.MessageCorrelationId => OrderId;

    Guid? ICorrelatedMessage.MessageTenantId => Mid;
}