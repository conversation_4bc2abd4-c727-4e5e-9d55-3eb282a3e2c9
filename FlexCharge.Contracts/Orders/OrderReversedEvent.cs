namespace FlexCharge.Contracts;

public record OrderReversedEvent : IdempotentEvent, ICorrelatedMessage
{
    public Guid OrderId { get; set; }
    public Guid Mid { get; set; } //Merchant Id
    public Guid? Pid { get; set; } //Partner Id
    public int Amount { get; set; }
    public DateTime DisputeDateTime { get; set; }

    public Guid TransactionId { get; set; }
    public string Currency { get; set; }

    Guid? ICorrelatedMessage.MessageCorrelationId => OrderId;
    Guid? ICorrelatedMessage.MessageTenantId => Mid;
}