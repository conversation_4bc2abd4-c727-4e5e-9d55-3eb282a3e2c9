namespace FlexCharge.Contracts.Commands;

public record PerformPartnerDebitCommand : IdempotentCommand
{
    public Guid FundSenderPid { get; set; }

    public int Amount { get; set; }
    public string Currency { get; set; }

    /// <summary>
    /// Optional string that can be used to pass data to the recipient bank.
    /// Examples an invoice number or transaction reference number.
    /// For SVB it should be 1-15 chars (alphanumeric, spaces, dashes, braces and any punctuation symbols)
    /// </summary>
    public string? TransactionIdentificationString { get; set; }


    public string CompanyEntryDescription { get; set; }
}