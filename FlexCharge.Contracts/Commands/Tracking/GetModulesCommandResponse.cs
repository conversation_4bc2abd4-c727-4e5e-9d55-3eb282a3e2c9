using System.Collections;

namespace FlexCharge.Contracts.Commands.Tracking;

public class GetModulesCommandResponse
{
    public string? InlineScript { get; set; }
    public string? Src { get; set; }
    public string? Link { get; set; }
    public string? Rel { get; set; }
    public string Name { get; set; }
    public string? Callback { get; set; }
    public bool? IsDefault { get; set; }
    public bool? IsActive { get; set; }
    public string? Category { get; set; }
    public string? Styles { get; set; }
    public string? Token { get; set; }
    public string? Attributes { get; set; }
}