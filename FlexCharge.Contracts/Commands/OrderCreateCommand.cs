namespace FlexCharge.Contracts.Commands;

public interface OrderCreateCommand
{
    public Guid _CorrelationId { get; set; }
    public Guid Mid { get; set; }
    public string SessionKey { get; set; }

    public Transaction Transaction { get; set; }
    public Payer Payer { get; set; }
    public Merchant Merchant { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
    public List<OrderItem> Items { get; set; }
    public ShippingInformation ShippingInformation { get; set; }
    public BillingInformation BillingInformation { get; set; }
    public Subscription Subscription { get; set; }
    public List<AdditionalField> AdditionalFields { get; set; }
}