using FlexCharge.Contracts.Vault;

namespace FlexCharge.Contracts.Commands.Vault;

public record DeTokenizeInstrumentCommand
{
    public Guid VaultKeyId { get; set; }
    public Guid Mid { get; set; }
    public int Amount { get; set; }

    public bool GetLatestInstrument { get; set; }
    public bool IsCit { get; set; }
    public string ECI { get; set; }
    public string CAVV { get; set; }
    public bool GetNetworkToken { get; set; } = true;
}