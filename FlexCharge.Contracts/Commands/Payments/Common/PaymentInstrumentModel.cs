namespace FlexCharge.Contracts.Commands.Common;

public class PaymentInstrumentModel
{
    public Guid PaymentInstrumentId { get; set; }

    /// <summary>
    /// Used when payment is authenticated with 3DS
    /// </summary>
    public string? ScaAuthenticationToken { get; set; }

    public string PaymentInstrumentToken { get; set; }
    public string Brand { get; set; }
    public string Type { get; set; } // credit/debit
    public string Bin { get; set; }
    public string Last4 { get; set; }
    public string? Country { get; set; }
}