namespace FlexCharge.Contracts.Commands;

public record CanChargeExternalTokenCommand : IdempotentCommand
{
    public string? ExternalAccountId { get; set; }
    public string ExternalPaymentInstrumentToken { get; set; }

    public Guid Mid { get; set; }
    public Guid OrderId { get; set; }
    public Guid? PayerId { get; set; }
}

public record CanChargeExternalTokenCommandResponse
{
    public bool? CanCharge { get; set; }

    public bool Success { get; set; }
    public string ResponseCode { get; set; }
    public string ResponseMessage { get; set; }
    public string BinNumber { get; set; }

    public Guid OrderId { get; set; }
    public bool GatewayFound { get; set; }

    public int GatewayOrder { get; set; }
    public string Provider { get; set; }
    public string ProviderTransactionToken { get; set; }
    public string ProviderResponseCode { get; set; }

    public string InternalResponseCode { get; set; }
    public string InternalResponseMessage { get; set; }
    public string InternalResponseGroup { get; set; }

    public int? NextGateway { get; set; }
    public IList<string> Errors { get; set; }
    public IList<KeyValuePair<string, string>> ErrorsWithCodes { get; set; }
}