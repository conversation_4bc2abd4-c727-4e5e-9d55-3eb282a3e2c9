using FlexCharge.Contracts.Commands;

namespace FlexCharge.Contracts;

public abstract record PaymentCancelEventBase : IdempotentEvent
{
    public Guid OrderId { get; set; }
    public Guid TransactionId { get; set; }
    public Guid RelatedTransactionId { get; set; }
    public Guid Mid { get; set; }
    public string PaymentType { get; set; }
    public string PaymentMethodType { get; set; }

    public string? Descriptor { get; set; }
    public string Description { get; set; }
    public string AmountFormatted { get; set; }
    public int Amount { get; set; }
    public int DiscountAmount { get; set; }
    public DateTime PaymentDate { get; set; }
    public bool NetworkTokenUsed { get; set; }
    public string Bin { get; set; }
    public string Last4 { get; set; }
}