namespace FlexCharge.Contracts;

public class PaymentMethod
{
    public string HolderName { get; set; }

    public string CardType { get; set; }
    public string CardBrand { get; set; }

    public string CardCountry { get; set; }

    string CardIssuer { get; set; }
    string CardLevel { get; set; }
    string Card<PERSON>ingerprint { get; set; }

    public int ExpirationMonth { get; set; }
    public int ExpirationYear { get; set; }

    public string CardBinNumber { get; set; }
    public string CardLast4Digits { get; set; }
    public string CardNumber { get; set; }
}