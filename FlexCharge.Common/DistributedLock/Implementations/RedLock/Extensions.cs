//see: https://github.com/samcook/RedLock.net

using System.Collections.Generic;
using FlexCharge.Common.Redis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RedLockNet.SERedis;
using RedLockNet.SERedis.Configuration;
using StackExchange.Redis;

namespace FlexCharge.Common.DistributedLock.Implementations.RedLock
{
    public static class Extensions
    {
        private static readonly string SectionName = "cache";

        public static IServiceCollection AddRedLockDistributedLock(this IServiceCollection services)
        {
            IConfiguration configuration;
            using (var serviceProvider = services.BuildServiceProvider())
            {
                configuration = serviceProvider.GetService<IConfiguration>();
            }

            services.Configure<RedisOptions>(configuration.GetSection(SectionName));
            var options = configuration.GetOptions<RedisOptions>(SectionName);

            ConfigurationOptions config;
#if DEBUG
            config = new ConfigurationOptions
            {
                EndPoints = {options.ConnectionString}, // replace with your Redis server's address
                ConnectTimeout = 60000, // connection timeout in milliseconds
                SyncTimeout = 60000, // synchronous operations timeout in milliseconds
                AbortOnConnectFail = false
            };
#else
            config = new ConfigurationOptions
            {
                EndPoints = {options.ConnectionString}, // replace with your Redis server's address
            };
#endif

            ConnectionMultiplexer connectionMultiplexer = ConnectionMultiplexer.Connect(config);


            //see: https://github.com/samcook/RedLock.net
            RedLockDistributedLockService.Initialize(
                RedLockFactory.Create(new List<RedLockMultiplexer>() {connectionMultiplexer})
            );


            // var connectionStringSplitport = options.ConnectionString.Split(':');
            // var endpoints = new List<RedLockEndPoint>
            // {
            //     new DnsEndPoint(connectionStringSplitport[0], int.Parse(connectionStringSplitport[1])),
            // };
            //
            // RedLockDistributedLockService.Initialize(RedLockFactory.Create(endpoints));

            services.AddTransient<IDistributedLockService, RedLockDistributedLockService>();

            return services;
        }
    }
}