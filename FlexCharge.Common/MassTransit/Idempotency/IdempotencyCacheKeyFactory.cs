using System;
using FlexCharge.Common.Cache;
using Microsoft.Extensions.Caching.Distributed;

namespace FlexCharge.Common.MassTransit.Idempotency;

public class IdempotencyCacheKeyFactory : CacheKeyFactoryBase
{
#if !DEBUG
    const int IDEMPOTENCY_LOCK_DURATION_IN_MINUTES = 15; // Idempotency is guaranteed for 15 minutes
#else
    // During development, we want to keep the lock for 60 minutes to avoid false duplicate calls
    // that can occur when a brakepoint is hit during debugging and debugging took more than 15 minutes 
    const int IDEMPOTENCY_LOCK_DURATION_IN_MINUTES = 60; 
#endif
    
    static string ServiceName { get; } = Extensions.ServiceName;

    /// <summary>
    /// Idempotency key for commands is NOT scoped to service name
    /// because it should be executed only once across all microservicesservices 
    /// </summary>
    /// <param name="idempotentKey"></param>
    /// <returns></returns>
    public static CacheKey CreateCommandIdempotencyKey(Guid idempotentKey) =>
        CreateScopedKey($"IK_{idempotentKey}",
            new DistributedCacheEntryOptions()
                {SlidingExpiration = TimeSpan.FromMinutes(IDEMPOTENCY_LOCK_DURATION_IN_MINUTES)});
    
    /// <summary>
    /// Idempotency key for events IS scoped to service name to avoid processing duplicate event by the same microservice
    /// </summary>
    /// <param name="idempotentKey"></param>
    /// <returns></returns>
    public static CacheKey CreateEventIdempotencyKey(Guid idempotentKey) =>
        CreateScopedKey($"IK_{ServiceName}_{idempotentKey}",
            new DistributedCacheEntryOptions()
                {SlidingExpiration = TimeSpan.FromMinutes(IDEMPOTENCY_LOCK_DURATION_IN_MINUTES)});
}