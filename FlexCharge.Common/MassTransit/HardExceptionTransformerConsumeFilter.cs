using System;
using System.Diagnostics;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using MassTransit;

namespace FlexCharge.Common.MassTransit;

/// <summary>
/// To avoid auto retries on Consumer exceptions we need to suppress initial exception and fire a hard one
/// </summary>
/// <typeparam name="T"></typeparam>
public class HardExceptionTransformerConsumeFilter<T> :
    IFilter<ConsumeContext<T>>
    where T : class
{
    [DebuggerNonUserCode]
    public async Task Send(ConsumeContext<T> context, IPipe<ConsumeContext<T>> next)
    {
        try
        {
            await next.Send(context);
        }
        // we don't want to wrap retry exceptions - they are handled by MassTransit as configured in Extensions.AddMassTransit() method
        catch (Exception ex) when (!(ex is MassTransitRetryException))
        {
            using var workspan = Workspan.Current ??
                                 Workspan.Start<HardExceptionTransformerConsumeFilter<T>>(
                                     $"CONSUMER: {typeof(T).Name}");

            var e = new Exception($"CONSUMER EXCEPTION: {ex.Message}", ex);
            workspan.RecordException(e);

            throw e;
        }
    }

    public void Probe(ProbeContext context)
    {
    }
}