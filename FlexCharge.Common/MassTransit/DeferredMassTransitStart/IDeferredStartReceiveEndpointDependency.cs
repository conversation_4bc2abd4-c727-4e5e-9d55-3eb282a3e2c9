using System.Threading.Tasks;
using MassTransit.Transports;

namespace FlexCharge.Common.MassTransit.DeferredMassTransitStart;

/// <summary>
/// Internal interface to support MassTransit start deferred until all dependencies are ready
/// </summary>
/// <remarks>See: https://www.youtube.com/watch?v=ZZdpz9StKCA</remarks>
internal interface IDeferredStartReceiveEndpointDependency : IReceiveEndpointDependency
{
    public void StartMassTransitReceiveEndpoints();
}

internal class DeferredStartReceiveEndpointDependency : IDeferredStartReceiveEndpointDependency
{
    readonly TaskCompletionSource<bool> _ready;

    public Task Ready => _ready.Task;

    public DeferredStartReceiveEndpointDependency()
    {
        _ready = new TaskCompletionSource<bool>(TaskCreationOptions.RunContinuationsAsynchronously);
    }

    public void StartMassTransitReceiveEndpoints()
    {
        _ready.TrySetResult(true);
    }
}