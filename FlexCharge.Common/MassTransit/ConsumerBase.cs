using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Common.MassTransit;

public abstract class ConsumerBase<TMessage> : IConsumer<TMessage>
    where TMessage : class
{
    protected virtual bool LogEnterAndExit => true;

    public ConsumerBase(IServiceScopeFactory serviceScopeFactory)
    {
        ServiceScopeFactory = serviceScopeFactory;
    }


    public async Task Consume(ConsumeContext<TMessage> context)
    {
        using var workspan = Workspan.Start<ConsumerBase<TMessage>>(GetType().Name)
            //.Context(context)
            .Baggage("MT_MessageId", context.MessageId)
            .Baggage("MT_CorrelationId", context.CorrelationId);

        if (context.Message is ICorrelatedMessage correlatedMessage)
        {
            workspan.Baggage("CorrelationId", correlatedMessage?.MessageCorrelationId);
            workspan.Baggage("TenantId", correlatedMessage?.MessageTenantId);
        }

        if (LogEnterAndExit)
            workspan.LogEnterAndExit();

        try
        {
            using var serviceScope = ServiceScopeFactory.CreateScope();
            var serviceProvider = serviceScope.ServiceProvider;

            ServiceProvider = serviceProvider;
            //ActivityService = serviceProvider.GetRequiredService<IActivityService>();
            Workspan = workspan;
            Context = context;

            await ConsumeMessage(context.Message, context.CancellationToken);
        }
        finally
        {
            ServiceProvider = null;
            //ActivityService = null;

            Workspan = null;
            Context = null;
        }
    }


    protected abstract Task ConsumeMessage(TMessage message, CancellationToken cancellationToken);

    protected ConsumeContext<TMessage> Context { get; private set; }


    #region Dependency Injection

    protected IServiceScopeFactory ServiceScopeFactory { get; private set; }
    public IServiceProvider ServiceProvider { get; private set; }

    protected T GetRequiredService<T>()
    {
        return ServiceProvider.GetRequiredService<T>();
    }

    #endregion

    #region Telemetry

    protected Workspan Workspan { get; private set; }

    #endregion

    #region Activities

    //protected IActivityService ActivityService { get; private set; }

    #endregion

    protected async Task Publish<T>(T message, CancellationToken cancellationToken = default)
        where T : class
    {
        await Context.Publish(message, cancellationToken);
    }
}