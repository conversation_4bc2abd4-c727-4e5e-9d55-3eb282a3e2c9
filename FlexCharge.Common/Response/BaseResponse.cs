using System.Collections.Generic;

namespace FlexCharge.Common.Response
{
    public abstract class BaseResponse
    {
        public BaseResponse()
        {
            this.Errors = new List<(string Error, string Key, string ErrorCode, bool FriendlyError)>();
            this.CustomProperties = new Dictionary<string, string>();
        }

        /// <summary>
        /// Gets a value indicating whether request has been completed successfully
        /// </summary>
        public bool Success
        {
            get { return this.Errors.Count == 0; }
        }

        public string Result { get; set; }
        public string Status { get; set; }
        public string StatusCode { get; set; }

        public virtual void AddError(string error, string key = "", string code = "", bool friendly = true)
        {
            this.Errors.Add((error, key, code, friendly));
        }

        public IList<(string Error, string Key, string ErrorCode, bool FriendlyError)> Errors { get; }

        public Dictionary<string, string> CustomProperties { get; set; }
    }
}