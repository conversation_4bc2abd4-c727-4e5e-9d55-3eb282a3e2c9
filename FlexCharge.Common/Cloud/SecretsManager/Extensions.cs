using FlexCharge.Common.Cloud.Account.Amazon;
using FlexCharge.Common.Cloud.SecretsManager;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Common.Cloud.BI.Amazon
{
    public static class AmazonSecretsManagerServiceCollectionExtensions
    {
        public static IServiceCollection AddAmazonSecretsManager(this IServiceCollection services)
        {
            services.AddTransient<ISecretsManager, AwsSecretsManager>();

            return services;
        }
    }
}