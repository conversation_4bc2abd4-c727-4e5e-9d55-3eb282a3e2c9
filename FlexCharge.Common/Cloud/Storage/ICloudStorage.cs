using System;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace FlexCharge.Common.Cloud.Storage;

public interface ICloudStorage
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="fileStreamToUpload"></param>
    /// <param name="storageName"></param>
    /// <param name="fileName"></param>
    /// <param name="folder"></param>
    /// <param name="allowPublicAccess">Enables public file access through public ACL. Must be enabled for each bucket on S3</param>
    /// <param name="uploadFileInChunks"></param>
    /// <returns></returns>
    Task UploadFileAsync(System.IO.Stream fileStreamToUpload, string storageName,
        string fileName, string folder, bool allowPublicAccess = false, bool uploadFileInChunks = false);

    Task UploadFileAsync(System.IO.Stream fileStreamToUpload, string storageName,
        string filePath, bool uploadFileInChunks = false);


    Task DeleteFileAsync(string storageName, string fileName, string folder);

    Task<string> CreateExpiringPublicFileUrl(DateTime linkExpires, string storageName,
        string fileName, string folder = "");

    /// <summary>
    /// Works only for S3 buckets with public access enabled
    /// or when Cloudfront CDN is used to provide public access to otherwise private files in S3 bucket
    /// </summary>
    /// <param name="storageName"></param>
    /// <param name="fileName"></param>
    /// <param name="folder"></param>
    /// <returns></returns>
    Task<string> CreatePublicFileUrlThroughCDNAsync(string storageName,
        string fileName, string folder = "");


    /// <summary>
    /// Creates a folder in a storage
    /// </summary>
    /// <param name="storageName"></param>
    /// <param name="folderPath"></param>
    /// <returns></returns>
    public Task CreateFolderAsync(string storageName, string folderPath);

    /// <summary>
    /// Creates a folder in a storage if it does not exist
    /// </summary>
    /// <param name="storageName"></param>
    /// <param name="folderPath"></param>
    /// <returns></returns>
    public Task CreateFolderIfMissingAsync(string storageName, string folderPath);

    /// <summary>
    /// Checks if a folder exists in a storage
    /// </summary>
    /// <param name="storageName"></param>
    /// <param name="folderPath"></param>
    /// <returns></returns>
    public Task<bool> DoesFolderExistAsync(string storageName, string folderPath);

    /// <summary>
    /// Returns a list of files names and folder names in a folder
    /// </summary>
    /// <param name="storageName"></param>
    /// <param name="folderPath"></param>
    /// <param name="maxResults">If more than 1000 is specified, maximum of 1000 items is returned</param>
    /// <returns></returns>
    public Task<List<string>> ListFilesAndFoldersRecursiveAsync(
        string storageName, string folderPath,
        int? maxResults = null);

    /// <summary>
    /// Checks if a file exists in a storage
    /// </summary>
    /// <param name="storageName"></param>
    /// <param name="filePath"></param>
    /// <returns></returns>
    Task<bool> DoesFileExistAsync(string storageName, string filePath);

    /// <summary>
    /// Deletes a file from a storage
    /// </summary>
    /// <param name="storageName"></param>
    /// <param name="sourceFilePath"></param>
    /// <returns></returns>
    Task DeleteFileAsync(string storageName, string sourceFilePath);

    /// <summary>
    /// Copies a file within the same storage
    /// </summary>
    /// <param name="storageName"></param>
    /// <param name="sourceFilePath"></param>
    /// <param name="destinationFilePath"></param>
    /// <param name="overwrite">If <paramref name="overwrite"/> is false and file exists then exception will be thrown</param>
    /// <returns></returns>
    Task CopyFileAsync(string storageName, string sourceFilePath, string destinationFilePath,
        bool overwrite);

    /// <summary>
    /// Copies a file from one storage to another
    /// </summary>
    /// <param name="sourceStorageName"></param>
    /// <param name="sourceFilePath"></param>
    /// <param name="destinationStorageName"></param>
    /// <param name="destinationFilePath"></param>
    /// <param name="overwrite">If <paramref name="overwrite"/> is false and file exists then exception will be thrown</param>
    /// <returns></returns>
    Task CopyFileAsync(string sourceStorageName, string sourceFilePath, string destinationStorageName,
        string destinationFilePath, bool overwrite);

    /// <summary>
    /// Moves a file within the same storage
    /// </summary>
    /// <param name="storageName"></param>
    /// <param name="sourceFilePath"></param>
    /// <param name="destinationFilePath"></param>
    /// <param name="overwrite">If <paramref name="overwrite"/> is false and file exists then exception will be thrown</param>
    /// <returns></returns>
    public Task MoveFileAsync(string storageName, string sourceFilePath, string destinationFilePath,
        bool overwrite = false);

    Task<Stream> GetFileStreamAsync(string storageName, string filePath);

    Task<long> GetFileSizeAsync(string storageName, string filePath);
}