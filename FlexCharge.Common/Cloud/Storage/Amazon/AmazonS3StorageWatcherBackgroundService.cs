using System;
using System.Threading;
using System.Threading.Tasks;
using Amazon.SQS.Model;
using FlexCharge.Common.Cloud.Storage.Amazon.AmazonDTOs;
using FlexCharge.Common.Cloud.Storage.Amazon.Internal;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils.Concurrency;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace FlexCharge.Common.Cloud.Storage.Amazon;

class AmazonS3StorageWatcherBackgroundService : AmazonSqsQueueListenerBackgroundServiceBase
{
    public static bool StartWatcherImmediately { get; set; }

    private readonly IOptions<AmazonS3StorageWatcherOptions> _amazonS3StorageWatcherOptions;

    static EventWatchers<AmazonS3StorageEventWatcher, ObjectCreatedOrRemovedEventArgs> _eventWatchers = new();

    public AmazonS3StorageWatcherBackgroundService(
        IOptions<AmazonS3StorageWatcherOptions> amazonS3StorageWatcherOptions)
    {
        _amazonS3StorageWatcherOptions = amazonS3StorageWatcherOptions;

        if (StartWatcherImmediately) Start();
    }


    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var workspan = Workspan.Start<AmazonDTOs.AmazonS3StorageWatcherBackgroundService>();

        var queueName = _amazonS3StorageWatcherOptions.Value.FullQueueNameWithScope;
        var pollingInterval = _amazonS3StorageWatcherOptions.Value.PollingInterval;

        try
        {
            await ProcessMessagesAsync(stoppingToken, queueName, pollingInterval);
        }
        catch (TaskCanceledException)
        {
        }
    }

    protected override async Task<bool> ProcessMessageAsync(Message msg)
    {
        using var workspan = Workspan.Start<AmazonDTOs.AmazonS3StorageWatcherBackgroundService>()
            .Baggage("S3_MessageId", msg.MessageId);

        SqsMessageBody body = JsonConvert.DeserializeObject<SqsMessageBody>(msg.Body);

        if (body.Subject == "Amazon S3 Notification")
        {
            var s3NotificationMessage =
                JsonConvert.DeserializeObject<AmazonDTOs.AmazonS3StorageWatcherBackgroundService.S3NotificationMessage>(
                    body.Message);

            workspan.Log.Information("S3 notification message {S3NotificationMessage}",
                JsonConvert.SerializeObject(s3NotificationMessage));


            if (s3NotificationMessage.Records == null)
            {
                workspan.Log.Information("No records in S3 notification message");
                return true;
            }

            foreach (var s3Notification in s3NotificationMessage.Records)
            {
                var eventName = s3Notification.EventName; //e.g. ObjectCreated:Put

                var bucketName = s3Notification.S3.Bucket.Name;
                var path = System.Web.HttpUtility.UrlDecode(s3Notification.S3.Object
                    .Key); //e.g. folder1/folder2/Test+1.xlsx

                GetFolderAndFileName(path, out var folder, out var fileName);

                workspan
                    .Baggage("S3_Folder", folder)
                    .Baggage("S3_FIleName", fileName);

                workspan.Log.Information("S3 notification message {EventName} for bucket {BucketName} and path {Path}",
                    eventName, bucketName, path);

                if (eventName.StartsWith("ObjectCreated"))
                {
                    await _eventWatchers.NotifyAllAsync(
                        new ObjectCreatedOrRemovedEventArgs(true, bucketName, folder, fileName));
                }
                else if (eventName.StartsWith("ObjectRemoved"))
                {
                    await _eventWatchers.NotifyAllAsync(
                        new ObjectCreatedOrRemovedEventArgs(false, bucketName, folder, fileName));
                }
                else
                {
                    workspan.Log.Warning("Unsupported event name: {EventName}", eventName);
                }
            }
        }

        return true;
    }

    private void GetFolderAndFileName(string path, out string folder, out string fileName)
    {
        int lastSlashIndex = path.LastIndexOf('/');
        folder = path.Substring(0, lastSlashIndex + 1);
        fileName = path.Substring(lastSlashIndex + 1);
    }

    public static void AddWatcher(AmazonS3StorageEventWatcher amazonS3StorageEventWatcher)
    {
        _eventWatchers.AddWatcher(Guid.NewGuid().ToString(), amazonS3StorageEventWatcher);
    }
}