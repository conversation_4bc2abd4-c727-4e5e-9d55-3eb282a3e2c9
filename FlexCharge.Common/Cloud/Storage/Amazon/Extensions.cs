using Amazon.S3;
using Amazon.SimpleNotificationService;
using Amazon.SQS;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Common.Cloud.Storage.Amazon
{
    public static class AmazonS3StorageServiceCollectionExtensions
    {
        public static IServiceCollection AddAmazonS3Storage(this IServiceCollection services)
        {
            IConfiguration configuration;
            using (var serviceProvider = services.BuildServiceProvider())
            {
                configuration = serviceProvider.GetService<IConfiguration>();
            }
            
            services.AddAWSService<IAmazonS3>();
            
            services.Configure<AmazonS3StorageOptions>(configuration.GetSection("amazonS3Storage"));
            services.AddTransient<ICloudStorage, AmazonS3Storage>();
            
            return services;
        }
        
        public static IServiceCollection AddAmazonS3StorageWatcher(this IServiceCollection services, bool startWatcherImmediately = true)
        {

            IConfiguration configuration;
            using (var serviceProvider = services.BuildServiceProvider())
            {
                configuration = serviceProvider.GetService<IConfiguration>();
            }
            
            services.Configure<AmazonS3StorageWatcherOptions>(configuration.GetSection("amazonS3StorageWatcher"));

            services.AddAWSService<IAmazonSQS>();
            services.AddAWSService<IAmazonSimpleNotificationService>();
            services.AddAWSService<IAmazonS3>();


            AmazonS3StorageWatcherBackgroundService.StartWatcherImmediately = startWatcherImmediately;
            
            services.AddHostedService<AmazonS3StorageWatcherBackgroundService>();
            services.AddTransient<ICloudStorageWatcher, AmazonS3StorageEventWatcher>();

            return services;
        }
    }
}