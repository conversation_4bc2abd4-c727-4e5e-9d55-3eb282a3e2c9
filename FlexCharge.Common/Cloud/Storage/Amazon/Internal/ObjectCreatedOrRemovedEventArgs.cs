namespace FlexCharge.Common.Cloud.Storage.Amazon.Internal;

public class ObjectCreatedOrRemovedEventArgs
{
    public ObjectCreatedOrRemovedEventArgs(bool isObjectCreated, string storageName, string folder, string fileName)
    {
        IsObjectCreated = isObjectCreated;
        StorageName = storageName;
        Folder = folder;
        FileName = fileName;
    }

    public bool IsObjectCreated { get; }
    public string StorageName { get; }
    public string Folder { get; }
    public string FileName { get; }
}