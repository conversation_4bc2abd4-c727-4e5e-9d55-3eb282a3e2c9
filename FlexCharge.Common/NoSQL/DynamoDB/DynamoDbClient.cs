using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using Amazon.DynamoDBv2.Model;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils;
using Polly;

namespace FlexCharge.Common.NoSQL.DynamoDB;

public class DynamoDbClient : IDisposable
{
    private readonly IAmazonDynamoDB _dynamoDb;
    private readonly string _tableNamePrefix;
    private readonly DynamoDBContext _dynamoContext;

    static ConcurrentDictionary<Type, (string TableName, string PartitionKey, string SortKey)>
        _typeToTableMapping = new();

    public DynamoDbClient(IAmazonDynamoDB dynamoDb)
    {
        _dynamoDb = dynamoDb;
        _dynamoContext = new DynamoDBContext(dynamoDb);
    }

    #region Table Mapping

    internal static (string TableName, string PartitionKeyName, string SortKeyName) GetTableMapping<T>()
    {
        var type = typeof(T);

        if (_typeToTableMapping.TryGetValue(type, out var mapping))
            return mapping;

        var tableName = GetScopedTableName<T>();

        var partitionKeyAttribute = ReflectionHelpers
            .GetPropertiesWithAttribute<DynamoDBHashKeyAttribute>(type, false)
            .FirstOrDefault();

        if (partitionKeyAttribute == null)
            throw new Exception($"Type {type.Name} does not have a property with DynamoDBHashKeyAttribute");

        var partitionKey = partitionKeyAttribute.Name;

        string? sortKey = null;
        var sortKeyAttribute = ReflectionHelpers
            .GetPropertiesWithAttribute<DynamoDBRangeKeyAttribute>(type, false)
            .FirstOrDefault();

        if (sortKeyAttribute != null)
            sortKey = sortKeyAttribute.Name;

        _typeToTableMapping[type] = (tableName, partitionKey, sortKey);

        return (tableName, partitionKey, sortKey);
    }

    public static string GetScopedTableName<T>()
    {
        var type = typeof(T);

        StringBuilder tableNamePrefix = new(EnvironmentHelper.GetCurrentEnvironment().ToString());

        tableNamePrefix.Append('_');

        if (EnvironmentHelper.IsInDevelopment)
        {
            AddLocalDebuggingPrefix(tableNamePrefix);
        }

        var tableNameAttribute = ReflectionHelpers
            .GetAttributesOfType<DynamoDBTableAttribute>(type, false).FirstOrDefault();

        var tableName = tableNamePrefix + (tableNameAttribute?.TableName ?? type.Name);
        return tableName;
    }

    private static void AddLocalDebuggingPrefix(StringBuilder tableName)
    {
        StringBuilder localDebuggingPrefix = new();

        // To make name unique for each developer machine
        localDebuggingPrefix.Append(Environment.UserName);

        localDebuggingPrefix.Append('_');

        tableName.Append(localDebuggingPrefix);

        // if (localDebuggingPrefix.Length != 0)
        // {
        //     
        //     // If need this prefix to be of predictable length -> use hash
        //
        //     //see: https://yetanotherchris.dev/csharp/friendly-unique-id-generation-part-2/
        //     var s = localDebuggingPrefix.ToString();
        //     var stableHash = StringNonCryptoHashingHelper.GetStableHashCode(s);
        //     tableName.Append(string.Format("{0:X}", stableHash).ToUpper());
        //     tableName.Append('_');
        // }
    }

    #endregion

    #region Batches

    /// <summary>
    /// 
    /// </summary>
    /// <param name="itemsToWrite"></param>
    /// <remarks><see cref="itemsToWrite"/> cannot contain any duplicate PartitionKey+SortKey items</remarks>
    public async Task WriteBatchAsync<T>(List<T> itemsToWrite)
    {
        if (itemsToWrite.Count == 0)
            return;

        var tableMapping = GetTableMapping<T>();

        // Convert your items to a list of WriteRequest objects
        var writeRequests = new List<WriteRequest>();
        foreach (var item in itemsToWrite)
        {
            //var document = new Document();
            // Assuming you have a method that converts your item to a Document object
            // This would typically use the DynamoDBContext to serialize your object into a Document
            var document = _dynamoContext.ToDocument(item);

            writeRequests.Add(new WriteRequest
            {
                PutRequest = new PutRequest {Item = document.ToAttributeMap()}
            });
        }

        // Amazon DynamoDB allows us to write up to 25 items in a single BatchWriteItem request
        const int MAX_DYNAMO_DB_WRITE_BATCH_SIZE = 25;
        var batches = SplitIntoChunks(writeRequests, MAX_DYNAMO_DB_WRITE_BATCH_SIZE);

        // Perform batch writes
        foreach (var batch in batches)
        {
            var requestItems = new Dictionary<string, List<WriteRequest>> {{tableMapping.TableName, batch}};
            var batchWriteRequest = new BatchWriteItemRequest {RequestItems = requestItems};

            BatchWriteItemResponse batchWriteResponse;

            int maxRetries = 10;
            int retryDelay = 100; // Initial delay in milliseconds (e.g., 100ms)
            int maxDelay = 20000; // Maximum delay in milliseconds (e.g., 20 seconds)
            double backoffFactor = 2; // Factor by which the delay is multiplied each retry

            do
            {
                try
                {
                    batchWriteResponse = await _dynamoDb.BatchWriteItemAsync(batchWriteRequest);

                    // Check for any unprocessed items and retry them,
                    // as DynamoDB may not process all items in a single batch
                    // if you exceed the throughput limits for the table or a particular partition
                    batchWriteRequest.RequestItems = batchWriteResponse.UnprocessedItems;
                }
                catch (ProvisionedThroughputExceededException e)
                {
                    Workspan.Current?.Log.Warning(
                        "DynamoDB BatchWriteItemAsync error > ProvisionedThroughputExceededException: {Exception}",
                        e.Message);
                }
                catch (RequestLimitExceededException e)
                {
                    Workspan.Current?.Log.Warning(
                        "DynamoDB BatchWriteItemAsync error > RequestLimitExceededException: {Exception}",
                        e.Message);
                }
                catch (Exception e)
                {
                    Workspan.Current?.Log.Error("DynamoDB BatchWriteItemAsync error: {Exception}",
                        e.Message);
                }


                if (batchWriteRequest.RequestItems.Count > 0)
                {
                    if (maxRetries-- <= 0)
                    {
                        throw new Exception("Cannot save batch to DynamoDB - max retries exceeded");
                    }

                    await Task.Delay(retryDelay); // Apply exponential back-off

                    // Increase the delay for the next retry, capped at the maximum delay
                    retryDelay = Math.Min((int) (retryDelay * backoffFactor), maxDelay);
                }
            } while (batchWriteRequest.RequestItems.Count > 0);
        }
    }

    private static List<List<WriteRequest>> SplitIntoChunks(List<WriteRequest> source, int chunkSize)
    {
        var result = new List<List<WriteRequest>>();
        for (int i = 0; i < source.Count; i += chunkSize)
        {
            result.Add(source.GetRange(i, Math.Min(chunkSize, source.Count - i)));
        }

        return result;
    }

    public async Task WriteItemToDynamoDbAsync<T>(IAmazonDynamoDB dynamoDb, string targetTable, T duplicateItem,
        Func<T, Dictionary<string, AttributeValue>> itemSelector)
    {
        await WriteBatchAsync(dynamoDb, targetTable,
            new List<T>() {duplicateItem}, itemSelector);
    }

    public async Task WriteBatchAsync<T>(
        IAmazonDynamoDB dynamoDb,
        string tableName,
        List<T> buffer,
        Func<T, Dictionary<string, AttributeValue>> itemSelector)
    {
        var batchItemsToWrite = buffer
            .Select(x =>
                new WriteRequest
                {
                    PutRequest =
                        new PutRequest
                        {
                            Item = itemSelector(x)
                        }
                }
            )
            .ToList();

        if (batchItemsToWrite.Count == 0)
            return;

        var batchWriteItemRequest = new BatchWriteItemRequest
        {
            RequestItems = new Dictionary<string, List<WriteRequest>>
            {
                {
                    tableName, batchItemsToWrite
                }
            }
        };


        BatchWriteItemResponse batchWriteResponse;

        int maxRetries = 10;
        int retryDelay = 100; // Initial delay in milliseconds (e.g., 100ms)
        int maxDelay = 20000; // Maximum delay in milliseconds (e.g., 20 seconds)
        double backoffFactor = 2; // Factor by which the delay is multiplied each retry

        do
        {
            try
            {
                batchWriteResponse = await dynamoDb.BatchWriteItemAsync(batchWriteItemRequest);

                // Check for any unprocessed items and retry them,
                // as DynamoDB may not process all items in a single batch
                // if you exceed the throughput limits for the table or a particular partition
                batchWriteItemRequest.RequestItems = batchWriteResponse.UnprocessedItems;
            }
            catch (ProvisionedThroughputExceededException e)
            {
                Workspan.Current?.Log.Warning("DynamoDB ProvisionedThroughputExceededException: {Exception}",
                    e.Message);
            }
            catch (RequestLimitExceededException e)
            {
                Workspan.Current?.Log.Warning(
                    "DynamoDB RequestLimitExceededException: {Exception}",
                    e.Message);
            }
            catch (Exception e)
            {
                Workspan.Current?.Log.Error("DynamoDB BatchWriteItemAsync error: {Exception}",
                    e.Message);
            }


            if (batchWriteItemRequest.RequestItems.Count > 0)
            {
                if (maxRetries-- <= 0)
                    throw new Exception("Cannot save batch to DynamoDB - max retries exceeded");

                await Task.Delay(retryDelay); // Apply exponential back-off

                // Increase the delay for the next retry, capped at the maximum delay
                retryDelay = Math.Min((int) (retryDelay * backoffFactor), maxDelay);
            }
        } while (batchWriteItemRequest.RequestItems.Count > 0);
    }

    #endregion

    #region Querying

    public async Task<T> GetItemAsync<T>(string partitionKey, bool useRetries = true)
    {
        var tableMapping = GetTableMapping<T>();

        var dynamoDbOperationConfig = new DynamoDBOperationConfig()
        {
            OverrideTableName = tableMapping.TableName
        };

        var retryPolicy = Polly.Policy
            .Handle<Exception>() // Specify the exceptions you expect during throttling or transient failures
            .WaitAndRetryAsync(3, // Number of retries
                retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // Exponential back-off
                onRetry: (exception, timeSpan, retryCount, context) =>
                {
                    Workspan.Current?.Log.Warning("Retrying DynamoDB query. Retry {RetryCount}, Exception: {Exception}",
                        retryCount, exception.Message);
                });

        if (useRetries)
        {
            return await retryPolicy.ExecuteAsync(async () =>
            {
                return await _dynamoContext.LoadAsync<T>(partitionKey, dynamoDbOperationConfig);
            });
        }
        else
        {
            return await _dynamoContext.LoadAsync<T>(partitionKey, dynamoDbOperationConfig);
        }
    }


    public async Task<T> GetItemAsync<T>(string partitionKey, string sortKey, bool useRetries = true)
    {
        var tableMapping = GetTableMapping<T>();

        var dynamoDbOperationConfig = new DynamoDBOperationConfig()
        {
            OverrideTableName = tableMapping.TableName
        };

        var retryPolicy = Polly.Policy
            .Handle<Exception>() // Specify the exceptions you expect during throttling or transient failures
            .WaitAndRetryAsync(3, // Number of retries
                retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // Exponential back-off
                onRetry: (exception, timeSpan, retryCount, context) =>
                {
                    Workspan.Current?.Log.Warning("Retrying DynamoDB query. Retry {RetryCount}, Exception: {Exception}",
                        retryCount, exception.Message);
                });

        if (useRetries)
        {
            return await retryPolicy.ExecuteAsync(async () =>
            {
                return await _dynamoContext.LoadAsync<T>(partitionKey, sortKey, dynamoDbOperationConfig);
            });
        }
        else
        {
            return await _dynamoContext.LoadAsync<T>(partitionKey, sortKey, dynamoDbOperationConfig);
        }
    }

    public enum ComparisonOperator
    {
        Equal,
        LessThan,
        LessThanOrEqual,
        GreaterThan,
        GreaterThanOrEqual,
        BeginsWith,
        Between
    }

    public async Task<List<T>> QueryItemsAsync<T>(string partitionKey,
        bool sortAscending = true,
        int? limit = null,
        SortKeyQuery sortKeyQuery = null,
        string paginationToken = null,
        bool consistentRead = false,
        CancellationToken cancellationToken = default)
    {
        //see: https://benfoster.io/blog/20200615-querying-a-dynamodb-partition-with-dotnet/

        var tableMapping = GetTableMapping<T>();

        var expressionAttributeValues = new Dictionary<string, DynamoDBEntry>
        {
            {":pk", partitionKey}
        };

        StringBuilder queryExpression = new($"{tableMapping.PartitionKeyName} = :pk");

        if (sortKeyQuery != null)
        {
            var sortKeyValueVariable = $":sk";
            expressionAttributeValues.Add(sortKeyValueVariable, sortKeyQuery.QueryParameter1);

            queryExpression.Append(" AND ");

            if (sortKeyQuery.SortKeyComparison == ComparisonOperator.BeginsWith)
            {
                // Construct the key expression based on whether a sort key is provided
                queryExpression.Append(
                    $"begins_with({tableMapping.SortKeyName}, {sortKeyValueVariable})"); // Include sort key comparison in the expression
            }
            else if (sortKeyQuery.SortKeyComparison == ComparisonOperator.Between)
            {
                var sortKeyValueVariable2 = $":sk2";
                expressionAttributeValues.Add(sortKeyValueVariable2, sortKeyQuery.QueryParameter2);

                // Construct the key expression based on whether a sort key is provided
                queryExpression.Append(
                    $"{tableMapping.SortKeyName} between {sortKeyValueVariable} and {sortKeyValueVariable2}"); // Include sort key comparison in the expression
            }
            else
            {
                string sortKeyComparisonOperator;

                switch (sortKeyQuery.SortKeyComparison)
                {
                    case ComparisonOperator.Equal:
                        sortKeyComparisonOperator = "=";
                        break;
                    case ComparisonOperator.LessThan:
                        sortKeyComparisonOperator = "<";
                        break;
                    case ComparisonOperator.LessThanOrEqual:
                        sortKeyComparisonOperator = "<=";
                        break;
                    case ComparisonOperator.GreaterThan:
                        sortKeyComparisonOperator = ">";
                        break;
                    case ComparisonOperator.GreaterThanOrEqual:
                        sortKeyComparisonOperator = ">=";
                        break;
                    case ComparisonOperator.BeginsWith:
                        sortKeyComparisonOperator = ">=";
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(ComparisonOperator),
                            sortKeyQuery.SortKeyComparison,
                            $"Unknown {nameof(ComparisonOperator)}");
                }

                // Construct the key expression based on whether a sort key is provided
                queryExpression.Append(
                    $"{tableMapping.SortKeyName} {sortKeyComparisonOperator} {sortKeyValueVariable}"); // Include sort key comparison in the expression
            }
        }


        var queryOperationConfig = new QueryOperationConfig
        {
            ConsistentRead = consistentRead,
            BackwardSearch = sortAscending ? false : true,
            PaginationToken = paginationToken,
            KeyExpression = new Expression
            {
                ExpressionStatement = queryExpression.ToString(),
                ExpressionAttributeValues = expressionAttributeValues
            }

            //NOTE: Do not use Filter expression, as it is applied after the query is executed!!!
        };

        if (limit != null)
            queryOperationConfig.Limit = limit.Value;

        // Don't use table.Query as it doesn't take limit to account

        var search = _dynamoContext.FromQueryAsync<T>(queryOperationConfig, new DynamoDBOperationConfig()
        {
            OverrideTableName = tableMapping.TableName
        });

        var results = new List<T>();

        do
        {
            List<T> nextSet = await search.GetNextSetAsync(cancellationToken);
            results.AddRange(nextSet);

            // Limit is applied only to each  GetNextSetAsync call. If it's called again it will return limit next items again
        } while (!search.IsDone && (limit == null || results.Count < limit.Value));

        return results;
    }

    public async Task<List<T>> QueryItemsByMultiplePartitionKeysAsync<T>(
        IEnumerable<string> partitionKeys,
        DateTime sortKey,
        ComparisonOperator sortKeyComparison = ComparisonOperator.Equal,
        bool sortAscending = true,
        int? limit = null,
        string paginationToken = null,
        CancellationToken cancellationToken = default)
    {
        return await QueryItemsByMultiplePartitionKeysAsync<T>(
            partitionKeys,
            sortKey.ToString("o"),
            sortKeyComparison,
            sortAscending,
            limit,
            paginationToken,
            cancellationToken);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="partitionKeys"></param>
    /// <param name="sortKey"></param>
    /// <param name="sortAscending"></param>
    /// <param name="limit">Maximum number of results returned per partition key</param>
    /// <param name="sortKeyComparison"></param>
    /// <param name="paginationToken"></param>
    /// <param name="cancellationToken"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<List<T>> QueryItemsByMultiplePartitionKeysAsync<T>(
        IEnumerable<string> partitionKeys,
        string sortKey,
        ComparisonOperator sortKeyComparison = ComparisonOperator.Equal,
        bool sortAscending = true,
        int? limit = null,
        string paginationToken = null,
        CancellationToken cancellationToken = default)
    {
        return await QueryItemsByMultiplePartitionKeysAsync<T>(
            partitionKeys,
            sortKey,
            sortKeyComparison,
            sortAscending,
            limit,
            paginationToken,
            cancellationToken);
    }

    public record SortKeyQuery(string QueryParameter1, ComparisonOperator SortKeyComparison)
    {
        public string? QueryParameter2 { get; init; }
    }

    public record QueryParameters(string PartitionKey, SortKeyQuery SortKeyQuery);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="queries"></param>
    /// <param name="sortAscending"></param>
    /// <param name="limit"></param>
    /// <param name="paginationToken"></param>
    /// <param name="consistentRead">Ensures that the very latest items stored in DynamoDB returned</param>
    /// <param name="cancellationToken"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<List<T>> QueryItemsByMultiplePartitionKeysAsync<T>(
        IEnumerable<QueryParameters> queries,
        bool sortAscending = true,
        int? limit = null,
        string paginationToken = null,
        bool consistentRead = false,
        int? maxDegreeOfParallelism = null,
        CancellationToken cancellationToken = default)
    {
        var tasks = queries.Select(x =>
                QueryItemsAsync<T>(
                    x.PartitionKey, sortAscending, limit, x.SortKeyQuery,
                    paginationToken, consistentRead, cancellationToken))
            .ToList();

        var results = await ThreadingHelpers.RunTasksAsync(tasks, maxDegreeOfParallelism);

        return results.SelectMany(x => x).ToList();
    }

    #endregion

    #region Atomic Operations

    public enum AtomicOperationType
    {
        Initialize,
        Set,
        Add,
        Subtract
    }

    public enum AtomicOperandType
    {
        Number,
        String,
        Boolean,
    }

    public async Task PerformAtomicOperationAsync(
        string tableName,
        AtomicOperationType operation,
        AtomicOperandType operandType,
        string attributeToChange, string value,
        string partitionKeyName, string partitionKey,
        bool useRetries,
        string? sortKeyName = null, string? sortKey = null)
    {
        await AtomicUpdateItemsAsync(
            new List<AtomicOperation> {new(operation, operandType, attributeToChange, value)},
            AtomicOperationReturnValue.None,
            tableName,
            partitionKeyName, partitionKey,
            useRetries, sortKeyName,
            sortKey);
    }

    public async Task PerformAtomicOperationsAsync<T>(
        List<AtomicOperation> operations,
        string partitionKey,
        bool useRetries,
        string? sortKey = null)
    {
        await AtomicUpdateItemsAsync<T>(
            operations,
            AtomicOperationReturnValue.None, useRetries,
            partitionKey, sortKey
        );
    }

    public record ItemAtomicOperations(string PartitionKey, List<AtomicOperation> Operations)
    {
        public string? SortKey { get; init; }
    };

    public async Task PerformAtomicOperationsAsync<T>(
        IEnumerable<ItemAtomicOperations> operations,
        bool useRetries,
        int? maxDegreeOfParallelism = null)
    {
        var tasks = operations.Select(x =>
                AtomicUpdateItemsAsync<T>(
                    x.Operations,
                    AtomicOperationReturnValue.None,
                    useRetries,
                    x.PartitionKey,
                    x.SortKey))
            .ToList();

        await ThreadingHelpers.RunTasksAsync(tasks, maxDegreeOfParallelism);
    }

    internal enum AtomicOperationReturnValue
    {
        // Nothing is returned in the response. This is the default value.
        None,

        // Returns all of the attributes of the item as they appeared before the update operation. This is useful if you want to see the state of the item before you modified it.
        AllOld,

        // Returns only the updated attributes as they appeared before the update operation.
        UpdatedOld,

        // Returns all of the attributes of the item after the update operation. This allows you to see the effect of the update, including any attributes that were automatically added.
        AllNew,

        // Returns only the attributes that were modified in the update operation.
        UpdatedNew
    }

    public record AtomicOperation(
        AtomicOperationType Operation,
        AtomicOperandType OperandType,
        string AttributeToChange,
        object Value);

    internal async Task<UpdateItemResponse?> AtomicUpdateItemsAsync<T>(
        List<AtomicOperation> operations,
        AtomicOperationReturnValue returnValueType,
        bool useRetries,
        string partitionKey, string? sortKey)
    {
        var tableMapping = GetTableMapping<T>();

        return await AtomicUpdateItemsAsync(operations, returnValueType,
            tableMapping.TableName, tableMapping.PartitionKeyName, partitionKey, useRetries, tableMapping.SortKeyName,
            sortKey);
    }


    internal async Task<UpdateItemResponse?> AtomicUpdateItemsAsync(
        List<AtomicOperation> operations,
        AtomicOperationReturnValue returnValueType,
        string tableName,
        string partitionKeyName, string partitionKey,
        bool useRetries,
        string? sortKeyName = null, string? sortKey = null
    )
    {
        if (operations.Count == 0)
            return null;

        #region Create key to update

        var key = new Dictionary<string, AttributeValue>
        {
            {partitionKeyName, new AttributeValue {S = partitionKey}},
        };

        if (sortKey != null)
        {
            if (string.IsNullOrWhiteSpace(sortKeyName))
                throw new ArgumentNullException(nameof(sortKeyName));

            key.Add(sortKeyName, new AttributeValue {S = sortKey});
        }

        #endregion

        #region Get ReturnValues string

        string returnValues;
        switch (returnValueType)
        {
            case AtomicOperationReturnValue.None:
                returnValues = "NONE";
                break;
            case AtomicOperationReturnValue.AllOld:
                returnValues = "ALL_OLD";
                break;
            case AtomicOperationReturnValue.UpdatedOld:
                returnValues = "UPDATED_OLD";
                break;
            case AtomicOperationReturnValue.AllNew:
                returnValues = "ALL_NEW";
                break;
            case AtomicOperationReturnValue.UpdatedNew:
                returnValues = "UPDATED_NEW";
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(returnValueType), returnValueType,
                    $"Unknown {nameof(AtomicOperationReturnValue)}");
        }

        #endregion

        var expressionAttributeValues = new Dictionary<string, AttributeValue>();

        StringBuilder updateExpression = new();

        updateExpression.Append("SET");

        int variableIndex = 0;
        bool firstArgument = true;
        bool addZero = false;
        foreach (var operation in operations)
        {
            string variableName = $"v{variableIndex++}";

            switch (operation.OperandType)
            {
                case AtomicOperandType.Number:
                    expressionAttributeValues.Add($":{variableName}",
                        new AttributeValue {N = operation.Value.ToString()});
                    break;
                case AtomicOperandType.String:
                    expressionAttributeValues.Add($":{variableName}",
                        new AttributeValue {S = (string) operation.Value});
                    break;
                case AtomicOperandType.Boolean:
                    expressionAttributeValues.Add($":{variableName}",
                        new AttributeValue {BOOL = (bool) operation.Value});
                    break;

                default:
                    throw new ArgumentOutOfRangeException(nameof(operation.OperandType), operation.OperandType,
                        $"Unknown {nameof(operation.OperandType)}");
            }


            if (firstArgument)
            {
                updateExpression.Append(" ");
                firstArgument = false;
            }
            else updateExpression.Append(", ");

            // Note: If a variable is defined but not used, DynamoDB returns validation exception.
            // E.g. do not define :zero if nut used

            switch (operation.Operation)
            {
                case AtomicOperationType.Initialize:
                    updateExpression.Append(
                        $"{operation.AttributeToChange} = if_not_exists({operation.AttributeToChange}, :{variableName})");
                    break;
                case AtomicOperationType.Set:
                    updateExpression.Append($"{operation.AttributeToChange} = :{variableName}");
                    break;
                case AtomicOperationType.Add:
                    //viewCount = if_not_exists(viewCount, :start) + :variable
                    updateExpression.Append(
                        $"{operation.AttributeToChange} = if_not_exists({operation.AttributeToChange}, :zero) + :{variableName}");
                    addZero = true;
                    break;
                case AtomicOperationType.Subtract:
                    //viewCount = if_not_exists(viewCount, :start) - :variable
                    updateExpression.Append(
                        $"{operation.AttributeToChange} = if_not_exists({operation.AttributeToChange}, :zero) - :{variableName}");
                    addZero = true;
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(operation.Operation), operation.Operation,
                        $"Unknown {nameof(AtomicOperation)}");
            }
        }

        if (addZero)
        {
            expressionAttributeValues.Add($":zero", new AttributeValue {N = "0"});
        }

        var request = new UpdateItemRequest
        {
            TableName = tableName,
            Key = key,
            ExpressionAttributeValues = expressionAttributeValues,
            UpdateExpression = updateExpression.ToString(),
            ReturnValues = returnValues
        };

        if (useRetries)
        {
            return await UpdateItemWithBackoffAsync(request);
        }
        else
        {
            return await _dynamoDb.UpdateItemAsync(request);
        }
    }

    private async Task<UpdateItemResponse> UpdateItemWithBackoffAsync(UpdateItemRequest request)
    {
        // Define the exponential back-off policy
        var retryPolicy = Polly.Policy
            .Handle<ProvisionedThroughputExceededException>()
            .WaitAndRetryAsync(5, retryAttempt => // Retry 5 times
                    TimeSpan.FromMilliseconds(Math.Pow(2, retryAttempt) * 100),
                onRetry: (exception, timeSpan, retryCount, context) =>
                {
                    Workspan.Current?.Log.Warning(
                        "Retrying DynamoDB UpdateItem. Retry {RetryCount}, Exception: {Exception}", retryCount,
                        exception.Message);
                }); // Exponential back-off

        // Execute the operation using the retry policy
        return await retryPolicy.ExecuteAsync(async () => { return await _dynamoDb.UpdateItemAsync(request); });
    }

    internal async Task<T> AtomicUpdateItemsAsync<T>(
        List<AtomicOperation> operations,
        AtomicOperationReturnValue returnValueType,
        string tableName,
        string partitionKeyName, string partitionKey,
        bool useRetries,
        string? sortKeyName = null, string? sortKey = null)
    {
        var updateResponse = await AtomicUpdateItemsAsync(operations, returnValueType,
            tableName, partitionKeyName, partitionKey,
            useRetries,
            sortKeyName, sortKey
        );

        // If updateResponse is null, return the default value of T, which is null for reference types
        if (updateResponse == null) return default(T);

        // Convert the returned attributes to a Document
        var document = Document.FromAttributeMap(updateResponse.Attributes);

        // Use the DynamoDBContext to convert the Document to the specified type
        var context = new DynamoDBContext(_dynamoDb);
        T result = context.FromDocument<T>(document);

        return result;
    }

    #endregion

    public void Dispose()
    {
        _dynamoContext?.Dispose();
    }
}