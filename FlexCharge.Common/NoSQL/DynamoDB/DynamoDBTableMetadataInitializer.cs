// NOT USED FOR NOW - DO NOT REMOVE - CAN BE USED FOR MANUAL TABLE METADATA POPULATION
// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Reflection;
// using Amazon;
// using Amazon.DynamoDBv2;
// using Amazon.DynamoDBv2.DataModel;
// using Amazon.DynamoDBv2.DocumentModel;
//
// static class DynamoDBTableMetadataInitializer
// {
//     public static void PopulateDynamoDbTableMetadataFromEntities(
//         IAmazonDynamoDB client, 
//         IDynamoDBContext context)
//     {
//         // // Alternative way - using reflection to populate table metadata manually
//         // // Find all classes marked with the DynamoDBTable attributes
//         // // and populating the DynamoDB table metadata
//         // // to avoid thread starvation that can occur if DynamoDB SDK populates metadata from server
//         // PopulateDynamoDbTablesMetadataFromEntities(client, context);
//     }
//
//    
//     private static void PopulateDynamoDbTablesMetadataFromEntities(IAmazonDynamoDB client, IDynamoDBContext context)
//     {
//         // Find all classes market with the DynamoDBTable attribute
//         var dynamoDbTableEntities = AppDomain.CurrentDomain.GetAssemblies()
//             .SelectMany(s => s.GetTypes())
//             .Where(p => Attribute.IsDefined(p, typeof(DynamoDBTableAttribute)));
//     
//         foreach (var dynamoDbEntity in dynamoDbTableEntities)
//         {
//             var tableAttribute = dynamoDbEntity.GetCustomAttribute<DynamoDBTableAttribute>()!;
//     
//            
//             var table = PopulateDynamoDbTableMetadata(client, dynamoDbEntity, tableAttribute);
//             
//             context.RegisterTableDefinition(table);
//         }
//     }
//     
//     private static Table PopulateDynamoDbTableMetadata(IAmazonDynamoDB client, Type dynamoDbEntity,
//         DynamoDBTableAttribute tableAttribute)
//     {
//         var tableBuilder = new TableBuilder(client, tableAttribute!.TableName);
//     
//         // Find all properties marked with the DynamoDBProperty attribute
//         var properties = dynamoDbEntity.GetProperties()
//             .Where(p => Attribute.IsDefined(p, typeof(DynamoDBPropertyAttribute)))
//             .ToList();
//     
//         foreach (var entityProperty in properties)
//         {
//             var propertyAttribute = entityProperty.GetCustomAttribute<DynamoDBPropertyAttribute>();
//     
//             switch (propertyAttribute)
//             {
//                 case DynamoDBGlobalSecondaryIndexHashKeyAttribute globalSecondaryIndexHashKeyAttribute:
//                     foreach (var indexName in globalSecondaryIndexHashKeyAttribute.IndexNames)
//                     {
//                         CreateGlobalSecondaryIndex(properties, indexName, tableBuilder,
//                             globalSecondaryIndexHashKeyAttribute, entityProperty);
//                     }
//     
//                     break;
//     
//                 case DynamoDBLocalSecondaryIndexRangeKeyAttribute localSecondaryIndexRangeKeyAttribute:
//                     foreach (var indexName in localSecondaryIndexRangeKeyAttribute.IndexNames)
//                     {
//                         CreateLocalSecondaryIndex(tableBuilder, indexName, localSecondaryIndexRangeKeyAttribute,
//                             entityProperty);
//                     }
//     
//                     break;
//     
//                 case DynamoDBHashKeyAttribute hashKeyAttribute:
//                     CreateHashKey(tableBuilder, hashKeyAttribute, entityProperty);
//                     break;
//     
//                 case DynamoDBRangeKeyAttribute rangeKeyAttribute:
//                     CreateRangeKey(tableBuilder, rangeKeyAttribute, entityProperty);
//                     break;
//             }
//         }
//         
//         return tableBuilder.Build();
//     }
//     
//     private static void CreateRangeKey(TableBuilder table, DynamoDBRangeKeyAttribute rangeKeyAttribute,
//         PropertyInfo entityProperty)
//     {
//         table.AddRangeKey(rangeKeyAttribute.AttributeName ?? entityProperty.Name,
//             GetDynamoDbType(entityProperty));
//     }
//     
//     private static void CreateHashKey(TableBuilder table, DynamoDBHashKeyAttribute hashKeyAttribute,
//         PropertyInfo entityProperty)
//     {
//         table.AddHashKey(hashKeyAttribute.AttributeName ?? entityProperty.Name,
//             GetDynamoDbType(entityProperty));
//     }
//     
//     private static void CreateLocalSecondaryIndex(TableBuilder table, string indexName,
//         DynamoDBLocalSecondaryIndexRangeKeyAttribute localSecondaryIndexRangeKeyAttribute, PropertyInfo entityProperty)
//     {
//         table.AddLocalSecondaryIndex(indexName,
//             localSecondaryIndexRangeKeyAttribute.AttributeName ?? entityProperty.Name,
//             GetDynamoDbType(entityProperty));
//     }
//     
//     private static void CreateGlobalSecondaryIndex(List<PropertyInfo> properties, string indexName, TableBuilder table,
//         DynamoDBGlobalSecondaryIndexHashKeyAttribute globalSecondaryIndexHashKeyAttribute, PropertyInfo entityProperty)
//     {
//         // trying to find if a range property exists for this index
//         var rangeProperties = properties.Where(p =>
//                 Attribute.IsDefined(p, typeof(DynamoDBGlobalSecondaryIndexRangeKeyAttribute)) &&
//                 p.GetCustomAttribute<DynamoDBGlobalSecondaryIndexRangeKeyAttribute>()?.IndexNames
//                     .Contains(indexName) == true)
//             .ToList();
//     
//         if (rangeProperties.Count() > 1)
//             throw new Exception("Only one range key is allowed for a global secondary index");
//     
//         if (rangeProperties.Any())
//         {
//             var rangeProperty = rangeProperties.First();
//             var globalSecondaryIndexRangeKeyAttribute = rangeProperty
//                 .GetCustomAttribute<DynamoDBGlobalSecondaryIndexRangeKeyAttribute>();
//     
//             table.AddGlobalSecondaryIndex(indexName,
//                 globalSecondaryIndexHashKeyAttribute.AttributeName ?? entityProperty.Name,
//                 GetDynamoDbType(entityProperty),
//                 globalSecondaryIndexRangeKeyAttribute.AttributeName ?? rangeProperty.Name,
//                 GetDynamoDbType(rangeProperty));
//         }
//         else
//         {
//             table.AddGlobalSecondaryIndex(indexName,
//                 globalSecondaryIndexHashKeyAttribute.AttributeName ?? entityProperty.Name,
//                 GetDynamoDbType(entityProperty));
//         }
//     }
//     
//     private static DynamoDBEntryType GetDynamoDbType(PropertyInfo hashKey)
//     {
//         var propertyType = hashKey.PropertyType;
//     
//         if (propertyType == typeof(string))
//             return DynamoDBEntryType.String;
//     
//         if (propertyType == typeof(bool))
//             return DynamoDBEntryType.Boolean;
//     
//         if (propertyType == typeof(DateTime))
//             return DynamoDBEntryType.String;
//     
//         if (propertyType == typeof(Guid))
//             return DynamoDBEntryType.String;
//     
//         if (propertyType == typeof(int))
//             return DynamoDBEntryType.Numeric;
//     
//         if (propertyType == typeof(double))
//             return DynamoDBEntryType.Numeric;
//     
//         if (propertyType == typeof(float))
//             return DynamoDBEntryType.Numeric;
//     
//         if (propertyType == typeof(decimal))
//             return DynamoDBEntryType.Numeric;
//     
//         if (propertyType == typeof(byte[]))
//             return DynamoDBEntryType.Binary;
//     
//         return DynamoDBEntryType.String;
//     }
// }

