using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
//using FlexCharge.Identity.DTO;
using GoogleReCaptcha.V3.Interface;
using Newtonsoft.Json;

namespace FlexCharge.Common.Recaptcha
{
    public interface IRecaptchaService
    {
        Task<bool> CheckRecaptchaAsync(string token, string? email);
    }

    public record RecaptchaData
    {
        [JsonProperty("success")] public bool? Success { get; set; }
        [JsonProperty("score")] public double? Score { get; set; }
        [JsonProperty("action")] public string? Action { get; set; }
        [JsonProperty("challenge_ts")] public string? ChallengeTimestamp { get; set; }
        [JsonProperty("hostname")] public string? Hostname { get; set; }
        [JsonProperty("error-codes")] public string?[] ErrorCodes { get; set; }
    }

    public class RecaptchaService : IRecaptchaService
    {
        private readonly ICaptchaValidator _captchaValidator;
        private readonly IMapper _mapper;

        public RecaptchaService(ICaptchaValidator captchaValidator, IMapper mapper)
        {
            _captchaValidator = captchaValidator;
            _mapper = mapper;
        }

        public async Task<bool> CheckRecaptchaAsync(string token, string? email)
        {
            using var workspan = Workspan.Start<RecaptchaService>()
                .Baggage("Token", token);

            try
            {
                var test = Environment.GetEnvironmentVariable("RECAPTCHA_SECRET_KEY");
                _captchaValidator.UpdateSecretKey(Environment.GetEnvironmentVariable("RECAPTCHA_SECRET_KEY"));
                // var isTokenValid = await _captchaValidator.IsCaptchaPassedAsync(token);
                //
                // if (!isTokenValid)
                // {
                //     workspan.RecordError("Recaptcha - invalid token. User: {Email}, Token: {Token}", Email, Token);
                //     return false;
                // }

                var captchaData = await _captchaValidator.GetCaptchaResultDataAsync(token);
                var result = captchaData.ToObject<RecaptchaData>();

                if (result.ErrorCodes != null && result.ErrorCodes.Any())
                {
                    foreach (var error in result.ErrorCodes)
                    {
                        workspan
                            .Tag("Error", error)
                            .Log.Error("Recaptcha error");
                    }

                    return false;
                }

                if (result.Score < 0.5)
                {
                    workspan.Log.Error(
                        "Recaptcha - score less than 0.5. User: {Email}, Token: {Token}");
                    return false;
                }

                return true;
            }
            catch (Exception e)
            {
                workspan.RecordException(e, "Recaptcha error");
                throw new FlexChargeException("Recaptcha error", e); // to make error message constant
            }
        }
    }
}