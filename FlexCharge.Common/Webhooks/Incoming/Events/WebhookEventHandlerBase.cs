using System;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Cache.DistributedMemoryDatabase;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Validation.FluentValidation;
using FlexCharge.Common.Webhooks.IncomingWebhooks.WebhookEvents;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Common.Webhooks.Incoming.Events;

public abstract class WebhookEventHandlerBase<TEvent, TEventData, TContext> : IWebhookEventHandler<TEvent, TContext>
{
    protected abstract string ProviderName { get; }
    protected TContext Context { get; private set; }
    public abstract string EventType { get; }
    protected TEvent Event { get; private set; }
    protected abstract TEventData? EventData { get; }

    protected abstract Task HandleEventInternalAsync();
    protected abstract Func<TEvent, string>? UniqueEventIdSelector { get; }
    protected abstract Func<TEvent, Guid?>? OrderIdSelector { get; }
    protected virtual AbstractValidator<TEventData>? GetEventDataValidator() => null;


    private IExternalRequestsDistributedMemoryDatabase _externalRequestsDistributedMemoryDatabase;
    private IServiceProvider _serviceProvider;


    public virtual void Initialize(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _externalRequestsDistributedMemoryDatabase = GetRequiredService<IExternalRequestsDistributedMemoryDatabase>();
    }


    public virtual async Task HandleAsync(string webhookDispatcherName, TContext context, TEvent eventToHandle)
    {
        var uniqueEventIdSelector = UniqueEventIdSelector;
        var orderIdSelector = OrderIdSelector;

        using var workspan = Workspan.Start<WebhookEventHandlerBase<TEvent, TEventData, TContext>>()
            .Baggage("EventId", uniqueEventIdSelector != null ? uniqueEventIdSelector(eventToHandle) : null)
            .Baggage("OrderId", orderIdSelector != null ? orderIdSelector(eventToHandle) : null)
            .Baggage("EventType", EventType)
            .Tag("Event", eventToHandle);

        try
        {
            workspan.Log.Information("Handling webhook event");

            Context = context;
            Event = eventToHandle;

            #region Idempotency - ensure we don't process the same event twice

            // Attention: to support retries this key should be removed from cache to enable retry

            var stripeEventIdempotencyKey =
                CacheKeyFactory.CreateExternalProviderWebhookEventIdempotencyKey(webhookDispatcherName, ProviderName,
                    UniqueEventIdSelector(eventToHandle));

            string nowTicks = DateTime.UtcNow.Ticks.ToString();

            var ticksInDistributedCache =
                await _externalRequestsDistributedMemoryDatabase.GetOrSetAsync(stripeEventIdempotencyKey.Key,
                    nowTicks, stripeEventIdempotencyKey.CacheOptions.AbsoluteExpirationRelativeToNow);

            if (ticksInDistributedCache != nowTicks)
            {
                workspan
                    .Log.Information("Webhook event was already processed");

                return;
            }

            #endregion

            ValidateAndThrow();

            // Process event
            await HandleEventInternalAsync();
        }
        catch (Exception ex)
        {
            workspan.RecordFatalException(ex);
            throw;
        }
    }

    protected virtual void ValidateAndThrow()
    {
        var eventData = EventData;

        // Validation of event data
        var validator = GetEventDataValidator();
        if (validator != null)
        {
            var validationResult = validator.Validate(eventData);
            validationResult.ThrowOnErrors(logErrors: true);
        }
    }


    protected TService GetRequiredService<TService>()
        where TService : notnull
    {
        return _serviceProvider.GetRequiredService<TService>();
    }
}