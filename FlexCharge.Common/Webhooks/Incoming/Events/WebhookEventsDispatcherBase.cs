using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils;
using Polly;

namespace FlexCharge.Common.Webhooks.IncomingWebhooks.WebhookEvents;

public abstract class WebhookEventsDispatcherBase<TEvent, TContext> : WebhookDispatcherBase<TContext>
    where TContext : class
{
    protected abstract Func<TEvent, string> EventTypeSelector { get; }
    protected abstract Task<TEvent> TransformToEventAsync();

    protected abstract TContext CreateContext(TEvent eventToProcess);

    private static object _eventHandlersLock = new object();
    private static Dictionary<string, Type> _eventHandlers;
    private readonly IServiceProvider _serviceProvider;

    public WebhookEventsDispatcherBase(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    protected override TContext? CreateContext() => null;

    private void RegisterEventHandlers()
    {
        using var workspan = Workspan.Start<WebhookEventsDispatcherBase<TEvent, TContext>>();

        if (_eventHandlers != null) return; //!!!

        lock (_eventHandlersLock)
        {
            if (_eventHandlers != null) return; //!!!

            var eventHandlers = new Dictionary<string, Type>();

            try
            {
                #region Using Reflection to find all classes that implement IStripeEventHandler

                ReflectionHelpers
                    .ScanForType<IWebhookEventHandler<TEvent, TContext>>(this.GetType().Assembly)
                    .ForEach(type =>
                    {
                        try
                        {
                            if (type.IsAbstract || type.IsInterface)
                                return;

                            var instance = (IWebhookEventHandler<TEvent, TContext>) Activator.CreateInstance(type);
                            if (!eventHandlers.TryAdd(instance.EventType.ToLowerInvariant(), type))
                            {
                                workspan.Log.Fatal("Webhook event handler already exists for event type {EventType}",
                                    instance.EventType);
                            }
                        }
                        catch (Exception e)
                        {
                            workspan.RecordFatalException(e);
                        }
                    });

                #endregion
            }
            catch (Exception ex)
            {
                workspan.RecordFatalException(ex);
            }
            finally
            {
                _eventHandlers = eventHandlers;
            }
        }
    }

    protected override async Task DispatchInternalAsync(string webhookDispatcherName, TContext context)
    {
        var eventToProcess = await TransformToEventAsync();

        // context is null here, so we need to create it
        context = CreateContext(eventToProcess);

        await ProcessEventAsync(webhookDispatcherName, eventToProcess, context);
    }


    private async Task ProcessEventAsync(string webhookDispatcherName, TEvent eventToProcess, TContext context)
    {
        if (GetEventHandlerOrDefault(eventToProcess, out var eventHandler))
        {
            eventHandler.Initialize(_serviceProvider);
            await eventHandler.HandleAsync(webhookDispatcherName, context, eventToProcess);
        }
        else
        {
            Workspan.Current!.Log.Information("No webhook event handler found for event type {EventType}",
                EventTypeSelector(eventToProcess));
        }
    }

    protected bool GetEventHandlerOrDefault(TEvent stripeEvent,
        out IWebhookEventHandler<TEvent, TContext> webhookEventHandler)
    {
        webhookEventHandler = null!;

        if (_eventHandlers == null) RegisterEventHandlers();

        _eventHandlers!.TryGetValue(EventTypeSelector(stripeEvent), out var webhookEventHandlerType);

        if (webhookEventHandlerType == null)
            return false;

        webhookEventHandler =
            (IWebhookEventHandler<TEvent, TContext>) Activator.CreateInstance(webhookEventHandlerType);

        return true;
    }

    protected Guid GetGuidFromQuery(string parameterName)
    {
        if (!QueryString.TryGetValue(parameterName, out var parameterQueryArguments))
            throw new FlexChargeException($"Missing {parameterName} in webhook query string");

        if (parameterQueryArguments.Count != 1)
            throw new FlexChargeException($"More than one {parameterName} in webhook query string");

        var stringValue = parameterQueryArguments.Single();

        if (string.IsNullOrWhiteSpace(stringValue))
            throw new FlexChargeException($"Empty {parameterName} in query string");

        if (!Guid.TryParse(stringValue, out var parameterValue))
            throw new FlexChargeException($"Invalid {parameterName} in webhook query string");

        return parameterValue;
    }

    protected string GetStringFromHeaders(string parameterName)
    {
        if (!this.RequestHeaders.TryGetValue(parameterName, out var parameterValues))
            throw new Exception($"{parameterName} not found in webhook request headers");

        var parameterValue = parameterValues.Single();

        return parameterValue;
    }
}