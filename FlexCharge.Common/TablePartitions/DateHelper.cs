using System;
using System.Collections.Generic;

namespace FlexCharge.Common.Partitions;

public static class DateHelper
{
    public static List<MonthYear> GetAllMonthsBetweenDates(DateOnly startDate, DateOnly endDate)
    {
        List<MonthYear> result = new();
        var strt = startDate;
        var end = endDate;

        while (strt.Month <= end.Month || strt.Year < end.Year) //todo-improve
        {
            result.Add(new MonthYear(strt.Month, strt.Year));
            strt = strt.AddMonths(1);
        }

        return result;
    }


    public static List<DateOnly> GetDatesBetweenDates(DateOnly startDate, DateOnly endDate)
    {
        List<DateOnly> allDates = new List<DateOnly>();

        for (DateOnly date = startDate; date <= endDate; date = date.AddDays(1))
        {
            allDates.Add(date);
        }

        return allDates;
    }


    public static MonthYear GtMonthYear(DateOnly startDate)
    {
        return new MonthYear(startDate.Month, startDate.Year);
    }
}

public record MonthYear(int Month, int Year);