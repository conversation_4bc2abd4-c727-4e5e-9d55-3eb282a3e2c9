using System;

namespace FlexCharge.Common.Partitions;

public class PartitionsDateRanges
{
    //min records date in default  and partitions tables(without archive)
    public DateOnly? MinOverallDate { get; set; }

    //max records date in default  and partitions tables(without archive)
    public DateOnly? MaxOverallDate { get; set; }

    //min record date in default table
    public DateOnly? MinDefaultTableDate { get; set; }

    //max record date in default table
    public DateOnly? MaxDefaultTableDate { get; set; }

    //min record date in archive table
    public DateOnly? MinArchiveTabletDate { get; set; }

    //max record date in archive table
    public DateOnly? MaxArchiveTableDate { get; set; }

    public void UpdateTableRange(DateOnly? date)
    {
        if (MinDefaultTableDate == null || date < MinDefaultTableDate)
        {
            MinDefaultTableDate = date;
        }

        if (MaxDefaultTableDate == null || date > MaxDefaultTableDate)
        {
            MaxDefaultTableDate = date;
        }

        if (MinOverallDate == null || date < MinOverallDate)
        {
            MinOverallDate = date;
        }

        if (MaxOverallDate == null || date < MaxOverallDate)
        {
            MaxOverallDate = date;
        }
    }
}