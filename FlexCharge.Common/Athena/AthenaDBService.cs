using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Amazon;
using Amazon.Athena;
using Amazon.Athena.Model;
using FlexCharge.Common.Athena;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.Services;
using FlexCharge.Utils;
using Microsoft.Extensions.Options;

public class AthenaDBService : IAthenaDBService
{
    public async Task ExecuteQueryAsync(
        string query,
        string athenaDatabaseName,
        string athenaOutputBucket,
        Func<Row, List<ColumnInfo>, bool, Task> processRowCallback,
        int executionTimeout,
        int retryTimeInterval,
        RegionEndpoint awsRegionEndpoint)
    {
        using var workspan = Workspan.Start<AthenaDBService>()
            .Baggage("Query", query)
            .Baggage("Database", athenaDatabaseName)
            .Baggage("OutputBucket", athenaOutputBucket)
            .Baggage("ExecutionTimeout", executionTimeout)
            .Baggage("RetryTimeInterval", retryTimeInterval)
            .LogEnterAndExit();

        try
        {
            var athenaClient = CreateAmazonAthenaClient(executionTimeout, awsRegionEndpoint);

            var queryExecutionId =
                await SubmitAthenaQuery(query, athenaClient, athenaDatabaseName, athenaOutputBucket);

            await WaitForQueryToComplete(athenaClient, queryExecutionId, retryTimeInterval);

            await ProcessResultRows(athenaClient, queryExecutionId, processRowCallback);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "AthenaDB service error");
            throw;
        }
    }

    public async Task ExecuteQueryAsync(
        string query,
        IOptions<AthenaDBOptions> athenaOptions,
        Func<Row, List<ColumnInfo>, bool, Task> processRowCallback = null)
    {
        using var workspan = Workspan.Start<AthenaDBService>();

        var options = athenaOptions?.Value;

        ArgumentNullException.ThrowIfNull(options);
        ArgumentNullException.ThrowIfNull(options.DatabaseName, "Missing Athena database name in options");
        ArgumentNullException.ThrowIfNull(options.OutputBucket, "Missing Athena output bucket in options");

        if (options.ExecutionTimeout <= 0)
            throw new ArgumentException("Value must be greater than 0", nameof(options.ExecutionTimeout));

        if (options.RetryTimeInterval <= 0)
            throw new ArgumentException("Value must be greater than 0", nameof(options.RetryTimeInterval));

        await ExecuteQueryAsync(query,
            options.DatabaseName, options.OutputBucket,
            processRowCallback,
            options.ExecutionTimeout, options.RetryTimeInterval,
            awsRegionEndpoint: null);
    }


    private AmazonAthenaClient CreateAmazonAthenaClient(int executionTimeout, RegionEndpoint awsRegionEndpoint = null)
    {
        // Create an Amazon Athena client
        var athenaConfig = new AmazonAthenaConfig
        {
            RegionEndpoint =
                awsRegionEndpoint, // can be null to let AWS SDK choose the region automatically based on the environment
            Timeout = TimeSpan.FromMilliseconds(executionTimeout),
        };

        var athenaClient = new AmazonAthenaClient(athenaConfig);

        return athenaClient;
    }

    private async Task<string> SubmitAthenaQuery(string athenaQuery,
        AmazonAthenaClient athenaClient,
        string athenaDatabaseName, string athenaOutputBucket)
    {
        // The QueryExecutionContext allows us to set the Database.
        var queryExecutionContext = new QueryExecutionContext()
        {
            Database = athenaDatabaseName
        };

        // The result configuration specifies where the results of the query should go in S3 and encryption options
        var resultConfiguration = new ResultConfiguration()
        {
            // You can provide encryption options for the output that is written.
            // EncryptionConfiguration = encryptionConfiguration
            OutputLocation = athenaOutputBucket
        };

        // Create the StartQueryExecutionRequest to send to Athena which will start the query.
        var startQueryExecutionRequest = new StartQueryExecutionRequest()
        {
            QueryString = athenaQuery,
            QueryExecutionContext = queryExecutionContext,
            ResultConfiguration = resultConfiguration
        };

        var startQueryExecutionResponse = await athenaClient.StartQueryExecutionAsync(startQueryExecutionRequest);
        return startQueryExecutionResponse.QueryExecutionId;
    }

    /*
     * Wait for an Athena query to complete, fail or to be cancelled. This is done by polling Athena over an
     * interval of time. If a query fails or is cancelled, then it will throw an exception.
     */
    private async Task WaitForQueryToComplete(AmazonAthenaClient athenaClient,
        String queryExecutionId, int retryTimeInterval)
    {
        var getQueryExecutionRequest = new GetQueryExecutionRequest()
        {
            QueryExecutionId = queryExecutionId
        };

        GetQueryExecutionResponse getQueryExecutionResponse = null;
        bool isQueryStillRunning = true;

        while (isQueryStillRunning)
        {
            getQueryExecutionResponse = await athenaClient.GetQueryExecutionAsync(getQueryExecutionRequest);
            var queryState = getQueryExecutionResponse.QueryExecution.Status.State;
            if (queryState == QueryExecutionState.FAILED)
            {
                throw new Exception("Query Failed to run with Error Message: " +
                                    getQueryExecutionResponse.QueryExecution.Status.StateChangeReason);
            }
            else if (queryState == QueryExecutionState.CANCELLED)
            {
                throw new Exception("Query was cancelled.");
            }
            else if (queryState == QueryExecutionState.SUCCEEDED)
            {
                isQueryStillRunning = false;
            }
            else
            {
                // Sleep an amount of time before retrying again.
                Thread.Sleep(TimeSpan.FromMilliseconds(retryTimeInterval));
            }

            //Console.WriteLine("Current Status is: " + queryState);
        }
    }


    /**
     * This code calls Athena and retrieves the results of a query.
     * The query must be in a completed state before the results can be retrieved and
     * paginated. The first row of results are the column headers.
     */
    private async Task ProcessResultRows(AmazonAthenaClient athenaClient, String queryExecutionId,
        Func<Row, List<ColumnInfo>, bool, Task> processRowCallback)
    {
        using var workspan = Workspan.Start<AthenaDBService>();

        GetQueryResultsRequest getQueryResultsRequest = new GetQueryResultsRequest()
        {
            // Max Results can be set but if its not set, it will choose the maximum page size
            // As of the writing of this code, the maximum value is 1000
            // MaxResults = 1000

            QueryExecutionId = queryExecutionId
        };

        var getQueryResultsResponse = await athenaClient.GetQueryResultsAsync(getQueryResultsRequest);


        bool isColumnRow = true;
        int processedRows = 0;
        int batchNumber = 0;
        while (true)
        {
            var results = getQueryResultsResponse.ResultSet.Rows;
            for (var rowIndex = 0; rowIndex < results.Count; rowIndex++)
            {
                Row row = results[rowIndex];
                if (isColumnRow) //The first row of the first page holds the column names
                {
                    isColumnRow = false;
                }
                else
                {
                    bool endOfBatch = rowIndex == results.Count - 1;

                    // Process the row 
                    await processRowCallback(row, getQueryResultsResponse.ResultSet.ResultSetMetadata.ColumnInfo,
                        endOfBatch);

                    processedRows++;

                    if (endOfBatch)
                    {
                        workspan.Log.Information("Athena export: processed rows {processedRows}", processedRows);
                    }
                }
            }

            if (processedRows == 0)
                workspan.Log.Information("Athena export: no rows to process");

            // If nextToken is null, there are no more pages to read. Break out of the loop
            if (string.IsNullOrEmpty(getQueryResultsResponse.NextToken)) break;

            getQueryResultsRequest.NextToken = getQueryResultsResponse.NextToken;

            const int RETRY_COUNT = 10;
            TimeSpan retryInterval = TimeSpan.FromMilliseconds(3000);
            int retriesLeft = RETRY_COUNT;

            bool retry = false;
            do
            {
                try
                {
                    getQueryResultsResponse = await athenaClient.GetQueryResultsAsync(getQueryResultsRequest);
                }
                catch (Exception e)
                {
                    workspan.Log.Error(e, "Athena GetQueryResultsAsync request error");
                    workspan.Log.Information("Athena retry: {retryCount}", RETRY_COUNT - retriesLeft + 1);

                    await Task.Delay(retryInterval);

                    retry = true;
                }
            } while (retry && retriesLeft-- > 0);


            batchNumber++;
        }
    }


    private static decimal ParsePercentage(string rowValue)
    {
        return rowValue is null ? 0 : decimal.Parse(rowValue);
    }

    private static int ParseCount(string rowValue)
    {
        return rowValue is null
            ? 0
            : int.Parse(rowValue);
    }

    private static long ParseAmountToLong(string rowValue)
    {
        return rowValue is null
            ? 0
            : long.Parse(rowValue);
    }


    private static decimal ParseAmountToDecimal(string rowValue)
    {
        return Formatters.LongToDecimal(ParseAmountToLong(rowValue));
    }

    #region Commented

// private static void ProcessRow(Row row, List<ColumnInfo> columnInfoList)
// {
//     for (int i = 0; i < columnInfoList.Count; ++i)
//     {
//         switch (columnInfoList[i].Type)
//         {
//             case "varchar":
//                 // Convert and Process as String
//                 break;
//             case "tinyint":
//                 // Convert and Process as tinyint
//                 break;
//             case "smallint":
//                 // Convert and Process as smallint
//                 break;
//             case "integer":
//                 // Convert and Process as integer
//                 break;
//             case "bigint":
//                 // Convert and Process as bigint
//                 break;
//             case "double":
//                 // Convert and Process as double
//                 break;
//             case "boolean":
//                 // Convert and Process as boolean
//                 break;
//             case "date":
//                 // Convert and Process as date
//                 break;
//             case "timestamp":
//                 // Convert and Process as timestamp
//                 break;
//             default:
//                 throw new Exception("Unexpected Type is not expected" + columnInfoList[i].Type);
//         }
//     }
}

#endregion