using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Amazon;
using Amazon.Athena.Model;
using FlexCharge.Common.Athena;
using Microsoft.Extensions.Options;


namespace FlexCharge.Orders.Services;

public interface IAthenaDBService
{
    public Task ExecuteQueryAsync(
        string query, string athenaDatabaseName,
        string athenaOutputBucket,
        Func<Row, List<ColumnInfo>, bool, Task> processRowCallback,
        int executionTimeout = 100000,
        int retryTimeInterval = 1000,
        RegionEndpoint awsRegionEndpoint = null);


    public Task ExecuteQueryAsync(
        string query,
        IOptions<AthenaDBOptions> athenaOptions,
        Func<Row, List<ColumnInfo>, bool, Task> processRowCallback = null);
}