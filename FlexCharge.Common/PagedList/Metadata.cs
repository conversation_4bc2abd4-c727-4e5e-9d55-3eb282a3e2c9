namespace FlexCharge.Common
{
    public class Metadata
    {
        public Metadata(IPagedList pagedList)
        {
            this.PageCount = pagedList.PageCount;

            this.TotalItemCount = pagedList.TotalItemCount;

            this.PageNumber = pagedList.PageNumber;

            this.PageSize = pagedList.PageSize;

            this.HasPreviousPage = pagedList.HasPreviousPage;

            this.HasNextPage = pagedList.HasNextPage;

            this.IsFirstPage = pagedList.IsFirstPage;

            this.IsLastPage = pagedList.IsLastPage;
        }

        int PageCount { get; set; }


        int TotalItemCount { get; set; }


        int PageNumber { get; set; }


        int PageSize { get; set; }


        bool HasPreviousPage { get; set; }


        bool HasNextPage { get; set; }


        bool IsFirstPage { get; set; }


        bool IsLastPage { get; set; }
    }


}
