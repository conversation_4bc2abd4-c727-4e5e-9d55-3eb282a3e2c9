using FlexCharge.Common.Cache;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Common.Shared.Merchants.Sites;

/// <summary>
/// Use this extensions to make shared merchant sites data available in a project
/// </summary>
public static class Extensions
{
    public static void AddSharedMerchantSitesService(this IServiceCollection services)
    {
        services.AddRedisCache();
        services.AddTransient<IMerchantSitesService, MerchantSitesService>();
    }
}