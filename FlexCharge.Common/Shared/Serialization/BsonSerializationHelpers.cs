using System;
using System.IO;
using FlexCharge.Common.Telemetry;
using Newtonsoft.Json;
using Newtonsoft.Json.Bson;

namespace FlexCharge.Common.Shared.Serialization;

public static class BsonSerializationHelpers
{
    public static byte[] Serialize<T>(T obj, byte version, DateTimeKind dateTimeKind = DateTimeKind.Utc)
    {
        using (var ms = new MemoryStream())
        {
            byte[] ver = new byte[] {(byte) version};
            ms.Write(ver);

            using (var writer = new BsonDataWriter(ms))
            {
                writer.DateTimeKindHandling = dateTimeKind;
                
                var serializer = new JsonSerializer();
                serializer.Serialize(writer, obj);
            }

            return ms.ToArray();
        }
    }

    static T Deserialize<T>(byte[] bson, int versionBytesCount, DateTimeKind dateTimeKind = DateTimeKind.Utc)
    {
        // Deserialize from BSON
        MemoryStream ms = new MemoryStream(bson);
        using (BsonDataReader reader = new BsonDataReader(ms))
        {
            reader.DateTimeKindHandling = dateTimeKind;
            
            JsonSerializer serializer = new JsonSerializer();

            if (versionBytesCount > 0) ms.Position = versionBytesCount;

            return serializer.Deserialize<T>(reader);
        }
    }

    public static T? Deserialize<T>(byte[] bsonWithVersion, bool throwOnError = true, DateTimeKind dateTimeKind = DateTimeKind.Utc)
    {
        using var workspan = Workspan.Current;

        byte version = bsonWithVersion[0];

        switch (version)
        {
            case 1:
                return Deserialize<T>(bsonWithVersion, 1, dateTimeKind);
            default:
                workspan?.RecordError("BSON Deserialization: unsupported version : {Version}", version);

                if (throwOnError) throw new Exception($"BSON Deserialization: unsupported version : {version}");
                else return default;
        }
    }
}
