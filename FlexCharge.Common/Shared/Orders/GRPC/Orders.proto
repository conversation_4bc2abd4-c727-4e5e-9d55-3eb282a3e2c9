syntax = "proto3";

import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";

package orders;

option csharp_namespace = "FlexCharge.Grpc.Orders";


// Common messages 

message SerializedRequest {
  string request = 1;
}

message SerializedResponse {
  string response = 1;
}


// Services

service GrpcOrdersService {
  rpc CreateOrder(SerializedRequest) returns (SerializedResponse);
}

