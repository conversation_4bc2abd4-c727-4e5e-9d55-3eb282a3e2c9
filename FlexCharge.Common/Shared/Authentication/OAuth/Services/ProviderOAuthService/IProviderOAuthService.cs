using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Contracts.Common;

namespace FlexCharge.Common.Shared.Authentication.OAuth.Services.ProviderOAuthService;

public interface IProviderOAuthService
{
    Task<bool> ProcessAuthorizationRequestAsync(OAuthProvider provider,
        Dictionary<string, List<string>> queryString,
        Dictionary<string, List<string>> requestHeaders, string requestBody);

    Task<string> GenerateOAuthLink(OAuthProvider provider, RelatedEntityType relatedEntityType, Guid relatedEntityId);

    Task StoreAccessTokenAsync(OAuthProvider provider, AccessToken accessToken);

    Task<AccessToken> GetLatestAccessTokenAsync(OAuthProvider provider,
        ExternalEntityType externalEntityType, string externalEntityId,
        RelatedEntityType relatedEntityType);

    Task<string> GetProviderConnectionStatusAsync(OAuthProvider provider, Guid mid);

    Task DeAuthorizeApplication(OAuthProvider provider,
        ExternalEntityType externalEntityType, string externalEntityId,
        RelatedEntityType relatedEntityType, Guid relatedEntityId,
        string messageApplicationId, string messageApplicationName);
}