using System;
using System.Threading;
using System.Threading.Tasks;

namespace FlexCharge.Common.Shared.UrlShortener;

public interface IUrlShortenerService
{
    Task<ShortUrl> ShortenUrlAsync(string path, DateTime? expirationTime, CancellationToken token = default);
    Task<string> GetDestinationUrlAsync(string path, CancellationToken token = default);
    Task<string?> GetDestinationUrlOrDefaultAsync(string path, CancellationToken token = default);


    Task CreateAsync(ShortUrl shortUrl, DateTime? expirationTime, CancellationToken token = default);
    Task UpdateAsync(ShortUrl shortUrl, DateTime? expirationTime, CancellationToken token = default);
    Task DeleteAsync(string path, bool ignoreIfNotExists = false, CancellationToken token = default);
    Task<bool> ExistsAsync(string path, CancellationToken token = default);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="shortUrl"></param>
    /// <param name="expirationTime"></param>
    /// <param name="token"></param>
    /// <returns>False, if a short url with the same path already exists</returns>
    Task<bool> TryCreateAsync(ShortUrl shortUrl, DateTime? expirationTime, CancellationToken token = default);

    string GetShortUrlLink(ShortUrl path);
}