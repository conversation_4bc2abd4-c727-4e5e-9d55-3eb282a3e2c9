using System;
using FluentValidation;

namespace FlexCharge.Common.Shared.UrlShortener;

public sealed record ShortUrl(string Path, string Destination);

public class ShortUrlValidator : AbstractValidator<ShortUrl>
{
    public ShortUrlValidator()
    {
        RuleFor(shortUrl => shortUrl.Destination)
            .NotNull()
            .NotEmpty()
            .Must(BeAValidUrl).WithMessage("{PropertyName} has to be a valid absolute URL.");

        RuleFor(shortUrl => shortUrl.Path)
            .NotNull()
            .NotEmpty()
            .MaximumLength(10).WithMessage("{PropertyName} cannot be longer than 10 characters.")
            .Matches("^[a-zA-Z0-9_-]*$")
            .WithMessage("{PropertyName} can only contain alphanumeric characters, underscores, and dashes.");
    }

    private static bool BeAValidUrl(string? destination)
    {
        return Uri.IsWellFormedUriString(destination, UriKind.Absolute);
    }
}