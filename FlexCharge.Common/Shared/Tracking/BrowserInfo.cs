using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;
using FlexCharge.Common.Mvc.Validators;

namespace FlexCharge.Common.Shared.Tracking;

public class BrowserInfo
{
    public int ScreenWidth { get; set; }
    public int ScreenHeight { get; set; }
    public int ColorDepth { get; set; }

    [NoUnicodeNullCharacters] public string UserAgent { get; set; }

    [NoUnicodeNullCharacters] public string Language { get; set; }

    public int TimeZone { get; set; }
    public bool JavaEnabled { get; set; }
    public bool JavascriptEnabled { get; set; }

    [NoUnicodeNullCharacters] public string Locale { get; set; }

    [NoUnicodeNullCharacters] public string Timezone { get; set; }

    [NoUnicodeNullCharacters] public string AppVersion { get; set; }

    [NoUnicodeNullCharacters] public string NetworkType { get; set; }

    [NoUnicodeNullCharacters] public string IpAddress { get; set; }

    [NoUnicodeNullCharacters] public string AcceptHeaders { get; set; }

    [JsonExtensionData] public Dictionary<string, JsonElement> AdditionalProperties { get; set; }
}