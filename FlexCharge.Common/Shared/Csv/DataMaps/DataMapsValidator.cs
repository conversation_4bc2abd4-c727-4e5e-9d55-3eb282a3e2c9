using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils;

namespace FlexCharge.Common.Shared.Csv.DataMaps;

public sealed class DataMapsValidator
{
    /// <summary>
    /// 
    /// </summary>
    /// <remarks>Use only in Startup code!!!</remarks>
    public static void ValidateDataMapsAndThrow<TAssembly>()
    {
        ValidateDataMapsAndThrowAsync<TAssembly>().Wait();
    }

    public static async Task ValidateDataMapsAndThrowAsync<TAssembly>()
    {
        using var workspan = Workspan.Start<DataMapsValidator>();

        // Locate all data maps using reflection
        var allDataMaps = ReflectionHelpers.ScanForType<TAssembly, IValidatableDataMap>()
            .Where(x => x.IsClass && !x.IsAbstract);

        foreach (var dataMapType in allDataMaps)
        {
            var dataMap = (IValidatableDataMap) Activator.CreateInstance(dataMapType);

            if (dataMap == null)
                throw new InvalidOperationException($"Failed to create instance of data map {dataMapType}");

            try
            {
                await dataMap.ValidateAndThrowAsync();
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e, "Failed to validate data map {DataMapType}", dataMapType);
                throw;
            }
        }
    }
}