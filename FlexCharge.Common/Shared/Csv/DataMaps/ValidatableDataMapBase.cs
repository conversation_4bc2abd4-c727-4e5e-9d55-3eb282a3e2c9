using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper.Configuration;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Utils;
using Newtonsoft.Json;

namespace FlexCharge.Common.Shared.Csv.DataMaps;

public abstract class ValidatableDataMapBase<TMapRecord, TMapFromType, TMapToType> : IValidatableDataMap
    where TMapRecord : class, new()
{
    protected virtual bool LogErrorOnDuplicateKey { get; } = true;
    protected virtual bool ThrowExceptionOnDuplicateKey { get; } = false;

    public IReadOnlyDictionary<TMapFromType, TMapToType> Map => _map;

    protected abstract string SourceFilePath { get; }

    private bool _loaded = false;

    private volatile object _dataLock = new();
    private volatile Dictionary<TMapFromType, TMapToType> _map;

    protected abstract void ImportRecord(Dictionary<TMapFromType, TMapToType> map, TMapRecord record);

    /// <summary>
    /// To validate fully loaded map - override this method and throw exceptions if the map is invalid
    /// </summary>
    /// <param name="map"></param>
    protected virtual void ValidateLoadedMap(Dictionary<TMapFromType, TMapToType> map)
    {
    }


    public ValidatableDataMapBase()
    {
        _map = new();
    }

    public virtual async Task LoadAsync(bool skipLoading = false, Action<TMapRecord>? preprocessRecord = null)
    {
        if (_loaded)
            return;

        if (skipLoading)
            return;

        await LoadMapInternalAsync(preprocessRecord);
    }

    public async Task ValidateAndThrowAsync()
    {
        var map = ValidateAndLoadMapInternal();
    }

    private async Task LoadMapInternalAsync(Action<TMapRecord>? preprocessRecord)
    {
        using var workspan = Workspan.Start<ValidatableDataMapBase<TMapRecord, TMapFromType, TMapToType>>();

        workspan
            .Baggage("SourceFilePath", SourceFilePath);

        lock (_dataLock)
        {
            if (_loaded)
                return;

            try
            {
                _map = ValidateAndLoadMapInternal(preprocessRecord);
            }
            catch (Exception e)
            {
                workspan.Log.Fatal(e, "Failed to load data map {DataMapType}", this.GetType().Name);
                _map = new Dictionary<TMapFromType, TMapToType>();
            }

            _loaded = true;
        }
    }

    private Dictionary<TMapFromType, TMapToType> ValidateAndLoadMapInternal(Action<TMapRecord>? preprocessRecord = null)
    {
        using var workspan = Workspan.Start<ValidatableDataMapBase<TMapRecord, TMapFromType, TMapToType>>()
            .Baggage("DataMapType", this.GetType().Name);

        workspan.Log.Information("Loading data map {DataMapType}", this.GetType().Name);

        var map = new Dictionary<TMapFromType, TMapToType>();
        IEnumerable<TMapRecord> records;

        using (var stream = File.OpenRead(SourceFilePath))
        {
            records = CSVHelper.Parse<TMapRecord>(stream,
                new CsvConfiguration(CultureInfo.InvariantCulture)
                {
                    //PrepareHeaderForMatch = args => args.Header.ToLower(),
                    HasHeaderRecord = true,
                });
        }

        foreach (var record in records)
        {
            if (preprocessRecord != null) preprocessRecord(record);

            ImportRecord(map, record);
        }

        ValidateLoadedMap(map);

        return map;
    }

    protected bool TryAddMapItem(Dictionary<TMapFromType, TMapToType> map, TMapFromType key, TMapToType value)
    {
        if (!map.TryAdd(key, value))
        {
            if (ThrowExceptionOnDuplicateKey)
            {
                throw new FlexChargeException(
                    $"Duplicate key {JsonConvert.SerializeObject(key)} in data map {this.GetType().Name}");
            }
            else if (LogErrorOnDuplicateKey)
            {
                Workspan.Current?.Log.Fatal("Duplicate key {Key} in data map {DataMapType}", key, this.GetType().Name);
            }


            return false;
        }

        return true;
    }

    protected bool TryAddMapItemIfNotExist(Dictionary<TMapFromType, TMapToType> map, TMapFromType key, TMapToType value)
    {
        if (map.ContainsKey(key))
            return false;

        return TryAddMapItem(map, key, value);
    }

    public List<KeyValuePair<TMapFromType, TMapToType>> ToList()
    {
        return _map.ToList();
    }

    protected void ValidateByDataAnnotationAttributesAndThrow(TMapRecord record)
    {
        var context = new ValidationContext(record);
        var results = new List<ValidationResult>();

        bool isValid = Validator.TryValidateObject(record, context, results, validateAllProperties: true);

        if (!isValid)
        {
            var validationErrors = string.Join("; ", results.Select(r => r.ErrorMessage));

            throw new FlexChargeException(validationErrors);
        }
    }
}