using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FlexCharge.Common.Shared.Csv.DataMaps;

public abstract class ValidatableDataMapListBase<TMapRecord, TMapFromType, TMapToType> :
    ValidatableDataMapBase<TMapRecord, TMapFromType, TMapToType>
    where TMapRecord : class, new()
{
    public IReadOnlyList<KeyValuePair<TMapFromType, TMapToType>> List => _list;

    private volatile List<KeyValuePair<TMapFromType, TMapToType>> _list;

    public override async Task LoadAsync(bool skipLoading = false, Action<TMapRecord>? preprocessRecord = null)
    {
        await base.LoadAsync(skipLoading, preprocessRecord);

        _list = ToList();
    }

    public List<TMapFromType> GetKeys()
    {
        return Map.Keys.ToList();
    }

    public List<TMapToType> GetValues()
    {
        return Map.Values.ToList();
    }
}