using System;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc.Validators;

namespace FlexCharge.Eligibility.DTO
{
    public class EvaluateRequest : TransmitAndEvaluateRequestBase
    {
        public Guid _CorrelationId { get; }

        public EvaluateRequest()
        {
            _CorrelationId = Guid.NewGuid();
        }

        [Required] public Guid? IdempotencyKey { get; set; }

        [NoUnicodeNullCharacters]
        public string?
            SenseKey
        {
            get;
            set;
        } //Unknown in case of Processor integration type - we'll try to find it by fuzzy matching

        public override Merchant? Merchant { get; set; }

        [RequiredIf(nameof(IsDeclined), true)] public PaymentMethod? PaymentMethod { get; set; }

        //[UtcDateTime]
        public DateTime? ExpiryDateUtc { get; set; }

        /// <summary>
        /// Only for MIT. If passed we'll try to connect to initial bank's recurring order
        /// </summary>
        public ThreeDsecure ThreeDsecure { get; set; }

        public Guid?
            OrderSessionKey
        {
            get;
            set;
        } //Only for MIT. If passed, we'll try to find the order by this key and return it's current state (as Outcome endpoint)

        public string? TransactionType { get; set; }
    }
}