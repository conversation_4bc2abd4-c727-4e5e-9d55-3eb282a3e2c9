using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc.Validators;

namespace FlexCharge.Eligibility.DTO;

public class ThreeDsecure
{
    [NoUnicodeNullCharacters] public string ThreeDsVersion { get; set; }

    /// <summary>
    /// ECI - Electronic Commerce Indicator
    /// </summary>
    [Required]
    [NoUnicodeNullCharacters]
    public string EcommerceIndicator { get; set; }

    /// <summary>
    /// CAVV - Cardholder Authentication Verification Value Result
    /// </summary>
    [Required]
    [NoUnicodeNullCharacters]
    public string AuthenticationValue { get; set; }

    //dsTransId
    /// <summary>
    /// Directory Server Transaction Id
    /// </summary>
    [Required]
    [NoUnicodeNullCharacters]
    public string DirectoryServerTransactionId { get; set; }

    /// <summary>
    /// XID - Transaction Identifier
    /// </summary>
    [NoUnicodeNullCharacters]
    public string Xid { get; set; }

    /// <summary>
    /// Authentication Value Algorithm
    /// </summary>
    [NoUnicodeNullCharacters]
    public string AuthenticationValueAlgorithm { get; set; }

    /// <summary>
    /// Directory Server Response Status
    /// </summary>
    [NoUnicodeNullCharacters]
    public string DirectoryResponseStatus { get; set; }

    /// <summary>
    /// Authentication Response Status
    /// </summary>
    [NoUnicodeNullCharacters]
    public string AuthenticationResponseStatus { get; set; }

    /// <summary>
    /// Is 3DSecure enrolled
    /// </summary>
    [NoUnicodeNullCharacters]
    public string Enrolled { get; set; }
}