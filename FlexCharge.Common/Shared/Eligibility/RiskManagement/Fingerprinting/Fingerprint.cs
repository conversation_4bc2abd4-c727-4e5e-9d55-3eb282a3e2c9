using System.Linq;

namespace FlexCharge.Eligibility.RiskManagement.Fingerprinting;

public class Fingerprint : IFingerprint
{
    private const string HashSalt = "BD4F27CC-A571-44E1-BA56-B0A09EE7B338";

    public FingerprintCategory Category { get; }
    public FingerprintType Type { get; }
    public bool IsFingerprintSource { get; }
    public string ValueHash { get; set; }
    public string? Meta { get; set; }
    public string HumanReadableDescription => $"{ValueHash} ({Type}, {Category})";

    public Fingerprint(FingerprintCategory category, FingerprintType type, bool isFingerprintSource,
        string valueToFingerprint)
    {
        Category = category;
        Type = type;
        IsFingerprintSource = isFingerprintSource;
        ValueHash = Utils.CryptoHashingHelper.ComputeContentSaltedHash($"{type}_{valueToFingerprint}", HashSalt,
            "SHA256");
    }

    public Fingerprint(FingerprintCategory category, FingerprintType type, string valueToFingerprint)
        : this(category, type, false, valueToFingerprint)
    {
    }

    /// <summary>
    /// Creates compound fingerprint (e.g. FirstNameHash_LastNameHash_PhoneHash)
    /// </summary>
    /// <param name="category"></param>
    /// <param name="type"></param>
    /// <param name="fingerprintParts"></param>
    public Fingerprint(FingerprintCategory category, FingerprintType type, params IFingerprint[] fingerprintParts) :
        this(category, type, Utils.CryptoHashingHelper.ConcatenateHashes(fingerprintParts.Select(x => x.ValueHash)))
    {
    }
}