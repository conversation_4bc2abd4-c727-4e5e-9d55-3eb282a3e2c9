using System;
using System.Collections.Generic;
using System.Linq;

namespace FlexCharge.Common.Shared.UIBuilder;

public static class UIBuilderExtensions
{
    private class FinishAction : IDisposable
    {
        private readonly Action _action;

        public FinishAction(Action action)
        {
            _action = action;
        }

        public void Dispose()
        {
            _action();
        }
    }

    public static IDisposable StartUIBuilder(this IUIBuilder builder)
    {
        builder.UIComponents = new List<UIComponent>();

        return new FinishAction(() => EndUIBuilder(builder));
    }

    public static void EndUIBuilder(this IUIBuilder builder)
    {
        builder.AddUniqueIdsToUIComponents(builder.UIComponents);
    }

    public static void AddUniqueIdsToUIComponents(this IUIBuilder builder, ICollection<UIComponent> uiComponents)
    {
        int nextId = 0;
        AddUniqueIdsToUIComponents(builder, uiComponents, ref nextId);
    }

    public static void AddUniqueIdsToUIComponents(this IUIBuilder builder, ICollection<UIComponent> uiComponents,
        ref int nextId)
    {
        if (uiComponents == null) return;

        //Needed to properly map controls in React (Ids must be unique in each popup, but stable between UI updates on data changes)
        foreach (var uiComponent in uiComponents)
        {
            if (uiComponent.UniqueId == null) uiComponent.UniqueId = $"widget-control-uid-{nextId++}";
            AddUniqueIdsToUIComponents(builder, uiComponent.Children, ref nextId);
        }
    }


    #region UI Components Factory Methods

    #region Titles and Text

    public static void ReplaceTitle(this IUIBuilder builder, string newTitle) => builder.MainTitle?.Text(newTitle);

    public static UIComponentBuilder Title(this IUIBuilder builder)
    {
        var titleComponentBuilder = CreateComponent(builder, ComponentType.Title);
        if (builder.MainTitle == null) builder.MainTitle = titleComponentBuilder;
        return titleComponentBuilder;
    }

    public static UIComponentBuilder SubTitle(this IUIBuilder builder) =>
        CreateComponent(builder, ComponentType.SubTitle);

    public static UIComponentBuilder Text(this IUIBuilder builder) =>
        CreateComponent(builder, ComponentType.Text);

    public static UIComponentBuilder SmallText(this IUIBuilder builder) =>
        CreateComponent(builder, ComponentType.SmallText);

    public static UIComponentBuilder Span(this IUIBuilder builder) =>
        CreateComponent(builder, ComponentType.Span);

    public static UIComponentBuilder Bold(this IUIBuilder builder) =>
        CreateComponent(builder, ComponentType.Bold);

    #endregion

    #region Lists

    public static UIGroupBuilder StartList(this IUIBuilder builder) => StartComponent(builder, ComponentType.List);

    public static UIGroupBuilder StartFullRowList(this IUIBuilder builder)
    {
        var row = StartRow(builder);
        var column = StartColumn(builder, columnSpan: 12);
        return StartComponent(builder, ComponentType.List).OnEnd(component =>
        {
            column.End();
            row.End();
        });
    }

    public static UIComponentBuilder ListItem(this IUIBuilder builder) =>
        CreateComponent(builder, ComponentType.ListItem);

    #endregion

    public static UIComponentBuilder HorizontalSeparator(this IUIBuilder builder) =>
        CreateComponent(builder, ComponentType.SeparatorHorizontal, null);

    #region Inputs

    public static UIComponentBuilder Select(this IUIBuilder builder, Guid dataId, int columnSpan = 12) =>
        Input(builder, InputType.Select, dataId, columnSpan);

    public static UIComponentBuilder FullRowSelect(this IUIBuilder builder, Guid dataId, int columnSpan = 12)
    {
        using (StartRow(builder))
        {
            return Select(builder, dataId, columnSpan);
        }
    }

    public static UIComponentBuilder SmallSelect(this IUIBuilder builder, Guid dataId) =>
        SmallInput(builder, InputType.Select, dataId);

    public static UIComponentBuilder ExtraSmallSelect(this IUIBuilder builder, Guid dataId) =>
        ExtraSmallInput(builder, InputType.Select, dataId);

    public static UIComponentBuilder FullRowInput(this IUIBuilder builder, InputType inputType, Guid dataId,
        int columnSpan = 12)
    {
        using (StartRow(builder))
        {
            return Input(builder, inputType, dataId, columnSpan);
        }
    }

    public static UIComponentBuilder Input(this IUIBuilder builder, InputType inputType, Guid dataId,
        int columnSpan = 12,
        ICollection<Validation> validations = null,
        string mask = null)
    {
        UIGroupBuilder columnBuilder = columnSpan > 0 ? StartColumn(builder, columnSpan) : null;
        var uiComponentBuilder = CreateComponent(builder, ComponentType.Input, dataId, inputType, validations, mask);
        columnBuilder?.End();

        return uiComponentBuilder;
    }

    public static UIComponentBuilder MediumInput(this IUIBuilder builder, InputType inputType, Guid dataId,
        ICollection<Validation> validations = null,
        string mask = null) => Input(builder, inputType, dataId, 9, validations, mask);

    public static UIComponentBuilder SmallInput(this IUIBuilder builder, InputType inputType, Guid dataId,
        ICollection<Validation> validations = null,
        string mask = null) => Input(builder, inputType, dataId, 6, validations, mask);

    public static UIComponentBuilder ExtraSmallInput(this IUIBuilder builder, InputType inputType, Guid dataId,
        ICollection<Validation> validations = null,
        string mask = null) => Input(builder, inputType, dataId, 3, validations, mask);

    public static UIComponentBuilder Checkbox(this IUIBuilder builder, Guid dataId)
    {
        return CreateComponent(builder, ComponentType.Checkbox, dataId);
    }

    public static UIComponentBuilder RadioButton(this IUIBuilder builder, Guid groupId, Guid dataId)
    {
        return CreateComponent(builder, ComponentType.RadioButton, dataId)
            .Name(groupId.ToString());
    }

    public static UIComponentBuilder InputDate(this IUIBuilder builder, Guid dataId)
    {
        return CreateComponent(builder, ComponentType.InputDate, dataId);
    }

    #endregion

    #region Images

    public static UIComponentBuilder Image(this IUIBuilder builder, string imageSource, string alt = null)
    {
        var image = CreateComponent(builder, ComponentType.Image);
        image.Component.Attributes.Add("src", imageSource);
        if (!string.IsNullOrWhiteSpace(alt))
        {
            image.Component.Attributes.Add("alt", alt);
        }

        return image;
    }

    #endregion

    #region Buttons and Links

    private static Guid _submitButtonId = new Guid("882A198A-51D4-4B02-8D75-036FF1F6F107");
    private static Guid _okButtonId = new Guid("96CE5CEE-CB7A-41B2-BF05-FC31274DD51C");

    public static UIComponentBuilder SubmitFormButton(this IUIBuilder builder, int columnSpan = 12,
        Align align = Align.None)
    {
        return Button(builder, ButtonType.SubmitForm, _submitButtonId, columnSpan, align);
    }

    public static UIComponentBuilder OKButton(this IUIBuilder builder, int columnSpan = 12,
        Align align = Align.None)
    {
        return OptionButton(builder, _okButtonId, columnSpan, align);
    }

    public static UIComponentBuilder OptionButton(this IUIBuilder builder, Guid buttonId, int columnSpan = 12,
        Align align = Align.None)
    {
        return Button(builder, ButtonType.Button, buttonId, columnSpan, align);
    }

    public static UIComponentBuilder Button(this IUIBuilder builder, ButtonType buttonType, Guid buttonDataId,
        int columnSpan, Align align)
    {
        UIComponentBuilder button;

        using (var column = StartColumn(builder, columnSpan)
                   .Class("ui-widget-df"))
        {
            switch (align)
            {
                case Align.Left:
                    column.Class("ui-widget-pr-5");
                    break;
                case Align.Right:
                    column.Class("ui-widget-pl-5");
                    break;
            }

            button = CreateComponent(builder, ComponentType.Button, dataId: buttonDataId);

            if (columnSpan == 12)
            {
                button
                    .Class("ui-widget-ok-button");
            }
            else
            {
                button
                    .Class("ui-widget-button")
                    .Class("ui-widget-button-negative")
                    .Class("ui-widget-h-100")
                    .Class("ui-widget-fg-2");
            }

            switch (buttonType)
            {
                case ButtonType.SubmitForm:
                    button
                        .Id("ui-widget-success-button");
                    break;
                case ButtonType.Button:
                    button
                        .Id("ui-widget-challenge-button");
                    break;
                default:
                    throw new Exception($"Unsupported button type: {buttonType}");
            }
        }

        return button;
    }

    public static UIComponentBuilder Link(this IUIBuilder builder, string url,
        LinkTarget target = LinkTarget.NewWindow, Guid? linkDataId = null)
    {
        var link = CreateComponent(builder, ComponentType.Link, dataId: linkDataId)
            .Attribute("href", url);

        switch (target)
        {
            case LinkTarget.NewWindow:
                link.Attribute("target", "_blank");
                break;
            default:
                throw new Exception($"Unsupported link target: {target}");
        }

        return link;
    }

    #endregion

    #region Containers

    public static UIGroupBuilder StartContent(this IUIBuilder builder)
    {
        UIGroupBuilder noMarginBottomDiv = null;
        var contentDiv = StartDiv(builder)
            .Class("ui-widget-content")
            .Class("ui-widget-content-challenge")
            .OnEnd(x => noMarginBottomDiv.End());

        noMarginBottomDiv = StartDiv(builder)
            .Class("no-margin-bottom");

        return contentDiv;
    }

    public static UIGroupBuilder StartMainContainer(this IUIBuilder builder) => StartDiv(builder);

    public static UIGroupBuilder StartComponent(this IUIBuilder builder, ComponentType componentType)
    {
        var componentBuilder = CreateComponent(builder, componentType);
        return new UIGroupBuilder(componentBuilder.Component, builder);
    }

    public static UIGroupBuilder StartContainer(this IUIBuilder builder, ComponentType componentType)
    {
        var componentBuilder = CreateComponent(builder, componentType);
        return new UIGroupBuilder(componentBuilder.Component, builder);
    }

    public static UIGroupBuilder StartDiv(this IUIBuilder builder) => StartContainer(builder, ComponentType.Div);

    #endregion


// public static UIGroupBuilder StartFormGroup(this IUIBuilder builder) => StartComponent(ComponentType.FormGroup);
// public static UIGroupBuilder StartFormRow(this IUIBuilder builder) => StartComponent(ComponentType.FormRow);
// public static UIGroupBuilder StartFlexRow(this IUIBuilder builder) => StartComponent(ComponentType.FlexRow);

    public static UIGroupBuilder StartRow(this IUIBuilder builder) =>
        StartDiv(builder)
            .Class("ui-widget-row")
            .OnEnd(rowComponent =>
            {
                if (rowComponent.Children.Count > 0)
                {
                    rowComponent.Children.First().Classes.Add("ui-widget-first-item");
                    rowComponent.Children.Last().Classes.Add("ui-widget-last-item");
                }
            });

    /// <summary>
    /// Creates row and full-width column
    /// </summary>
    /// <returns></returns>
    public static UIGroupBuilder StartFullRow(this IUIBuilder builder)
    {
        UIGroupBuilder column = null;

        var row = StartRow(builder)
            .OnEnd(x => column.End());

        column = StartColumn(builder, columnSpan: 12);

        return row;
    }


    public static UIGroupBuilder StartColumn(this IUIBuilder builder, int columnSpan = 12) =>
        StartComponent(builder, ComponentType.Div)
            .Class($"ui-widget-col-{columnSpan}");

    public static UIComponentBuilder CreateComponent(this IUIBuilder builder,
        ComponentType componentType,
        Guid? dataId = null,
        InputType? inputType = null,
        ICollection<Validation> validations = null,
        string mask = null)
    {
        UIComponentBuilder componentBuilder;

        var component = new UIComponent()
        {
            //CureId = CureId, 
            Component = componentType.ToString(), InputType = inputType?.ToString()
        };

        componentBuilder = new UIComponentBuilder(component);
        componentBuilder.UIBuilder = builder;

        component.DataId = dataId;
        component.Order = builder.UIComponents.Count;
        component.Validations = validations;
        component.Mask = mask;

        componentBuilder.Create();


        return componentBuilder;
    }

    #endregion
}