using System.Collections.Generic;

namespace FlexCharge.Common.Shared.UIBuilder;

public enum ComponentType
{
    Title,
    SubTitle,
    Text,
    SmallText,

    SeparatorHorizontal,

    //Link,

    //Text decoration
    Span,
    Bold,
    Break,
    Center,

    //Lists
    List,
    ListItem,

    #region Commented

    // FormGroup, //Wrapper element for inputs
    // FormRow, //Wrapper element for separate inputs that must be combined on the same row,
    //     
    // FlexRow, //Wrapper element for separate inputs that must be separate on the same row,

    #endregion

    Input, //Input

    #region Commented

    // MediumInput, //75% width input
    // SmallInput, //50% width input
    // ExtraSmallInput, //25% width input

    #endregion

    Checkbox, //Checkbox input type
    RadioButton,
    InputDate, //Datepicker input

    Div,

    Image,

    Button,
    Link,
    CloseButton
}

public enum InputType
{
    Text,
    Number,
    Select,
    Cvv, // Credit card's CVV code
    CreditCard,
    CreditCardNoHolderName,
    CreditCardNumber,
    OpenBanking_Plaid,
    Spreedly_3DS,
    Tokenex_3DS
}

public enum Align
{
    None,
    Left,
    Right
}

public enum ButtonType
{
    SubmitForm, // Button with verification
    Button // Button without verification
}

public enum LinkTarget
{
    NewWindow, // Opens the linked document in a new window or tab
}

public interface IUIBuilder
{
    public List<UIComponent> UIComponents { get; set; }
    public UIGroupBuilder CurrentGroupBuilder { get; set; }
    public UIComponentBuilder MainTitle { get; set; }
}

public class UIBuilder : IUIBuilder
{
    public List<UIComponent> UIComponents { get; set; }
    public UIGroupBuilder CurrentGroupBuilder { get; set; }
    public UIComponentBuilder MainTitle { get; set; }
}