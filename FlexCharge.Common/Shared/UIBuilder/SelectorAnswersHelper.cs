using System;
using System.Collections.Generic;

namespace FlexCharge.Common.Shared.UIBuilder;

public class Answer
{
    public Guid Id { get; }
    public string Name { get; }

    public Answer(Guid id, string name)
    {
        Id = id;
        Name = name;
    }
}

public class ValueAnswer<TValue> : Answer
{
    public TValue Value { get; }

    public ValueAnswer(Guid id, string name, TValue value) : base(id, name)
    {
        Value = value;
    }
}

public class SelectorAnswersHelper<TAnswer> where TAnswer : Answer
{
    public SelectorAnswersHelper(IEnumerable<TAnswer> possibleAnswers)
    {
        _AnswerGuidToAnswerDictionary = new Dictionary<Guid, TAnswer>();
        foreach (var answer in possibleAnswers)
        {
            _AnswerGuidToAnswerDictionary[answer.Id] = answer;
        }
    }

    private Dictionary<Guid, TAnswer> _AnswerGuidToAnswerDictionary;

    public bool TryToGetAnswer(Guid answerGuid, out TAnswer answer)
    {
        return _AnswerGuidToAnswerDictionary.TryGetValue(answerGuid, out answer);
    }
}