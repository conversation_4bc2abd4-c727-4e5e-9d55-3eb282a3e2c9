using System;
using System.Collections.Generic;
using FlexCharge.Common.Shared.UIBuilder.DTO;

namespace FlexCharge.Common.Shared.UIBuilder;

// class Attribute
// {
//     public string Key { get; set; }
//     public string Value { get; set; }
// }

public class UIComponent
{
    public string UniqueId { get; set; }
    public int? Order { get; set; }

    public Guid? DataId { get; set; }
    public string CureId { get; set; }
    public string Component { get; set; }
    public string? InputType { get; set; } // Text, number, CVV, Zip, Date of birth
    public ICollection<string> Classes { get; } = new List<string>();

    public IDictionary<string, string> Attributes { get; } =
        new Dictionary<string, string>(); // tel, email: , name, id, autocomplete,

    public IDictionary<string, string> Properties { get; } =
        new Dictionary<string, string>();


    public string Mask { get; set; }
    public string DefaultValue { get; set; }
    public string Placeholder { get; set; }
    public string Text { get; set; }
    public string Description { get; set; }
    public string Tooltip { get; set; }

    public ICollection<Validation> Validations { get; set; }
    public string Language { get; set; } = "en"; //ISO 639-1 (two-letter)
    public bool RTL { get; set; } = false;

    public ICollection<UIComponent> Children { get; set; }

    public ICollection<ChallengeAnswerOption> PotentialAnswers { get; set; }


    internal UIComponent Clone()
    {
        UIComponent clone = (UIComponent) base.MemberwiseClone();

        if (PotentialAnswers != null)
        {
            clone.PotentialAnswers = new List<ChallengeAnswerOption>();
            foreach (var answer in PotentialAnswers)
            {
                clone.PotentialAnswers.Add(answer.Clone());
            }
        }

        return clone;
    }
}