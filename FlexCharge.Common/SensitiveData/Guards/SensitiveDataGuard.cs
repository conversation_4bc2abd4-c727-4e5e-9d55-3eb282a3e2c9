using System.Collections.Generic;
using System.Linq;
using System.Security;
using FlexCharge.Common.RuntimeEnvironment;

namespace FlexCharge.Common.SensitiveData.Guards;

public static class SensitiveDataGuard
{
    #region Test Cards List

    public static HashSet<string> AccountUpdaterTestCards = new()
    {
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************"
        // "****************",
        // "****************"
    };


    private static readonly string[] TestCardNumbersWhitelist =
    {
        #region FlexCharge Dummy Payment Gateway Test Cards

        #region TokenEx Test Cards

        //see: https://docs.tokenex.com/docs/3ds-version-1-challenge-test-cases

        #region Device Channel: Browser

        #region Version 2 Frictionless

        "****************", //Payment Authentication request that results in a fully authenticated frictionless authentication
        "2303779951000347", //	Payment Authentication request that results in a not-authenticated frictionless authentication
        "2303779951000370", //Payment Authentication request that results in a not-authenticated frictionless authentication
        "****************",

        #endregion

        #region Version 2 Challenge

        "2303779951000446", //Payment-Authentication request that results in a fully authenticated challenged authentication

        "2303779951000453", //Payment-Authentication request that results in a not-authenticated challenged authentication
        "2303779951000420", //Payment-Authentication request that results in a not-authenticated challenged authentication

        #endregion

        #endregion

        #region Device Channel: 3RI

        #region Version 2 Frictionless

        "2303779951000529", //Payment-Authentication request with a whitelist that results in a fully authenticated frictionless authentication with the whitelist result
        "2303779951000503", //Payment-Authentication request with a whitelist that results in a not-authenticated frictionless authentication with the whitelist result

        #endregion

        #region Version 2 Challenge

        "2303779951000537", //Payment-Authentication request that results in a fully authenticated decoupled authentication
        "2303779951000511", //Payment-Authentication request that results in a not-authenticated decoupled authentication

        #endregion

        #endregion

        #region Informational Only 3DS Test Cards

        #region 3DS 2.2.0

        "2320160000000001",

        #endregion

        #region 3DS 2.1.0

        "2303770003400000",

        #endregion

        #endregion

        #endregion

        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",

        #endregion

        # region Worldpay

        "****************",
        "****************",

        #endregion


        #region Vgs

        "51002600099240", //25/12
        "****************",
        "51002600235851",
        "****************",

        #endregion


        #region Adyen

        "****************", //737 03/2030
        "****************", //737 03/2030
        "****************", //737 03/2030,
        "422211080808",
        "*************",

        #endregion


        #region Merchant-Specific Test Cards

        #region Everi Test Cards

        "****************", // Mastercard
        "****************", //  Mastercard
        "****************", //  VISA
        "***************", //  Amex
        "****************", //  VISA
        "****************", //  Mastercard
        "****************", //  Discover
        "****************", //  VISA
        "****************", //  Mastercard
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",

        #endregion

        #region Braintree Test Cards

        "****************",

        #endregion

        #endregion

        #region Rapyd test Cards -any expiration date and 3 digits CVV

        ///   Do Not Honor.
        "****************",
        ///  Stolen Card, pick up.
        "****************",
        ///Insufficient Funds.
        "****************",
        ///approve
        "****************",

        #endregion


        //// Nuvei Test Cards

        // //Not working??? Y - CVV
        // see: https://helpdesk.nuvei.com/doku.php?id=developer:integration_docs:sandbox_testing
        // "3400000000000000", //American Express 	 	Y
        // "****************", //Debit MasterCard 	 	Y
        // "3600000000000008", //Diners 	 	N
        // "****************", //Discover 	 	Y
        // "3528000000000007", //JCB 	 	Y
        // "5000330000000000", //Maestro 	 	Y
        // "5001650000000000", //MasterCard 	 	Y
        // "6301144000000009", //Switch	N
        // "3779810000000005", //Visa Credit 	 	Y
        // "****************", //Visa Debit 	 	Y
        // "****************", //Visa Electron 	 	N 

        // Without CVV mentioned - Working
        // see: https://helpdesk.nuvei.com/doku.php?id=developer:integration_docs:testing-guide#testing_resources
        "****************", //VISA
        "****************", //Mastercard
        "6304990000000000044", //Laser
        "300000000000000004", //Maestro
        "5641820000000005", //UK Domestic Maestro
        "****************", //Electron
        "****************", //Visa Debit
        "573470089010012", //Debit Master Card
        "***************", //American Express
        "3569990000000009", //JCB
        "36000000000008", //Diners
        "6767622222222222222", //Solo 

        "****************", //not valid  by luhn

        //// NMI Test Cards
        // see: https://paymentcloudinc.com/blog/nmi-test-cards/
        // see: https://support.nmi.com/hc/en-gb/articles/115002375583-Test-Cards
        // For CSC/CVV, please use the first 3 digits of the card number (PAN)
        // Pass amount lower than 1 USD to get a declined transaction
        "****************", // VISA CREDIT
        "****************", // VISA DEBIT
        "****************", // Mastercard CREDIT,
        "****************", // Mastercard DEBIT
        "***************", // AMEX

        //NMI 3DS2 Test Cards
        //For expiry date, please use 08/2025
        //For Cardholder Name please use Test Card
        //For CVV use 999

        //Successful Frictionless Flow Authentication
        "****************", // VISA
        "****************", // Mastercard
        "***************", // AMEX

        // Paysafe test cards
        //see: https://developer.paysafe.com/en/api-docs/cards/test-and-go-live/simulating-card-payments/
        //see: https://developer.paysafe.com/en/api-docs/3ds/test-and-go-live/test-cards/
        "****************", //paysafe valid
        "****************", //paysafe valid


        //Successful Challenge Flow Authentication (use a password of 123456)
        "****************", // VISA
        "****************", // Mastercard
        "***************", // AMEX

        //Authentication Attempted
        "****************", // VISA
        "****************", // Mastercard
        "***************", // AMEX

        //Authentication Failed (use a password of 111111)
        "****************", // VISA
        "****************", // Mastercard
        "***************", // AMEX

        //Authentication Unavailable
        "****************", // VISA
        "****************", // Mastercard
        "***************", // AMEX

        //Authentication Rejected
        "****************", // VISA
        "****************", // Mastercard
        "***************", // AMEX

        "****************", //checkout
        "****************", //checkout AU

        "****************", // Checkout DECLINE

        //Test Card PANs for use with AVS
        //AMEX
        "***************", //3761 	4 Amex Street, Southampton 	SO31 6XY
        "***************", //1111 	27 Broadway, New York 	10004-1601
        //Diners
        "36555500001111", //1111 	Diners Club UK Ltd, 53 Diners Road, Salford 	M5 3BH
        //Discover
        "****************", //      Discover 444 	16 5TH ST SE, Washington DC 	20003-1120
        //JCB
        "3561000000000005", //356 	5 JCB Street, Hereford 	HR3 5TR
        "3566000020000410", //      Flat 10, 47 Park Street, London 	W1K 7EB
        //Maestro
        "6761000000000006", //676 	6 Maestro Street, Exeter 	EX16 7EF
        "6333000023456788", //888 	1 Bd Victor, Paris, France 	75015
        //Mastercard
        "****************", //512 	56 Gloucester Road, Glasgow 	GL1 2US
        "****************", //999 	73 Whiteladies Road, Clifton, Bristol 	BS8 2NT
        "****************", //541 	107 Central Park West, New York 	10023
        "****************", //541 	1116 Amsterdam Ave, New York 	10027
        "5761000000000008", //576 	8 Mastercard Street, Highbridge 	TA6 4GA
        //Visa
        "****************", //123 	123 Fake Street, Sprinfield 	SF1 234
        "****************", //412 	782 Northampton Lane, Hull 	HL8 2UA
        "****************", //      28 Bishopgate Street, Sedgeford 	PE36 4AW
        "****************", //222 	34 Broadway, New York 	10004-1608
        "****************", //476 	1 Visa Street, Crewe 	CW4 7NT
        "****************", //476 	84 Hudson St, New York 	10013
        "****************", //476 	315 W 36th St, New York 	10018


        //// Spreedly Test Cards

        // Good Cards
        "****************",
        "****************", //stripe
        "****************455",
        "****************",
        "2223003122003222",
        "***************", //AMEX
        "****************",
        "****************",

        // Bad Cards
        "****************",
        "*******************",
        "****************",
        "2720992720992729",
        "***************", //AMEX


        //AU test cards
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************",
        "****************"
    };

    #endregion

    public static void IsTestCreditCardGuard(string cardNumber)
    {
        if (EnvironmentHelper.IsInProduction || cardNumber == null) return;
        if (cardNumber.Any(char.IsLetter)) return;

        //Filtering card name to contain only digits
        cardNumber = new string(cardNumber.Where(x => char.IsDigit(x)).ToArray());

        if (!TestCardNumbersWhitelist.Contains(cardNumber))
            throw new SecurityException(
                "Only test Credit Cards are allowed in test environments. please visit: https://docs.flexfactor.io/docs/testing-cards for a list of test card numbers.");
    }
}