using FlexCharge.Common.SensitiveData.Obfuscation;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace FlexCharge.Common.Activities;

internal static class ObfuscationJsonSerializerSettings
{
    public static JsonSerializerSettings Get(bool storeTypeInformation, int maxDepth = 5)
    {
        var settings = new JsonSerializerSettings()
        {
            TypeNameHandling = storeTypeInformation ? TypeNameHandling.All : TypeNameHandling.None,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            ContractResolver = ObfuscatorContractResolver.Default,
            MaxDepth = maxDepth, // To avoid out of memory exception
            Converters =
            {
                // Serialize enums as strings, as it's more readable and integer values can change over time
                new StringEnumConverter()
            },
        };

        return settings;
    }
}