// using System;
// using System.Collections.Generic;
// using System.ComponentModel.DataAnnotations.Schema;
// using System.Linq;
// using System.Reflection;
// using Microsoft.EntityFrameworkCore;
//
// namespace FlexCharge.Common.PostgreSql;
//
// internal class JsonbPropertyScanner
// {
//     public static HashSet<Type> GetJsonbPropertyTypes<TContext>()
//     {
//         var jsonbPropertyTypes = new HashSet<Type>();
//
//         // Get the current assembly
//         var assembly = typeof(TContext).Assembly;
//
//         // Find all DbContext derived classes
//         var dbContextTypes = assembly.GetTypes()
//             .Where(t => t == typeof(TContext) || t.IsSubclassOf(typeof(TContext)));
//
//         foreach (var dbContextType in dbContextTypes)
//         {
//             // Get all DbSet properties
//             var dbSetProperties = dbContextType.GetProperties()
//                 .Where(p => p.PropertyType.IsGenericType &&
//                             p.PropertyType.GetGenericTypeDefinition() == typeof(DbSet<>));
//
//             foreach (var dbSetProperty in dbSetProperties)
//             {
//                 // Get the entity type
//                 var entityType = dbSetProperty.PropertyType.GetGenericArguments().First();
//
//                 // Get all properties marked with [Column(TypeName = "jsonb")]
//                 var jsonbProperties = entityType.GetProperties()
//                     .Where(p => p.GetCustomAttribute<ColumnAttribute>()?.TypeName == "jsonb");
//
//                 foreach (var jsonbProperty in jsonbProperties)
//                 {
//                     jsonbPropertyTypes.Add(jsonbProperty.PropertyType);
//                 }
//             }
//         }
//
//         return jsonbPropertyTypes;
//     }
// }

