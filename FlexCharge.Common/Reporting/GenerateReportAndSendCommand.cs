using System;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SendGrid.Helpers.Mail;

namespace FlexCharge.Common.Reporting;

public class GenerateReportAndSendCommand : BackgroundWorkerCommand
{
    private readonly Guid _reportId;
    private readonly IReport _report;
    private readonly object _param;
    private readonly DateTime _from;
    private readonly DateTime _to;
    private readonly string _email;
    private readonly string _receiverName;

    public GenerateReportAndSendCommand(Guid reportId, DateTime from,
        DateTime to, string email, string receiverName, object param)
    {
        _reportId = reportId;
        _param = param;
        _from = from;
        _to = to;
        _email = email;
        _receiverName = receiverName;
    }

    protected override async Task ExecuteAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<GenerateReportAndSendCommand>().LogEnterAndExit();

        var emailSender = serviceProvider.GetService<IEmailSender>();
        var activityService = serviceProvider.GetService<IActivityService>();

        var report = serviceProvider.GetServices<IReport>().First(s => s.ReportId == _reportId);

        var reportCsv = await report.GenerateAsync(_from,
            _to,
            _param);

        if (reportCsv == null)
        {
            workspan.Log.Fatal("Report Generation Failure");

            await activityService.CreateActivityAsync(
                Activities.ReportingErrorActivities.ReportGenerationFailure,
                set => set.Data(_param));

            return;
        }

        workspan.Log.Information("Internal Report Generated");

        await activityService.CreateActivityAsync(
            Activities.InternalReportingActivities.InternalReporting_ReportGenerated,
            set => set.Data(_param));

        await SendReportToUserEmailAsync(emailSender, reportCsv, _email, _receiverName,
            report, _from,
            _to);
    }

    static async Task SendReportToUserEmailAsync(IEmailSender emailSender, string csv, string email,
        string receiverName, IReport report, DateTime? startDate, DateTime? endDate
    )
    {
        using var workspan = Workspan.Start<GenerateReportAndSendCommand>();
        var options = report.Options;
        try
        {
            string emailSubject = $"Report generated";

#if DEBUG
            //TODO D: REMOVE TEST!!!
            //email = "<EMAIL>";
            ////email = "<EMAIL>";s
#endif
            var tozip = csv.Length > 15_000_000;
            var content = tozip
                ? ZipHelper.Zip(csv, report.ReportName, "csv")
                : EncoderDecoder.Base64Encode(csv);
            await emailSender.SendEmailAsync(
                email,
                emailSubject, "Content", new
                {
                    name = receiverName,
                    reportName = report.ReportName,
                    subject = emailSubject,
                    startDate = startDate?.ToString("yyyy/MM/dd") ?? "",
                    endDate = endDate?.ToString("yyyy/MM/dd") ?? ""
                    // reportStatistics_PercentComplete =
                    //     (reportGenerationStatistics.RowsActuallyGenerated * 100.0 /
                    //      reportGenerationStatistics.TotalRowsRequested).ToString(CultureInfo.InvariantCulture),
                    // reportStatistics_RowsActuallyGenerated = reportGenerationStatistics.RowsActuallyGenerated,
                    // reportStatistics_TotalRowsRequested = reportGenerationStatistics.TotalRowsRequested
                },
                options.EmailTemplateId,
                new Attachment
                {
                    Content = content,
                    Type = tozip ? "application/zip" : null,
                    Filename = tozip ? $"{report.ReportName}.zip" : $"{report.ReportName}.csv"
                });
            workspan.Log.Information("Attachment {size} csv", csv.Length);
            if (tozip) workspan.Log.Information("Attachment {size}  zip", content.Length);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }
    }
}