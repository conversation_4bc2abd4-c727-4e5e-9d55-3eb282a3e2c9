using FlexCharge.Common.Activities.Attributes;
using FlexCharge.Common.Shared.Activities;
using FlexCharge.Contracts.Activities;

namespace FlexCharge.Common.Reporting.Activities;

[Category(ActivityCategories.Reporting_ReportGeneration, ActivityInformationLevelFlags.Information)]
public enum InternalReportingActivities
{
    InternalReporting_ReportGenerated,
}

[Category(ActivityCategories.Reporting_ReportGeneration_Errors, ActivityInformationLevelFlags.Error)]
public enum ReportingErrorActivities
{
    ReportGenerationFailure,
}