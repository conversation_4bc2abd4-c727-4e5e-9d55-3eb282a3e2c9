using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.Options;
using StackExchange.Redis;

namespace FlexCharge.Common.Cache.DistributedMemoryDatabase;

public class RedisDistributedMemoryDatabase : IDistributedMemoryDatabase
{
    public RedisDistributedMemoryDatabase(IOptions<RedisCacheOptions> optionsAccessor)
    {
        if (optionsAccessor == null)
        {
            throw new ArgumentNullException(nameof(optionsAccessor));
        }

        _options = optionsAccessor.Value;

        // This allows partitioning a single backend cache for use with multiple apps/services.
        _instance = _options.InstanceName ?? string.Empty;
    }

    protected IDatabase GetDatabase()
    {
        return _connection.GetDatabase();
    }

    public async Task<string[]> GetAllStringsFromListAsync(string listName, CancellationToken token = default)
    {
        if (listName == null)
            throw new ArgumentNullException(nameof(listName));

        token.ThrowIfCancellationRequested();

        await ConnectAsync(token).ConfigureAwait(false);

        var redis = GetDatabase();

        var redisValueArray = await redis.ListRangeAsync(listName);

        var values = new string[redisValueArray.Length];

        for (var i = 0; i < redisValueArray.Length; i++)
        {
            values[i] = redisValueArray[i];
        }

        return values;
    }

    public async Task InsertToListAtBeginningAsync<T>(string listName, T value,
        TimeSpan? listExpiry, int? trimToLength = null,
        CancellationToken token = default)
    {
        if (listName == null)
            throw new ArgumentNullException(nameof(listName));

        if (value == null)
            throw new ArgumentNullException(nameof(value));


        token.ThrowIfCancellationRequested();

        await ConnectAsync(token).ConfigureAwait(false);
        var redisValue = ConvertToRedisValue(value);

        // Do not use await for actions in transaction!!! This will cause deadlock in KeyExpireAsync!!!
        // see: https://github.com/StackExchange/StackExchange.Redis/issues/711
        var transaction = GetDatabase().CreateTransaction();
        {
            _ = transaction.ListLeftPushAsync(listName, redisValue, flags: CommandFlags.FireAndForget);

            if (listExpiry != null)
                _ = transaction.KeyExpireAsync(listName, listExpiry, flags: CommandFlags.FireAndForget);

            if (trimToLength != null)
                _ = transaction.ListTrimAsync(listName, 0, trimToLength.Value - 1, CommandFlags.FireAndForget);
        }
        await transaction.ExecuteAsync(); // note: no FireAndForget on this one
    }

    public async Task InsertToListAtBeginningAsync<T>(string listName, T[] values,
        TimeSpan? listExpiry, int? trimToLength = null,
        CancellationToken token = default)
    {
        if (listName == null)
            throw new ArgumentNullException(nameof(listName));

        if (values == null)
            throw new ArgumentNullException(nameof(values));


        token.ThrowIfCancellationRequested();

        await ConnectAsync(token).ConfigureAwait(false);
        var redisValues = values.Select(ConvertToRedisValue<T>).ToArray();

        // Do not use await for actions in transaction!!! This will cause deadlock in KeyExpireAsync!!!
        // see: https://github.com/StackExchange/StackExchange.Redis/issues/711
        var transaction = GetDatabase().CreateTransaction();
        {
            _ = transaction.ListLeftPushAsync(listName, redisValues, flags: CommandFlags.FireAndForget);

            if (listExpiry != null)
                _ = transaction.KeyExpireAsync(listName, listExpiry, flags: CommandFlags.FireAndForget);

            if (trimToLength != null)
                _ = transaction.ListTrimAsync(listName, 0, trimToLength.Value - 1, CommandFlags.FireAndForget);
        }
        await transaction.ExecuteAsync(); // note: no FireAndForget on this one
    }

    public async Task AddToListAsync<T>(string listName, T value,
        TimeSpan? listExpiry, int? trimToLength = null,
        CancellationToken token = default)
    {
        if (listName == null)
            throw new ArgumentNullException(nameof(listName));

        if (value == null)
            throw new ArgumentNullException(nameof(value));


        token.ThrowIfCancellationRequested();

        await ConnectAsync(token).ConfigureAwait(false);

        var redisValue = ConvertToRedisValue(value);


        // Do not use await for actions in transaction!!! This will cause deadlock in KeyExpireAsync!!!
        // see: https://github.com/StackExchange/StackExchange.Redis/issues/711
        var transaction = GetDatabase().CreateTransaction();
        {
            _ = transaction.ListRightPushAsync(listName, redisValue, flags: CommandFlags.FireAndForget);

            if (listExpiry != null)
                _ = transaction.KeyExpireAsync(listName, listExpiry, flags: CommandFlags.FireAndForget);

            if (trimToLength != null)
                _ = transaction.ListTrimAsync(listName, -trimToLength.Value, -1, CommandFlags.FireAndForget);
        }
        await transaction.ExecuteAsync(); // note: no FireAndForget on this one
    }

    public async Task AddToListAsync<T>(string listName, T[] values,
        TimeSpan? listExpiry, int? trimToLength = null,
        CancellationToken token = default)
    {
        if (listName == null)
            throw new ArgumentNullException(nameof(listName));

        if (values == null)
            throw new ArgumentNullException(nameof(values));


        token.ThrowIfCancellationRequested();

        await ConnectAsync(token).ConfigureAwait(false);

        var redisValues = values.Select(ConvertToRedisValue).ToArray();

        // Do not use await for actions in transaction!!! This will cause deadlock in KeyExpireAsync!!!
        // see: https://github.com/StackExchange/StackExchange.Redis/issues/711
        var transaction = GetDatabase().CreateTransaction();
        {
            _ = transaction.ListRightPushAsync(listName, redisValues, flags: CommandFlags.FireAndForget);

            if (listExpiry != null)
                _ = transaction.KeyExpireAsync(listName, listExpiry, flags: CommandFlags.FireAndForget);

            if (trimToLength != null)
                _ = transaction.ListTrimAsync(listName, -trimToLength.Value, -1, CommandFlags.FireAndForget);
        }
        await transaction.ExecuteAsync(); // note: no FireAndForget on this one
    }

    private RedisValue ConvertToRedisValue<T>(T value)
    {
        switch (value)
        {
            case string stringValue:
                return stringValue;
            case int intValue:
                return intValue;
            case long longValue:
                return longValue;
            case double doubleValue:
                return doubleValue;
            case float floatValue:
                return floatValue;
            case Guid guidValue:
                return guidValue.ToString();
            default:
                throw new Exception($"Conversion to RedisValue is not not supported for type: {value.GetType().Name}");
        }
    }

    // public async Task<bool> SetIfNotExistsAsync(string key, string value, TimeSpan? expiry, CancellationToken token = default)
    // {
    //     token.ThrowIfCancellationRequested();
    //
    //     await ConnectAsync(token).ConfigureAwait(false);
    //
    //     return await GetDatabase().StringSetAsync(key, value, expiry, When.NotExists);
    // }

    public async Task DeleteKeysWithPrefixAsync(string prefix, CancellationToken token = default)
    {
        token.ThrowIfCancellationRequested();

        await ConnectAsync(token).ConfigureAwait(false);

        var database = GetDatabase();

        const int batchSize = 1000; // Number of keys to fetch in each SCAN
        var cursor = 0L; // Start with cursor 0
        var connectionMultiplexer = database.Multiplexer;

        do
        {
            // Use SCAN to fetch keys with the prefix
            var result = await database.ExecuteAsync("SCAN", cursor.ToString(), "MATCH", $"{prefix}*", "COUNT",
                batchSize.ToString());
            var scanResult = (RedisResult[]) result;

            // Update cursor for the next iteration
            cursor = long.Parse((string) scanResult[0]);

            // Extract keys
            var keys = (RedisKey[]) scanResult[1];

            if (keys?.Length > 0)
            {
                // Group keys by hash slot - this is required because Redis commands that operate on multiple keys must be sent to the same hash slot
                // see: https://stackoverflow.com/questions/70086318/multi-key-operations-must-involve-a-single-slot-redis
                var keyList = keys.GroupBy(k => connectionMultiplexer.GetHashSlot(k));

                // Send each group to a bulk command
                foreach (var keyGroup in keyList)
                {
                    // Delete the keys in a batch
                    await database.KeyDeleteAsync(keyGroup.ToArray());
                }
            }
        } while (cursor != 0); // Continue until the cursor loops back to 0
    }

    public async Task<string> GetOrSetAsync(string key, string valueToSet, TimeSpan? relativeExpiration = null,
        CancellationToken token = default)
    {
        if (key == null)
        {
            throw new ArgumentNullException(nameof(key));
        }

        if (valueToSet == null)
        {
            throw new ArgumentNullException(nameof(valueToSet));
        }

        token.ThrowIfCancellationRequested();

        await ConnectAsync(token).ConfigureAwait(false);

        var database = GetDatabase();

        RedisResult result;
        if (relativeExpiration != null)
        {
            // Lua script for GetOrSet
            // KEYS[1] = key
            // ARGV[1] = relative-expiration (long, in seconds, -1 for none) - Min(absolute-expiration - Now, sliding-expiration)
            // ARGV[2] = data (string)
            // this order should not change LUA script depends on it
            string getOrSetLuaScript = @"
            local currentValue = redis.call('GET', KEYS[1])
            if currentValue == false then
                redis.call('SET', KEYS[1], ARGV[2], 'EX', ARGV[1])
                return ARGV[2]
            else
                return currentValue
            end
        ";

            // Execute the Lua script
            result = await database.ScriptEvaluateAsync(
                getOrSetLuaScript,
                new RedisKey[] {_instance + key},
                new RedisValue[]
                {
                    (long) (relativeExpiration.Value.TotalSeconds),
                    valueToSet
                }).ConfigureAwait(false);
        }
        else
        {
            // Lua script for GetOrSet
            // KEYS[1] = key
            // ARGV[1] = data (string)
            // this order should not change LUA script depends on it
            string getOrSetLuaScript = @"
            local currentValue = redis.call('GET', KEYS[1])
            if currentValue == false then
                redis.call('SET', KEYS[1], ARGV[1])
                return ARGV[2]
            else
                return currentValue
            end
        ";

            // Execute the Lua script
            result = await database.ScriptEvaluateAsync(
                getOrSetLuaScript,
                new RedisKey[] {_instance + key},
                new RedisValue[]
                {
                    valueToSet
                }).ConfigureAwait(false);
        }


        return result.ToString();
    }

    #region Redis Connection Management

    //see: Microsoft.Extensions.Caching.StackExchangeRedis

    private volatile IConnectionMultiplexer _connection;
    private IDatabase _cache;
    private bool _disposed;

    private readonly RedisCacheOptions _options;
    private readonly string _instance;

    private readonly SemaphoreSlim _connectionLock = new SemaphoreSlim(initialCount: 1, maxCount: 1);


    private async Task ConnectAsync(CancellationToken token = default(CancellationToken))
    {
        CheckDisposed();
        token.ThrowIfCancellationRequested();

        if (_cache != null)
        {
            return;
        }

        await _connectionLock.WaitAsync(token).ConfigureAwait(false);
        try
        {
            if (_cache == null)
            {
                if (_options.ConnectionMultiplexerFactory is null)
                {
                    if (_options.ConfigurationOptions is not null)
                    {
                        _connection = await ConnectionMultiplexer.ConnectAsync(_options.ConfigurationOptions)
                            .ConfigureAwait(false);
                    }
                    else
                    {
                        _connection = await ConnectionMultiplexer.ConnectAsync(_options.Configuration)
                            .ConfigureAwait(false);
                    }
                }
                else
                {
                    _connection = await _options.ConnectionMultiplexerFactory();
                }

                PrepareConnection();
                _cache = _connection.GetDatabase();
            }
        }
        finally
        {
            _connectionLock.Release();
        }
    }

    private void PrepareConnection()
    {
        ValidateServerFeatures();
        TryRegisterProfiler();
    }

    private void ValidateServerFeatures()
    {
        _ = _connection ?? throw new InvalidOperationException($"{nameof(_connection)} cannot be null.");
    }

    private void TryRegisterProfiler()
    {
        _ = _connection ?? throw new InvalidOperationException($"{nameof(_connection)} cannot be null.");

        if (_options.ProfilingSession != null)
        {
            _connection.RegisterProfiler(_options.ProfilingSession);
        }
    }

    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;
        _connection?.Close();
    }

    private void CheckDisposed()
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(this.GetType().FullName);
        }
    }

    #endregion
}