using System;
using System.Text.RegularExpressions;
using FlexCharge.Common.RuntimeEnvironment;

namespace FlexCharge.Common.DataBase;

public static class ReadOnlyConnectionString
{
    public static string MakeReadOnly(string connectionString)
    {
        // Add readonly to connection string of this format:
        // $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';";

        var host = GetHost(connectionString);

        // For local development and local migrations
        if (string.IsNullOrWhiteSpace(host))
            return connectionString;

        var hostParts = host.Split(".");

        // Database on localhost is always read-write
        if (string.Compare(host, "localhost", StringComparison.OrdinalIgnoreCase) != 0)
        {
            if (hostParts.Length < 3)
                throw new Exception("Host is not in correct format");

            if (EnvironmentHelper.IsInProduction)
            {
                hostParts[0] += "-portal-readonly";
            }
            else
            {
                hostParts[0] += "-readonly";
            }
        }

        var readonlyHost = string.Join(".", hostParts);

        //Console.WriteLine("DB Host: " + readonlyHost);

        var readonlyConnectionString = ReplaceHost(connectionString, readonlyHost);

        return readonlyConnectionString;
    }

    static string GetHost(string connectionString)
    {
        var match = Regex.Match(connectionString, @"Host=([^;]+)");

        if (match.Success)
        {
            return match.Groups[1].Value;
        }

        return null; // or throw an exception, depending on your needs
    }

    static string ReplaceHost(string connectionString, string newHost)
    {
        string pattern = @"(?<=Host=)[^;]+";
        string replacement = newHost;

        return Regex.Replace(connectionString, pattern, replacement);
    }
}