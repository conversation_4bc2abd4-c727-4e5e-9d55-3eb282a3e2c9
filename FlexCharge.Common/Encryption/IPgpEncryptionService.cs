using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Org.BouncyCastle.Bcpg;
using Org.BouncyCastle.Bcpg.OpenPgp;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Security;

namespace FlexCharge.Common.Encryption;

public interface IPgpEncryptionService
{
    Task EncryptFileAsync(string publicKeyPath, string inputFile, string outputFile);
    Task EncryptFileAsync(string publicKeyPath, Stream inputStream, string outputFile);

    //Task<Stream> EncryptStreamAsync(string publicKeyPath, Stream inputStream);
    //Task<Stream> EncryptStreamAsync(string publicKeyPath, Stream inputStream, bool armor, bool withIntegrityCheck);

    Task EncryptStreamAsync(string outputFileName,
        string publicKeyPath,
        Stream inputStream,
        Stream outputStream,
        //bool armor,
        bool withIntegrityCheck);

    Task EncryptStreamAsync(string outputFileName,
        PgpPublicKey publicKey,
        Stream inputStream,
        Stream outStream,
        // bool armor,
        bool withIntegrityCheck);
    //void EncryptFile(string publicKeyPath, string inputFile, string outputFile);
    //Task EncryptStream(string publicKeyPath, Stream inputStream, string outputFile);

    Task DecryptFileAsync(string publicKeyPath, string privateKeyPath, string password, string inputFile,
        string outputFile);

    Task<Stream> DecryptStreamAsync(string publicKeyPath, string privateKeyPath, string password, Stream inputStream);

    Task<Stream> DecryptStreamAsync(PgpPublicKey publicKey, PgpPrivateKey privateKey, string password,
        Stream inputStream);

    // void DecryptFile(string publicKeyPath, string privateKeyPath, string password, string inputFile,
    //     string outputFile);

    void GenerateKeys(string user, string password, string publicKeyNamePath, string privateKeyNamePath);

    PgpPublicKey LoadPublicKeyFromString(string publicKeyString);
    PgpPublicKey LoadPublicKey(string publicKeyFilePath);
    PgpPrivateKey LoadPrivateKey(PgpPublicKey publicKey, string privateKeyFilePath, string password);
    PgpPrivateKey LoadPrivateKeyFromString(PgpPublicKey publicKey, string privateKeyString, string password);
}

public class PgpEncryptionService : IPgpEncryptionService
{
    public void GenerateKeys(string user, string password, string publicKeyNamePath, string privateKeyNamePath)
    {
        // Generate a 2048-bit RSA key pair
        var generator = new RsaKeyPairGenerator();
        generator.Init(new KeyGenerationParameters(new SecureRandom(), 2048));
        var keyPair = generator.GenerateKeyPair();

        // Create a PGP key pair
        var pgpKeyPair = new PgpKeyPair(
            PublicKeyAlgorithmTag.RsaGeneral, keyPair, DateTime.UtcNow);

        // Set key pair attributes
        var userId = user;
        var passphrase = password.ToCharArray();
        var subpacketGenerator = new PgpSignatureSubpacketGenerator();
        subpacketGenerator.SetKeyFlags(false, PgpKeyFlags.CanEncryptCommunications | PgpKeyFlags.CanEncryptStorage);

        // Create a PGP key ring
        var keyRingGenerator = new PgpKeyRingGenerator(
            PgpSignature.PositiveCertification,
            pgpKeyPair,
            userId,
            SymmetricKeyAlgorithmTag.Aes256,
            passphrase,
            true,
            subpacketGenerator.Generate(),
            null,
            new SecureRandom());

        // Save the public and private keys to separate files
        using (var publicKeyStream = File.CreateText($"{publicKeyNamePath}.asc"))
        {
            using (var armoredOutputStream = new ArmoredOutputStream(publicKeyStream.BaseStream))
            {
                keyRingGenerator.GeneratePublicKeyRing().Encode(armoredOutputStream);
            }
        }

        using (var privateKeyStream = File.CreateText($"{privateKeyNamePath}.asc"))
        {
            using (var armoredOutputStream = new ArmoredOutputStream(privateKeyStream.BaseStream))
            {
                keyRingGenerator.GenerateSecretKeyRing().Encode(armoredOutputStream);
            }
        }
    }

    public async Task EncryptFileAsync(string publicKeyPath, Stream inputStream, string outputFile)
    {
        try
        {
            if (inputStream.Length > 0)
            {
                using (Stream outputStream = File.Create(outputFile))
                {
                    await EncryptStreamAsync(outputFile, publicKeyPath, inputStream, outputStream, true);
                }
            }
            else
            {
                Console.WriteLine("Input stream is empty. Skipping encryption.");
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task EncryptFileAsync(string publicKeyPath, string inputFile, string outputFile)
    {
        try
        {
            using (Stream inputStream = File.OpenRead(inputFile))
            {
                if (inputStream.Length > 0)
                {
                    using (Stream outputStream = File.Create(outputFile))
                    {
                        await EncryptStreamAsync(outputFile, publicKeyPath, inputStream, outputStream, true);

                        outputStream.Close();
                    }

                    // Check if the output file is empty
                    FileInfo outputFileInfo = new FileInfo(outputFile);
                    if (outputFileInfo.Length == 0)
                    {
                        Console.WriteLine("Output file is empty. Deleting the file.");
                        File.Delete(outputFile);
                    }
                }
                else
                {
                    Console.WriteLine("Input file is empty. Skipping encryption.");
                }
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task DecryptFileAsync(string publicKeyPath, string privateKeyPath, string password, string inputFile,
        string outputFile)
    {
        using Stream inputStream = File.OpenRead(inputFile);
        using Stream outputStream = File.Create(outputFile);

        await DecryptStreamAsync(publicKeyPath, privateKeyPath, password, inputStream, outputStream);
    }

    public async Task EncryptStreamAsync(string outputFileName, string publicKey, Stream inputStream,
        Stream outStream,
        // bool armor,
        bool withIntegrityCheck)
    {
        // Read public key
        PgpPublicKey pubKey = LoadPublicKey(publicKey);

        // Prepare encGen
        PgpEncryptedDataGenerator encGen =
            new PgpEncryptedDataGenerator(SymmetricKeyAlgorithmTag.Cast5, withIntegrityCheck, new SecureRandom());
        encGen.AddMethod(pubKey);

        //using Stream encOut = armor ? new ArmoredOutputStream(outStream) : outStream;

        // Open an encrypted stream
        using (Stream encryptedOut = encGen.Open(outStream, new byte[1 << 16]))
        {
            // Prepare literal data generator
            PgpLiteralDataGenerator literalGen = new PgpLiteralDataGenerator();

            // Open literal data generator
            using (Stream literalOut = literalGen.Open(encryptedOut, PgpLiteralData.Binary, outputFileName,
                       inputStream.Length,
                       DateTime.UtcNow))
            {
                // Write data
                await inputStream.CopyToAsync(literalOut);
                await literalOut.FlushAsync();
            }
        }
    }

    public async Task EncryptStreamAsync(string outputFileName, PgpPublicKey publicKey, Stream inputStream,
        Stream outStream,
        // bool armor,
        bool withIntegrityCheck)
    {
        // Prepare encGen
        PgpEncryptedDataGenerator encGen =
            new PgpEncryptedDataGenerator(SymmetricKeyAlgorithmTag.Cast5, withIntegrityCheck, new SecureRandom());
        encGen.AddMethod(publicKey);

        //using Stream encOut = armor ? new ArmoredOutputStream(outStream) : outStream;

        // Open an encrypted stream
        using (Stream encryptedOut = encGen.Open(outStream, new byte[1 << 16]))
        {
            // Prepare literal data generator
            PgpLiteralDataGenerator literalGen = new PgpLiteralDataGenerator();

            // Open literal data generator
            using (Stream literalOut = literalGen.Open(encryptedOut, PgpLiteralData.Binary, outputFileName,
                       inputStream.Length,
                       DateTime.UtcNow))
            {
                // Write data
                await inputStream.CopyToAsync(literalOut);
                await literalOut.FlushAsync();
            }
        }
    }

    // public async Task<Stream> EncryptStreamAsync(string publicKeyPath, Stream inputStream, bool armor, bool withIntegrityCheck)
    // {
    //     // read public key
    //     PgpPublicKey pubKey = LoadPublicKey(publicKeyPath);
    //
    //     // prepare encGen
    //     PgpEncryptedDataGenerator encGen = new PgpEncryptedDataGenerator(SymmetricKeyAlgorithmTag.Cast5, withIntegrityCheck, new SecureRandom());
    //     encGen.AddMethod(pubKey);
    //
    //     // create a memory stream for the output
    //     MemoryStream outputStream = new MemoryStream();
    //
    //     using Stream encOut = armor ? new ArmoredOutputStream(outputStream) : outputStream;
    //
    //     // open an encrypted stream
    //     using (Stream encryptedOut = encGen.Open(encOut, new byte[1 << 16]))
    //     {
    //         // prepare literal data generator
    //         PgpLiteralDataGenerator literalGen = new PgpLiteralDataGenerator();
    //
    //         // open literal data generator
    //         using (Stream literalOut = literalGen.Open(encryptedOut, PgpLiteralData.Binary, "label", inputStream.Length, DateTime.UtcNow))
    //         {
    //             // write data
    //             await inputStream.CopyToAsync(literalOut);
    //         }
    //     }
    //
    //     // reset the position of the outputStream to be able to read from the start
    //     outputStream.Position = 0;
    //
    //     return outputStream;
    // }

    private async Task DecryptStreamAsync(string publicKeyPath, string privateKeyPath, string password,
        Stream inputStream,
        Stream outputStream)
    {
        try
        {
            PgpPublicKey PublicKey = LoadPublicKey(publicKeyPath);
            PgpPrivateKey privateKey = LoadPrivateKey(PublicKey, privateKeyPath, password);

            PgpObjectFactory pgpFactory = new PgpObjectFactory(PgpUtilities.GetDecoderStream(inputStream));
            PgpObject pgpObject = pgpFactory.NextPgpObject();

            PgpEncryptedDataList encryptedDataList = null;
            if (pgpObject is PgpEncryptedDataList)
            {
                encryptedDataList = (PgpEncryptedDataList) pgpObject;
            }
            else
            {
                encryptedDataList = (PgpEncryptedDataList) pgpFactory.NextPgpObject();
            }

            PgpPublicKeyEncryptedData publicKeyEncryptedData = null;
            foreach (PgpPublicKeyEncryptedData data in encryptedDataList.GetEncryptedDataObjects())
            {
                if (data.KeyId == PublicKey.KeyId)
                {
                    publicKeyEncryptedData = data;
                    break;
                }
            }

            if (publicKeyEncryptedData == null)
            {
                throw new ArgumentException("Private key not matched to any encrypted data.");
            }

            Stream clearStream = publicKeyEncryptedData.GetDataStream(privateKey);
            PgpObjectFactory clearFactory = new PgpObjectFactory(clearStream);
            PgpObject clearObject = clearFactory.NextPgpObject();

            if (clearObject is PgpCompressedData)
            {
                PgpCompressedData compressedData = (PgpCompressedData) clearObject;
                PgpObjectFactory compressedFactory = new PgpObjectFactory(compressedData.GetDataStream());
                clearObject = compressedFactory.NextPgpObject();
            }

            if (clearObject is PgpLiteralData)
            {
                PgpLiteralData literalData = (PgpLiteralData) clearObject;

                Stream inputDataStream = literalData.GetInputStream();
                byte[] buffer = new byte[1 << 16];
                int bytesRead;
                while ((bytesRead = inputDataStream.Read(buffer, 0, buffer.Length)) > 0)
                {
                    outputStream.Write(buffer, 0, bytesRead);
                }
            }
            else if (clearObject is PgpOnePassSignatureList)
            {
                throw new PgpException("Encrypted message contains a signed message - not supported.");
            }
            else
            {
                throw new PgpException("Message is not a simple encrypted file - type unknown.");
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task<Stream> DecryptStreamAsync(string publicKeyString, string privateKeyString, string password,
        Stream inputStream)
    {
        try
        {
            PgpPublicKey PublicKey = LoadPublicKeyFromString(publicKeyString);
            PgpPrivateKey privateKey = LoadPrivateKeyFromString(PublicKey, privateKeyString, password);

            PgpObjectFactory pgpFactory = new PgpObjectFactory(PgpUtilities.GetDecoderStream(inputStream));
            PgpObject pgpObject = pgpFactory.NextPgpObject();

            PgpEncryptedDataList encryptedDataList = null;
            if (pgpObject is PgpEncryptedDataList)
            {
                encryptedDataList = (PgpEncryptedDataList) pgpObject;
            }
            else
            {
                encryptedDataList = (PgpEncryptedDataList) pgpFactory.NextPgpObject();
            }

            PgpPublicKeyEncryptedData publicKeyEncryptedData = null;
            foreach (PgpPublicKeyEncryptedData data in encryptedDataList.GetEncryptedDataObjects())
            {
                if (data.KeyId == PublicKey.KeyId)
                {
                    publicKeyEncryptedData = data;
                    break;
                }
            }

            if (publicKeyEncryptedData == null)
            {
                throw new ArgumentException("Private key not matched to any encrypted data.");
            }

            Stream clearStream = publicKeyEncryptedData.GetDataStream(privateKey);
            PgpObjectFactory clearFactory = new PgpObjectFactory(clearStream);
            PgpObject clearObject = clearFactory.NextPgpObject();

            if (clearObject is PgpCompressedData)
            {
                PgpCompressedData compressedData = (PgpCompressedData) clearObject;
                PgpObjectFactory compressedFactory = new PgpObjectFactory(compressedData.GetDataStream());
                clearObject = compressedFactory.NextPgpObject();
            }

            MemoryStream decryptedStream = new MemoryStream();

            if (clearObject is PgpLiteralData)
            {
                PgpLiteralData literalData = (PgpLiteralData) clearObject;

                Stream inputDataStream = literalData.GetInputStream();
                byte[] buffer = new byte[1 << 16];
                int bytesRead;
                while ((bytesRead = await inputDataStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    await decryptedStream.WriteAsync(buffer, 0, bytesRead);
                }
            }
            else if (clearObject is PgpOnePassSignatureList)
            {
                throw new PgpException("Encrypted message contains a signed message - not supported.");
            }
            else
            {
                throw new PgpException("Message is not a simple encrypted file - type unknown.");
            }

            decryptedStream.Position = 0;
            return decryptedStream;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task<Stream> DecryptStreamAsync(PgpPublicKey publicKey, PgpPrivateKey privateKey, string password,
        Stream inputStream)
    {
        try
        {
            PgpObjectFactory pgpFactory = new PgpObjectFactory(PgpUtilities.GetDecoderStream(inputStream));
            PgpObject pgpObject = pgpFactory.NextPgpObject();

            PgpEncryptedDataList encryptedDataList = null;
            if (pgpObject is PgpEncryptedDataList)
            {
                encryptedDataList = (PgpEncryptedDataList) pgpObject;
            }
            else
            {
                encryptedDataList = (PgpEncryptedDataList) pgpFactory.NextPgpObject();
            }

            PgpPublicKeyEncryptedData publicKeyEncryptedData = null;
            foreach (PgpPublicKeyEncryptedData data in encryptedDataList.GetEncryptedDataObjects())
            {
                if (data.KeyId == publicKey.KeyId)
                {
                    publicKeyEncryptedData = data;
                    break;
                }
            }

            if (publicKeyEncryptedData == null)
            {
                throw new ArgumentException("Private key not matched to any encrypted data.");
            }

            Stream clearStream = publicKeyEncryptedData.GetDataStream(privateKey);
            PgpObjectFactory clearFactory = new PgpObjectFactory(clearStream);
            PgpObject clearObject = clearFactory.NextPgpObject();

            if (clearObject is PgpCompressedData)
            {
                PgpCompressedData compressedData = (PgpCompressedData) clearObject;
                PgpObjectFactory compressedFactory = new PgpObjectFactory(compressedData.GetDataStream());
                clearObject = compressedFactory.NextPgpObject();
            }

            MemoryStream decryptedStream = new MemoryStream();

            if (clearObject is PgpLiteralData)
            {
                PgpLiteralData literalData = (PgpLiteralData) clearObject;

                Stream inputDataStream = literalData.GetInputStream();
                byte[] buffer = new byte[1 << 16];
                int bytesRead;
                while ((bytesRead = await inputDataStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    await decryptedStream.WriteAsync(buffer, 0, bytesRead);
                }
            }
            else if (clearObject is PgpOnePassSignatureList)
            {
                throw new PgpException("Encrypted message contains a signed message - not supported.");
            }
            else
            {
                throw new PgpException("Message is not a simple encrypted file - type unknown.");
            }

            decryptedStream.Position = 0;
            return decryptedStream;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public PgpPublicKey LoadPublicKey(string publicKeyFilePath)
    {
        Console.WriteLine("Loading public key from string");
        using (Stream keyStream = File.OpenRead(publicKeyFilePath))
        {
            PgpPublicKeyRingBundle keyRingBundle = new PgpPublicKeyRingBundle(PgpUtilities.GetDecoderStream(keyStream));
            foreach (PgpPublicKeyRing keyRing in keyRingBundle.GetKeyRings())
            {
                foreach (PgpPublicKey key in keyRing.GetPublicKeys().OfType<PgpPublicKey>())
                {
                    return key;
                }
            }

            throw new PgpException("Public key file does not contain any public keys.");
        }
    }

    public PgpPublicKey LoadPublicKeyFromString(string publicKeyString)
    {
        // string fileContent = File.ReadAllText(publicKeyString);
        Console.WriteLine("Loading public key from string");
        byte[] publicKeyBytes = Encoding.UTF8.GetBytes(publicKeyString);

        using (Stream keyStream = new MemoryStream(publicKeyBytes))
        {
            PgpPublicKeyRingBundle keyRingBundle = new PgpPublicKeyRingBundle(PgpUtilities.GetDecoderStream(keyStream));
            foreach (PgpPublicKeyRing keyRing in keyRingBundle.GetKeyRings())
            {
                foreach (PgpPublicKey key in keyRing.GetPublicKeys().OfType<PgpPublicKey>())
                {
                    return key;
                }
            }

            throw new PgpException("Public key string does not contain any public keys.");
        }
    }

    public PgpPrivateKey LoadPrivateKey(PgpPublicKey publicKey, string privateKeyFilePath, string password)
    {
        using (Stream keyStream = File.OpenRead(privateKeyFilePath))
        {
            PgpSecretKeyRingBundle keyRingBundle = new PgpSecretKeyRingBundle(PgpUtilities.GetDecoderStream(keyStream));
            PgpSecretKeyRing keyRing = keyRingBundle.GetSecretKeyRing(publicKey.KeyId);
            if (keyRing == null)
            {
                throw new PgpException("Private key file does not contain a secret key ring for the given public key.");
            }

            PgpSecretKey secretKey = keyRing.GetSecretKey();
            return secretKey.ExtractPrivateKey(password.ToCharArray());
        }
    }

    public PgpPrivateKey LoadPrivateKeyFromString(PgpPublicKey publicKey, string privateKeyString, string password)
    {
        byte[] privateKeyBytes = Encoding.UTF8.GetBytes(privateKeyString);

        using (Stream keyStream = new MemoryStream(privateKeyBytes))
        {
            PgpSecretKeyRingBundle keyRingBundle = new PgpSecretKeyRingBundle(PgpUtilities.GetDecoderStream(keyStream));
            PgpSecretKeyRing keyRing = keyRingBundle.GetSecretKeyRing(publicKey.KeyId);
            if (keyRing == null)
            {
                throw new PgpException(
                    "Private key string does not contain a secret key ring for the given public key.");
            }

            PgpSecretKey secretKey = keyRing.GetSecretKey();
            return secretKey.ExtractPrivateKey(password.ToCharArray());
        }
    }
}