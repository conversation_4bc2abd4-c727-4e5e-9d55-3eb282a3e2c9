using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net.Http;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace FlexCharge.Common.Authentication
{
    public class JwtHandler : IJwtHandler
    {
        private static readonly ISet<string> DefaultClaims = new HashSet<string>
        {
            JwtRegisteredClaimNames.Sub,
            JwtRegisteredClaimNames.UniqueName,
            JwtRegisteredClaimNames.Jti,
            JwtRegisteredClaimNames.Iat,
            ClaimTypes.Role,
        };

        private readonly JwtSecurityTokenHandler _jwtSecurityTokenHandler = new JwtSecurityTokenHandler();
        private readonly IOptions<JwtOptions> _options;
        private readonly SigningCredentials _signingCredentials;
        private readonly TokenValidationParameters _tokenValidationParameters;

        public RsaSecurityKey SigningKey(string Key, string Expo)
        {
            return new RsaSecurityKey(new RSAParameters()
            {
                Modulus = Base64UrlEncoder.DecodeBytes(Key),
                Exponent = Base64UrlEncoder.DecodeBytes(Expo)
            });
        }

        public JwtHandler(IOptions<JwtOptions> options)
        {
            _options = options;

            if (options.Value.IsExternal)
            {
                var cognitoIssuer =
                    $"https://cognito-idp.{options.Value.Region}.amazonaws.com/{options.Value.UserPoolId}";
                var jwtKeySetUrl = $"{cognitoIssuer}/.well-known/jwks.json";

                // TODO: Optimize using connection pool
                // see: https://learn.microsoft.com/en-us/dotnet/fundamentals/networking/http/httpclient-guidelines
                using var httpClient = new HttpClient();
                var json = httpClient.GetAsync(jwtKeySetUrl).Result.Content.ReadAsStringAsync().Result;

                // serialize the result
                var keys = JsonConvert.DeserializeObject<JsonWebKeySet>(json).Keys;

                _signingCredentials = new SigningCredentials(keys[0], SecurityAlgorithms.Sha256);
                _tokenValidationParameters = new TokenValidationParameters
                {
                    IssuerSigningKeyResolver = (s, securityToken, identifier, parameters) =>
                    {
                        // get JsonWebKeySet from AWS
                        // cast the result to be the type expected by IssuerSigningKeyResolver
                        return (IEnumerable<SecurityKey>) keys;
                    },
                    ValidIssuer = cognitoIssuer,
                    ValidateIssuerSigningKey = true,
                    ValidateIssuer = options.Value.ValidateIssuer,
                    ValidateLifetime = options.Value.ValidateLifetime,
                    ValidAudience = options.Value.ValidAudience
                };
            }
            else
            {
                var issuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_options.Value.SecretKey));
                _signingCredentials = new SigningCredentials(issuerSigningKey, SecurityAlgorithms.HmacSha256);

                _tokenValidationParameters = new TokenValidationParameters
                {
                    IssuerSigningKey = issuerSigningKey,
                    ValidIssuer = _options.Value.Issuer,
                    ValidAudiences = _options.Value.ValidAudiences,
                    ValidateAudience = _options.Value.ValidateAudience,
                    ValidateLifetime = _options.Value.ValidateLifetime
                };
            }
        }

        public JsonWebToken CreateToken(string userId, string[] aud, string[] roles = null, List<Claim> claims = null,
            DateTime? exp = null)
        {
            if (string.IsNullOrWhiteSpace(userId))
            {
                throw new ArgumentException("User id claim can not be empty.", nameof(userId));
            }

            var now = DateTime.UtcNow;
            var jwtClaims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, userId),
                new Claim(JwtRegisteredClaimNames.UniqueName, userId),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, now.ToTimestamp().ToString()),
            };

            if (_options.Value?.ValidAudiences != null)
                foreach (var item in _options.Value?.ValidAudiences)
                {
                    jwtClaims.Add(new Claim(JwtRegisteredClaimNames.Aud, item));
                }

            if (!string.IsNullOrEmpty(_options.Value.ValidAudience))
            {
                jwtClaims.Add(new Claim(JwtRegisteredClaimNames.Aud, _options.Value.ValidAudience));
            }

            if (roles != null)
            {
                foreach (var role in roles)
                {
                    if (!string.IsNullOrWhiteSpace(role))
                    {
                        jwtClaims.Add(new Claim(ClaimTypes.Role, role));
                    }
                }
            }

            var customClaims = claims?.Select(claim => new Claim(claim.Type, claim.Value)).ToArray()
                               ?? Array.Empty<Claim>();
            jwtClaims.AddRange(customClaims);
            var expires = exp ?? now.AddMinutes(_options.Value.ExpiryMinutes);

            var cognitoIssuer =
                $"https://cognito-idp.{_options.Value.Region}.amazonaws.com/{_options.Value.UserPoolId}";

            var jwt = new JwtSecurityToken(
                issuer: cognitoIssuer,
                claims: jwtClaims,
                notBefore: now,
                expires: expires,
                signingCredentials: _signingCredentials
            );

            var token = new JwtSecurityTokenHandler().WriteToken(jwt);

            return new JsonWebToken
            {
                AccessToken = token,
                RefreshToken = string.Empty,
                Expires = expires.ToTimestamp(),
                Id = userId,
                //Role = roles.Any() ? roles : default,
                //Claims = jwtClaims
            };
        }

        public JsonWebTokenPayload GetTokenPayload(string accessToken)
        {
            _jwtSecurityTokenHandler.ValidateToken(accessToken, _tokenValidationParameters,
                out var validatedSecurityToken);
            if (!(validatedSecurityToken is JwtSecurityToken jwt))
            {
                return null;
            }

            return new JsonWebTokenPayload
            {
                Subject = jwt.Subject,
                Role = jwt.Claims.SingleOrDefault(x => x.Type == ClaimTypes.Role)?.Value,
                Expires = jwt.ValidTo.ToTimestamp(),
                Claims = jwt.Claims.Where(x => !DefaultClaims.Contains(x.Type))
                    .ToDictionary(k => k.Type, v => v.Value)
            };
        }


        public Guid ValidateToken(string token)
        {
            var principal = GetPrincipal(token);
            if (principal == null)
            {
                return Guid.Empty;
            }

            ClaimsIdentity identity;
            try
            {
                identity = (ClaimsIdentity) principal.Identity;
            }
            catch (NullReferenceException)
            {
                return Guid.Empty;
            }

            //var userIdClaim = identity.FindFirst("userId");
            var userIdClaim = identity.FindFirst(ClaimTypes.NameIdentifier);
            var userId = new Guid(userIdClaim.Value);
            return userId;
        }

        public ClaimsPrincipal GetPrincipal(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtToken = (JwtSecurityToken) tokenHandler.ReadToken(token);
                if (jwtToken == null)
                {
                    return null;
                }

                //IdentityModelEventSource.ShowPII = true;
                SecurityToken securityToken;
                ClaimsPrincipal principal = tokenHandler.ValidateToken(token,
                    _tokenValidationParameters, out securityToken);
                return principal;
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}