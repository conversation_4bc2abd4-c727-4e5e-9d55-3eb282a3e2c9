namespace FlexCharge.Common.Authentication;

public class SuperAdminGroups
{
    public const string SUPER_ADMIN = "SUPER_ADMIN";
    public const string ADMIN_SALES = "ADMIN_SALES";
    public const string ADMIN_OPERATIONS = "ADMIN_OPERATIONS";
    public const string ADMIN_RISK = "ADMIN_RISK";

    public const string MERCHANT_ADMIN = "MERCHANT_ADMIN";

    public const string PARTNER_ADMIN = "PARTNER_ADMIN";
    public const string PARTNER_ADMIN_SALES = "PARTNER_ADMIN_SALES";
    public const string PARTNER_ADMIN_OPERATIONS = "PARTNER_ADMIN_OPERATIONS";
    public const string PARTNER_ADMIN_RISK = "PARTNER_ADMIN_RISK";

    public const string INTEGRATION_PARTNER_ADMIN = "INTEGRATION_PARTNER_ADMIN";
    public const string USER = "USER";
    public const string BASE_USER = "BASE_USER";
    public const string SALESAGENCY_ADMIN = "SALESAGENCY_ADMIN";
    public const string SALESAGENCY_AGENT = "SALESAGENCY_AGENT";
}