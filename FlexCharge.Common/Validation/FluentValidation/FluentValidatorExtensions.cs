using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FluentValidation;
using FluentValidation.Results;

namespace FlexCharge.Common.Validation.FluentValidation;

public static class FluentValidatorExtensions
{
    // public static ValidationResult Validate<T1, T2>(this (T1, T2) instance, Action<InlineValidator<(T1, T2)>> configure)
    //
    // {
    //     var validator = new InlineValidator<(T1, T2)>();
    //     configure(validator);
    //     var result=validator.Validate(instance);
    //     return result;
    // }

    public static ValidationResult Validate<T>(this T instance, Action<InlineValidator<T>> configure)

    {
        var validator = new InlineValidator<T>();
        configure(validator);

        var result = validator.Validate(instance);

        return result;
    }

    public static async Task<ValidationResult> ValidateAsync<T>(this T instance, Action<InlineValidator<T>> configure)
    {
        var validator = new InlineValidator<T>();
        configure(validator);

        var result = await validator.ValidateAsync(instance);

        return result;
    }

    public static void ThrowOnErrors(this ValidationResult result, bool logErrors,
        Func<ValidationFailure, bool> filter = null)
    {
        if (!result.IsValid)
        {
            var errors =
                (filter == null ? result.Errors : result.Errors.Where(filter))
                .ToList();

            if (logErrors)
            {
                var notSevereErrors = errors
                    .Where(e => e.Severity != Severity.Error).ToList();

                foreach (var infoOrWarning in notSevereErrors)
                {
                    if (infoOrWarning.Severity == Severity.Info)
                    {
                        Workspan.Current?.Log.Information(infoOrWarning.ErrorMessage);
                    }
                    else if (infoOrWarning.Severity == Severity.Warning)
                    {
                        Workspan.Current?.Log.Warning(infoOrWarning.ErrorMessage);
                    }
                }
            }

            var severeErrors = errors.Where(e => e.Severity == Severity.Error).ToList();
            if (severeErrors.Any())
            {
                var exception =
                    new FlexValidationMultipleErrorsException(
                        "Validation error");

                foreach (var error in severeErrors)
                {
                    if (logErrors) Workspan.Current?.Log.Error(error.ErrorMessage);

                    #region Commented

                    // var errorKey = error.PropertyName;
                    //
                    // // Data dictionary can't have duplicate keys
                    // // If the key already exists, append a number to the key
                    // int i = 1;
                    // while (exception.Data.Contains(errorKey))
                    // {
                    //     errorKey = error.PropertyName + $" #{++i}";
                    // }
                    //
                    // exception.Data.Add(errorKey, error.ErrorMessage);

                    #endregion

                    exception.AddError(error.PropertyName, error.ErrorMessage);
                }

                throw exception;
            }
        }
    }

    /// <summary>
    /// If the <see cref="validation"/>predicate is false, the <see cref="transformAction"/> is executed and the validation error is added
    /// </summary>
    /// <param name="ruleBuilder"></param>
    /// <param name="validation"></param>
    /// <param name="transformAction"></param>
    /// <typeparam name="T"></typeparam>
    /// <typeparam name="TProp"></typeparam>
    /// <returns></returns>
    public static IRuleBuilderOptionsConditions<T, TProp> TransformIfNotValid<T, TProp>(
        this IRuleBuilder<T, TProp> ruleBuilder,
        Func<TProp, bool> validation,
        Action<T> transformAction
    )
    {
        return ruleBuilder.Custom((value, context) =>
        {
            if (!validation(value))
            {
                transformAction(context.InstanceToValidate);
                context.AddFailure(
                    new ValidationFailure(context.DisplayName, "Value is not correct - transformation applied")
                        {Severity = Severity.Warning});
            }
        });
    }


    public static IRuleBuilderOptions<T, TProperty> WithErrorCode<T, TProperty, E>(
        this IRuleBuilderOptions<T, TProperty> rule, E errorCode)
        where E : struct, Enum
    {
        return rule.WithErrorCode(errorCode.ToString()).WithMessage(Enum.GetName(errorCode));
    }


    public static bool HasError<T>(this ValidationResult result, T errorCode)
        where T : struct, Enum
    {
        return result.Errors.Any(e => e.ErrorCode == errorCode.ToString());
    }

    // TODO: Should be refactored to use FlexValidationMultipleErrorsException and renamed to ValidWhenAndThrow ot something like that
    // public static void ValidWhen<T>(this T instance, Action<InlineValidator<T>> configure)
    //     where T : IFluentValidationModel
    // {
    //     var validator = new InlineValidator<T>();
    //     configure(validator);
    //
    //     var res = validator.Validate(instance);
    //
    //     if (!res.IsValid)
    //     {
    //         var severeErrors = res.Errors.Where(e => e.Severity == Severity.Error).ToList();
    //         if (severeErrors.Any())
    //         {
    //             if (severeErrors.Count > 1)
    //             {
    //                 throw new FlexValidationException(int.Parse(severeErrors.First().ErrorCode),
    //                     severeErrors.First().ErrorMessage);
    //             }
    //             else
    //             {
    //                 var ex = severeErrors.Select(e =>
    //                     new FlexValidationException(int.Parse(severeErrors.First().ErrorCode),
    //                         severeErrors.First().ErrorMessage));
    //             }
    //         }
    //     }
    //
    //
    //     validator.ValidateAndThrow(instance);
    // }
}