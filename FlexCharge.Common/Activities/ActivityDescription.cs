using System;
using System.Reflection;
using FlexCharge.Common.Activities.Attributes;
using FlexCharge.Contracts.Activities;
using FlexCharge.Utils;

namespace FlexCharge.Common.Activities;

record ActivityDescription(
    Type EnumType,
    string? Category,
    ActivityInformationLevelFlags? InformationLevel,
    NotifyAttribute[]? Notifications,
    StatisticsAttribute[]? Statistics
)
{
    public static ActivityDescription Create<TActivityNameEnum>(TActivityNameEnum activityNameEnum)
        where TActivityNameEnum : Enum
    {
        var activityEnumType = typeof(TActivityNameEnum);

        var categoryAttribute = ReflectionHelpers
            .GetFirstAttributeOfTypeOrDefault<CategoryAttribute>(
                activityEnumType.GetCustomAttributes());

        var notifications = ReflectionHelpers.GetEnumValueAttributes<NotifyAttribute>(activityNameEnum);
        var statistics = ReflectionHelpers.GetEnumValueAttributes<StatisticsAttribute>(activityNameEnum);

        return new ActivityDescription(activityEnumType, categoryAttribute?.Category,
            GetInformationLevel(activityEnumType, categoryAttribute), notifications, statistics);
    }

    private static ActivityInformationLevelFlags? GetInformationLevel(Type activityEnumType,
        CategoryAttribute categoryAttribute)
    {
        ActivityInformationLevelFlags? informationLevel = null;

        FieldInfo activityNameEnumFieldInfo = activityEnumType.GetField(activityEnumType.ToString());
        InformationLevelAttribute informationLevelAttribute =
            activityNameEnumFieldInfo?.GetCustomAttribute<InformationLevelAttribute>();

        if (informationLevelAttribute != null)
        {
            informationLevel = informationLevelAttribute.InformationLevelFlags;
        }
        else
        {
            // if no attribute is found, use the default information level from the category
            informationLevel = categoryAttribute?.DefaultInformationLevelFlags;
        }

        return informationLevel;
    }
}