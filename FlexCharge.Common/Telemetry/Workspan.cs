using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Threading;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Logging;
using MassTransit;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Newtonsoft.Json;
using OpenTelemetry.Trace;
using Serilog.Core;
using Serilog.Events;

namespace FlexCharge.Common.Telemetry;

//see: https://jimmybogard.com/building-end-to-end-diagnostics-and-tracing-a-primer-trace-context/
//see: https://jimmybogard.com/building-end-to-end-diagnostics-activitysource-and-open/
//see: https://github.com/open-telemetry/opentelemetry-dotnet/blob/main/src/OpenTelemetry.Api/README.md#instrumenting-a-libraryapplication-with-net-activity-api
//see: https://github.com/open-telemetry/opentelemetry-dotnet/blob/main/examples/Console/TestOTelShimWithConsoleExporter.cs
//see: https://github.com/open-telemetry/opentelemetry-dotnet/blob/main/examples/MicroserviceExample/Utils/Messaging/MessageReceiver.cs

public class Workspan : IDisposable
{
    //private static readonly AssemblyName AssemblyName = typeof(Workspan).Assembly.GetName();
    //internal static readonly ActivitySource ActivitySource = new(AssemblyName.Name, AssemblyName.Version.ToString());

    private static ActivitySource ActivitySource { get; set; }

    private Activity _InternalActivity;

    //public static Workspan Current { get; private set; }
    protected string? Id => _InternalActivity?.Id;
    protected Workspan? Parent { get; private set; }

    protected string Name { get; private set; }

    private readonly string _callerClassName;
    private readonly string _callerFilePath;
    private readonly string _callerMemberName;
    private readonly int _callerLineNumber;

    private Action EndAction { get; set; }

    private IDisposable LoggerScope { get; set; }
    private Type LoggerCategoryType { get; set; }

    //see: Microsoft Activity.Current implementation
    private static readonly AsyncLocal<Workspan?> _Current = new();

    /// <summary>
    /// Gets or sets the current operation (Activity) for the current thread.  This flows
    /// across async calls.
    /// </summary>
    public static Workspan? Current
    {
        get { return _Current.Value; }
        set
        {
            if (ValidateSetCurrent(value))
            {
                SetCurrent(value);
            }
        }
    }

    #region Current Workspan Support

    private static bool ValidateSetCurrent(Workspan? workspan)
    {
        bool canSet = workspan == null || (workspan.Id != null && !workspan.IsFinished);
        if (!canSet)
        {
            NotifyError(new InvalidOperationException($"Can't set current {nameof(Workspan)}"));
        }

        return canSet;
    }

    public bool IsFinished { get; set; } = false;

    private static void NotifyError(Exception exception)
    {
        // Throw and catch the exception.  This lets it be seen by the debugger
        // ETW, and other monitoring tools.   However we immediately swallow the
        // exception.   We may wish in the future to allow users to hook this
        // in other useful ways but for now we simply swallow the exceptions.
        try
        {
            throw exception;
        }
        catch
        {
        }
    }

    private static void SetCurrent(Workspan? workspan)
    {
        _Current.Value = workspan;
    }

    #endregion

    private Workspan? _previousActiveWorkspan;

    public void Start()
    {
        _previousActiveWorkspan = Current;
        SetCurrent(this);
        if (Parent == null)
        {
            if (_previousActiveWorkspan != null)
            {
                // The parent change should not form a loop.   We are actually guaranteed this because
                // 1. Un-started activities can't be 'Current' (thus can't be 'parent'), we throw if you try.
                // 2. All started activities have a finite parent change (by inductive reasoning).
                Parent = _previousActiveWorkspan;
            }
        }

        AddBaggageToTags(this);
    }

    private void AddBaggageToTags(Workspan workspan)
    {
        if (workspan == null) return;

        if (workspan.AllBaggage != null)
        {
            foreach (var item in workspan.AllBaggage)
            {
                Tag(item.Key, item.Value);
            }
        }

        AddBaggageToTags(workspan.Parent);
    }

    private void Stop()
    {
        _InternalActivity?.Stop();

        if (!IsFinished)
        {
            IsFinished = true;

            SetCurrent(_previousActiveWorkspan);

            //AddBaggageToTags(this);
        }
    }

    // protected Workspan SetParentWorkspan(Workspan parentWorkspan)
    // {
    //     //Parent = parentWorkspan;
    //     _InternalActivity?.SetParentId(parentWorkspan.Id);
    //
    //     return this;
    // }
    //
    // public Workspan SetExternalParentWorkspanId(string parentWorkspanId)
    // {
    //     _InternalActivity?.SetParentId(parentWorkspanId);
    //
    //     return this;
    // }

    // public Workspan SetExternalParentWorkspanId(Guid parentWorkspanId)
    // {
    //     SetExternalParentWorkspanId(parentWorkspanId.ToString("N"));
    //
    //     return this;
    // }

    public static void Initialize(string serviceName, string? serviceVersion)
    {
        ActivitySource = new ActivitySource(serviceName, serviceVersion);
    }

    protected Workspan(string name, //Guid spanId,
        //Workspan? parentWorkspan,
        string callerFilePath, string callerClassName, string callerMemberName, int callerLineNumber)
    {
        Name = name;

        _callerFilePath = callerFilePath;
        _callerClassName = callerClassName;
        _callerMemberName = callerMemberName;
        _callerLineNumber = callerLineNumber;

        _InternalActivity = ActivitySource?.StartActivity(name);

        if (_InternalActivity == null)
        {
            Console.WriteLine("FATAL ERROR: Add services.AddTelemetry(); call to use Workspans in your application.");
#if DEBUG
            throw new Exception("Add services.AddTelemetry(); call to use Workspans in your application.");
#endif
        }


        //if (parentWorkspan != null) SetParentWorkspan(parentWorkspan);

        Start();
    }

    protected static Workspan StartInternal<TWorkspanOwner>(string name, //Workspan? parentWorkspan,
        string callerFilePath, string callerMemberName, int callerLineNumber)
    {
        var workspan = new Workspan(name, /*parentWorkspan,*/ callerFilePath, typeof(TWorkspanOwner).Name,
            callerMemberName, callerLineNumber)
        {
            LoggerCategoryType = typeof(TWorkspanOwner),
        };

        workspan.Tag("SourceCode",
            $"{workspan._callerClassName}.{workspan._callerMemberName}:{workspan._callerLineNumber}");

        return workspan;
    }

    public static Workspan Start<TWorkspanOwner>(string name = null,
        //Workspan? parentWorkspan = null,
        [CallerFilePath] string callerFilePath = null, [CallerMemberName] string callerMemberName = null,
        [CallerLineNumber] int callerLineNumber = -1)
    {
        var workspan =
            StartInternal<TWorkspanOwner>(name ?? callerMemberName, /*parentWorkspan,*/ callerFilePath,
                callerMemberName, callerLineNumber);

        // if (logEnterAndExit)
        // {
        //     workspan.Log.Enrich(callerFilePath, callerMemberName, callerLineNumber)
        //         .Information("ENTERED: {Workspan} > {Name}", name ?? typeof(TWorkspanOwner).Name,
        //             name ?? callerMemberName);
        //
        //     workspan.EndAction = () =>
        //     {
        //         workspan.Log
        //             .Enrich(callerFilePath, callerMemberName, callerLineNumber)
        //             .Information("EXIT: {Workspan} > {Name}", name ?? typeof(TWorkspanOwner).Name,
        //                 name ?? callerMemberName);
        //     };
        // }

        return workspan;
    }

    public static Workspan StartEndpoint<TWorkspanOwner>(ControllerBase controller, object? request,
        AppOptions globalData,
        //Guid? spanId = null,
        //Workspan? parentWorkspan = null,
        [CallerFilePath] string callerFilePath = null, [CallerMemberName] string callerMemberName = null,
        [CallerLineNumber] int callerLineNumber = -1)
    {
        string endpointDescription =
            $"{globalData.Name} => {controller.Request.Method} {controller.HttpContext.Request.Path + controller.HttpContext.Request.QueryString}";

        //var controllerName = controller.GetType().Name;

        var workspan = StartInternal<TWorkspanOwner>(endpointDescription, //parentWorkspan,
            callerFilePath, callerMemberName, callerLineNumber);


        workspan
            .Tag("Environment", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"))
            //see: https://stackoverflow.com/questions/55289631/inconsistent-behaviour-with-modelstate-validation-asp-net-core-api 
            .Tag("TraceId", Activity.Current?.Id ?? controller.HttpContext.TraceIdentifier)
            .Request(request)
            //.Tag("Body", controller.HttpContext.Request)
            .Log
            //.Enrich("Payload", request)
            .Information(
                $"ENTERED: {endpointDescription}", endpointDescription);

        workspan.EndAction = () =>
        {
            workspan.Log
                .Information(
                    $"EXITED: {endpointDescription}", endpointDescription);
        };

        return workspan;
    }

    public void End()
    {
        Stop();

        if (EndAction != null)
        {
            try
            {
                EndAction();
            }
            catch (Exception e)
            {
                Log.Error(e, $"EXCEPTION: {nameof(Workspan)} end action has thrown exception");
            }
        }

        LoggerScope?.Dispose();
        //Current = Parent;
    }

    public void Dispose()
    {
        End();
        _InternalActivity?.Dispose();
        _InternalActivity = null;
    }

    #region Logging Support

    #region Commented

    // public ILogger LogInformation => LogInternal(LogEventLevel.Information);
    //
    // public ILogger LogWarning => LogInternal(LogEventLevel.Warning);
    //
    // public ILogger LogError(Exception ex) => LogInternal(LogEventLevel.Error);
    //
    // public ILogger LogCritical(Exception ex) => LogInternal(LogEventLevel.Fatal);
    //
    // //public Microsoft.Extensions.Logging.ILogger LogTrace() => Log(LogEventLevel.Trace);
    // public ILogger LogVerbose => LogInternal(LogEventLevel.Verbose);
    // public ILogger LogDebug => LogInternal(LogEventLevel.Debug);
    //
    // private ILogger LogInternal(LogEventLevel logLevel, Exception? ex = null)
    // {
    //     //return new Logger(LogInternal(this)) {_logLevel = logLevel, _exception = ex};
    //     return LogInternal(this);
    // }

    // private static Serilog.ILogger LogInternal(Workspan workspan)
    // {
    //     //[{GetType().Name}.{callerMemberName}:{callerLineNumber}]
    //
    //     return Serilog.Log
    //         .ForContext(workspan.LoggerCategoryType)
    //         .ForContext(new WorkspanEnricher(workspan));
    //
    //     //.Log(logLevel, message + " [{categoryTypeName}.{callerMemberName}:{callerLineNumber}]", currentWorkspan._callerClassName, currentWorkspan._callerMemberName, currentWorkspan._callerLineNumber);
    // }

    #endregion

    public Serilog.ILogger Log
    {
        get
        {
            return Serilog.Log
                    .ForContext(this.LoggerCategoryType)
                //.ForContext(new WorkspanEnricher(this))
                ;

            //.Log(logLevel, message + " [{categoryTypeName}.{callerMemberName}:{callerLineNumber}]", currentWorkspan._callerClassName, currentWorkspan._callerMemberName, currentWorkspan._callerLineNumber);
        }
    }

    public Serilog.ILogger LogEligibility
    {
        get { return Log; }
    }

    public Serilog.ILogger LogSecurity
    {
        get { return Log; }
    }

    //public Serilog.ILogger Log => Serilog.Log.ForContext(new WorkspanEnricher(this));


    public class WorkspanEnricher : ILogEventEnricher
    {
        //see: https://jimmybogard.com/increasing-trace-cardinality-with-tags-and-baggage/

        //private WeakReference<Workspan> _Workspan;

        public WorkspanEnricher( /*Workspan workspan*/)
        {
            //_Workspan = new WeakReference<Workspan>(workspan);
        }

        public void Enrich(LogEvent logEvent,
            ILogEventPropertyFactory propertyFactory)
        {
            //var workspan = _Workspan.TryGetTarget(out var workspan);

            var workspan = Workspan.Current;

            if (workspan != null)
            {
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("CodeSource",
                    $"{workspan._callerClassName}.{workspan._callerMemberName}"));
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("CodeLine", workspan._callerLineNumber));

                if (workspan.AllBaggage != null)
                {
                    foreach (var (key, value) in workspan.AllBaggage)
                    {
                        logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty(key, value));
                    }
                }

                if (workspan.Tags != null)
                {
                    foreach (var (key, value) in workspan.Tags)
                    {
                        logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty(key, value));
                    }
                }
            }
        }
    }

    #endregion

    #region Attributes/Tags Support

    IEnumerable<KeyValuePair<string, string?>>? Tags => _InternalActivity?.Tags;

    ConcurrentDictionary<string, string?> _allBaggage;
    IDictionary<string, string?>? AllBaggage => _allBaggage;


    public Workspan Tag(string key, object? value)
    {
        if (_InternalActivity?.IsAllDataRequested == true)
        {
            // OpenTelemetry users should use SetTag and do not use other methods like AddTag, SetCustomProperty on Activity.
            //see: https://github.com/open-telemetry/opentelemetry-dotnet/blob/main/src/OpenTelemetry.Api/README.md#introduction-to-opentelemetry-net-tracing-api


            // Tags are not passed to child activities, but automatically included in logs 
            // see: https://jimmybogard.com/increasing-trace-cardinality-with-tags-and-baggage/
            _InternalActivity?.SetTag(key,
                value != null
                    ? JsonConvert.SerializeObject(value,
                        ObfuscationJsonSerializerSettings.Get(storeTypeInformation: false))
                    : "null");
        }

        return this;
    }

    public Workspan Tag(string key, string? value)
    {
        if (_InternalActivity?.IsAllDataRequested == true)
        {
            // OpenTelemetry users should use SetTag and do not use other methods like AddTag, SetCustomProperty on Activity.
            //see: https://github.com/open-telemetry/opentelemetry-dotnet/blob/main/src/OpenTelemetry.Api/README.md#introduction-to-opentelemetry-net-tracing-api


            // Tags are not passed to child activities, but automatically included in logs 
            // see: https://jimmybogard.com/increasing-trace-cardinality-with-tags-and-baggage/
            _InternalActivity?.SetTag(key, value);
        }

        return this;
    }

    public Workspan Tag(string key, Guid? value) =>
        Tag(key, value?.ToString());

    public Workspan Payload(object? payload) =>
        Tag("Payload", payload);

    public Workspan Request(object? request) =>
        Tag("Request", request);

    public Workspan Response(object? response) =>
        Tag("Response", response);

    public Workspan Message(object? message) =>
        Tag("Message",
            message != null
                ? JsonConvert.SerializeObject(message,
                    ObfuscationJsonSerializerSettings.Get(storeTypeInformation: false))
                : "null");

    public Workspan Context<T>(ConsumeContext<T> context) where T : class => Message(context?.Message);

    #region Baggage Support

    private void AddToBaggageInternal(string key, string? value)
    {
        // NOTE!!! DON'T USE _InternalActivity?.SetBaggage() - IT FORCES EXCEPTION (400 BAD REQUEST) IN HttpWebRequest
        if (_InternalActivity?.IsAllDataRequested == true)
        {
            if (_allBaggage == null)
            {
                Interlocked.CompareExchange(ref _allBaggage, new ConcurrentDictionary<string, string>(), null);
            }

            // No JsonSerialization here, because it's too heavy for baggage (so no obfuscation for objects needed)
            _allBaggage[key] = value;
        }
    }

    public Workspan Baggage(string key, object? value)
    {
        Tag(key, value);

        AddToBaggageInternal(key, value?.ToString());

        return this;
    }

    #region Baggage overloads are added to avoid boxing (creating objects for primitive types)

    public Workspan Baggage(string key, Guid? value)
    {
        Tag(key, value);

        AddToBaggageInternal(key, value?.ToString());

        return this;
    }

    public Workspan Baggage(string key, int? value)
    {
        Tag(key, value);

        AddToBaggageInternal(key, value?.ToString());

        return this;
    }

    public Workspan Baggage(string key, double? value)
    {
        Tag(key, value);

        AddToBaggageInternal(key, value?.ToString());

        return this;
    }

    public Workspan Baggage(string key, float? value)
    {
        Tag(key, value);

        AddToBaggageInternal(key, value?.ToString());

        return this;
    }

    public Workspan Baggage(string key, string? value)
    {
        Tag(key, value);

        AddToBaggageInternal(key, value);

        return this;
    }

    public Workspan Baggage(string key, bool? value)
    {
        Tag(key, value);

        AddToBaggageInternal(key, value.ToString());

        return this;
    }

    #endregion

    #endregion

    public Workspan Populate(Action<Workspan> populate)
    {
        populate(this);

        return this;
    }

    #endregion

    private void SetOKStatus(string? description = null)
    {
        _InternalActivity?.SetStatus(ActivityStatusCode.Ok, description);
    }

    private void SetErrorStatus(string? description = null)
    {
        _InternalActivity?.SetStatus(ActivityStatusCode.Error, description);
    }

    public void RecordEndpointCriticalApiError(Exception exception)
    {
        RecordException(exception, true, $"EXCEPTION: {Name} API ERROR");
    }

    public void RecordEndpointBadRequest(ModelStateDictionary modelState)
    {
        Log
            .Enrich("Model State",
                JsonConvert.SerializeObject(modelState,
                    ObfuscationJsonSerializerSettings.Get(storeTypeInformation: false)))
            .Warning("BAD REQUEST");

        //SetErrorStatus(errorMessage);
    }

    [MessageTemplateFormatMethod("messageTemplate")]
    public void AddEvent(string messageTemplate, params object[] propertyValues)
    {
        //Event is created automatically in our custom Seriog enricher (ActivityFromLogEnricher)
        Log.Information(messageTemplate, propertyValues);
    }

    #region Exceptions Handling

    [MessageTemplateFormatMethod("messageTemplate")]
    public void RecordException(Exception exception, string messageTemplate, params object[] propertyValues)
    {
        RecordException(exception, false, messageTemplate, propertyValues);
    }

    public void RecordException(Exception exception)
    {
        RecordException(exception, true);
    }

    public void RecordException(Exception exception, bool setWorkspanErrorStatus)
    {
        RecordException(exception, false, null);
    }

    [MessageTemplateFormatMethod("messageTemplate")]
    public void RecordException(Exception exception, bool setWorkspanErrorStatus, string messageTemplate,
        params object[] propertyValues)
    {
        _InternalActivity?.RecordException(exception);
        Log.Error(exception, messageTemplate ?? exception.Message, propertyValues);

        if (setWorkspanErrorStatus)
        {
            SetErrorStatus();
        }
    }

    [MessageTemplateFormatMethod("messageTemplate")]
    public void RecordFatalException(Exception exception, string messageTemplate, params object[] propertyValues)
    {
        RecordFatalException(exception, false, messageTemplate, propertyValues);
    }

    public void RecordFatalException(Exception exception)
    {
        RecordFatalException(exception, true);
    }

    public void RecordFatalException(Exception exception, bool setWorkspanErrorStatus)
    {
        RecordFatalException(exception, false, null);
    }

    [MessageTemplateFormatMethod("messageTemplate")]
    public void RecordFatalException(Exception exception, bool setWorkspanErrorStatus, string messageTemplate,
        params object[] propertyValues)
    {
        _InternalActivity?.RecordException(exception);
        Log.Fatal(exception, messageTemplate ?? exception.Message, propertyValues);

        if (setWorkspanErrorStatus)
        {
            SetErrorStatus();
        }
    }

    #endregion


    #region Errors Handling

    [MessageTemplateFormatMethod("messageTemplate")]
    public void AddError(string messageTemplate, params object[] propertyValues)
    {
        //Event is created automatically in our custom Seriog enricher (ActivityFromLogEnricher)
        //IN: Eligibility service > Evaluate => 
        Log.Error($"ERROR: {Name} => {messageTemplate}", propertyValues);
    }

    [MessageTemplateFormatMethod("messageTemplate")]
    public void RecordError(string messageTemplate, params object[] propertyValues)
    {
        SetErrorStatus();

        Log.Error($"ERROR: {Name} => {messageTemplate}", propertyValues);
    }

    #endregion

    #region [Commented] RecordExceptionAndThrow (Not used, because, in general and due to security reasons, log messages are more detailed than exception messages)

    // [DoesNotReturn]
    // public void RecordExceptionAndThrow<TException>(string exceptionMessage)
    //     where TException : Exception, new()
    // {
    //     var exception = new TException();
    //     
    //     RecordException(exception, true);
    //
    //     throw exception;
    // }
    //
    // [DoesNotReturn]
    // public void RecordExceptionAndThrow<TException>(string exceptionMessage = null, params object[] propertyValues)
    //     where TException : Exception, new()
    //  {
    //       var exception = new TException();
    //       RecordException(exception, true, exceptionMessage, propertyValues);
    //  
    //       throw exception;
    //  }

    #endregion

    public Workspan LogEnterAndExit()
    {
        Log.Enrich(_callerFilePath, _callerMemberName, _callerLineNumber)
            .Information("ENTERED: {Workspan} > {Name}", _callerClassName, Name);

        EndAction = () =>
        {
            Log
                .Enrich(_callerFilePath, _callerMemberName, _callerLineNumber)
                .Information("EXIT: {Workspan} > {Name}", _callerClassName, Name);
        };

        return this;
    }

    internal bool TryGetBaggage(string key, out string value)
    {
        value = null;

        return AllBaggage?.TryGetValue(key, out value) == true;
    }
}