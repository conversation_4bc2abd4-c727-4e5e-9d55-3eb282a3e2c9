using System;
using System.Collections.Generic;
using System.Linq;

namespace FlexCharge.Common.Telemetry.PerformanceCounters;

internal sealed class MetricsObservable : IMetricsObservable
{
    private readonly List<IObserver<MetricUpdate>> _observers;

    public MetricsObservable(IEnumerable<IObserver<MetricUpdate>> observers)
    {
        _observers = observers.ToList();
    }

    public IDisposable Subscribe(IObserver<MetricUpdate> observer)
    {
        lock (_observers)
            _observers.Add(observer);

        return new ActOnDispose(() =>
        {
            lock (_observers)
                _observers.Remove(observer);
        });
    }

    public void WriteMetric(ref MetricUpdate metricUpdate)
    {
        lock(_observers)
            foreach (var observer in _observers)
                observer.OnNext(metricUpdate);
    }

    public void Dispose()
    {
        lock (_observers)
        {
            foreach (var observer in _observers)
                observer.OnCompleted();

            _observers.Clear();
        }
    }

    private class ActOnDispose : IDisposable
    {
        private readonly Action _act;
        private bool _disposed = false;

        public ActOnDispose(Action act)
        {
            _act = act;
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            _act();
        }
    }
}