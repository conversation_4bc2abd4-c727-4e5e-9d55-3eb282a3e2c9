using System.Net.Http;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Http;

namespace FlexCharge.Common.Telemetry.HttpRequests;

public class RequestTelemetryInformation
{
    const int REQUEST_CONTENT_MAX_LENGTH_BYTES = 10 * 1024; //10KB
    const int RESPONSE_CONTENT_MAX_LENGTH_BYTES = 10 * 1024; //10KB
    public string Method { get; set; }
    [JsonPropertyName("Path")] public string? RequestPath { get; set; }
    public string Host { get; set; }
    public string? Query { get; set; }
    public string? Scheme { get; set; }
    public string? Protocol { get; set; }


    public string? Request { get; set; }
    public string? RequestContentType { get; set; }

    public string? Response { get; set; }
    public long LatencyInMs { get; set; }
    public string ResponseContentType { get; set; }
    public int ResponseStatusCode { get; set; }

    public string TraceId { get; set; }

    public static RequestTelemetryInformation Create(HttpContext context, string? requestContent, string? responseBody,
        long latencyInMs)
    {
        RequestTelemetryInformation info = new();
        info.Request = Truncate(requestContent, REQUEST_CONTENT_MAX_LENGTH_BYTES);
        info.RequestContentType = context.Request.ContentType;
        info.Response = Truncate(responseBody, RESPONSE_CONTENT_MAX_LENGTH_BYTES);
        info.LatencyInMs = latencyInMs;
        info.ResponseContentType = context.Response.ContentType;

        info.RequestPath = context.Request.Path.Value;
        info.Method = context.Request.Method;
        info.Query = context.Request.QueryString.Value;
        info.Host = context.Request.Host.Value;
        info.Scheme = context.Request.Scheme;
        info.Protocol = context.Request.Protocol;

        info.ResponseStatusCode = context.Response.StatusCode;

        //see: https://stackoverflow.com/questions/55289631/inconsistent-behaviour-with-modelstate-validation-asp-net-core-api
        info.TraceId = System.Diagnostics.Activity.Current?.Id ?? context.TraceIdentifier;

        return info;
    }

    public static RequestTelemetryInformation Create(
        HttpRequestMessage request, string requestContent,
        HttpResponseMessage response, string responseContent,
        long latencyInMs)
    {
        RequestTelemetryInformation info = new();

        info.Request = Truncate(requestContent, REQUEST_CONTENT_MAX_LENGTH_BYTES);
        //info.RequestContentType = request.ContentType;
        info.Response = Truncate(responseContent, RESPONSE_CONTENT_MAX_LENGTH_BYTES);
        info.LatencyInMs = latencyInMs;
        //info.ResponseContentType = response.ContentType;

        var requestUri = request.RequestUri;
        info.RequestPath = requestUri?.AbsolutePath;
        info.Method = request.Method.Method;
        info.Query = requestUri?.Query;
        info.Host = requestUri?.Host;
        info.Scheme = requestUri?.Scheme;
        //info.Protocol = requestUri?.Protocol;

        info.ResponseStatusCode = (int) response.StatusCode;

        return info;
    }

    static string Truncate(string value, int maxLength)
    {
        if (string.IsNullOrEmpty(value)) return value;
        return value.Length <= maxLength ? value : value.Substring(0, maxLength);
    }
}