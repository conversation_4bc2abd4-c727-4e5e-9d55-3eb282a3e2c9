using System;

namespace FlexCharge.Common.Telemetry.HttpRequests;

/// <summary>
/// Use this attribute to log public API telemetry for requests that contain sensitive data
/// marked by <see cref="FlexCharge.Common.SensitiveData.Obfuscation.SensitiveDataAttribute"/>
/// </summary>
public class SensitiveDataPublicApiTelemetryAttribute : PublicApiTelemetryAttribute
{
    private Type? _SensitiveRequestJsonType;
    private Type? _SensitiveResponseJsonType;

    internal override bool SensitiveDataInRequest => _SensitiveRequestJsonType != null;
    internal override Type? SensitiveRequestJsonType => _SensitiveRequestJsonType;

    internal override bool SensitiveDataInResponse => _SensitiveResponseJsonType != null;
    internal override Type? SensitiveResponseJsonType => _SensitiveResponseJsonType;

    public SensitiveDataPublicApiTelemetryAttribute(Type sensitiveRequestJsonType)
    {
        _SensitiveRequestJsonType = sensitiveRequestJsonType;
    }

    public SensitiveDataPublicApiTelemetryAttribute(Type? sensitiveRequestJsonType, Type? sensitiveResponseJsonType)
    {
        _SensitiveRequestJsonType = sensitiveRequestJsonType;
        _SensitiveResponseJsonType = sensitiveResponseJsonType;
    }
}