//see: https://michaelscodingspot.com/attributes-and-middleware-in-asp-net-core/

using System;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;

namespace FlexCharge.Common.Telemetry.HttpRequests
{
    public static class HttpRequestsTelemetryApplicationBuilderExtensions
    {
        /// <summary>
        /// Should be right before UseEndpoints to work correctly!!!
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static IApplicationBuilder UsePublicHttpRequestsTelemetry(this IApplicationBuilder builder)
        {
            builder.UseMiddleware<PublicApiTelemetryMiddleware>();

            return builder;
        }
    }

    public static class HttpContextAccessorExtensions
    {
        public static void AddCorrelation(this IHttpContextAccessor httpContextAccessor, Guid? tenantId,
            Guid? correlationId)
        {
            var httpContext = httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                if (tenantId != null)
                    httpContext.Items["TenantId"] = tenantId;

                if (correlationId != null)
                    httpContext.Items["CorrelationId"] = correlationId;
            }
        }
    }
}