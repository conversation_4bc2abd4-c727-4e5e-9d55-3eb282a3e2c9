// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Threading.Tasks;
// using FlexCharge.Common;
// using Microsoft.EntityFrameworkCore;
// using NUnit.Framework;
// using FlexCharge.Orders.Services.PayoutServices;
// using FlexCharge.Orders.Entities;
// using FlexCharge.Orders.Entities.Extensions;
// using FlexCharge.Common.Exceptions;
// using FlexCharge.Common.Types;
// using FlexCharge.Orders.DTO.Batches;
// using FlexCharge.Common.Shared.Payments;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Utils;
// using Microsoft.AspNetCore.Http;
// using Microsoft.Extensions.Configuration;
// using Microsoft.Extensions.DependencyInjection;
// using Moq;
//
// namespace FlexCharge.Orders.Tests.Services
// {
//     [TestFixture]
//     public class BatchServiceTests
//     {
//         private PostgreSQLDbContext _dbContext;
//         private ReadOnlyPostgreSQLDbContext _readOnlyDbContext;
//         private BatchService _service;
//         private Mock<IHttpContextAccessor> _mockHttpContextAccessor;
//
//
//         [SetUp]
//         public void Setup()
//         {
//             var testConfigData = new Dictionary<string, string>
//             {
//                 {"app:Name", "TestValue"},
//                 {"app:Version", "42"}
//                 // ... more keys if needed
//             };
//
//             // 2) Build an IConfiguration from this data
//             var configuration = new ConfigurationBuilder()
//                 .AddInMemoryCollection(testConfigData)
//                 .Build();
//
//             var services = new ServiceCollection();
//             services.AddSingleton<IConfiguration>(configuration);
//             services.Configure<AppOptions>(configuration.GetSection(AppOptions.SectionKey));
//
//             // 2) Register the required services
//             services.AddHttpContextAccessor(); // for IHttpContextAccessor
//             services.AddTelemetry(); // for IHttpContextAccessor
//
//             var serviceProvider = services.BuildServiceProvider();
//
//             _mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
//
//
//             // 1) Setup EF Core in-memory
//             var options = new DbContextOptionsBuilder<PostgreSQLDbContext>()
//                 .UseInMemoryDatabase(databaseName: "BatchServiceTestDb_" + Guid.NewGuid())
//                 .Options;
//
//             _dbContext = new PostgreSQLDbContext(options, _mockHttpContextAccessor.Object, serviceProvider);
//             // The read-only context can be the same for in-memory:
//             _readOnlyDbContext =
//                 new ReadOnlyPostgreSQLDbContext(options, _mockHttpContextAccessor.Object, serviceProvider);
//
//             // 2) Create the service under test
//             _service = new BatchService(_dbContext, _readOnlyDbContext);
//
//             // 3) Seed some sample data
//             SeedTestData();
//         }
//
//         [TearDown]
//         public void Teardown()
//         {
//             _dbContext.Database.EnsureDeleted();
//             _dbContext.Dispose();
//             _readOnlyDbContext.Dispose();
//         }
//
//         #region Sample Tests
//
//         [Test]
//         public void MerchantGetSettlementsAsync_EmptyReceiverId_ThrowsException()
//         {
//             // Arrange
//             var receiverId = Guid.Empty;
//
//             // Act & Assert
//             var ex = Assert.ThrowsAsync<FlexChargeException>(async () =>
//                 await _service.MerchantGetSettlementsAsync(receiverId, null));
//             Assert.That(ex.Message, Is.EqualTo("Invalid receiver id"));
//         }
//
//         [Test]
//         public async Task MerchantGetSettlementsAsync_ValidReceiver_ReturnsResult()
//         {
//             // Arrange
//             var receiverId = TestData.MerchantReceiver.Id;
//
//             // Act
//             var result = await _service.MerchantGetSettlementsAsync(receiverId, null);
//
//             // Assert
//             Assert.IsNotNull(result);
//             // We seeded one SUCCESS batch for that receiver
//             Assert.AreEqual(1, result.Count, "Expect exactly one settlement for this receiver.");
//             Assert.AreEqual(TestData.BatchSuccess.Id, result[0].PaymentRef,
//                 "PaymentRef should match the seeded SUCCESS batch's ID.");
//         }
//
//         [Test]
//         public async Task GetBatchByIdAsync_BatchExists_ReturnsBatch()
//         {
//             // Arrange
//             var existingBatchId = TestData.BatchUnprocessed.Id;
//
//             // Act
//             var batch = await _service.GetBatchByIdAsync(existingBatchId);
//
//             // Assert
//             Assert.IsNotNull(batch);
//             Assert.AreEqual(existingBatchId, batch.Id);
//         }
//
//         [Test]
//         public void GetBatchByIdAsync_BatchNotFound_ThrowsException()
//         {
//             // Arrange
//             var nonExistentId = Guid.NewGuid();
//
//             // Act & Assert
//             var ex = Assert.ThrowsAsync<FlexChargeException>(
//                 async () => await _service.GetBatchByIdAsync(nonExistentId));
//             Assert.That(ex.Message, Is.EqualTo("batch not found"));
//         }
//
//         [Test]
//         public async Task PostBatch_UnprocessedBatch_SetsIsPostedTrue()
//         {
//             // Arrange
//             var unprocessedBatchId = TestData.BatchUnprocessed.Id;
//
//             // Act
//             await _service.PostBatch(unprocessedBatchId);
//
//             // Assert
//             var updated = await _dbContext.Batches.FindAsync(unprocessedBatchId);
//             Assert.IsTrue(updated.IsPosted);
//         }
//
//         [Test]
//         public void PostBatch_BatchWithNegativePayout_ThrowsException()
//         {
//             // Arrange
//             var negativePayoutBatchId = TestData.BatchNegativePayout.Id;
//
//             // Act & Assert
//             var ex = Assert.ThrowsAsync<FlexValidationException>(async () =>
//                 await _service.PostBatch(negativePayoutBatchId));
//             Assert.That(ex.Message, Does.Contain("Payout amount is negative"));
//         }
//
//         #endregion
//
//         #region Helper Methods and Test Data
//
//         /// <summary>
//         /// Minimal seeding of data to test the batch service logic:
//         ///  - Some financial accounts for sender/receiver
//         ///  - Some batches with different statuses
//         /// </summary>
//         private void SeedTestData()
//         {
//             // 1) Create sample financial accounts
//             var senderAccount = new FinancialAccount
//             {
//                 Id = Guid.NewGuid(),
//                 RelatedEntityId = Guid.NewGuid(), // partner or something
//                 RelatedEntityType = FinancialAccountsRelatedEntityType.Partner,
//                 RelatedEntityDba = "Test Partner"
//             };
//
//             var receiverAccount = new FinancialAccount
//             {
//                 Id = Guid.NewGuid(),
//                 RelatedEntityId = Guid.NewGuid(), // merchant
//                 RelatedEntityType = FinancialAccountsRelatedEntityType.Merchant,
//                 RelatedEntityDba = "Test Merchant"
//             };
//
//             // 2) Create a SUCCESS batch
//             var successBatch = new Batch
//             {
//                 Id = Guid.NewGuid(),
//                 SenderId = senderAccount.Id,
//                 ReceiverId = receiverAccount.Id,
//                 PayoutStatus = nameof(BatchStatus.SUCCESS),
//                 TotalAmount = 10000,
//                 // ... other fields
//                 From = DateTime.UtcNow.AddDays(-7),
//                 To = DateTime.UtcNow.AddDays(-1),
//                 CreatedOn = DateTime.UtcNow.AddDays(-8),
//             };
//
//             // 3) Create an UNPROCESSED batch
//             var unprocessedBatch = new Batch
//             {
//                 Id = Guid.NewGuid(),
//                 SenderId = senderAccount.Id,
//                 ReceiverId = receiverAccount.Id,
//                 PayoutStatus = nameof(BatchStatus.UNPROCESSED),
//                 IsPosted = false,
//                 TotalAmount = 5000,
//                 From = DateTime.UtcNow.AddDays(-3),
//                 To = DateTime.UtcNow.AddDays(-2),
//                 CreatedOn = DateTime.UtcNow.AddDays(-4),
//             };
//
//             // 4) Create a negative payout batch (to test PostBatch negative check)
//             var negativePayoutBatch = new Batch
//             {
//                 Id = Guid.NewGuid(),
//                 SenderId = senderAccount.Id,
//                 ReceiverId = receiverAccount.Id,
//                 PayoutStatus = nameof(BatchStatus.UNPROCESSED),
//                 // e.g., total is smaller than fees, so payout is negative
//                 TotalAmount = 1000,
//                 FlexChargeFeesAmount = 1500,
//                 IsPosted = false,
//                 From = DateTime.UtcNow.AddDays(-3),
//                 To = DateTime.UtcNow.AddDays(-2),
//                 CreatedOn = DateTime.UtcNow.AddDays(-4),
//             };
//
//             // 5) Add them to the DB
//             _readOnlyDbContext.FinancialAccounts.Add(senderAccount);
//             _readOnlyDbContext.FinancialAccounts.Add(receiverAccount);
//             _readOnlyDbContext.Batches.AddRange(successBatch, unprocessedBatch, negativePayoutBatch);
//
//             _readOnlyDbContext.SaveChanges();
//
//             _dbContext.FinancialAccounts.Add(senderAccount);
//             _dbContext.FinancialAccounts.Add(receiverAccount);
//             _dbContext.Batches.AddRange(successBatch, unprocessedBatch, negativePayoutBatch);
//
//             _dbContext.SaveChanges();
//
//             // To reference them easily in tests, store in static or local fields
//             TestData.Sender = senderAccount;
//             TestData.MerchantReceiver = receiverAccount;
//             TestData.BatchSuccess = successBatch;
//             TestData.BatchUnprocessed = unprocessedBatch;
//             TestData.BatchNegativePayout = negativePayoutBatch;
//         }
//
//         /// <summary>
//         /// Helper container to hold references to seeded data for easy access in tests
//         /// </summary>
//         private static class TestData
//         {
//             public static FinancialAccount Sender;
//             public static FinancialAccount MerchantReceiver;
//             public static Batch BatchSuccess;
//             public static Batch BatchUnprocessed;
//             public static Batch BatchNegativePayout;
//         }
//
//         #endregion
//     }
// }

