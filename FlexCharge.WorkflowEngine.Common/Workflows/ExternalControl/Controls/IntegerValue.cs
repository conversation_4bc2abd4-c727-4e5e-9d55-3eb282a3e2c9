using Amazon.DynamoDBv2.Model;
using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl.Controls;

/// <summary>
/// Enabled by default, some functionality disabled when switched off
/// </summary>
public class IntegerValue : ExternalControlBase<int>
{
    public override ExternalControlType Type => ExternalControlType.IntegerValue;

    private int _defaultValue;

    public override int DefaultValue => _defaultValue;

    public IntegerValue(Guid id, string name, string description, string group, string subGroup,
        ExternalControlOwnerType ownerType, string ownerName, int defaultValue,
        int? minValue = null, int? maxValue = null
        //, Guid ownerId
    )
        : base(id, name, description, group, subGroup, ownerType, ownerName
            //, ownerId
        )
    {
        Name = name;
        Description = description;
        _defaultValue = defaultValue;

        MinValue = minValue;
        MaxValue = maxValue;
    }

    internal IntegerValue(ExternalControlDefinition controlDefinition) : this(
        controlDefinition.Id,
        controlDefinition.Name,
        controlDefinition.Description,
        controlDefinition.Group,
        controlDefinition.SubGroup,
        Enum.Parse<ExternalControlOwnerType>(controlDefinition.OwnerType),
        controlDefinition.OwnerName,
        int.Parse(controlDefinition.DefaultValue),
        controlDefinition.MinValue != null ? int.Parse(controlDefinition.MinValue) : null,
        controlDefinition.MaxValue != null ? int.Parse(controlDefinition.MaxValue) : null
    )
    {
    }


    public override bool IsValidValue()
    {
        if (Value == null)
            return true;

        if (MinValue != null && Value < MinValue)
            return false;

        if (MaxValue != null && Value > MaxValue)
            return false;

        return true;
    }
}