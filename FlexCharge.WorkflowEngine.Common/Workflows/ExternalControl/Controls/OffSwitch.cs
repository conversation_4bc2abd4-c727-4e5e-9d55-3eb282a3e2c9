using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl.Controls;

/// <summary>
/// Enabled by default, some functionality disabled when switched off
/// </summary>
public class OffSwitch : ExternalControlBase<bool>
{
    public override ExternalControlType Type => ExternalControlType.OffSwitch;

    public override bool DefaultValue => true; // On means not switched off!!!

    public OffSwitch(Guid id, string name, string description, string group, string subGroup,
        ExternalControlOwnerType ownerType, string ownerName
        //, Guid ownerId
    )
        : base(id, name, description, group, subGroup, ownerType, ownerName
            //, ownerId
        )
    {
        Name = name;
        Description = description;
    }

    internal OffSwitch(ExternalControlDefinition controlDefinition) : this(
        controlDefinition.Id,
        controlDefinition.Name,
        controlDefinition.Description,
        controlDefinition.Group,
        controlDefinition.SubGroup,
        Enum.Parse<ExternalControlOwnerType>(controlDefinition.OwnerType),
        controlDefinition.OwnerName
    )
    {
    }

    public bool IsSwitchedOff() => Enabled && (Value == false);

    public override bool IsValidValue() => true;
}