using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using FlexCharge.WorkflowEngine.Common.Workflows.Designer;
using FlexCharge.WorkflowEngine.Common.Workflows.Designer.Model;
using FlexCharge.WorkflowEngine.Services.WorkflowsService;
using FlexCharge.Utils;

namespace FlexCharge.WorkflowEngine.Workflows;

public partial class WorkflowDefinition
{
    public Common.Workflows.Designer.Model.WorkflowDefinition SerializeDesign()
    {
        Common.Workflows.Designer.Model.WorkflowDefinition workflowDefinition = new() { };
        
        workflowDefinition.Nodes = new ();
        workflowDefinition.Edges = new ();
        
        HashSet<int> alreadySerializedNodes = new();
        
        SerializeChainOfBlocks(StartBlock);
        
        // Process orphaned chains of blocks (not reachable from start block
        foreach (var block in GetOrphanedChainsOfBlocks())
        {
            SerializeChainOfBlocks(block);
        }
        
        return workflowDefinition;

        #region SerializeChainOfBlocks function

        void SerializeChainOfBlocks(BlockDefinition startBlock)
        {
            TraverseBlockDefinitionsGraph(startBlock,
                (block, parentBlock) =>
                {
                    // Orphaned chains can lead to separate ways of getting to the same block
                    if (!alreadySerializedNodes.Add(block.RefId))
                        return false;

                    alreadySerializedNodes.Add(block.RefId);
                    
                    workflowDefinition.Nodes.Add(block.SerializeDesign(parentBlock));
                    return true;
                },

                (block, link) => workflowDefinition.Edges.Add(link.SerializeDesign())
            );
        }

        #endregion
    }
    
    public static FlexCharge.WorkflowEngine.Workflows.WorkflowDefinition DeserializeDesign(
        Common.Workflows.Designer.Model.WorkflowDefinition definition)
    {
        FlexCharge.WorkflowEngine.Workflows.WorkflowDefinition workflowDefinition = new();


        BlockDefinition startBlock = null;
        SortedDictionary<int, BlockDefinition> blocks = new();
        foreach (Node node in definition.Nodes)
        {

           var nodeId = NodeIdParser.GetNodeId(node.Id);
           var blockDefinition = BlockDefinition.DeserializeDesign(node);
           blocks.Add(nodeId, blockDefinition);

           if (blockDefinition.Type == BlockType.Trigger)
           {
               if (node.ParentNode != null)
                     throw new Exception("Trigger block should not have parent node");
               
               if (startBlock != null)
                   throw new Exception("Multiple start blocks found");
               
               startBlock = blockDefinition;
           }
        }

        foreach (Edge edge in definition.Edges)
        {
            var data = edge.Data as JsonElement?;

            var sourceNodeName = edge.Source;
            var targetNodeName = edge.Target;

            if (sourceNodeName != null && targetNodeName != null)
            {
                var sourceNodeId = NodeIdParser.GetNodeId(sourceNodeName);
                var targetNodeId = NodeIdParser.GetNodeId(targetNodeName);

                var sourceBlock = blocks[sourceNodeId];

                var linkDefinition = LinkDefinition.Create(
                    edge.Id,
                    edge.Label,
                    sourceBlock, 
                    NodeHandleIdParser.GetConnectorIndex(edge.SourceHandle),
                    blocks[targetNodeId],
                    NodeHandleIdParser.GetConnectorIndex(edge.TargetHandle));

                if (sourceBlock.Type == BlockType.Condition)
                {
                    // Temporary solution - text should come from block implementation and can be set/updated dynamically
                    // on block's parameter change
                    linkDefinition.Text = linkDefinition.SourceConnectorIndex == 0 ? "True" : "False";
                }

                sourceBlock.AddLink(linkDefinition);
            }

            // blocks.Add(new BlockDefinition()
            // {
            //     
            //     RefId = NodeIdParser.GetNodeId(edge.Id),
            //     Type = BlockTypeToNodeTypeConverter.GetBlockType(edge.Type),
            //     Name = data.GetPropertyAsStringOrDefault("label"),
            //     FullName = data.GetPropertyAsStringOrDefault("fullName"),
            //     Icon = data.GetPropertyAsStringOrDefault("icon"),
            // });
        }
        
        workflowDefinition.Blocks = new();

        if (startBlock != null)
        {
            //Start block should always be the first one
            workflowDefinition.Blocks.Add(startBlock);
        }

        foreach (Node node in definition.Nodes)
        {
            var nodeId = NodeIdParser.GetNodeId(node.Id);
            var blockDefinition = blocks[nodeId];

            // Start block has been already added
            if (blockDefinition == startBlock)
                continue;
            
            if (node.ParentNode == null)
            {
                workflowDefinition.Blocks.Add(blockDefinition);
            }
            else
            {
                var parentBlockDefinition = blocks[NodeIdParser.GetNodeId(node.ParentNode)];
                
                if (parentBlockDefinition == null)
                    throw new Exception($"Parent block with ID {node.ParentNode} not found");

                if (parentBlockDefinition.ChildBlocks == null)
                    parentBlockDefinition.ChildBlocks = new();

                parentBlockDefinition.ChildBlocks.Add(blockDefinition);
            }
        }
        
        return workflowDefinition;
    }
}

