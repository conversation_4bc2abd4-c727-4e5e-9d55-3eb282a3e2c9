using Newtonsoft.Json;

namespace FlexCharge.WorkflowEngine.Workflows;

public partial class LinkDefinition
{
    public string Name { get; set; }

    /// <summary>
    /// To avoid serializing of nested objects, this ID is used during serialization instead of direct reference to Block
    /// </summary>
    public int TargetRefId { get; set; }

    [JsonIgnore] public BlockDefinition SourceBlock { get; set; }
    public int SourceConnectorIndex { get; set; }

    [JsonIgnore] public BlockDefinition TargetBlock { get; set; }
    public int TargetConnectorIndex { get; set; }
    public string Text { get; set; }


    public static LinkDefinition Create(
        string linkName, string linkText,
        BlockDefinition sourceBlockDefinition,
        int sourceConnectorIndex,
        BlockDefinition targetBlockDefinition,
        int targetConnectorIndex)
    {
        return new()
        {
            Name = linkName,
            Text = linkText,
            //SourceRefId = blockDefinition.RefId,

            // This is for deserialization purposes, as SourceBlock and TargetBlock are not serialized
            // to avoid nested Json hierarchy
            TargetRefId = targetBlockDefinition.RefId,

            SourceBlock = sourceBlockDefinition,
            SourceConnectorIndex = sourceConnectorIndex,

            TargetBlock = targetBlockDefinition,
            TargetConnectorIndex = targetConnectorIndex,
        };
    }
}