namespace FlexCharge.WorkflowEngine.Services.WorkflowsService;

public class NodeHandleIdParser
{
    public static int GetConnectorIndex(string nodeHandle)
    {
        if (string.IsNullOrWhiteSpace(nodeHandle))
            return 0;

        if (nodeHandle.Length > 1)
            throw new Exception("Invalid node handle: " + nodeHandle);

        // a - 0, b-1, ...
        int connectorIndex = nodeHandle[0] - 'a';

        return connectorIndex;
    }

    public static string GetHandleId(int connectorIndex)
    {
        if (connectorIndex < 0 || connectorIndex > 25)
            throw new Exception("Invalid connector index: " + connectorIndex);

        // a - 0, b-1, ...
        char handleId = (char) ('a' + connectorIndex);

        return handleId.ToString();
    }
}