namespace FlexCharge.WorkflowEngine.Workflows;

public partial class BlockDefinition
{
    /// <summary>
    /// This ID is not stable and should be used for serialization/deserialization purposes only
    /// </summary>
    public int RefId { get; set; }

    public string Name { get; set; }
    public string FullName { get; set; }
    public string? InstanceName { get; set; }


    public string Icon { get; set; }

    public BlockType Type { get; set; } = BlockType.Default;

    public Dictionary<string, string>? Parameters { get; set; }

    public BlockLayout? Layout { get; set; }

    public List<LinkDefinition> Links { get; set; }
    public List<BlockDefinition> ChildBlocks { get; set; }

    public void AddLink(LinkDefinition link)
    {
        if (Links == null)
            Links = new();

        Links.Add(link);
    }

    public List<ExternalControlDefinition> ExternalControls { get; set; }

    public class BlockLayout
    {
        public float? X { get; set; }
        public float? Y { get; set; }
        public float? Width { get; set; }
        public float? Height { get; set; }
    }
}