using FlexCharge.Contracts.Commands.Workflows;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;

namespace FlexCharge.WorkflowEngine.Workflows;

public record ExternalControlDefinition
{
    public string Type { get; set; }

    public Guid Id { get; set; }

    public bool Enabled { get; set; }

    public string Name { get; set; }
    public string Description { get; set; }

    public string Group { get; set; }
    public string SubGroup { get; set; }

    public string OwnerType { get; set; }
    public string OwnerName { get; set; }

    public string DefaultValue { get; set; }
    public string? MinValue { get; set; }
    public string? MaxValue { get; set; }

    public static ExternalControlDefinition Create(IExternalControl externalControl)
    {
        return new ExternalControlDefinition
        {
            Type = externalControl.Type.ToString(),
            Id = externalControl.Id,
            Enabled = externalControl.Enabled,
            Name = externalControl.Name,
            Description = externalControl.Description,
            Group = externalControl.Group,
            SubGroup = externalControl.SubGroup,
            OwnerType = externalControl.OwnerType.ToString(),
            OwnerName = externalControl.OwnerName,
            DefaultValue = externalControl.SerializeDefaultValue(),
            MinValue = externalControl.SerializeMinValue(),
            MaxValue = externalControl.SerializeMaxValue(),
        };
    }

    public static ExternalControlDefinition Create(
        GetWorkflowExternalControlsCommandResponse.ExternalControlDefinition controlDefinition)
    {
        return new ExternalControlDefinition
        {
            Type = controlDefinition.Type,
            Id = controlDefinition.Id,
            Name = controlDefinition.Name,
            Description = controlDefinition.Description,
            Group = controlDefinition.Group,
            SubGroup = controlDefinition.SubGroup,
            OwnerType = controlDefinition.OwnerType,
            OwnerName = controlDefinition.OwnerName,
            DefaultValue = controlDefinition.DefaultValue,
            MinValue = controlDefinition.MinValue,
            MaxValue = controlDefinition.MaxValue,
        };
    }
}