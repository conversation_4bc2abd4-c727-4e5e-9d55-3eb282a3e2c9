using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FlexCharge.WorkflowEngine.Entities
{
    public interface IEntity
    {
        Guid Id { get; set; }
        bool IsDeleted { get; set; }
    }

    public interface IAuditableEntity : IEntity
    {
        DateTime CreatedOn { get; set; }
        DateTime ModifiedOn { get; set; }
        string CreatedBy { get; set; }
        string ModifiedBy { get; set; }
    }

    public class Entity : IEntity
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Key]
        public Guid Id { get; set; }

        public bool IsDeleted { get; set; }
    }

    public class AuditableEntity : Entity, IAuditableEntity
    {
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
        public DateTime ModifiedOn { get; set; } = DateTime.UtcNow;
        public string CreatedBy { get; set; }
        public string ModifiedBy { get; set; }
    }

    /// <summary>
    /// Adds Version column for RowVersion concurrent update check
    /// </summary>
    /// <remarks>
    /// see: https://www.npgsql.org/efcore/modeling/concurrency.html?tabs=data-annotations
    /// see: https://learn.microsoft.com/en-us/ef/core/saving/concurrency?tabs=data-annotations
    /// see: http://www.kamilgrzybek.com/design/handling-concurrency-aggregate-pattern-and-ef-core/
    /// </remarks>
    public class VersionedEntity : AuditableEntity
    {
        [Timestamp] public uint Version { get; set; }
    }
}