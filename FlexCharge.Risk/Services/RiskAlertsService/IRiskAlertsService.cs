using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Risk.DTO;

namespace FlexCharge.Risk.Services;

public interface IRiskAlertsService
{
    /// <summary>
    /// Retrieves a list of triggered rules from Redshift.
    /// </summary>
    Task<List<TriggeredRuleDTO>> GetTriggeredRulesAsync(int top = 1);

    /// <summary>
    /// Sends a notification to the appropriate channel.
    /// </summary>
    /// <param name="triggeredRule"></param>
    /// <returns></returns>
    Task SendNotificationAsync(TriggeredRuleDTO triggeredRule);

    /// <summary>
    /// Sends a notification to the appropriate channel.
    /// </summary>
    /// <param name="triggeredRules"></param>
    /// <returns></returns>
    Task SendNotificationAsync(List<TriggeredRuleDTO> triggeredRules);

    /// <summary>
    /// Executes risk alert queries to update triggered rules.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task RunRiskAlertQueriesAsync();
}