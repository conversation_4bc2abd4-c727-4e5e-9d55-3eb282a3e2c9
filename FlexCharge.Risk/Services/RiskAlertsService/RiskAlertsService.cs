using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Cloud.SecretsManager;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Telemetry;
using FlexCharge.Risk.DTO;
using Npgsql;

namespace FlexCharge.Risk.Services;

public class RiskAlertsService : IRiskAlertsService
{
    private readonly IEmailSender _emailSender;
    private readonly ISecretsManager _awsSecretManager;

    public RiskAlertsService(
        IEmailSender emailSender,
        ISecretsManager awsSecretManager)
    {
        _emailSender = emailSender;
        _awsSecretManager = awsSecretManager;
    }


    /// <summary>
    /// Retrieves a list of triggered rules from Redshift.
    /// </summary>
    /// <param name="top"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task<List<TriggeredRuleDTO>> GetTriggeredRulesAsync(int top = 1)
    {
        using var workspan = Workspan.Start<RiskAlertsService>();

        var triggeredRules = new List<TriggeredRuleDTO>();

        var connectionString =
            await _awsSecretManager.GetSecretValueAsync(
                Environment.GetEnvironmentVariable("REDSHIFT_CONNECTION_STRING"));

        using (var conn = new NpgsqlConnection(connectionString))
        {
            await conn.OpenAsync();

            // Use parameterized queries if you pass user inputs
            string query = @"
                SELECT 
                    insert_date,
                    triggered_date,
                    merchant_id,
                    merchant_name,
                    alert_name,
                    alert_description,
                    alert_id
                FROM risk_department.public.triggered_rules
                WHERE snoozed_flag = 'No' 
                    AND insert_date = current_date
                --LIMIT @Top;
            ";

            try
            {
                using (var cmd = new NpgsqlCommand(query, conn))
                {
                    //cmd.Parameters.AddWithValue("@InsertDate", "");
                    //cmd.Parameters.AddWithValue("@Top", top);

                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var rule = new TriggeredRuleDTO
                            {
                                InsertDate = reader.GetDateTime(0),
                                TriggeredDate = reader.GetDateTime(1),
                                MerchantId = reader.GetString(2),
                                MerchantName = reader.GetString(3),
                                AlertName = reader.GetString(4),
                                AlertDescription = reader.GetString(5),
                                AlertId = reader.GetString(6)
                            };

                            triggeredRules.Add(rule);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "Error executing query: {Query}", query);
                throw;
            }
        }

        workspan.Log.Information($"Retrieved {triggeredRules.Count} triggered rule(s) from Redshift.");
        return triggeredRules;
    }

    /// <summary>
    /// Sends a notification to the appropriate channel.
    /// </summary>
    /// <param name="alert"></param>
    public async Task SendNotificationAsync(TriggeredRuleDTO alert)
    {
        using var workspan = Workspan.Start<RiskAlertsService>()
            .Baggage("MerchantId", alert.MerchantId)
            .Baggage("MerchantName", alert.MerchantName)
            .Baggage("AlertName", alert.AlertName)
            .LogEnterAndExit();

        var data = new
        {
            InsertDate = alert.InsertDate,
            TriggeredDate = alert.TriggeredDate,
            MerchantId = alert.MerchantId,
            MerchantName = alert.MerchantName,
            AlertName = alert.AlertName,
            Subject = $"{alert.MerchantId} - {alert.MerchantName}"
        };

        workspan.Log.Information(
            "Sending email notification to: <EMAIL> about {MerchantId} - {MerchantName} for alert {AlertName}",
            alert.MerchantId, alert.MerchantName, alert.AlertName);

        await _emailSender.SendEmailAsync("<EMAIL>",
            $"{alert.MerchantId} - {alert.MerchantName}", "Test",
            data, "d-249054dbd8714c3393aee8c754987fe5");
    }

    /// <summary>
    /// Sends a notification to the appropriate channel.
    /// </summary>
    /// <param name="alert"></param>
    public async Task SendNotificationAsync(List<TriggeredRuleDTO> alerts)
    {
        foreach (var alert in alerts)
        {
            await SendNotificationAsync(alert);
        }
    }

    /// <summary>
    /// Executes risk alert queries to update triggered rules.
    /// </summary>
    public async Task RunRiskAlertQueriesAsync()
    {
        using var workspan = Workspan.Start<RiskAlertsService>();
        workspan.Log.Information("Starting execution of risk alert queries.");

        try
        {
            var connectionString =
                await _awsSecretManager.GetSecretValueAsync(
                    Environment.GetEnvironmentVariable("REDSHIFT_CONNECTION_STRING"));

            using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();

            var queries = TriggeredRulesQueries.GetAllQueries();

            foreach (var query in queries)
            {
                try
                {
                    using var command = new NpgsqlCommand(query, connection);
                    command.CommandTimeout = 180;

                    await command.ExecuteNonQueryAsync();
                    workspan.Log.Information("Successfully executed query: {Query}",
                        query.Substring(0, Math.Min(query.Length, 100))); // Log first 100 chars
                }
                catch (Exception ex)
                {
                    workspan.Log.Error(ex, "Error executing query: {Query}",
                        query.Substring(0, Math.Min(query.Length, 100)));
                }
            }

            workspan.Log.Information("Completed execution of risk alert queries.");
        }
        catch (Exception ex)
        {
            workspan.Log.Error(ex, "Critical failure in executing risk alert queries.");
        }
    }
}