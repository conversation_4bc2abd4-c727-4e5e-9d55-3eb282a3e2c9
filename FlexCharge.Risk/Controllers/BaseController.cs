using FlexCharge.Common.Authentication;
using FlexCharge.Common.Response;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;

namespace FlexCharge.Risk.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class BaseController : ControllerBase
    {
        protected Guid UserId
            => string.IsNullOrWhiteSpace(User?.Identity?.Name) ?
                Guid.Empty :
                Guid.Parse(User.Identity.Name);

        protected Guid GetMID()
        {
            var IsValidMid = Guid.TryParse(HttpContext.User.Claims.Where(x => x.Type == MyClaimTypes.MERCHANT_ID)
                .Select(x => x.Value).SingleOrDefault(), out Guid mid);

            if (IsValidMid)
                return mid;

            return mid;
        }

        protected Guid GetPID()
        {
            var IsValidPid = Guid.TryParse(HttpContext.User.Claims.Where(x => x.Type == MyClaimTypes.PARTNER_ID)
                .Select(x => x.Value).SingleOrDefault(), out Guid pid);

            if (IsValidPid)
                return pid;

            return pid;
        }

        protected IActionResult ReturnResponse<T>(T response) where T : BaseResponse
        {
            if (!response.Success)
            {
                if (response.CustomProperties.Any())
                    response.CustomProperties.ToList().ForEach(x => ModelState.AddModelError(x.Key, x.Value));

                response.Errors.Where(x => x.FriendlyError).ToList().ForEach(x => ModelState.AddModelError(x.ErrorCode, x.Error));

                var problemDetails = new ValidationProblemDetails(ModelState)
                {
                    Status = StatusCodes.Status400BadRequest,
                };
                problemDetails.Extensions["traceId"] = HttpContext.TraceIdentifier;

                return BadRequest(problemDetails);
            }
            else
                return Ok(response);
        }
    }
}