using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands.Risk;
using FlexCharge.Risk.Services;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Risk.Consumers;

public class ExecuteRiskTriggeredAlertsCommandConsumer : CommandConsumer<ExecuteRiskTriggeredAlertsCommand,
    ExecuteRiskTriggeredAlertsCommandResponse>
{
    private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;

    public ExecuteRiskTriggeredAlertsCommandConsumer(
        IServiceScopeFactory serviceScopeFactory,
        IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue) : base(serviceScopeFactory)
    {
        _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
    }

    protected override async Task<ExecuteRiskTriggeredAlertsCommandResponse> ConsumeCommand(
        ExecuteRiskTriggeredAlertsCommand command, CancellationToken cancellationToken)
    {
        try
        {
            _backgroundWorkerCommandQueue.Enqueue(new SendMerchantRiskAlertsCommand());
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);
        }

        return new ExecuteRiskTriggeredAlertsCommandResponse
        {
            Success = true
        };
    }
}