// using System;
// using System.IO;
// using System.Linq;
// using System.Linq.Expressions;
// using System.Threading.Tasks;
// using FlexCharge.Payments.Entities;
// using FlexCharge.Payments.Services.Pay;
// using FlexCharge.Payments.Services.PaymentServices;
// using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
// using FlexCharge.Payments.Services.SVBAchServices.Models;
// using Microsoft.AspNetCore.Http;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Configuration;
// using Moq;
// using NUnit.Framework;
//
// namespace FlexCharge.Payments.Tests;
//
// [TestFixture]
// public class SVB_ACH_Integration_Tests
// {
//     private Mock<PostgreSQLDbContext> _dbContext;
//     private Mock<PaymentOrchestrator> _PaymentOrchestrator;
//     private Mock<IHttpContextAccessor> _httpContextAccessorMock;
//
//     public IConfiguration _config { get; set; }
//
//     [SetUp]
//     public void Setup()
//     {
//         _config = new ConfigurationBuilder()
//             .SetBasePath(Directory.GetCurrentDirectory())
//             .AddJsonFile(@"appsettings.Staging.json", false, false)
//             .AddEnvironmentVariables().Build();
//
//         _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
//
//         _dbContext = new Mock<PostgreSQLDbContext>(_config, _httpContextAccessorMock.Object);
//         _PaymentOrchestrator = new Mock<PaymentOrchestrator>(_dbContext.Object);
//         
//         // Create a list of Gateways
//         var gateways = new List<Gateway>
//         {
//             new Gateway { Id = Guid.NewGuid(), IsSandbox = true, Name = GatewayTypesConstants.Svb },
//             // Add more gateways as needed
//         };
//
//         // Mock the Gateways DbSet
//         _dbContext.Setup(x => x.Gateways).ReturnsDbSet(gateways);
//
//     }
//
//     [Test]
//     public async Task ACH_DEBIT_Test()
//     {
//         Guid mid = Guid.NewGuid();
//
//         var gateway =
//             await _dbContext.Object.Gateways.FirstOrDefaultAsync(x =>
//                 x.Merchant.Mid == mid && x.IsSandbox && x.Name == GatewayTypesConstants.Svb);
//
//         var orderId = Guid.NewGuid();
//         var payerId = Guid.NewGuid();
//
//         var res = await _PaymentOrchestrator.Object.AchDebitAsync(new AchTransferRequest
//         {
//             Mid = mid,
//             Gateway = gateway,
//             OrderId = orderId,
//             PayerId = payerId,
//             Amount = 150,
//             FeeAmount = 0,
//             CurrencyCode = "USD",
//             Currency = "USD",
//             UseDynamicDescriptor = true,
//             RetainInstrument = true,
//             IsVerifiedAch = false,
//             IdentificationNumber = "000015234AB",
//             Processor = GatewayTypesConstants.Svb,
//             Provider = OpenBankingProviders.Plaid,
//             ProviderId = gateway.Id,
//             CompanyEntryDescription = "FLX ACH",
//             EffectiveEntryDate = DateTime.Now.Date.ToString("yyyy-MM-dd"),
//             SecType = SecCodeEnum.PPD,
//             Sender = new AchAccountDTO
//             {
//                 Id = default,
//                 Name = "FlexCharge",
//                 InstitutionName = "SVB",
//                 AccountType = AccountTypeEnum.CHECKING,
//                 AccountNumber = "**********",
//                 RoutingNumber = "*********",
//             },
//             Receiver = new AchAccountDTO
//             {
//                 Id = default,
//                 Name = "Michael Clarke",
//                 InstitutionName = "SVB",
//                 AccountType = AccountTypeEnum.CHECKING,
//                 AccountNumber = "**********",
//                 RoutingNumber = "*********",
//             }
//         });
//
//         Assert.IsNotNull(res);
//         Assert.AreEqual(100, res.ProviderResponseCode);
//         Assert.AreEqual(true, res.Success);
//
//         // Add more assertions based on the expected result
//     }
// }