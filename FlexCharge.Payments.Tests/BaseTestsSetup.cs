using System;
using System.Collections.Generic;
using FlexCharge.Common;
using FlexCharge.Common.Telemetry;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;

namespace FlexCharge.Payments.Tests;

[TestFixture]
public abstract class BaseTestsSetup
{
    protected Mock<IHttpContextAccessor> MockHttpContextAccessor;
    protected IServiceProvider ServiceProvider;

    [SetUp]
    public virtual void Setup()
    {
        var testConfigData = new Dictionary<string, string>
        {
            {"app:Name", "TestValue"},
            {"app:Version", "42"}
        };

        // Build IConfiguration
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(testConfigData)
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.Configure<AppOptions>(configuration.GetSection(AppOptions.SectionKey));

        // Add necessary services
        services.AddHttpContextAccessor();
        services.AddTelemetry(); // Ensure Telemetry is included for Workspans

        ServiceProvider = services.BuildServiceProvider();

        MockHttpContextAccessor = new Mock<IHttpContextAccessor>();
    }
}