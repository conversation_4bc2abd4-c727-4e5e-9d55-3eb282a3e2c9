using System;
using System.Collections.Generic;
using System.Linq;
using FlexCharge.Common;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Entities.JsonbModels;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Tests;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;

namespace FlexCharge.Tests
{
    [TestFixture]
    public class SupportedGatewayFilterHelperTests : BaseTestsSetup
    {
        private List<SupportedGateway> _gateways;
        private OrchestrationOptions _options;

        private Mock<IHttpContextAccessor> _mockHttpContextAccessor;

        [SetUp]
        public override void Setup()
        {
            SeedDefaultGateways();
            _options = new OrchestrationOptions();

            base.Setup();
        }

        private void SeedDefaultGateways()
        {
            _gateways = new List<SupportedGateway>
            {
                new SupportedGateway
                {
                    NameIdentifier = "GatewayA",
                    IsActive = true,
                    Capabilities = new CapabilitiesModel
                    {
                        SupportLoadBalancing = true,
                        SupportedCountries = "US,CA",
                        SupportedCurrencies = "USD,EUR",
                        CardBrands = "Visa,MasterCard",
                        SupportCIT = true,
                        SupportMIT = true,
                        MasterCard_MonthlyMaxTransactionsCount = 500,
                        MasterCard_MonthlyMaxValue = 100000,
                        Visa_MonthlyMaxTransactionsCount = 300,
                        Visa_MonthlyMaxValue = 50000,
                        TotalDailyMaxTransactionsValue = 30
                    },
                    Metrics = new CycleMetrics
                    {
                        MasterCardCurrentTransactionsCount = 100,
                        MasterCardCurrentTransactionsValue = 20000,
                        VisaCurrentTransactionsCount = 50,
                        VisaCurrentTransactionsValue = 10000,
                        Quarantine_AddedOn = null // Not quarantined
                    }
                },
                new SupportedGateway
                {
                    NameIdentifier = "GatewayB",
                    IsActive = true,
                    Capabilities = new CapabilitiesModel
                    {
                        SupportLoadBalancing = true,
                        SupportedCountries = "UK,DE",
                        SupportedCurrencies = "GBP,EUR",
                        CardBrands = "MasterCard",
                        SupportCIT = false,
                        SupportMIT = false
                    },
                    Metrics = new CycleMetrics
                    {
                        Quarantine_AddedOn = DateTime.UtcNow.AddHours(-6) // Recently quarantined
                    }
                }
            };
        }

        [TestCase(null, true)]
        [TestCase("US", true)]
        [TestCase("CA", true)]
        [TestCase("UK", false)]
        [TestCase("FR", false)]
        public void FilterGateways_CountryFiltering(string country, bool expected)
        {
            _options.Country = country;
            var result = SupportedGatewayFilterHelper.FilterGateways(_gateways, _options);
            Assert.AreEqual(expected, result.SupportedGateways.Any());
        }

        [TestCase("Visa", true)]
        [TestCase("MasterCard", true)]
        [TestCase("Amex", false)]
        public void FilterGateways_CardBrandFiltering(string cardBrand, bool expected)
        {
            _options.Brand = (CardBrand) Enum.Parse(typeof(CardBrand), cardBrand);
            var result = SupportedGatewayFilterHelper.FilterGateways(_gateways, _options);

            Assert.AreEqual(expected, result.SupportedGateways.Any());
        }

        [TestCase("USD", true)]
        [TestCase("EUR", true)]
        [TestCase("GBP", false)]
        public void FilterGateways_CurrencyFiltering(string currency, bool expected)
        {
            _options.Currency = currency;
            var result = SupportedGatewayFilterHelper.FilterGateways(_gateways, _options);

            //write a log with the results
            foreach (var gateway in result.SupportedGateways)
                Console.WriteLine($"Gateway: {gateway.NameIdentifier}");

            Assert.AreEqual(expected, result.SupportedGateways.Any());
        }

        [TestCase(null, true)]
        [TestCase(true, true)]
        [TestCase(false, true)]
        public void FilterGateways_CIT_MIT_Support(bool? isCit, bool expected)
        {
            _options.IsCIT = isCit;
            var result = SupportedGatewayFilterHelper.FilterGateways(_gateways, _options);
            Assert.AreEqual(expected, result.SupportedGateways.Any());
        }

        [TestCase(200, 40000, true)]
        [TestCase(1600, 220000, false)] // Exceeds count limit
        [TestCase(600, 220000, false)] // Exceeds value limit
        public void FilterGateways_MonthlyTransactionLimits(int transactionCount, int transactionValue, bool expected)
        {
            //set the values for the first gateway
            _gateways[0].Capabilities.TotalMonthlyMaxTransactionsCount = 1000;
            _gateways[0].Capabilities.TotalMonthlyMaxTransactionsValue = 200000;

            // set metrics 
            _gateways[0].Metrics.TotalTransactionCount = transactionCount;
            _gateways[0].Metrics.TotalTransactionAmount = transactionValue;

            var result = SupportedGatewayFilterHelper.FilterGateways(_gateways, _options);

            Assert.AreEqual(expected, result.SupportedGateways.Any());
        }

        [TestCase(100, 20000, 50, 10000, true)]
        [TestCase(600, 120000, 400, 80000, false)] // Exceeds MC & Visa limits
        public void FilterGateways_CurrentTransactionLimits(
            int mcCount, int mcValue, int visaCount, int visaValue, bool expected)
        {
            _gateways[0].Metrics.MasterCardCurrentTransactionsCount = mcCount;
            _gateways[0].Metrics.MasterCardCurrentTransactionsValue = mcValue;
            _gateways[0].Metrics.VisaCurrentTransactionsCount = visaCount;
            _gateways[0].Metrics.VisaCurrentTransactionsValue = visaValue;

            //keep only 1 gateway
            _gateways.RemoveAt(1);

            var result = SupportedGatewayFilterHelper.FilterGateways(_gateways, _options);
            Assert.AreEqual(expected, result.SupportedGateways.Any());
        }

        // [Test]
        // public void FilterGateways_QuarantineFiltering()
        // {
        //     // Quarantine all gateways
        //     foreach (var supportedGateway in _gateways)
        //         supportedGateway.Metrics.Quarantine_AddedOn = DateTime.UtcNow.AddHours(-6);
        //
        //     var result = SupportedGatewayFilterHelper.FilterGateways(_gateways, _options);
        //
        //     Assert.AreEqual(0, result.SupportedGateways.Count);
        // }

        [TestCase(20, ExpectedResult = 1)] // Below daily threshold
        [TestCase(40, ExpectedResult = 0)] // Above daily threshold
        public int FilterGateways_Is_Below_Daily_Threshold_Test(int totalDailyTransactionAmount)
        {
            // Quarantine all gateways
            foreach (var supportedGateway in _gateways)
                supportedGateway.Metrics.TotalDailyTransactionAmount = totalDailyTransactionAmount;

            var result = SupportedGatewayFilterHelper.FilterGateways(_gateways, _options);

            // Assert.AreEqual(1, result.SupportedGateways.Count);
            return result.SupportedGateways.Count;
        }

        [Test]
        public void FilterGateways_NoValidGateways_ReturnsEmptyList()
        {
            //deactivate the all gateways
            foreach (var supportedGateway in _gateways)
                supportedGateway.IsActive = false;

            var result = SupportedGatewayFilterHelper.FilterGateways(_gateways, _options);
            Assert.IsEmpty(result.SupportedGateways);
        }

        [TestCase(3000000, 1000000, ExpectedResult = true, TestName = "Under Limit - Should Pass")]
        [TestCase(3000000, 2900000, ExpectedResult = false, TestName = "Over Limit - Should Fail")]
        [TestCase(3000000, 1500000, ExpectedResult = false, TestName = "No Days Left - Should Fail")]
        public bool? Is_Below_Daily_Threshold_Test(int totalLimitCents, int processedCents)
        {
            var gateway = new SupportedGateway
            {
                Capabilities = new CapabilitiesModel() {TotalMonthlyMaxTransactionsValue = totalLimitCents},
                Metrics = new CycleMetrics {TotalTransactionAmount = processedCents}
            };

            var options = new OrchestrationOptions();
            return SupportedGatewayFilterHelper.Is_Below_Daily_Threshold(gateway, options);
        }

        [TestCase(2, 20, null, 30, true)]
        [TestCase(2, 25, null, null, null)]
        [TestCase(2, 25, null, 20, false)] // Exceeds count limit
        [TestCase(2, 25, null, 20, false)] // Exceeds value limit
        public void FilterGateways_DailyTransactionAmountLimits(int transactionCount, int transactionValue,
            int dailyMaxCount, int dailyMaxValue, bool expected)
        {
            //set the values for the first gateway
            _gateways[0].Capabilities.TotalDailyMaxTransactionsCount = dailyMaxCount;
            _gateways[0].Capabilities.TotalDailyMaxTransactionsValue = dailyMaxValue;

            // set metrics 
            _gateways[0].Metrics.TotalDailyTransactionCount = transactionCount;
            _gateways[0].Metrics.TotalDailyTransactionAmount = transactionValue;

            var result = SupportedGatewayFilterHelper.Is_Below_Daily_Amount_Limit(_gateways[0], _options);

            Assert.AreEqual(expected, result);
        }


        // Inline tests for Is_Tier_Qualified using the TestCase attribute.
        [TestCase(3, 2, ExpectedResult = true, TestName = "Gateway tier 3 is qualified when Merchant tier is 2")]
        [TestCase(2, 2, ExpectedResult = true, TestName = "Gateway tier 2 is qualified when Merchant tier is 2")]
        [TestCase(1, 2, ExpectedResult = false, TestName = "Gateway tier 1 is not qualified when Merchant tier is 2")]
        public bool? Is_Tier_Qualified_ReturnsExpectedResult(int gatewayTier, int merchantTier)
        {
            var gateway = new SupportedGateway {SponsorBank = "BankA", Tier = gatewayTier};
            var options = new OrchestrationOptions {RiskTier = merchantTier};
            return SupportedGatewayFilterHelper.Is_Tier_Qualified(gateway, options);
        }

        [Test]
        public void Is_Tier_Qualified_WithNullOptions_ReturnsNull()
        {
            var gateway = new SupportedGateway {SponsorBank = "BankA", Tier = 2};
            OrchestrationOptions options = null;
            var result = SupportedGatewayFilterHelper.Is_Tier_Qualified(gateway, options);
            Assert.IsNull(result);
        }

        [Test]
        public void Is_Tier_Qualified_WithNullMerchantTier_ReturnsNull()
        {
            var gateway = new SupportedGateway {SponsorBank = "BankA", Tier = 2};
            var options = new OrchestrationOptions {RiskTier = null};
            var result = SupportedGatewayFilterHelper.Is_Tier_Qualified(gateway, options);
            Assert.IsNull(result);
        }

        [TestCase("BankA", new[] {"BankB", "BankC"}, ExpectedResult = true, TestName = "SponsorBank BankA not used")]
        [TestCase("BankA", new[] {"BankA", "BankB"}, ExpectedResult = false, TestName = "SponsorBank BankA is used")]
        public bool? Exclude_Already_Used_SponsorBank_ReturnsExpectedResult(string sponsorBank, string[] usedBanks)
        {
            var gateway = new SupportedGateway {SponsorBank = sponsorBank, Tier = 1};
            var options = new OrchestrationOptions
            {
                AlreadyTriedProviders = new List<(Guid SupportedGatewayId, DateTime? Timestamp)>
                    {(Guid.NewGuid(), DateTime.UtcNow)},
                UsedSponsoredBanks = new List<string>(usedBanks)
            };
            return SupportedGatewayFilterHelper.Exclude_Already_Used_SponsorBank(gateway, options);
        }

        [Test]
        public void Exclude_Already_Used_SponsorBank_WithNullAlreadyTriedProviders_ReturnsNull()
        {
            var gateway = new SupportedGateway {SponsorBank = "BankA", Tier = 1};
            var options = new OrchestrationOptions
            {
                AlreadyTriedProviders = null,
                UsedSponsoredBanks = new List<string> {"BankB"}
            };
            var result = SupportedGatewayFilterHelper.Exclude_Already_Used_SponsorBank(gateway, options);
            Assert.IsNull(result);
        }

        [Test]
        public void Exclude_Already_Used_SponsorBank_WithNullUsedSponsoredBanks_ReturnsNull()
        {
            var gateway = new SupportedGateway {SponsorBank = "BankA", Tier = 1};
            var options = new OrchestrationOptions
            {
                AlreadyTriedProviders = new List<(Guid SupportedGatewayId, DateTime? Timestamp)>
                    {(Guid.NewGuid(), DateTime.UtcNow)},
                UsedSponsoredBanks = null
            };
            var result = SupportedGatewayFilterHelper.Exclude_Already_Used_SponsorBank(gateway, options);
            Assert.IsNull(result);
        }
    }
}