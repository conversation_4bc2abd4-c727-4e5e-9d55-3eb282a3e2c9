using System.Threading.Tasks;
using FlexCharge.Payments;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe;
using NUnit.Framework;
using Microsoft.AspNetCore.Http;
using Moq;

namespace FlexCharge.PaymentsUtils.Tests
{
    [TestFixture]
    public class PaysafeResponseCodeMappingTests : ResponseCodeMappingTestsBase
    {
        private PostgreSQLDbContext _dbContext;
        private Mock<IHttpContextAccessor> _mockHttpContextAccessor;

        [TestCase(null, null, ExpectedResult = "-2")]
        [TestCase(null, "", ExpectedResult = "-2")]
        [TestCase(null, "WRONG", ExpectedResult = "-2")]
        [TestCase("", null, ExpectedResult = "-2")]
        [TestCase("", "", ExpectedResult = "-2")]
        [TestCase("", "WRONG", ExpectedResult = "-2")]
        [TestCase("A_NEW_CODE", null, ExpectedResult = "-1")]
        [TestCase("A_NEW_CODE", "", ExpectedResult = "-1")]
        [TestCase("A_NEW_CODE", "WRONG", ExpectedResult = "-1")]
        [TestCase("COMPLETED", "", ExpectedResult = "0")]
        [TestCase("COMPLETED", "ANY", ExpectedResult = "0")]
        [TestCase("COMPLETED", null, ExpectedResult = "0")]
        [TestCase("HTTP422", "", ExpectedResult = "V01")]
        [TestCase("3014", "ANY", ExpectedResult = "5")]
        [TestCase("3014", "", ExpectedResult = "5")]
        [TestCase("3014", null, ExpectedResult = "5")]
        public async Task<string> TestMappingAsync(string? providerResponseCode, string? providerResponseMessage)
        {
            var mappedResponse =
                await PaysafeResponseMapper.GetMappedResponseAsync(providerResponseCode, providerResponseMessage);

            return mappedResponse.MappedResponseCode;
        }
    }
}