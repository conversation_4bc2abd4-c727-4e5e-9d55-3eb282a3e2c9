using NUnit.Framework;
using FlexCharge.PaymentsUtils;

namespace FlexCharge.PaymentsUtils.Tests
{
    [TestFixture]
    public class CapabilitiesHelperTests
    {
        // Tests for CannotSetDupCheckValue
        [TestCase(true, true, ExpectedResult = true)]
        [TestCase(true, false, ExpectedResult = true)]
        [TestCase(false, true, ExpectedResult = false)]
        [TestCase(false, false, ExpectedResult = false)]
        [TestCase(null, true, ExpectedResult = true)]
        [TestCase(null, false, ExpectedResult = false)]
        [TestCase(true, null, ExpectedResult = true)]
        [TestCase(false, null, ExpectedResult = false)]
        [TestCase(null, null, ExpectedResult = false)] // defaultValue is false
        public bool CannotSetDupCheckValue_Tests(bool? gatewayValue, bool? supportedGatewayValue)
        {
            return CapabilitiesHelper.CannotSetDupCheckValue(gatewayValue, supportedGatewayValue);
        }

        // Tests for CanSetDynamicDescriptorValue
        [TestCase(true, true, ExpectedResult = true)]
        [TestCase(true, false, ExpectedResult = true)]
        [TestCase(false, true, ExpectedResult = false)]
        [TestCase(false, false, ExpectedResult = false)]
        [TestCase(null, true, ExpectedResult = true)]
        [TestCase(null, false, ExpectedResult = false)]
        [TestCase(true, null, ExpectedResult = true)]
        [TestCase(false, null, ExpectedResult = false)]
        [TestCase(null, null, ExpectedResult = true)] // defaultValue is true
        public bool CanSetDynamicDescriptorValue_Tests(bool? gatewayValue, bool? supportedGatewayValue)
        {
            return CapabilitiesHelper.CanSetDynamicDescriptorValue(gatewayValue, supportedGatewayValue);
        }

        // Tests for UseDynamicDescriptorSuffix
        [TestCase(true, true, ExpectedResult = true)]
        [TestCase(true, false, ExpectedResult = true)]
        [TestCase(false, true, ExpectedResult = false)]
        [TestCase(false, false, ExpectedResult = false)]
        [TestCase(null, true, ExpectedResult = true)]
        [TestCase(null, false, ExpectedResult = false)]
        [TestCase(true, null, ExpectedResult = true)]
        [TestCase(false, null, ExpectedResult = false)]
        [TestCase(null, null, ExpectedResult = false)] // defaultValue is false
        public bool UseDynamicDescriptorSuffix_Tests(bool? gatewayValue, bool? supportedGatewayValue)
        {
            return CapabilitiesHelper.UseDynamicDescriptorSuffix(gatewayValue, supportedGatewayValue);
        }

        // Tests for CanSetUserDefinedValues
        [TestCase(true, true, ExpectedResult = true)]
        [TestCase(true, false, ExpectedResult = true)]
        [TestCase(false, true, ExpectedResult = false)]
        [TestCase(false, false, ExpectedResult = false)]
        [TestCase(null, true, ExpectedResult = true)]
        [TestCase(null, false, ExpectedResult = false)]
        [TestCase(true, null, ExpectedResult = true)]
        [TestCase(false, null, ExpectedResult = false)]
        [TestCase(null, null, ExpectedResult = false)] // defaultValue is false
        public bool CanSetUserDefinedValues_Tests(bool? gatewayValue, bool? supportedGatewayValue)
        {
            return CapabilitiesHelper.CanSetUserDefinedValues(gatewayValue, supportedGatewayValue);
        }

        // Tests for CanSetDescriptorAddress (gatewayValue is always null)
        [TestCase(true, ExpectedResult = true)]
        [TestCase(false, ExpectedResult = false)]
        [TestCase(null, ExpectedResult = false)] // defaultValue is false
        public bool CanSetDescriptorAddress_Tests(bool? supportedGatewayValue)
        {
            return CapabilitiesHelper.CanSetDescriptorAddress(null, supportedGatewayValue);
        }

        // Tests for CanSetDescriptorUrl (gatewayValue is always null)
        [TestCase(true, ExpectedResult = true)]
        [TestCase(false, ExpectedResult = false)]
        [TestCase(null, ExpectedResult = false)] // defaultValue is false
        public bool CanSetDescriptorUrl_Tests(bool? supportedGatewayValue)
        {
            return CapabilitiesHelper.CanSetDescriptorUrl(null, supportedGatewayValue);
        }

        // Additional tests for ResolveBool method (if accessible)
        [TestCase(true, true, false, ExpectedResult = true)]
        [TestCase(false, false, true, ExpectedResult = false)]
        [TestCase(null, null, true, ExpectedResult = true)]
        [TestCase(null, null, false, ExpectedResult = false)]
        public bool ResolveBool_Tests(bool? gatewayValue, bool? supportedGatewayValue, bool defaultValue)
        {
            // Assuming ResolveBool is accessible for testing
            // If it's private, this test won't be possible unless using reflection
            return (bool) typeof(CapabilitiesHelper)
                .GetMethod("ResolveBool",
                    System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.NonPublic)
                .Invoke(null, new object[] {gatewayValue, supportedGatewayValue, defaultValue});
        }
    }
}