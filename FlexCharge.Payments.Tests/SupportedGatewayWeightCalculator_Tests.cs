using System;
using System.Collections.Generic;
using System.Linq;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Entities.JsonbModels;
using FlexCharge.Payments.Services.PaymentServices.ProcessingStrategies;
using FlexCharge.Payments.Tests;
using NUnit.Framework;
using FlexCharge.PaymentsUtils;

namespace FlexCharge.PaymentsUtils.Tests
{
    [TestFixture]
    public class SupportedGatewayWeightCalculator_Tests : BaseTestsSetup
    {
        /// <summary>
        /// Single Gateway: For a single Visa gateway, regardless of its computed pre-normalized weight,
        /// the normalized weight should be 1.0.
        /// </summary>
        [TestCase(1000000, 200000, 1.0, "Visa", TestName = "SingleGateway_Visa_NormalizedWeightIsOne")]
        [TestCase(1000000, 200000, 1.0, "Mastercard", TestName = "SingleGateway_Mastercard_NormalizedWeightIsOne")]
        public void CalculateWeights_SingleGateway_Test(int capacity, int utilization, double expectedNormalized,
            string cardBrand)
        {
            // Arrange: Create one gateway with specified capacity and utilization.
            var gateway = new SupportedGateway
            {
                Name = "Gateway1",
                Capabilities = new CapabilitiesModel {Visa_MonthlyMaxValue = capacity},
                Metrics = new CycleMetrics() {VisaCurrentTransactionsValue = utilization}
            };

            var gateways = new List<SupportedGateway> {gateway};
            CardBrand brand = (CardBrand) Enum.Parse(typeof(CardBrand), cardBrand, true);

            // Act: Calculate weights.
            var weights = SupportedGatewayWeightCalculator.CalculateWeights(gateways, brand);

            // Assert: Since there's only one gateway, its normalized weight must be 1.0.
            Assert.AreEqual(1, weights.Count, "Expected exactly one gateway in result.");
            double actual = weights[gateway];
            Assert.That(actual, Is.EqualTo(expectedNormalized).Within(0.0001),
                $"Expected normalized weight {expectedNormalized} but got {actual}");
        }

        /// <summary>
        /// Gateways without capabilities should be skipped from weight calculation.
        /// </summary>
        [Test]
        public void CalculateWeights_GatewaysWithoutCapabilities_ShouldBeSkipped()
        {
            // Arrange
            var gatewayWithoutCapabilities = new SupportedGateway
            {
                Name = "NoCapabilitiesGateway",
                Capabilities = null, // Missing capabilities
                Metrics = new CycleMetrics {VisaCurrentTransactionsValue = 100000}
            };

            var gateways = new List<SupportedGateway> {gatewayWithoutCapabilities};
            var brand = CardBrand.Visa;

            // Act & Assert: Expect an exception since there are no valid gateways.
            var ex = Assert.Throws<Exception>(() => SupportedGatewayWeightCalculator.CalculateWeights(gateways, brand));
            StringAssert.Contains(
                "Total weight calculation resulted in zero for Visa, No valid supported gateways available",
                ex.Message);
        }

        /// <summary>
        /// Gateways without metrics should default to zero utilization, meaning they get full weight.
        /// </summary>
        [Test]
        public void CalculateWeights_GatewaysWithoutMetrics_ShouldDefaultToZeroUtilization()
        {
            // Arrange
            var gatewayWithoutMetrics = new SupportedGateway
            {
                Name = "NoMetricsGateway",
                Capabilities = new CapabilitiesModel {Visa_MonthlyMaxValue = 1000000},
                Metrics = null // No metrics available
            };

            var gateways = new List<SupportedGateway> {gatewayWithoutMetrics};
            var brand = CardBrand.Visa;

            // Act
            var weights = SupportedGatewayWeightCalculator.CalculateWeights(gateways, brand);

            // Assert: The only gateway should receive 100% weight.
            Assert.AreEqual(1, weights.Count, "Expected exactly one gateway in result.");
            Assert.AreEqual(1.0, weights[gatewayWithoutMetrics],
                "Expected weight to be 1.0 for gateway with no metrics.");
        }

        /// <summary>
        /// Multiple Gateways: Two Visa gateways with different utilizations.
        /// Pre-normalized weights are:
        ///   Gateway1: (capacity - utilization1)/capacity = (1000000 - 200000)/1000000 = 0.8
        ///   Gateway2: (capacity - utilization2)/capacity = (1000000 - 500000)/1000000 = 0.5
        /// Total pre-normalized = 0.8 + 0.5 = 1.3, so normalized weights are:
        ///   Gateway1 = 0.8/1.3 ≈ 0.61538, Gateway2 = 0.5/1.3 ≈ 0.38462.
        /// </summary>
        [TestCase(1000000, 200000, 1000000, 500000, 0.61538, 0.38462, "Visa",
            TestName = "MultipleGateways_Visa_NormalizedWeightsCalculated")]
        [TestCase(1000000, 200000, 1000000, 500000, 0.61538, 0.38462, "Mastercard",
            TestName = "MultipleGateways_Mastercard_NormalizedWeightsCalculated")]
        public void CalculateWeights_MultipleGateways_Test(
            int cap1, int util1, int cap2, int util2,
            double expectedWeight1, double expectedWeight2, string cardBrand)
        {
            // Arrange: Create two gateways.
            var gateway1 = new SupportedGateway
            {
                Name = "Gateway1",
                Capabilities = new CapabilitiesModel
                {
                    Visa_MonthlyMaxValue = cap1,
                    MasterCard_MonthlyMaxValue = cap1
                },
                Metrics = new CycleMetrics
                {
                    VisaCurrentTransactionsValue = util1,
                    MasterCardCurrentTransactionsValue = util1
                }
            };
            var gateway2 = new SupportedGateway
            {
                Name = "Gateway2",
                Capabilities = new CapabilitiesModel
                {
                    Visa_MonthlyMaxValue = cap2,
                    MasterCard_MonthlyMaxValue = cap2
                },
                Metrics = new CycleMetrics
                {
                    VisaCurrentTransactionsValue = util2,
                    MasterCardCurrentTransactionsValue = util2
                }
            };

            var gateways = new List<SupportedGateway> {gateway1, gateway2};
            CardBrand brand = (CardBrand) Enum.Parse(typeof(CardBrand), cardBrand, true);

            // Act: Calculate weights.
            var weights = SupportedGatewayWeightCalculator.CalculateWeights(gateways, brand);

            // Assert: Check each gateway's normalized weight and ensure the total sums to 1.
            Assert.AreEqual(2, weights.Count, "Expected two gateways in the result.");
            double actualWeight1 = weights[gateway1];
            double actualWeight2 = weights[gateway2];
            Assert.That(actualWeight1, Is.EqualTo(expectedWeight1).Within(0.0001),
                $"Gateway1: Expected {expectedWeight1}, got {actualWeight1}");
            Assert.That(actualWeight2, Is.EqualTo(expectedWeight2).Within(0.0001),
                $"Gateway2: Expected {expectedWeight2}, got {actualWeight2}");
            Assert.That(weights.Values.Sum(), Is.EqualTo(1.0).Within(0.0001), "Normalized weights should sum to 1.0");
        }

        /// <summary>
        /// Unsupported Card Network: When an unsupported card network (e.g., Amex) is passed,
        /// the method should calculate zero weight for each gateway. If total weight is zero,
        /// an exception is thrown.
        /// </summary>
        [TestCase(1000000, 0, "Amex", TestName = "UnsupportedNetwork_Amex_ThrowsExceptionDueToZeroWeight")]
        public void CalculateWeights_UnsupportedCardNetwork_Test(int capacity, int utilization, string cardBrand)
        {
            // Arrange: Create a gateway with valid capabilities, but use an unsupported card network.
            var gateway = new SupportedGateway
            {
                Name = "Gateway1",
                Capabilities = new CapabilitiesModel
                {
                    Visa_MonthlyMaxValue = capacity,
                    MasterCard_MonthlyMaxValue = capacity
                },
                Metrics = new CycleMetrics
                {
                    VisaCurrentTransactionsValue = utilization,
                    MasterCardCurrentTransactionsValue = utilization
                }
            };

            var gateways = new List<SupportedGateway> {gateway};
            CardBrand brand = (CardBrand) Enum.Parse(typeof(CardBrand), cardBrand, true);

            // Act & Assert: Expect an exception because total weight will be zero.
            var ex = Assert.Throws<Exception>(() =>
                SupportedGatewayWeightCalculator.CalculateWeights(gateways, brand));
            StringAssert.Contains("Total weight calculation resulted in zero", ex.Message);
        }


        private const int DEFAULT_MAX_VALUE = 1000000;

        private SupportedGateway CreateGateway(string name, int? visaMaxValue, int? masterMaxValue)
        {
            return new SupportedGateway
            {
                Name = name,
                Capabilities = new CapabilitiesModel()
                {
                    Visa_MonthlyMaxValue = visaMaxValue,
                    MasterCard_MonthlyMaxValue = masterMaxValue
                }
            };
        }

        [Test]
        public void CalculateWeights_ShouldDistributeWeightsCorrectly()
        {
            var gateways = new List<SupportedGateway>
            {
                CreateGateway("GatewayA", 50000, 60000),
                CreateGateway("GatewayB", 100000, 120000),
                CreateGateway("GatewayC", 250000, 300000)
            };

            var weights = SupportedGatewayWeightCalculator.CalculateWeights(gateways, CardBrand.Visa);

            Assert.IsNotEmpty(weights);
            Assert.That(weights.Values.All(weight => weight > 0), Is.True);
            Assert.That(weights.Values.Sum(), Is.EqualTo(1.0).Within(0.00001));
        }

        [Test]
        public void CalculateWeights_ShouldHandleNegativeValuesGracefully()
        {
            var gateways = new List<SupportedGateway>
            {
                CreateGateway("GatewayA", -50000, 60000),
                CreateGateway("GatewayB", 100000, 120000)
            };

            var weights = SupportedGatewayWeightCalculator.CalculateWeights(gateways, CardBrand.Visa);

            Assert.IsNotEmpty(weights);
            Assert.That(weights.Values.All(weight => weight >= 0), Is.True);
            Assert.That(weights.Values.Sum(), Is.EqualTo(1.0).Within(0.00001));
        }

        [Test]
        public void CalculateWeights_ShouldHandleZeroMaxValueGateways()
        {
            var gateways = new List<SupportedGateway>
            {
                CreateGateway("GatewayA", 0, 60000),
                CreateGateway("GatewayB", 100000, 120000)
            };

            var weights = SupportedGatewayWeightCalculator.CalculateWeights(gateways, CardBrand.Visa);

            Assert.IsNotEmpty(weights);
            Assert.That(weights.Values.All(weight => weight >= 0), Is.True);
            Assert.That(weights.Values.Sum(), Is.EqualTo(1.0).Within(0.00001));
        }

        [Test]
        public void CalculateWeights_ShouldThrowExceptionIfAllWeightsAreZero()
        {
            var gateways = new List<SupportedGateway>
            {
                CreateGateway("GatewayA", 0, 0),
                CreateGateway("GatewayB", 0, 0)
            };

            Assert.Throws<Exception>(() => SupportedGatewayWeightCalculator.CalculateWeights(gateways, CardBrand.Visa));
        }

        [Test]
        public void CalculateWeights_ShouldHandleNullCapabilities()
        {
            var gateways = new List<SupportedGateway>
            {
                new SupportedGateway {Name = "GatewayA", Capabilities = null},
                CreateGateway("GatewayB", 100000, 120000)
            };

            var weights = SupportedGatewayWeightCalculator.CalculateWeights(gateways, CardBrand.Visa);

            Assert.IsNotEmpty(weights);
            Assert.That(weights.ContainsKey(gateways[1]), Is.True);
            Assert.That(weights.ContainsKey(gateways[0]), Is.False); // GatewayA should be ignored
        }

        [Test]
        public void CalculateWeights_ShouldThrowExceptionIfNoGatewaysAvailable()
        {
            var gateways = new List<SupportedGateway>();

            Assert.Throws<ArgumentException>(() =>
                SupportedGatewayWeightCalculator.CalculateWeights(gateways, CardBrand.Visa));
        }
    }

    [TestFixture]
    public class CalculateCapacityBasedWeightTests : BaseTestsSetup
    {
        private const int DEFAULT_MAX_VALUE = 1000000;
        private const double DEFAULT_WEIGHT = 1.0;

        private SupportedGateway CreateGateway(string name, int? visaMax, int? visaUsed, int? masterMax,
            int? masterUsed)
        {
            return new SupportedGateway
            {
                Name = name,
                Capabilities = new CapabilitiesModel()
                {
                    Visa_MonthlyMaxValue = visaMax,
                    MasterCard_MonthlyMaxValue = masterMax
                },
                Metrics = visaUsed.HasValue || masterUsed.HasValue
                    ? new CycleMetrics()
                    {
                        VisaCurrentTransactionsValue = visaUsed ?? 0,
                        MasterCardCurrentTransactionsValue = masterUsed ?? 0
                    }
                    : null
            };
        }

        [Test]
        public void CalculateCapacityBasedWeight_ShouldCalculateCorrectly_ForVisa()
        {
            var gateway = CreateGateway("VisaGateway", 10000, 5000, null, null);
            double weight =
                SupportedGatewayWeightCalculator.CalculateCapacityBasedWeight(gateway, 10000, CardBrand.Visa);

            Assert.That(weight, Is.GreaterThan(0));
            Assert.That(weight, Is.LessThanOrEqualTo(1.0));
        }

        [Test]
        public void CalculateCapacityBasedWeight_ShouldReturnZero_WhenNoMetricsAvailable()
        {
            var gateway = CreateGateway("NoMetricsGateway", 10000, null, null, null);
            double weight =
                SupportedGatewayWeightCalculator.CalculateCapacityBasedWeight(gateway, 10000, CardBrand.Visa);

            Assert.That(weight, Is.GreaterThan(0));
            Assert.That(weight, Is.LessThanOrEqualTo(1.0));
        }

        [Test]
        public void CalculateCapacityBasedWeight_ShouldReturnZero_WhenTotalAmountIsZero()
        {
            var gateway = CreateGateway("ZeroTotalGateway", 10000, 5000, null, null);
            double weight = SupportedGatewayWeightCalculator.CalculateCapacityBasedWeight(gateway, 0, CardBrand.Visa);

            Assert.That(weight, Is.EqualTo(0.0));
        }

        [Test]
        public void CalculateCapacityBasedWeight_ShouldHandleNegativeRemainingCapacity()
        {
            var gateway = CreateGateway("NegativeCapacityGateway", 10000, 15000, null, null);
            double weight =
                SupportedGatewayWeightCalculator.CalculateCapacityBasedWeight(gateway, 10000, CardBrand.Visa);

            Assert.That(weight, Is.GreaterThanOrEqualTo(0));
        }

        [Test]
        public void CalculateCapacityBasedWeight_ShouldReturnZero_ForUnsupportedCardNetwork()
        {
            var gateway = CreateGateway("UnsupportedNetworkGateway", 10000, 5000, 10000, 5000);
            double weight =
                SupportedGatewayWeightCalculator.CalculateCapacityBasedWeight(gateway, 10000, (CardBrand) 999);

            Assert.That(weight, Is.EqualTo(0.0));
        }
    }
}