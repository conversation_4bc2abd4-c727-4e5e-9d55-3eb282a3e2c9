using System;
using MassTransit.Futures.Contracts;

namespace FlexCharge.Webhooks.Entities;

public class WebhookLogEntry : AuditableEntity
{
    public string EventName { get; set; }
    public int Status { get; set; } //- HttpStatus
    public string ResponseMessage { get; set; }
    public DateTime TimeStamp { get; set; } //- use createdOn
    public Guid SubscriberId { get; set; }
    public string Payload { get; set; } // Json
    public bool IsSandbox { get; set; }
    public string Environment { get; set; }
    public Guid EventInvocationId { get; set; }
    public Guid WebhookId { get; set; } //
}