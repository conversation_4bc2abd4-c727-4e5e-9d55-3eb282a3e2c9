#if DEBUG
using System;
using FlexCharge.Webhooks.Services;

namespace FlexCharge.Webhooks.SignatureVerificationSample;

public static class Program
{
    public static void TestMain(string[] args)
    {
        // From FlexCharge Merchant Portal
        string subscriberKey = "XRmKBxG5uvt1qWzqvp+T6CAbTo0MB89GTxXZD5cHA56RP7Mj4NbnHQOR1Y8uorUU9YQz8ujaVRUdm9vTSkPZSw==";

        // Webhook received payload
        //var payload = message.Content.ReadAsStringAsync().Result;
        var payload = "{\"Event\":\"order.completed\",\"TimeStamp\":\"2023-03-20T17:16:40.898703Z\",\"EventData\":null,\"ExternalOrderId\":\"a9735210-1349-49bf-bfde-b737dd07872a\",\"OrderId\":\"ac9674ed-cbfe-49aa-bc8b-eb1d2b74c429\",\"ConfirmationId\":\"22ACD1D9\",\"IsTestMode\":true,\"IsResent\":false}";
        
        // Merchant's Webhook endpoint host
        string host = "fctestwebhook.free.beeceptor.com";

        // From webhook's headers:
        
        //var nonce = headers.GetValues("x-fc-nonce").First();
        var nonce = "5f1c2de28a76457c9cb79d1740f2260a";

        //var date = headers.GetValues("x-fc-date").First();
        var date = "Mon, 20 Mar 2023 17:16:40 GMT";

        //var var flexChargeAuthorization = headers.GetValues("x-fc-authorization").First();
        var flexChargeAuthorization = "HMAC-SHA512 SignedHeaders=x-fc-nonce;x-fc-date;host;x-fc-content-sha512&Signature=+HXN8ZewgINLk+uC/UI92HSWmLK7gZOECPxOGEM91ATyfyzScMF/+osEK5B0UjO7OFqahDvesSo8jmUWMZtQnA==";
        
        
        // Extracting signature from x-fc-authorization header
        var signature = flexChargeAuthorization.Substring(flexChargeAuthorization.IndexOf("Signature=") + 10);


        var recalculatedSignature = HMACSHA512.ComputeWebhookSignature(subscriberKey, host, payload, nonce, date);
        var verified = recalculatedSignature == signature;
        
        Console.WriteLine($"Signature verification result: {verified}");
    }
}
#endif