Initial Setup

1. Goto Init.ps1 => Replace $microServiceName with your service name and $dbName with your name
2. Open powershell and navigate to the project root folder Run .\Init.ps1 script
3. Right click the project Add > DockerSupport


Run docker 
1. disable consul if you want to run a test instance


Adding entities:

1. When adding entities create a class in "Entities" folder and inherit from "AuditableEntity".
2. <PERSON>o "MsSqlContext" and add the soft delete functionality on "OnModelCreating" function.
	-  modelBuilder.Entity<{EntityClassName}>().Property<bool>("IsDeleted");
    -  modelBuilder.Entity<{EntityClassName}>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

Adding DTO's

1. Add a class in DTO folder
2. Add mappings on the AutoMapper.cs class
	- 
