using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
/* OLD, NOT USED
namespace FlexCharge.Webhooks.Authorization
{
    public class WebhookClaims
    {
        public const string WEBHOOK_READ_ALL = "webhook.read.all";
        public const string WEBHOOK_READ_ONE = "webhook.read.one";
        public const string WEBHOOK_ADD = "webhook.add";
        public const string WEBHOOK_UPDATE = "webhook.update";
        public const string WEBHOOK_DELETE = "webhook.delete";
        public const string WEBHOOK_MANAGE = "webhook.manage";
    }
    
    public class EventClaims
    {
        public const string EVENT_READ_ALL = "event.read.all";
        public const string EVENT_READ_ONE = "event.read.one";
        public const string EVENT_ADD = "event.add";
        public const string EVENT_UPDATE = "event.update";
        public const string EVENT_DELETE = "event.delete";
        public const string EVENT_MANAGE = "event.manage";
    }
}
*/