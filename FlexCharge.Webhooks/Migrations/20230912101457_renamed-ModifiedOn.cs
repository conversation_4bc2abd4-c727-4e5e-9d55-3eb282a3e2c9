using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Webhooks.Migrations
{
    /// <inheritdoc />
    public partial class renamedModifiedOn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ModifyOn",
                table: "WebhooksLog",
                newName: "ModifiedOn");

            migrationBuilder.RenameColumn(
                name: "ModifyOn",
                table: "Webhooks",
                newName: "ModifiedOn");

            migrationBuilder.RenameColumn(
                name: "ModifyOn",
                table: "Subscribers",
                newName: "ModifiedOn");

            migrationBuilder.RenameColumn(
                name: "ModifyOn",
                table: "Events",
                newName: "ModifiedOn");

            migrationBuilder.RenameColumn(
                name: "ModifyOn",
                table: "Activities",
                newName: "ModifiedOn");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ModifiedOn",
                table: "WebhooksLog",
                newName: "ModifyOn");

            migrationBuilder.RenameColumn(
                name: "ModifiedOn",
                table: "Webhooks",
                newName: "ModifyOn");

            migrationBuilder.RenameColumn(
                name: "ModifiedOn",
                table: "Subscribers",
                newName: "ModifyOn");

            migrationBuilder.RenameColumn(
                name: "ModifiedOn",
                table: "Events",
                newName: "ModifyOn");

            migrationBuilder.RenameColumn(
                name: "ModifiedOn",
                table: "Activities",
                newName: "ModifyOn");
        }
    }
}
