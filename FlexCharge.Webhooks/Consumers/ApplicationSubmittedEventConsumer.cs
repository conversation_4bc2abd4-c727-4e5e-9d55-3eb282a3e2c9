using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class ApplicationSubmittedEventConsumer : IdempotentEventConsumer<ApplicationSubmittedEvent>
{
    private IWebhookService _webhookService;

    public ApplicationSubmittedEventConsumer(IWebhookService webhookService, IServiceScopeFactory serviceScopeFactory) :
        base(serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeEvent(ApplicationSubmittedEvent message, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ApplicationSubmittedEvent>()
            .Tag("Message", JsonConvert.SerializeObject(message));

        try
        {
            var webhookEvent = message;

            string event_name = $"application.created";


            var webhookPayload = new WebhookPayload(
                event_name,
                DateTime.UtcNow,
                webhookEvent.ApplicationId,
                webhookEvent.Pid,
                message,
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            bool sentSuccessfully =
                await _webhookService.InvokeWebhookAsync(
                    webhookEvent.ApplicationId,
                    webhookEvent.Pid,
                    webhookEvent.IntegrationPartnerId,
                    event_name,
                    payload,
                    webhookEvent.ApplicationId);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, $"Unable to invoke webhook");
        }
    }
}