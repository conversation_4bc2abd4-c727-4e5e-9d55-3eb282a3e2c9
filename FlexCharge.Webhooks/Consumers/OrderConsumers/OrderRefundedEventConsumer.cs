using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Webhooks.DTO;
using FlexCharge.Webhooks.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Webhooks.Consumers;

public class OrderRefundedEventConsumer : IdempotentEventConsumer<OrderRefundedEvent>
{
    private IWebhookService _webhookService;

    public OrderRefundedEventConsumer(IWebhookService webhookService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _webhookService = webhookService;
    }

    protected override async Task ConsumeEvent(OrderRefundedEvent message, CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId)
            .Log.Information("ORDER REFUNDED");

        try
        {
            var orderRefundedEventEvent = message;
            string event_name = $"order.refunded";

            var eventData = new RefundEventData
            (
                orderRefundedEventEvent.Timestamp,
                orderRefundedEventEvent.Message,
                orderRefundedEventEvent.TransactionId,
                orderRefundedEventEvent.InitialTransactionReference,
                orderRefundedEventEvent.Amount,
                orderRefundedEventEvent.Currency
            );


            var webhookPayload = new OrderWebhookPayload(
                event_name,
                DateTime.UtcNow,
                orderRefundedEventEvent.Mid,
                orderRefundedEventEvent.Pid,
                orderRefundedEventEvent.ExternalOrderId,
                orderRefundedEventEvent.OrderId,
                null,
                eventData,
                !EnvironmentHelper.IsInProduction,
                false);

            var payload = JsonConvert.SerializeObject(webhookPayload);

            bool sentSuccessfully = await _webhookService.InvokeWebhookAsync(orderRefundedEventEvent.Mid,
                orderRefundedEventEvent.Pid,
                null,
                event_name,
                payload,
                orderRefundedEventEvent.OrderId);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e, $"Unable to invoke webhook");
        }
    }
}