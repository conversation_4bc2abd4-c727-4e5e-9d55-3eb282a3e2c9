using EntityFramework.Exceptions.Common;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Logging.LogSuppression;
using FlexCharge.Common.Telemetry;
using FlexCharge.Scheduler.Entities;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Scheduler.Services;

public class InternalIdToSchedulerIdMapperService : IInternalIdToSchedulerIdMapper
{
    private readonly PostgreSQLDbContext _dbContext;

    public InternalIdToSchedulerIdMapperService(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task AddOrOverrideMappingAsync(Guid internalId, string schedulerId, bool isRecurringJob,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<InternalIdToSchedulerIdMapperService>()
            .Baggage("InternalId", internalId)
            .Baggage("SchedulerId", schedulerId)
            .Baggage("IsRecurringJob", isRecurringJob);

        JobGuidToIdMapping jmapping = null;
        try
        {
            // We expect UniqueConstraintException to be thrown here -> turning error into warning in the log
            using var _ = DatabaseLogSuppressor
                .SuppressUniqueConstraintError<PostgreSQLDbContext>("JobGuidToIdMappings", "PK_JobGuidToIdMappings");

            jmapping = new JobGuidToIdMapping(internalId, schedulerId, isRecurringJob);
            await _dbContext.JobGuidToIdMappings.AddAsync(jmapping, token);
            await _dbContext.SaveChangesAsync(token);
        }
        catch (UniqueConstraintException e)
        {
            _dbContext.Entry(jmapping).State = EntityState.Detached;
            jmapping =
                await _dbContext.JobGuidToIdMappings.SingleOrDefaultAsync(x => x.Id == internalId, token);
            jmapping.SchedulerJobId = schedulerId;
            jmapping.IsRecurringJob = isRecurringJob;
            await _dbContext.SaveChangesAsync(token);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<(string SchedulerJobId, bool IsRecurringJob)> GetMappingAsync(Guid internalId,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<InternalIdToSchedulerIdMapperService>()
            .Baggage("InternalId", internalId);

        try
        {
            var job_id_mapping =
                await _dbContext.JobGuidToIdMappings.SingleOrDefaultAsync(x => x.Id == internalId, token);

            return new(job_id_mapping.SchedulerJobId, job_id_mapping.IsRecurringJob);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task RemoveMappingAsync(Guid internalId, CancellationToken token)
    {
        using var workspan = Workspan.Start<InternalIdToSchedulerIdMapperService>()
            .Baggage("InternalId", internalId);

        try
        {
            var job_id_mapping_to_remove =
                await _dbContext.JobGuidToIdMappings.SingleOrDefaultAsync(x => x.Id == internalId, token);

            if (job_id_mapping_to_remove != null)
            {
                _dbContext.JobGuidToIdMappings.Remove(job_id_mapping_to_remove);
                await _dbContext.SaveChangesAsync(token);
            }
            else
            {
                workspan.Log.Error("No scheduler id found for internal id: {InternalId}", internalId);
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task RemoveMappingBySchedulerIdAsync(string schedulerId, CancellationToken token)
    {
        using var workspan = Workspan.Start<InternalIdToSchedulerIdMapperService>()
            .Baggage("SchedulerId", schedulerId);

        try
        {
            foreach (var job_id_mapping_to_remove in
                     await _dbContext.JobGuidToIdMappings.Where(x => x.SchedulerJobId == schedulerId).ToListAsync())
            {
                _dbContext.JobGuidToIdMappings.Remove(job_id_mapping_to_remove);
            }

            await _dbContext.SaveChangesAsync(token);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }
}