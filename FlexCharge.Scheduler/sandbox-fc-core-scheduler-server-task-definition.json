{"containerDefinitions": [{"name": "core-scheduler", "image": "556663010871.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-scheduler:97fee549a1a0d04d6a050cc40c90e3d1dc824825", "cpu": 0, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"], "interval": 20, "timeout": 5, "retries": 3, "startPeriod": 60}, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "scheduler_service_sandbox"}, {"name": "DB_DATABASE", "value": "fc_scheduler"}, {"name": "DB_HOST", "value": "flexcharge-sandbox.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Sandbox"}, {"name": "AWS_COGNITO_USER_POOL_ID", "value": "us-east-1_7Snu9L5Rt"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}, {"name": "NEW_RELIC_APP_NAME", "value": "Scheduler-sandbox"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_DB_SCHEDULER_PASSWORD-UlB4OQ"}, {"name": "AWS_IAM_COGNITO_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_IAM_COGNITO_KEY-SVc81A"}, {"name": "AWS_IAM_COGNITO_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_IAM_COGNITO_SECRET-7KOA9j"}, {"name": "AWS_IAM_COGNITO_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_COGNITO_CLIENT_ID-flykbv"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_SNS_IAM_ACCESS_KEY-vt5dWo"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:SANDBOX_SNS_IAM_SECRET_KEY-iDGfTK"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-scheduler-server-sandbox", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-scheduler-server-sandbox", "taskRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Sandbox-Role", "executionRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Sandbox-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024"}